﻿#include "stdafx.h"
#include "kproxypdfplugin.h"
#include <public_header/kliteui/kdrawhelper.h>
#include <kprometheus/kpromeapplication.h>
#include <kprometheus/kpromemainwindow.h>
#include <kprometheus/kpromeevents.h>
#include <kprometheus/kappidlesvr.h>
#include <auth/productinfo.h>
#include "infocollector/infocollector.h"
#include <kprometheus/kpromecentralarea.h>
#include <kapprunmonitor.h>
#include "krt/kconfigmanagercenter.h"

//TODO(zhangxinzhao):
#ifdef Q_OS_DARWIN
static const QString s_pluginEntrance = "newpdf";
#else
static const QString s_pluginEntrance = "newdocs";
#endif

KPdfProxyPugin::KPdfProxyPugin(QObject* parent)
	: QObject(parent)
	, m_appObj(nullptr)
	, m_hasTriggered(false)
	, m_loadWidget(nullptr)
	, m_failWindow(nullptr)
	, m_source("pdf_co_newpdf")
{
	 m_pluginPropertyMap.clear();
}

KPdfProxyPugin::~KPdfProxyPugin()
{

}

QString KPdfProxyPugin::appID() const
{
	return m_appID;
}

void KPdfProxyPugin::setAppID(const QString& appID)
{
	m_appID = appID;
	if (KAppMgr::getInstance()->isLoaded())
	{
		setupAppObj();
	}
	else
	{
		connect(KAppMgr::getInstance(), SIGNAL(loadFinished()),
			this, SLOT(onAppMgrLoadFinished()), Qt::UniqueConnection);
	}
}

void KPdfProxyPugin::setAppProperty(std::map<QString, QString> pluginPropertyMap)
{
	m_pluginPropertyMap = pluginPropertyMap;
}

void KPdfProxyPugin::onTrigger()
{
	if (!m_appObj || !m_appObj->checkIsValid())
	{
		SENDAPPRRUNINFO_PARAMS3(m_appID, getAppRunFailedStringByCode(AppRunFailedCode_NoAppObj), m_source);
		if (m_pluginPropertyMap.count("url") != 0)
		{
			QString url = getUrl(m_pluginPropertyMap["url"]);
			openUrl(url);
		}
		return;
	}

	if (m_appObj->isLoading())
	{
		SENDAPPRRUNINFO_PARAMS3(m_appID, getAppRunFailedStringByCode(AppRunFailedCode_PendingTask), m_source);
		return;
	}
	m_params.clear();
	m_appObj->setProperty("entrance", s_pluginEntrance);
	
	if ((m_appObj->isLoaded() || m_appObj->checkIsCanLoadInLocal()) && !m_hasTriggered)
	{
		m_hasTriggered = true;
		setAppObjPorperty();
		runApp();
		if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kpdffromscanner") == 0)
			emit onPluginLoadSuccess("kpdffromscanner");
	}
	else
	{
		SENDAPPRRUNINFO_PARAMS4(m_params, m_appID, getAppRunFailedStringByCode(AppRunFailedCode_StartLoadAppObj), m_source);
		m_appObj->load(KAppObj::Load_Manual);
	}
}

void KPdfProxyPugin::requestWidget(QWidget* parent, QString from/* = "wpsoffice"*/, QString p0Value/* = QString()*/)
{
	if (!m_appObj || !m_appObj->checkIsValid())
		return;
	if ((m_appObj->isLoaded() || m_appObj->checkIsCanLoadInLocal()))
		return;
	m_parentWidget = parent;
	m_loadWidget = new KPluginLoadWindow(parent, m_appObj, from);
	QString titleTr = m_appObj->getProp(KAPPOBJ_PROP_NAME).toString();
	if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kpdffromscanner") == 0)
		titleTr = tr("New from scanner");
	else if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "photo2pdf") == 0)
		titleTr = tr("New from picture");
	else if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "pdfcadmutualconverter") == 0)
		titleTr = tr("CAD to PDF");
	m_loadWidget->setLoadTitleText(tr("Loading %1, please wait").arg(titleTr));
	connect(m_loadWidget, &KPluginLoadWindow::loadSuccess, this, &KPdfProxyPugin::onLoadSuccess);
	connect(m_loadWidget, &KPluginLoadWindow::loadCanceled, this, &KPdfProxyPugin::onLoadCanceled);
	connect(m_loadWidget, &KPluginLoadWindow::loadFailed, this, &KPdfProxyPugin::onLoadFailed);
	connect(m_loadWidget, SIGNAL(showPopupFailedWindow()), this, SLOT(onShowPopupFailedWindow()));
	connect(m_loadWidget, SIGNAL(loadProgressUpdate(double)), this, SLOT(loadProgressUpdate(double)));
	if (m_loadWidget->isVisible())
		return;
	m_loadWidget->show();
}

void KPdfProxyPugin::setupAppObj()
{
	m_appObj = KAppMgr::getInstance()->getApp(m_appID);
}

void KPdfProxyPugin::setAppObjPorperty()
{
	m_param.clear();
//TODO(zhangxinzhao):
#ifdef Q_OS_DARWIN
	if (m_appID == "kpdf2wordv3")
#else
	if (m_appID == "kpdf2wordv2")
#endif
	{
		std::map<QString, QString>::iterator iter;
		iter = m_pluginPropertyMap.begin();
		while (iter != m_pluginPropertyMap.end()) {
			if (iter->first != "url")
				m_param = iter->second;
			iter++;
		}
		return;
	}
	QObject* entryObject = m_appObj->entryObject();
	if (entryObject)
	{
		std::map<QString, QString>::iterator iter;
		iter = m_pluginPropertyMap.begin();
		while(iter != m_pluginPropertyMap.end()) {
			char*  chPropertyName;
			QByteArray ba = iter->first.toLatin1(); 
			chPropertyName=ba.data();
			entryObject->setProperty(chPropertyName, iter->second);
			iter++;
		}
	}	
}

void KPdfProxyPugin::runApp()
{
	if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kpdffromscanner") == 0)//在pdfmain里面有插件启动流程逻辑
		return;
	KAppContext context;
	context.entrance = Entrance_Newdocs;
#ifdef Q_OS_DARWIN
	context.src = "newpdf";
#else
	context.src = m_source;//首页新建//tup componen
#endif
	QWidget* hostWindow = promeApp->currentPromeMainWindow();
	context.hostWindow = hostWindow;
	context.arguments["action"] = m_param;
#ifdef Q_OS_DARWIN
	context.arguments["componentname"] = "newpdf";
#else
	context.arguments["componentname"] = "pdf_co_newpdf";
#endif
	for (auto iter = m_params.cbegin(); iter != m_params.cend(); iter++)
		context.arguments.insert(iter.key(), iter.value());
	SENDAPPRRUNINFO_PARAMS3_CONTEXT(&context, m_appID, getAppRunFailedStringByCode(AppRunFailedCode_StartRunAppObj));
	m_appObj->run(context);
}

QString KPdfProxyPugin::getUrl(const QString& url)
{
#ifdef Q_OS_WIN
	QString version = KDcInfoCDetail::getVersion();
	QString channel = KDcInfoCDetail::getMC();

	QString fullUrl = "";
 	fullUrl = url;
	fullUrl.append("&chan=").append(channel).append("&ver=").append(version);
	return fullUrl;
#else
	return QString();
#endif
}

void KPdfProxyPugin::openUrl(const QString& url)
{
	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return;

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;

	if (krt::kcmc::support("PdfNewDocsOpenUrl"))
		contentArea->addBrowserPage(url);
}

QString KPdfProxyPugin::getEntrance() const
{
	return s_pluginEntrance;
}

void KPdfProxyPugin::onLoadSuccess()
{
	SENDAPPRRUNINFO_PARAMS4(m_params, m_appID, getAppRunFailedStringByCode(AppRunFailedCode_LoadAppObjSuccess), m_source);
	emit onpluginJsIdProgress(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), 0);
	if (m_loadWidget)
		m_loadWidget->close();
	if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kpdffromscanner") == 0)
	{
		emit onPluginLoadSuccess("kpdffromscanner");
		return;
	}

	onTrigger();
}

void KPdfProxyPugin::onLoadFailed()
{
	SENDAPPRRUNINFO_PARAMS4(m_params, m_appID, getAppRunFailedStringByCode(AppRunFailedCode_LoadAppObjFailed), m_source);
}

void KPdfProxyPugin::onLoadCanceled()
{
	SENDAPPRRUNINFO_PARAMS4(m_params, m_appID, getAppRunFailedStringByCode(AppRunFailedCode_LoadAppObjCancel), m_source);
}

void KPdfProxyPugin::onOpenUrl()
{
	if (m_failWindow)
		m_failWindow->close();
	if (m_pluginPropertyMap.count("url") != 0)
	{
		QString url = getUrl(m_pluginPropertyMap["url"]);
		openUrl(url);
	}
}

void KPdfProxyPugin::loadProgressUpdate(double progress)
{
	emit onpluginJsIdProgress(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), progress);
}

void KPdfProxyPugin::onShowPopupFailedWindow()
{
	if (m_appObj)
	{
		emit onpluginJsIdProgress(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), 0);
		m_failWindow = new KPluginLoadFailedWidget(m_parentWidget, m_appObj);
		connect(m_failWindow, SIGNAL(openUrl()), this, SLOT(onOpenUrl()));
		if (m_failWindow->isVisible())
			return;
		QString appObjText = m_appObj->getProp(KAPPOBJ_PROP_NAME).toString();
		if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kpdffromscanner") == 0)
			appObjText = tr("New from scanner");
		else if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "photo2pdf") == 0)
			appObjText = tr("New from picture");
		else if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "pdfcadmutualconverter") == 0)
			appObjText = tr("New from CAD file");
		else if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kpdf2wordv2") == 0)
			appObjText = tr("PDF2Word");
		else if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kdoctranslate") == 0)
			appObjText = tr("PDFDocTranslate");

		QString contentText = "";
		if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kpdf2wordv2") == 0)
		{
			if (QString::compare(m_param, "Merge") == 0)
				contentText = tr("%1 loading exception, please go to the web version for operation, or try again later.").arg(tr("PDF2MergeSplit"));
			else
				contentText = tr("%1 loading exception, please go to the web version for operation, or try again later.").arg(appObjText);
		}
		else if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kdoctranslate") == 0)
			contentText = tr("%1 loading exception, please try again later.").arg(appObjText);
		else
			contentText = tr("Network error,%1 load failed, please try again later.").arg(appObjText);

		m_failWindow->setContentText(contentText);
		bool isShowToWeb = true;
		if (QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kpdffromscanner") == 0 
			|| QString::compare(m_appObj->getProp(KAPPOBJ_PROP_ID).toString(), "kdoctranslate") == 0)
			isShowToWeb = false;
		m_failWindow->setShowIsWebBtn(isShowToWeb);
		m_failWindow->show();
	}
}
