﻿#include "stdafx.h"
#include "knewdochelper.h"
#include "knewdocjsapi.h"
#include "utility/kxsettings.h"
#include "utility/knewdocshelper.h"

#include <krt/dirs.h>
#include <krt/kentrycontrol/kentrycontrolproxy.h>
#include <kprometheus/kpromeapplication.h>
#include <kprometheus/kpromeskin.h>
#include <kprometheus/kpromepluginwidget.h>
#include "ksolite/kpluginmgr.h"
#include "krt/krt.h"
#include <auth/productinfo.h>
#include "kdocercorelitehelper.h"

#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include "utilities/path/module/kcurrentmod.h"

#include <ksolite/kclientsvrproxy.h>
#include <ksolite/kdomainmgr.h>
#include <kprometheus/kpromemainwindow.h>
#include <kprometheus/kpromecentralarea.h>

#include "src/kpromenewdocspage.h"
#include "tabbar/kpromenewdocstabbarconfig.h"
#include <ksolite/kconfigcentersettings.h>
#include "src/knewdocswebwidget.h"
#include <ksolite/kpluginconfig.h>
#include <ksolite/ksupportutil.h>
#ifdef Q_OS_DARWIN
#include <kprometheus/kpromeupdatemgr.h>
#endif
#include "ksolite/kdocer/kdocercoreitef.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
#include <kdocerfunctional.h>
#include <ksolite/kdocer/kdocerresnetwork/kdocernetworkwrapper.h>
#include "krt/kconfigmanagercenter.h"

#define szConfigFilename	"config.ini"
#define szLangFilename		"language.ini"
#define szTSFileName		"mui/"

namespace{
#if defined(Q_OS_LINUX) && !defined(Q_OS_OHOS) && !defined(Q_OS_ANDROID) && !defined(WEB_OFFICE_ENABLE)
	const char* const g_strCreateDocConfigSwitchName = "createDocConfig";
#endif

	struct WebResInfo
	{
		WebResInfo() {};
		WebResInfo(const QString& name, const QString& version) :
			webResName(name),
			webResVersion(version) {}

		QString webResName;
		QString webResVersion;
	};

	QString getNewDocerModuleResourcePath()
	{
#if (defined(Q_OS_DARWIN) || defined(Q_OS_OHOS))
		return docer::base::KCurrentModule::getModuleResourceDirPath();
#else
		return QDir::fromNativeSeparators(krt::dirs::resources()) % "/addons/" % docer::base::KCurrentModule::getCurrentModuleName();
#endif
	}

	QString readUrlNew(const QString& url, const ResDir& resDir = ResDir::HomePath)
	{
		if (resDir == ResDir::HomePath && krt::kcmc::support("NewDocReadUrlNewByDomainUrl"))
		{
			std::unique_ptr<KxPathProvider::WebResDirDetail> detail(new KxPathProvider::WebResDirDetail);
			QString webResDir = KxPathProvider::getWebResDir(resDir, detail.get());
			return KDocerUtils::getDomainUrl("personallocal") % QLatin1String("/addons/knewdocs") % detail->subPath % url;
		}

		return QString("file:///") % KxPathProvider::getWebResDir(resDir) % url;
	}

	QString getComponentUrl(const QString& appName)
	{
		return krt::kentrycontrol::isEntryNeedHide("template/local/" % appName) 
			? readUrlNew("index.html#/local?pageType=localAndPrivatePage&app=" % appName, EnterprisePath)
			: readUrlNew("index.html#/screen?app=" % appName);
	}
}

KxCreateDocInstance& KxCreateDocInstance::instance()
{
	static KxCreateDocInstance inst;
	return inst;
}

KxCreateDocInstance::KxCreateDocInstance()
{
	QDir dir(docer::base::KCurrentModule::getModuleResourceDirPath());
	init(dir);
}

KxCreateDocInstance::~KxCreateDocInstance()
{
	unInit();
}

QString KxCreateDocInstance::getCurrentModuleName()
{
	return docer::base::KCurrentModule::getCurrentModuleName();
}

QString KxCreateDocInstance::getSubThemeDirPath()
{
	return _dir.filePath("mui/default/theme");
}

void KxCreateDocInstance::init(const QDir& dir)
{
	_dir = dir;
	KxPathProvider::init(dir);

	initRes();
	onCoreInited();
}

void KxCreateDocInstance::unInit()
{
	unInitRes();
}

void KxCreateDocInstance::setUserPanelJson(const QJsonObject& jsonObj)
{
	m_jsonUserPanel = jsonObj;
	emit panelJsonUpdated(jsonObj);
}


QJsonObject KxCreateDocInstance::getUserPanelJson()
{
	return m_jsonUserPanel;
}


void KxCreateDocInstance::setUserPanelArray(const QJsonArray& jsonArray)
{
	m_arrayUserPanel = jsonArray;
	emit panelArrayUpdated(jsonArray);
}

QJsonArray KxCreateDocInstance::getUserPanelArray()
{
	return m_arrayUserPanel;
}

bool KxCreateDocInstance::downloadFontList(const QVariantList& fontList)
{
	bool downloadFont = false;
	if (fontList.isEmpty())
	{
		KxLoggerLite::writeWarning(KPluginNameW,
			L"KxCreateDocInstance::downloadFontList: fontList.isEmpty()");
		return downloadFont;
	}

	QString strInfo =
		QString("KxCreateDocInstance::downloadFontList: %1").
		arg(JsonHelper::convertVariantToString(fontList));
	KxLoggerLite::writeInfo(KPluginNameW, strInfo.toStdWString());

	QChar dirSeparator = QDir::separator();
	QString docerFontsPath = krt::dirs::officeHome() % dirSeparator % "docerFonts" % dirSeparator;
	QFontDatabase fontDB;
	for (QVariantList::const_iterator fontItem = fontList.constBegin();
		fontItem != fontList.constEnd(); ++fontItem)
	{
		QVariantMap fontMap = fontItem->toMap();
		if (fontMap.isEmpty())
			continue;

		QString name = fontMap.value("name").toString();
		if (name.isEmpty())
			continue;

		//字体已安装过
		if (fontDB.hasFamily(name))
			continue;

		QString installFilePath = docerFontsPath % name % ".ttf";
		//字体文件已存在
		if (QFile::exists(installFilePath))
			continue;

		QString url = fontMap.value("url").toString();
		QString md5 = fontMap.value("md5").toString();
		QString id = fontMap.value("id").toString();
		QStringList subUrls = fontMap.value("subUrls").toStringList();
		qlonglong size = fontMap.value("size").toLongLong();
		QString downloadKey = fontMap.value("download_key").toString();

		DownloadArgs downloadArgs;
		downloadArgs.resourceArgs.md5 = md5;
		downloadArgs.resourceArgs.resSize = size;
		downloadArgs.resourceKey = "cloudfont";
		downloadArgs.resourceArgs.id = id;
		downloadArgs.commonArgs.plgName = docer::base::KCurrentModule::getCurrentModuleName();
		downloadArgs.commonArgs.plgVer = docer::base::KCurrentModule::getFileVersion();
		downloadArgs.saveArgs.bUnzip = true;
		downloadArgs.saveArgs.bCheckMd5 = true;
		downloadArgs.resourceArgs.downloadKey = downloadKey;
		downloadArgs.resourceArgs.urls << url << subUrls;

		QString saveFilePath = docerFontsPath % "cache" % dirSeparator % name;
		downloadArgs.saveArgs.saveFilePath = saveFilePath;
		downloadArgs.saveArgs.installFilePath = installFilePath;
		downloadArgs.methodInfo.option = DirectGetOperation;
		KDocerResNetworkSDK::getInstance().startDownload(downloadArgs,
			bindContext(this, [=](const QString& downloadId, DownloadSuccessInfo info) {
				auto docerCoreLite= getIKDocerCoreLite();
				if (!docerCoreLite)
					return;

				QObject* pObject = dynamic_cast<QObject*>(docerCoreLite);
				if (!pObject)
					return;

				QVariantMap mapData;
				QVariantMap mapInfo = fontMap;
				mapInfo.insert("name", name.toUtf8().toBase64());
				mapInfo.insert("fontFile", installFilePath.toUtf8().toBase64());
				mapData.insert("info", mapInfo);
				mapData.insert("module", KPluginName);
				constexpr const char* syncFontList = "syncFontList";
				mapData.insert("action", syncFontList);
				QMetaObject::invokeMethod(pObject, "BroadcastMessage", Qt::QueuedConnection,
					Q_ARG(const QString&, syncFontList), Q_ARG(const QVariantMap&, mapData));
			}),
			[=](const QString&, int) {},
			[=](const QString&, DownloadFailInfo) {});

		//至少下载了一个字体才返回true
		downloadFont = true;
	}

	return downloadFont;
}

void KxCreateDocInstance::initRes()
{
	QResource::registerResource(_dir.filePath("mui/default/icons_svg.data"));

	if (promeApp->promeSkin() && promeApp->promeSkin()->getResourceLoader())
	{
	promeApp->promeSkin()->getResourceLoader()->
		addIconSearchDir(TYPE_SVG, QString(":/knewdocs/mui/default"));
		promeApp->promeSkin()->addSubPackage(getCurrentModuleName(), getSubThemeDirPath());
	}
}

void KxCreateDocInstance::unInitRes()
{
	QResource::unregisterResource(_dir.filePath("mui/default/icons_svg.data"));
}

void KxCreateDocInstance::onCoreInited()
{
	QStringList lstQm;
	lstQm.append(KPluginName);
	promeApp->translator()->installTranslation(_dir.filePath("mui/"), lstQm);
}

KxCreateDocConfig::KxCreateDocConfig()
{
	loadConfig();
	if(nullptr != promeApp->cloudSvrProxy())
		connect(promeApp->cloudSvrProxy(), SIGNAL(loginStatusChanged()), this, SLOT(slot_reloadComponentUrlConfig()), Qt::QueuedConnection);
	
#if defined(Q_OS_LINUX) && !defined(Q_OS_OHOS) && !defined(Q_OS_ANDROID) && !defined(WEB_OFFICE_ENABLE)
	if (krt::kcmc::support("OnlineTemplateLibrary")
			&& !(promeApp->getDomainMgr()->isIntranet() || krt::kcmc::support("IntranetVersion")))
	{
		if (promeApp && promeApp->getAccountSDK())
		{
			promeApp->getAccountSDK()->connectToLoginInfoChanged(g_strCreateDocConfigSwitchName,
				this, SLOT(onLoginStatusChanged()));
			connect(promeApp->getAccountSDK(), SIGNAL(privilegeInfoChanged()),
							this, SLOT(onLoginStatusChanged()));
		}
	}
#endif
}

KxCreateDocConfig::~KxCreateDocConfig()
{
}

KxCreateDocConfig& KxCreateDocConfig::instance()
{
	static KxCreateDocConfig s_obj;
	return s_obj;
}

QString readUrl(const QSettings& settings, const QString& key)
{
	QString url = settings.value(key).toString();
	if (!url.isEmpty() &&
		!url.startsWith("file:///") &&
		!url.startsWith("http://") &&
		!url.startsWith("https"))
	{
		url = "file:///" + KxPathProvider::getHomePath() + "/res/" +  url;
	}

	return url;
}

QString readPdfUrl(const QSettings& settings, const QString& key)
{
	QString url = settings.value(key).toString();
	if (!url.isEmpty() &&
		!url.startsWith("file:///") &&
		!url.startsWith("http://") &&
		!url.startsWith("https"))
	{
		url = "file:///" + KxPathProvider::getHomePath() + "/res/pdf/" +  url;
	}
	return url;
}

void KxCreateDocConfig::handleComponentUrlConfig()
{
	bool bEnterpriseUsePersonalUrl = false;
#if defined(Q_OS_LINUX) && !defined(Q_OS_OHOS) && !defined(Q_OS_ANDROID) && !defined(WEB_OFFICE_ENABLE)
	if (krt::kcmc::support("OnlineTemplateLibrary")
			&& !(promeApp->getDomainMgr()->isIntranet() || krt::kcmc::support("IntranetVersion"))
			&& promeApp->hasSpecificPrivileges(QStringList({ "docer_wps", "docer_et", "docer_wpp", "company_exclusive_template_lib", "edu_company_template_library", "docer_mball", "docer_mbelse" })))
	{
		bEnterpriseUsePersonalUrl = true;
	}
#endif

	// 首页URL
	if ((krt::kcmc::support("ChooseEnterpriseOldPath") && !bEnterpriseUsePersonalUrl) || !krt::kcmc::support("CommonDocTemplate"))
	{	
		bool bEnable([]() -> bool
			{
				if (promeApp && promeApp->getDomainMgr())
				{
					if (ksolite::isSupported(ksolite::IntranetVersion7))
						return false;
					if (promeApp->getDomainMgr()->isIntranet()
						|| krt::kcmc::support("IntranetVersion")
						)
						return true;
				}
				return false;
			}());

		if (bEnable)
		{
			m_newDocUrls["pageDocerWps"] = readUrlNew("index.html#/docer?app=wps", EnterpriseOldPath);
			m_newDocUrls["pageDocerWpp"] = readUrlNew("index.html#/docer?app=wpp", EnterpriseOldPath);
			m_newDocUrls["pageDocerEt"] = readUrlNew("index.html#/docer?app=et", EnterpriseOldPath);
		}
		else
		{
			m_newDocUrls["pageDocerWps"] = readUrlNew("index.html#/local?pageType=localAndPrivatePage&app=wps", EnterprisePath);
			m_newDocUrls["pageDocerWpp"] = readUrlNew("index.html#/local?pageType=localAndPrivatePage&app=wpp", EnterprisePath);
			m_newDocUrls["pageDocerEt"] = readUrlNew("index.html#/local?pageType=localAndPrivatePage&app=et", EnterprisePath);
		}
	}
	else
	{
		if(bEnterpriseUsePersonalUrl)
		{
			m_newDocUrls["pageDocerWps"] = readUrlNew("index.html#/screen?app=wps", PersonalPath);
			m_newDocUrls["pageDocerWpp"] = readUrlNew("index.html#/screen?app=wpp", PersonalPath);
			m_newDocUrls["pageDocerEt"] = readUrlNew("index.html#/screen?app=et", PersonalPath);
		}
		else
		{
			m_newDocUrls["pageDocerWps"] = getComponentUrl("wps");
			m_newDocUrls["pageDocerWpp"] = getComponentUrl("wpp");
			m_newDocUrls["pageDocerEt"] = getComponentUrl("et");
		}
	}
	if (krt::kcmc::support("OnlyFocusOnPDF"))
		m_newDocUrls["pageDocerPdf"] = readUrlNew("pdf/index.html#home", PersonalPath);
	else
		m_newDocUrls["pageDocerPdf"] = readUrlNew("index.html#/pdfscreen?app=pdf", PersonalPath);
}

void KxCreateDocConfig::loadConfig()
{
	handleComponentUrlConfig();

#ifdef Q_OS_DARWIN
	QString docerKDocs = KDocerUtils::getDomainUrl("docerkdocs");
	m_newDocUrls["pageKOtl"] = QString("%1/new/airpage/0-0?from=mac_wps").arg(docerKDocs);
	m_newDocUrls["pageKSheet"] = QString("%1/new/airsheet/0-0?from=mac_wps").arg(docerKDocs);;
	m_newDocUrls["pageKDbSheet"] = QString("%1/new/dbsheet/0-0?from=mac_wps").arg(docerKDocs);
#else
	m_newDocUrls["pageKOtl"] = readUrlNew("index.html#/applist/flexpaper", KOtlPath);
	m_newDocUrls["pageKSheet"] = readUrlNew("index.html#/applist/ksheet", KOtlPath);
	m_newDocUrls["pageKDbSheet"] = readUrlNew("index.html#/applist/dbsheet", KOtlPath);
#endif

	QString docerClientWeb = KDocerUtils::getDomainUrl("docerclientweb");
	m_newDocUrls["widgetChuangKit"] = QString("%1/ckt-newdocs/index.html").arg(docerClientWeb);
	m_newDocUrls["widgetProcesson_Flow"] = QString("%1/processon-newdocs/index.html#/?app=flow").arg(docerClientWeb);
	m_newDocUrls["widgetProcesson_Mind"] = QString("%1/processon-newdocs/index.html#/?app=mind").arg(docerClientWeb);

	m_newDocUrls["search"] = readUrlNew("index.html#/searchResult?app_type=0&show_type=embed&source=client");
	m_newDocUrls["pageMytpl"] = readUrlNew("index.html#/screenmytpl?");

	//目前仅做埋点上报
	//KDocerPackageWebRes::verify(KPluginName, KxPathProvider::getWebResDir());

	m_mapDocerDocs[KPromePluginWidget::pageDocerWps] = KPromePluginWidget::pageWps;
	m_mapDocerDocs[KPromePluginWidget::pageDocerWpp] = KPromePluginWidget::pageWpp;
	m_mapDocerDocs[KPromePluginWidget::pageDocerEt]  = KPromePluginWidget::pageEt;
	m_mapDocerDocs[KPromePluginWidget::pageDocerPdf]  = KPromePluginWidget::pagePdf;

	QString lang = QString::number(krt::i18n::displayLCID()) + '/';
	if (krt::kcmc::support("OfficialComponent"))
	{
		QSettings config(KxPathProvider::getConfigFilePath(), QSettings::IniFormat);
		m_newDocUrls["pageDocerOfficial"] = readUrl(config, lang + "wps") + "&official=1";
		m_mapDocerDocs[KPromePluginWidget::pageDocerOfficial] = KPromePluginWidget::pageWps;
	}
}

void KxCreateDocConfig::addArg(QString& url, const QString& arg) const
{
	ASSERT(!url.isEmpty());
	ASSERT(!arg.isEmpty());

	QChar qch = url[url.length() - 1];
	if (qch != '&' && qch != '?')
		url += '&';
	url += arg;
}

QByteArray KxCreateDocConfig::encode32Md5(const QByteArray& key)
{
	return QCryptographicHash::hash(key, QCryptographicHash::Md5).toHex();
}

QString KxCreateDocConfig::getCreateDocUrl(const QString& app) const
{
	QString createDocUrl = m_newDocUrls[app];

	if (KWebViewDebugFlag::IsEnableWebDebug())
	{
		KCCPluginsSettings settings;
		settings.beginGroup(KPluginName);
		settings.beginGroup("debug");
		if (settings.contains(app))
			return settings.value(app).toString();
	}
#ifdef Q_OS_DARWIN
	//本地资源是否存在校验，不满足弹窗提示用户
    auto checkDocUrl = createDocUrl;
    if(checkDocUrl.startsWith("file:///"))
    {
        checkDocUrl = QUrl(checkDocUrl).path();
        if(!QFile::exists(checkDocUrl))
        {
            QTimer::singleShot(0, KPromeUpdateMgr::instance(), &KPromeUpdateMgr::onAppArrivedNewVersion);
        }
    }
#endif
	return createDocUrl;
}

QString KxCreateDocConfig::getSkinConfigPath() const
{
	return KxPathProvider::getWebResDir() + "/skin.kuip";
}

QString KxCreateDocConfig::getCurrentWorkspaceId() const
{
	QString currentWorkspaceId = workspace_helper::PersonalWorkspaceId();

	auto cloudSvrProxy = promeApp->cloudSvrProxy();
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;
	if (account)
		isLogined = account->isLogined();
	if (cloudSvrProxy && isLogined)
		currentWorkspaceId = cloudSvrProxy->currentWorkspaceId();

	return currentWorkspaceId;
}


QString KxCreateDocConfig::getCurrentGroupId() const
{
	QString currentGroupId = "0";

	auto cloudSvrProxy = promeApp->cloudSvrProxy();
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;
	if (account)
		isLogined = account->isLogined();
	if (cloudSvrProxy && isLogined)
	{
		auto groupInfo = cloudSvrProxy->getCompanyGroupInfo(getCurrentWorkspaceId(), "corpspecial");
		if (groupInfo && !groupInfo->groupId.isEmpty())
			currentGroupId = groupInfo->groupId;
	}

	return currentGroupId;
}

void KxCreateDocConfig::openPromeBrowser(const QString& url, bool bUrlToolbarVisible /*= false*/)
{
	if (!url.startsWith("http://", Qt::CaseInsensitive) && !url.startsWith("https://", Qt::CaseInsensitive))
		return;
	KPromeMainWindow* mw = promeApp->currentPromeMainWindow();
	if (!mw)
		return;

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;
	QVariantMap extraMap;
	extraMap["BrowserType"] = KPromeBrowserType::WPSApp;
	extraMap["appId"] = "kprome_new_docs_startup";
	contentArea->addBrowserPage(url, bUrlToolbarVisible, KPromePluginWidget::pageBrowser, nullptr, extraMap);
}

void KxCreateDocConfig::openDocerPage(const QString& action, const QString& param)
{
	if (kcoreApp->clientService())
	{
		kcoreApp->clientService()->addDocerPage(
			promeApp->currentPromeMainWindow(), action, param);
	}
}

void KxCreateDocConfig::slot_reloadComponentUrlConfig()
{
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;
	if (account)
		isLogined = account->isLogined();
	if (isLogined)
	{
		auto accountSdk = kcoreApp->getAccountSDK();
		if (nullptr == accountSdk)
			return;
		auto account = accountSdk->getAccount();
		if (nullptr == account)
			return;
		auto obj = qobject_cast<kacctoutsdk::KAccountSdkProxy*>(sender());
		if (nullptr != obj)
			disconnect(accountSdk, &kacctoutsdk::KAccountSdkProxy::accountInfoReady,
				this, &KxCreateDocConfig::slot_reloadComponentUrlConfig);

		if (!account->isReady())
		{
			connect(accountSdk, &kacctoutsdk::KAccountSdkProxy::accountInfoReady,
				this, &KxCreateDocConfig::slot_reloadComponentUrlConfig, Qt::UniqueConnection);
			return;
		}
		handleComponentUrlConfig();
	}
	else
	{
		handleComponentUrlConfig();
	}
}

QString KxCreateDocConfig::getSearchResultUrl() const
{
	return m_newDocUrls["search"];
}

int KxCreateDocConfig::getDocerType(int type) const
{
	return m_mapDocerDocs[type];
}


int KxCreateDocConfig::getDocumentType(const QString& filePath) const
{
	QFileInfo fileInfo(filePath);
	QHash<QString, QString> args;
	QString ext = fileInfo.suffix();

	static QString qWpsAllSupportType = "wps;wpt;doc;docx;dot;rtf;txt;htm;html;mht;mhtml;uof;xml;docm;wpss;dotx";
	static QString qEtAllSupportType = "et;ett;xls;xlsx;xlsm;xlsb;xlam;xltx;xltm;xls;xlt;xla;xlw;odc;uxdc;txt;xml;\
								htm;html;mht;mhtml;dbf;uof;prn;csv;ets;mdb;accdb;dqy;dbc;udl;dsn;odc;db";
	static QString qWppAllSupportType = "dps;dpt;pptx;ppt;pptm;ppsx;pps;ppsm;potx;pot;potm;wpd;uof;dpss";
	static QString qPdfAllSupportType = "pdf";

	static QStringList wpsList = qWpsAllSupportType.split(";");
	if (wpsList.contains(ext))
		return KPromePluginWidget::pageWps;

	static QStringList wppList = qWppAllSupportType.split(";");
	if (wppList.contains(ext))
		return KPromePluginWidget::pageWpp;

	static QStringList etList = qEtAllSupportType.split(";");
	if (etList.contains(ext))
		return KPromePluginWidget::pageEt;

	static QStringList pdfList = qPdfAllSupportType.split(";");
	if (pdfList.contains(ext))
		return KPromePluginWidget::pagePdf;

	return KPromePluginWidget::pageWps;
}

QString KxCreateDocConfig::buildBeautifyTemplatePassword(const QString& strFileKey)
{
	if (strFileKey.isEmpty())
		return QString();

	//strPasswordSalt：固定字符串，存于客户端，生成加密密码的算法需要用到；
	const QString strPasswordSalt = "PRQCSnZ2iDE00xLCE4PuIJNbKRHwZli4";

	QByteArray strFileKeyMd5(strFileKey.toUtf8());
	for (int i = 0; i < 2; ++i)
		strFileKeyMd5 = encode32Md5(strFileKeyMd5);

	QByteArray strPasswordSaltMd5 = encode32Md5(strPasswordSalt.toUtf8());

	QByteArray strPassword;
	for (int i = 0; i < 8; ++i)
	{
		strPassword.append(strFileKeyMd5.mid(i * 4, 4));
		strPassword.append(strPasswordSaltMd5.mid((7 - i) * 4, 4));
	}

	for (int i = 0; i < 3; ++i)
		strPassword = encode32Md5(strPassword);

	return strPassword;
}

int KxCreateDocConfig::getAppPageType(const QString& appName)const
{
	if (!appName.compare("wps", Qt::CaseInsensitive))
		return KPromePluginWidget::pageWps;	
	else if (!appName.compare("wpp", Qt::CaseInsensitive))
		return KPromePluginWidget::pageWpp;	
	else if (!appName.compare("et", Qt::CaseInsensitive))
		return KPromePluginWidget::pageEt;
	else if (!appName.compare("pdf", Qt::CaseInsensitive))
		return KPromePluginWidget::pagePdf;
	else if (!appName.compare("processonFlow", Qt::CaseInsensitive))
		return KPromePluginWidget::pageProcesson_Flow;
	else if (!appName.compare("processonMind", Qt::CaseInsensitive))
		return KPromePluginWidget::pageProcesson_Mind;
	else
		return KPromePluginWidget::invalidWidgetType;
}

#if defined(Q_OS_LINUX) && !defined(Q_OS_OHOS) && !defined(Q_OS_ANDROID) && !defined(WEB_OFFICE_ENABLE)
void KxCreateDocConfig::onLoginStatusChanged()
{
	m_newDocUrls.clear();
	m_mapDocerDocs.clear();
	loadConfig();
}
#endif

/*
 * KxPathProvider
 */

QDir KxPathProvider::_dir;

void KxPathProvider::init(const QDir& dir)
{
	_dir = dir;
}

QString KxPathProvider::getHomePath()
{
	qDebug() << "KxPathProvider::getHomePath:" << _dir.absoluteFilePath(QString());
	return _dir.absoluteFilePath(QString());
}

QString KxPathProvider::getWebResDir(const ResDir resDir /*= HomePath*/, WebResDirDetail* detail /*= nullptr*/)
{
	static QMap<ResDir, WebResInfo> s_resMap = {
		{ResDir::HomePath, WebResInfo(KPluginName, "version")},
		{ResDir::PersonalPath, WebResInfo(KPluginName, "version")},
		{ResDir::KOtlPath, WebResInfo("knewdocskotl", "kotlversion")}
	};

	auto getFolderName = [](const ResDir resDir)
	{
		QString folderName = "/";
		switch (resDir)
		{
		case KOtlPath:
			folderName = folderName.append("kotl/");
			break;
		case HomePath:
			if (krt::kcmc::support("PdfNewDocsWebResDirHomePathAppendPersonal"))
				folderName = folderName.append("personal/");
			break;
		case PersonalPath:
			folderName = folderName.append("personal/");
			break;
		case EnterpriseOldPath:
			folderName = folderName.append("enterpriseOld/");
			break;
		case EnterprisePath:
			folderName = folderName.append("enterprise/");
			break;
		default:
			break;
		}
		return folderName;
	};
	QString webRes = docer::base::KCurrentModule::getModuleResourceDirPath() % "/res";
	QString resPath = webRes % getFolderName(resDir);

	if (resDir == EnterprisePath && kcmc::support("KdocerNewPageLocalPageResPath"))
	{
		QString path = getNewDocerModuleResourcePath() % "/res" % getFolderName(resDir);
		return QDir(path).exists() ? path : webRes % getFolderName(HomePath);
	}

	if (detail)
		detail->subPath = "/res" % getFolderName(resDir);

	return resPath;
	
}

QString KxPathProvider::getConfigFilePath()
{
	return getHomePath() + "/res/" + szConfigFilename;
}

QString KxPathProvider::getNetworkRequestFilePath()
{
	QString userId = "default";
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	if (account)
		userId = account->getUserId();

	return getOffice6NewDocBasePath() + QDir::separator() + userId + QDir::separator() + "networkcache.ini";
}

QString KxPathProvider::getConfigTransCacheFilePath()
{
	return getOffice6NewDocBasePath() + QDir::separator() + "new_ConfigTransCache.ini";
}

QString KxPathProvider::getOffice6NewDocBasePath()
{
	return getOfficeDataPath() + QDir::separator() + "newdocs";
}

QString KxPathProvider::getLanguageFilePath()
{
	return _dir.absoluteFilePath(szLangFilename);
}

QString KxPathProvider::getOfficeDataPath()
{
	return krt::dirs::officeData();
}

const QDir KxPathProvider::getKsoTemplateDir()
{
	QDir dir(krt::dirs::downloadTemplates());
	if (!dir.exists())
		dir.mkpath(dir.path());
	return dir;
}

QString KxPathProvider::getTemplateFilePath(
	const QString& templClass,
	const QString& templFilename)
{
	QDir dir = getTemplateDownDir(getKsoTemplateDir().path());
	//QDir dir = getKsoTemplateDir();
	dir.mkdir(templClass);
	dir.cd(templClass);
	return dir.absoluteFilePath(templFilename);
}

void KxPathProvider::setTemplateDownDir( const QString& strPath )
{
	KxCommonSettings sets;
	sets.beginGroup("wpshomeoptions");
	sets.setValue("TemplateDownPath", strPath);
	sets.endGroup();
}

QString KxPathProvider::getTemplateDownDir( const QString& strDefaultPath /*= ""*/ )
{
	QString strVal;
	KxCommonSettings sets;
	sets.beginGroup("wpshomeoptions");
	strVal = sets.value("TemplateDownPath", strDefaultPath).toString();
	sets.endGroup();
	if (strVal.isEmpty() || !QDir(strVal).exists())
		strVal = strDefaultPath;
	return strVal;
}

namespace
{
	QColor getCommonBackgroundColor()
	{
		return KDrawHelper::getColorFromTheme(
			"KNewDocsWebWidget", KDrawHelper::Prop_Background,
			KDrawHelper::stringToColor("#FFFFFFFF"));
	}
}

void KNewDocDrawHelper::drawCommonBackground(QWidget* w)
{
	if (w)
	{
		QPainter painter(w);
		QRect rc = w->rect();
		painter.fillRect(rc, getCommonBackgroundColor());
	}
}

void KNewDocDrawHelper::setBackgroundPattle(QWidget* w)
{
	if (w)
	{
		QPalette palette = w->palette();
		palette.setColor(QPalette::Background, getCommonBackgroundColor());
		w->setPalette(palette);
	}
}

bool KNewDocDrawHelper::isDarkSkin()
{
	return KDrawHelper::isDarkSkin();
}
