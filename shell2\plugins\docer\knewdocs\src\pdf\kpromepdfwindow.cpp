﻿#include "stdafx.h"
#include "kpromepdfwindow.h"
#include "knewdochelper.h"
#include <kprometheus/kpromecloudsvrproxy.h>
#include <kprometheus/kpromemainwindow.h>
#include <kprometheus/kprometab.h>
#include <kprometheus/kprometabbar.h>
#include <kprometheus/kpromecentralarea.h>
#include <kprometheus/kpromewpspage.h>
#include <perftool/perftool.h>
#include "kpromenewdocspage.h"
#include "kxpdfnewdochelper.h"
#include <ksolite/krecentfile.h>
#include <kliteui/kfilefilter.h>
#include "kdocerbasehelper.h"
#include <ksolite/kappmgr/kappmanager.h>
#include <krt/product.h>
#include "kpromenewdocsstartup.h"
#include "knewdocjsapihelper.h"
#include <auth/productinfo.h>
#ifdef Q_OS_WIN
#include <shellapi.h>
#endif
#include <ksolite/kdcinfoc.h>
#include <kdocertoolkit/helper/kdocercommonhelper.h>
#include "kwpsenterpriseintranet/kintranetauthorize_i.h"
#include <kprometheus/kpromeskin.h>
#include <kso/framework/api/pdf.h>
#include <kxshare/kxapplication.h>
#include <ksolite/ksolog/kxloggerlite.h>
#include <kcomctl/ktriggerroutines.h>
#include "ksolite/kdomainmgr.h"
#include "qttools/ksafe_process.h"
#include <kdocerfunctional.h>
#include <ksolite/kdocer/kdocerresnetwork/kdocernetworkwrapper.h>
#include "ksolite/kfpccomb/kfpccomb.h"
#include <ksolite/kplugin.h>
#include <ksolite/kpluginmanager.h>
#include "kfpccomb/kfpccombsdk.h"
#include <kfpccomb/kfpccombmoduledefine.h>
#include "mobilescan/kxmobilescanwidget.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "krt/kconfigmanagercenter.h"

const char* const PIC2PDF_COMB = "/photo2pdf?from=pdf_new";
const char* const PDF2PHOTO_COMB = "/pdf2photo?from=pdf_new";
const char* const CAD2PDF_COMB = "/cad2pdf?from=pdf_new";
const char* const MERGE_COMB = "/merge2pdf?from=pdf_new";
const char* const PDF2WORD_COMB = "/pdf2word?from=pdf_new";
const char* const PDF2EXCEL_COMB = "/pdf2excel?from=pdf_new";
const char* const SPLIT_COMB = "/split?from=pdf_new";
const char* const PDF2PPT_COMB = "/pdf2ppt?from=pdf_new";

const char* const s_strPdfSkinChangedJsCallBack = "onSkinChanged";
const char* const s_strAppMgrLoadFinishCallBack = "onAppMgrLoadFinish";

const char* const EVENTNAME_CAD2PDF = "pdf_convert_cad2pdf";// PDF_转换_CAD转PDF
const char* const EVENTNAME_PIC2PDF = "pdf_convert_pic2pdf";// PDF_转换_图片转pdf

const char* const APPID_PDFFROMSCANNER = "kpdffromscanner";// PDF_从扫描仪新建插件ID
const char* const APPID_EXPORTPDF = "kexportpdf";// PDF_输出为PDF插件ID
const char* const APPID_TRANSLATE = "kdoctranslate";// PDF_从翻译插件ID
constexpr const char* const APPID_MOBILESCAN = "kwebmobilescan"; //PDF_从手机扫描新建

const char* const EVENTNAME_PDF2WORD = "pdf_convert_pdf2word";// PDF_转换_pdf转WORD
const char* const EVENTNAME_PDF2PHOTO = "pdf_convert_pdf2pic";// PDF_转换_pdf转图片
const char* const EVENTNAME_PDFMERGE = "pdf_convert_merge";// PDF_转换_合并

const char* const EVENTNAME_PDF2EXCEL = "pdf_convert_pdf2excel"; // PDF_转换_PDF转Excel
const char* const EVENTNAME_PDF2PPT = "pdf_convert_pdf2ppt"; // PDF_转换_pdf转PPT
const char* const EVENTNAME_PDFSPLIT = "pdf_convert_split"; // PDF_转换_拆分

const char* const EVENTNAME_DOCTRANSLATOR = "pdf_addevalue_doctranslator";// PDF_增值功能_全文翻译

#ifdef Q_OS_DARWIN 
#define FPCCOMB_MODULE_ID_SCAN_ENTRY_SHOW (61557)
#define FPCCOMB_SCAN_ENTRY_PROJECT_ID (139)
#else
#define FPCCOMB_MODULE_ID_SCAN_ENTRY_SHOW (61134)
#define FPCCOMB_SCAN_ENTRY_PROJECT_ID (60)
#endif

namespace
{
#ifdef Q_OS_DARWIN
	typedef void (*PBCDlgShowFunc)();
	typedef void (*SetFilePathFunc)(const QList<PdfFileInfo>&);
	typedef void (*SetPolicyFunc)(int);
	typedef void (*SetSourceFunc)(const QString&);
#endif
	const char* const s_strDownloadProgressJsCallBack = "updateDownloadPercent";
	const char* const s_strDownloadSuccessJsCallBack = "downloadComplete";
	const char* const s_strDownloadErrorJsCallBack = "downloadFailed";
	const char* const s_strDownloadCancelJsCallBack = "downloadCanceled";

	QString dealDuplicateFileNames(QString realPdfFileName, QString filePath, QString suffixStr)
	{
		QStringList listPathName = realPdfFileName.split(".");
		QString fileTxtName;
		if (listPathName.count() < 1)
			return QString();
		fileTxtName = listPathName[0] + suffixStr;
		QString pathFile = filePath + fileTxtName;
		if (QFile::exists(pathFile))
		{
			QStringList listName = realPdfFileName.split("(");
			if (listName.size() == 2)
			{
				QString strNumber = listName[1];
				QStringList listStrNumber = strNumber.split(")");
				strNumber = listStrNumber[0];
				strNumber = QString::number(strNumber.toInt() + 1);
				fileTxtName = listName[0] + "(" + strNumber + ")" + suffixStr;
			}
			else
			{
				fileTxtName = listPathName[0] + "(1)" + suffixStr;
			}
		}
		pathFile = filePath + fileTxtName;
		if (QFile::exists(pathFile))
			fileTxtName = dealDuplicateFileNames(fileTxtName, filePath, suffixStr);
		return fileTxtName;
	}
	const QString getLocalPath(const QString& url, const QString& name)
	{
		QDir dir(krt::dirs::kingsoftHome() + QDir::separator() + "PDF" + QDir::separator() + "downloadTemplates");
		if (!dir.exists())
			dir.mkpath(dir.path());
		QString filePath = url;
		int pos = filePath.indexOf("?");
		if (pos > 0)
			filePath.truncate(pos);

		QString suffix = "." + QFileInfo(filePath).suffix();
		return QDir::toNativeSeparators(dir.path() + "/" + QFileInfo(filePath).baseName() + "/" + dealDuplicateFileNames(name, dir.path() + "/" + QFileInfo(filePath).baseName() + "/", suffix));
	}

	bool isMobileScanKfpccombShowEntry(const QString& key)
	{
		bool isShowNewEntry = false;
		KFpccomb* pFpccomb = KFpccomb::getInstance();
		if (!pFpccomb)
			return isShowNewEntry;
		auto retData = pFpccomb->getData(FPCCOMB_SCAN_ENTRY_PROJECT_ID, FPCCOMB_MODULE_ID_SCAN_ENTRY_SHOW);
		if (!retData)
			return isShowNewEntry;
		auto fpList = retData->getChild();
		if (fpList.isEmpty())
			return isShowNewEntry;
		auto fpItem = fpList.first();
		auto dataMap = fpItem->getData();
		auto value = dataMap[key];
		if (!value.isNull() && value->isBoolType())
			isShowNewEntry = value->boolValue();
		return isShowNewEntry;
	}
}

KxPromePdfWindowJsApi::KxPromePdfWindowJsApi(KPromeNewDocsStartup* window, KxWebViewContainer* webView)
	: KxNewDocJsApiBase/*ksolite::KxCommonJsApi*/(webView)
	, _window(window)
	, m_proxyPlugin(nullptr)
	, m_bIsShowSelectFileDlg(false)
	, m_isPageReady(false)
	, m_currentMessageCnt(0)
	, m_bScriptReady(false)
{
	connect(promeApp->promeSkin(), SIGNAL(skinChanged()), this, SLOT(onSkinChanged()));
	connect(this, SIGNAL(webScriptReady()), this, SLOT(onScriptReady()));
	if(KAppMgr::getInstance())
		connect(KAppMgr::getInstance(), SIGNAL(loadFinished()), this, SLOT(onAppMgrLoadFinished()));
	initConfig();
}

void KxPromePdfWindowJsApi::onScriptReady()
{
	m_bScriptReady = true;
	foreach(auto doTask, m_onScriptReadyTasks)
		doTask();
	m_onScriptReadyTasks.clear();
}

KxPromePdfWindowJsApi::~KxPromePdfWindowJsApi()
{
	if (m_proxyPlugin)
	{
		delete m_proxyPlugin;
		m_proxyPlugin = nullptr;
	}
}

void KxPromePdfWindowJsApi::runApp(const QString& appId, const std::map<QString, QString>& appProperties)
{
	if (m_proxyPlugin)
	{
		delete m_proxyPlugin;
		m_proxyPlugin = nullptr;
	}
	m_proxyPlugin = new KPdfProxyPugin(this);
	connect(m_proxyPlugin, SIGNAL(onPluginLoadSuccess(QString)), this, SLOT(onPluginLoadSuccess(QString)));
	m_proxyPlugin->setAppID(appId);
	m_proxyPlugin->setAppProperty(appProperties);
	m_proxyPlugin->requestWidget(promeApp->currentPromeMainWindow(), "wpsoffice", "TabNewPDF_Plugin");
	m_proxyPlugin->onTrigger();
}

void KxPromePdfWindowJsApi::notifyCancelDownload(const QString& templateId)
{
	if (m_downloadfileIdSet.contains(templateId))
	{
		QVariantMap result;
		result["id"] = templateId;
		callbackToJS(s_strDownloadCancelJsCallBack, formatResult(result));
		m_downloadfileIdSet.remove(templateId);
		auto newDocsPage = getNewDocsPage();
		if (newDocsPage)
			newDocsPage->decDownloadTemplateCount();
	}
}

void KxPromePdfWindowJsApi::notifyDownloadProgress(const QString& templateId, int nPercent)
{
	if (m_downloadfileIdSet.contains(templateId))
	{
		QVariantMap result;
		result["id"] = templateId;
		result["progress"] = nPercent;
		callbackToJS(s_strDownloadProgressJsCallBack, formatResult(result));
	}
}

void KxPromePdfWindowJsApi::notifyDownloadSuccess(const QString& templateId, const QString& fileName)
{
	if (m_downloadfileIdSet.contains(templateId))
	{
		QVariantMap result;
		result["id"] = templateId;
		callbackToJS(s_strDownloadSuccessJsCallBack, formatResult(result));
		m_downloadfileIdSet.remove(templateId);
		auto newDocsPage = getNewDocsPage();
		if (newDocsPage)
			newDocsPage->decDownloadTemplateCount();
	}
}

void KxPromePdfWindowJsApi::notifyDownloadError(const QString& templateId, kdocerresnetwork::DownloadFailInfo info)
{
	if (m_downloadfileIdSet.contains(templateId))
	{
		QVariantMap result;
		result["id"] = templateId;
		callbackToJS(s_strDownloadErrorJsCallBack, formatResult(result));
		m_downloadfileIdSet.remove(templateId);
		auto newDocsPage = getNewDocsPage();
		if (newDocsPage)
			newDocsPage->decDownloadTemplateCount();
	}
}

QWidget* KxPromePdfWindowJsApi::widgetParent()
{
	return promeApp->currentPromeMainWindow();
}

void KxPromePdfWindowJsApi::registPushUIEventHandler(KxWebViewJSContext& context)
{
#ifndef Q_OS_LINUX
	if (_window)
		QTimer::singleShot(0, _window, SLOT(showWebWidget()));
#endif
}

void KxPromePdfWindowJsApi::openPdfUnifyPayDlg(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;
	if (auto proxy = promeApp->cloudSvrProxy())
	{
		QString csource = QString::fromUtf8(args.value("csource").toString().toUtf8().data());
		QString position = QString::fromUtf8(args.value("position").toString().toUtf8().data());
		QString component = QString::fromUtf8(args.value("component").toString().toUtf8().data());
		QString payconfig = QString::fromUtf8(args.value("payconfig").toString().toUtf8().data());
		bool bModal = args.value("bModal", true).toBool();
		QString source = csource + QString("&position=%1&component=%2&payconfig=%3").arg(position, component, payconfig);
		proxy->showPayWindow(source, "", bModal, _window);
	}
}

void KxPromePdfWindowJsApi::newBlankDocument()
{
	onNewDocument("blank", "", "pdf");
}

void KxPromePdfWindowJsApi::downloadTemplate(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QVariantList list = args.value("params").toList();
	//重置一次，不然会在多组件下载的时候，显示成上一次完成的100
	callbackToJS(s_strDownloadProgressJsCallBack, formatResult(0));
	for (auto i = list.constBegin();i != list.constEnd(); ++i)
	{
		QVariantMap item_map = i->toMap();
		QString url = item_map["url"].toString();
		QString name = item_map["name"].toString();
		name = name.replace(QRegExp(QString::fromLatin1("[\\\\/:*?\"<>|]")), "");
		QString templateId = item_map["id"].toString();
		QString taskId = downloadTemplate(url, name, templateId);
		if (!taskId.isEmpty())
		{
			//返回的ID为模板ID
			setResult(context, templateId);
			auto newDocsPage = getNewDocsPage();
			if (newDocsPage)
				newDocsPage->incDownloadTemplateCount();
			m_downloadfileIdSet.insert(templateId);
		}
	}
}

QString KxPromePdfWindowJsApi::downloadTemplate(QString& url, QString& name, const QString& templateId)
{
	url = QUrl::fromPercentEncoding(url.toUtf8());
	name = QUrl::fromPercentEncoding(name.toUtf8());
	if (url.isEmpty() || name.isEmpty())
		return 0;

	const QString localPath = getLocalPath(url, name);
	//若文件以及存在，则直接打开文件
	QFileInfo fi(localPath);
	if (fi.exists())
	{
		QVariantMap result;
		result["progress"] = 100;
		result["id"] = templateId;
		callbackToJS(s_strDownloadProgressJsCallBack, formatResult(result));
		callbackToJS(s_strDownloadSuccessJsCallBack, formatResult(result));
		KPromeMainWindow* mw = nullptr;
		bool isNewStandaloneWindow = false;
		mw = findMainWindow(isNewStandaloneWindow);
		onOpenDocument(mw, localPath, "", getFileType(localPath));
		return 0;
	}
	QString taskId = KxNewDocJsApiHelper::instance().getDownloadID(templateId);
	if (!taskId.isEmpty())
		return taskId;
	kdocerresnetwork::DownloadArgs args;
	args.saveArgs.saveFilePath = localPath;
	args.resourceArgs.urls.append(url);
	taskId = KDocerResNetworkSDK::getInstance().startDownload(args,
		bindContext(this, [=](const QString& downloadId, const DownloadSuccessInfo& info) {
			onDownloadSuccess(templateId, info.path);
		}), bindContext(this, [=](const QString& downloadId, int nProgress) {
			onDownloadProgress(templateId, nProgress);
		}), bindContext(this, [=](const QString& downloadId, const DownloadFailInfo& info) {
			onDownloadError(templateId, info);
		}));

	if(!taskId.isEmpty())
		KxNewDocJsApiHelper::instance().addDownloadTask(taskId, templateId);
	return taskId;
}

void KxPromePdfWindowJsApi::cancelDownload(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty())
	{
		auto newDocsPage = getNewDocsPage();
		if (newDocsPage)
			newDocsPage->decDownloadTemplateCount();
		QString templateId = args.value("id").toString();
		QString taskId = KxNewDocJsApiHelper::instance().getDownloadID(templateId);
		if (!taskId.isEmpty())
		{
			KDocerResNetworkSDK::getInstance().cancelDownload(taskId);
			KxNewDocJsApiHelper::instance().notifyCancelDownload(this, templateId);
		}
		m_downloadfileIdSet.remove(templateId);
	}
}

QString KxPromePdfWindowJsApi::getFileType(const QString& fileName)
{
	QString strExt = QFileInfo(fileName).suffix().toLower();

	if (!strExt.isEmpty())
	{
		if (KFileFilter::instance()->isSupportExt(filterWps, strExt))
			return "wps";
		if (KFileFilter::instance()->isSupportExt(filterEt, strExt))
			return "et";
		if (KFileFilter::instance()->isSupportExt(filterWpp, strExt))
			return "wpp";
		if (KFileFilter::instance()->isSupportExt(filterPdf, strExt))
			return "pdf";
	}
	return "pdf";
}

//检查对应插件appId 应用配置 是否可用
bool KxPromePdfWindowJsApi::checkAppValidity(const QString& appId)
{
	KAppMgr* appMgr = KAppMgr::getInstance();
	if (!appMgr || !appMgr->isLoaded())
		return false;
	KAppObj* appObj = appMgr->getApp(appId);
#if defined(Q_OS_DARWIN) || defined(Q_OS_OHOS)
	//Mac暂不支持从Office文件新建入口，该应用暂不支持
	if (appId == APPID_EXPORTPDF)
		return false;
#endif
	bool bValid = appObj && appObj->checkIsValid();
	if (appId == APPID_MOBILESCAN)
	{
#if defined(Q_OS_DARWIN)
		bValid = true;
#else
		bValid = krt::pluginconfig::isPluginConfiged(APPID_MOBILESCAN);
#endif
		QHash<QString, QString> args;
		KDocerUtils::postGeneralEvent("mobile_qrscan_newfuncshow", args, 0, 0, KDC_All);
		if (bValid)
			bValid = isMobileScanKfpccombShowEntry("EnableMobileScan");
	};
	return bValid;
}

void KxPromePdfWindowJsApi::newPdfFromFile()
{
	onExportPdfDocument();
}

void KxPromePdfWindowJsApi::newPdfFromScan()
{
	if (m_proxyPlugin)
	{
		delete m_proxyPlugin;
		m_proxyPlugin = nullptr;
	}
	m_proxyPlugin = new KPdfProxyPugin(this);
	connect(m_proxyPlugin, SIGNAL(onPluginLoadSuccess(QString)), this, SLOT(onPluginLoadSuccess(QString)));
	m_proxyPlugin->setAppID("kpdffromscanner");
	m_proxyPlugin->requestWidget(promeApp->currentPromeMainWindow(), "wpsoffice", "TabNewPDF_Plugin");
	m_proxyPlugin->onTrigger();
	return;
}

void KxPromePdfWindowJsApi::newFromWebPage()
{
	onNewDocument("newweb", "", "pdf");
}

void KxPromePdfWindowJsApi::newlyBuildPlugInUnit(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty())
	{
		auto newDocsPage = getNewDocsPage();
		if (newDocsPage)
			newDocsPage->decDownloadTemplateCount();
		int templateId = args.value("id").toInt();

		switch ((PdfPluginJsId)templateId)
		{
		case PdfPluginJsId_BlankPdf:
			newBlankDocument();
			break;
		case PdfPluginJsId_Open:
			openDocument();
			break;
		case PdfPluginJsId_NewFromPicture:
		case PdfPluginJsId_NewFromCADFile:
			clicktRecommendationFunction(context);
			break;
		case PdfPluginJsId_NewFromFile:
			newPdfFromFile();
			break;
		case PdfPluginJsId_NewFromWebPage:
			newFromWebPage();
			break;
		case PdfPluginJsId_NewFromScanner:
			newPdfFromScan();
			break;
		case PdfPluginJsId_NewFromMobileScan:
			newPdfFromMobileScan();
			break;
		default:
			break;
		}
	}
}

void KxPromePdfWindowJsApi::openDocument()
{
	QVariantMap params;
	params.insert("showRecentTab", "1");
	params.insert("showCloudTab", "1");
	params.insert("AllowMultiSel", "0");

	QStringList filterType;
	filterType << "pdf" << "ebook";

#ifdef Q_OS_WIN
	if (krt::kcmc::support("PdfNewDocsPromePdfOpenDocFilterFdf"))
		filterType << "fdf";
#endif

	_openDocument("", params, filterType);
}

void KxPromePdfWindowJsApi::navigateOnNewWindow(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	QString url = QString::fromUtf8(args.value("url").toString().toUtf8().data());
	if (!url.startsWith("http://", Qt::CaseInsensitive) && !url.startsWith("https://", Qt::CaseInsensitive))
		return;
	if (args.contains("promebrowser"))
	{
		if (args.value("promebrowser").toBool())
		{
			onOpenPromeBrowser(url);
			return;
		}
	}
	if (krt::ksafe::KProcess::checkUrl(url))
		kqttools::openUrl(url);
}

void KxPromePdfWindowJsApi::newBlankDocument(KxWebViewJSContext& context)
{
	onNewDocument("blank", "", "pdf");
}

void KxPromePdfWindowJsApi::newPdfFromFile(KxWebViewJSContext& context)
{
	onExportPdfDocument();
}

void KxPromePdfWindowJsApi::newPdfFromScan(KxWebViewJSContext& context)
{
	onNewDocument("newscan", "", "pdf");
}

void KxPromePdfWindowJsApi::newPdfFromMobileScan()
{
	KCloudSvrProxy* pCloud = kcoreApp->cloudService();
	if (pCloud == nullptr)
	{
		KxLoggerLite::writeInfo(KPluginNameW,
			QString("%1:KCloudSvrProxy is nullptr").arg(__FUNCTION__).toStdWString());
		return;
	}
	if (!pCloud->isLogined())
	{
		if (!pCloud->isLoginWinVisible())
			pCloud->login();
		return;
	}
	if (KPluginManager::getInstance() == nullptr)
	{
		KxLoggerLite::writeInfo(KPluginNameW,
			QString("%1:KPluginManager::getInstance is nullptr").arg(__FUNCTION__).toStdWString());
		return;
	}
	auto funcMobileScan = [this]() {
		static bool s_bIsCalling = false;
		if (s_bIsCalling)
			return;
		QScopedValueRollback<bool> scopeValue(s_bIsCalling, true);
		QScopedPointer<KxMobileScanWidget> pWebWidget(new KxMobileScanWidget(_window));
		if (pWebWidget.isNull()
			|| !pWebWidget->init())
		{
			return;
		}
		pWebWidget->exec();
		QStringList lstfiles = pWebWidget->getScanfiles();
		KPromeMainWindow* pMainWin = promeApp ? promeApp->currentPromeMainWindow() : nullptr;
		if (pMainWin && lstfiles.size())
		{
			QVariantMap extParams;
			extParams["startupSource"] = "km_scan";
			pMainWin->notifyOpenFiles(
				lstfiles,
				KPromePage::LastOneDirectExeOtherProxy,
				nullptr,
				extParams);
		}};
#if defined(Q_OS_DARWIN)
	funcMobileScan();
	return;
#endif
	KPlugin* pPlugin = KPluginManager::getInstance()->getPlugin(APPID_MOBILESCAN);
	if (pPlugin)
	{
		connect(pPlugin, &KPlugin::loadFailed, this, []() {
			KxLoggerLite::writeInfo(KPluginNameW,
				QString("%1:KPlugin kwebmobilescan load failed").arg(__FUNCTION__).toStdWString());
			});
		connect(pPlugin, &KPlugin::loadSuccess, this, funcMobileScan);
		if (KPlugin::State::Loaded == pPlugin->load())
			funcMobileScan();
	}
}

void KxPromePdfWindowJsApi::openDocument(KxWebViewJSContext& context)
{
	if (!promeApp)
		return;
	auto mainwindow = promeApp->findRelativePromeMainWindow(this);
	if (mainwindow == nullptr)
		return;
	QVariantMap params;
	params.insert("showRecentTab", "1");
	params.insert("showCloudTab", "1");
	params.insert("AllowMultiSel", "0");
	params.insert("BusinessFrom", "FD_Pdf_QuickOpenFile");
	promeApp->showOpenFileDialog(mainwindow, params);
}

void KxPromePdfWindowJsApi::clicktRecommendationFunction(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;
	QString strPluginIdOld = QString::fromUtf8(args.value("pluginId").toString().toUtf8().data());
	QString strPluginId = QString::fromUtf8(args.value("id").toString().toUtf8().data());
	if (strPluginId.isEmpty())
		strPluginId = strPluginIdOld;
	runAppByPluginId(strPluginId);
}

void KxPromePdfWindowJsApi::onDownloadProgress(const QString& templateId, int nProgress)
{
	int nPercent = std::min(nProgress, 99);
	QVariantMap result;
	result["progress"] = nPercent;
	result["id"] = templateId;

	callbackToJS(s_strDownloadProgressJsCallBack, formatResult(result));
	KxNewDocJsApiHelper::instance().notifyDownloadProgress(this, templateId, nPercent);
}

void KxPromePdfWindowJsApi::onDownloadSuccess(const QString& templateId, const QString& filePath)
{
	auto newDocsPage = getNewDocsPage();
	if (!newDocsPage)
		return;
	QVariantMap result;
	result["id"] = templateId;
	result["progress"] = 100;
	QString strResult = formatResult(result);
	callbackToJS(s_strDownloadSuccessJsCallBack, strResult);
	callbackToJS(s_strDownloadProgressJsCallBack, strResult);
	KPromeMainWindow* mw = nullptr;
	bool isNewStandaloneWindow = false;

	mw = findMainWindow(isNewStandaloneWindow);
	newDocsPage->decDownloadTemplateCount();
	onOpenDocument(mw, filePath, "", getFileType(filePath));
	KxNewDocJsApiHelper::instance().notifyDownloadSuccess(this, templateId, filePath);
	KxNewDocJsApiHelper::instance().removeByResId(templateId);
	m_downloadfileIdSet.remove(templateId);
}

void KxPromePdfWindowJsApi::onDownloadError(const QString& templateId, kdocerresnetwork::DownloadFailInfo info)
{
	QVariantMap result;
	result["id"] = templateId;
	callbackToJS(s_strDownloadErrorJsCallBack, formatResult(result));
	KxNewDocJsApiHelper::instance().removeByResId(templateId);

	KxNewDocJsApiHelper::instance().notifyDownloadError(this, templateId, info);
	auto newDocsPage = getNewDocsPage();
	if (newDocsPage)
		newDocsPage->decDownloadTemplateCount();
	if (m_downloadfileIdSet.contains(templateId))
		m_downloadfileIdSet.remove(templateId);
}

void KxPromePdfWindowJsApi::onPluginLoadSuccess(QString appId)
{
	if (appId == "kpdffromscanner")
		onNewDocument("newscan", "", "pdf");
}

void KxPromePdfWindowJsApi::closeNewStandaloneMainwindow(bool isNewStandaloneWindow, QString appType, KPromeMainWindow* mw)
{
	if (isNewStandaloneWindow)
	{
		KPromeMainWindow* currentMw = promeApp->findRelativePromeMainWindow(this);
		if (currentMw)
			currentMw->close();

		//多组件模式在不匹配自己格式的情况下，是调用其它进程打开，所以要关闭创建出来的独立窗口。
		if (promeApp->isPromeShelllessMode() && !KPromePage::isMatchMode(m_createDocApp[appType]))
		{
			if (mw)
				mw->close();
		}
	}
}

void KxPromePdfWindowJsApi::_openDocument(QString appType, QVariantMap params, QStringList filterType)
{
	KPromeMainWindow* mw = nullptr;
	bool isNewStandaloneWindow = false;

	mw = findMainWindow(isNewStandaloneWindow);

	if (!mw || mw->getIsExportPdfFile())//一个主窗口只同时转换一个文档
		return;
	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;

	if (m_bIsShowSelectFileDlg)
		return;

	m_bIsShowSelectFileDlg = true;
	KPromeTab* pCurrentTab = nullptr;
	if (mw->tabBar())
	{
		pCurrentTab = mw->tabBar()->currentTab();
	}
	KPromePage* pCurrentPage = mw->centralArea()->currentPage();

	QStringList filePathList = promeApp->showSelectFileDialog(mw, params, filterType);

	m_bIsShowSelectFileDlg = false;
	if (filePathList.isEmpty())
	{
		if (isNewStandaloneWindow)
			mw->close();
		return;
	}
	QString strSuffix =  QFileInfo(filePathList.front()).suffix().toLower();
	if (!filePathList.isEmpty() && (strSuffix == "pdf" || strSuffix == "mobi" || strSuffix == "epub" || strSuffix == "fdf"))//选中pdf文档或电子书走打开流程
	{

		QStringList openLists;
		QStringList notOpenLists;
		if (isNewStandaloneWindow)
		{
			mw->findOpenStateFiles(filePathList, openLists, notOpenLists);
		}
		onOpenDocument(mw, filePathList.front(), "");
		if (isNewStandaloneWindow)
		{
			closeNewStandaloneMainwindow(isNewStandaloneWindow, appType, mw);
			//如果被其它窗口打开了。要把创建出来的独立窗口关闭。否则在其它窗口激活会留一个空白窗口。
			if (!openLists.isEmpty())
			{
				mw->close();
			}
		}
		return;
	}
	filePathList.push_front("exportpdf");

	bool bClose = (!promeApp->startupInfo()->isIndependentMode() ||
		KPromePage::isMatchMode(m_createDocApp[appType]));
	if (bClose)
	{
		KPromePage* page = contentArea->addPage(m_createDocApp[appType]);
		if (!page)
			return;
		if (pCurrentTab)
		{
			pCurrentTab->setAllowReAttachSubwindow(true);
			pCurrentTab->reAttachSubPage(nullptr);
		}
	}

	mw->notifyHandleExtendedFunc(filePathList, KPromeStartup::OpenMode_ForcePdf);

	//独立窗口在PDF多组件模式不要关闭，会导致独立窗口关闭。
	if (bClose && mw->headerbarType() == KPromeHeaderBarBase::Standalone && promeApp->isPromeShelllessMode())
	{
		bClose = false;
	}
	if (bClose)
		emit closeTab();

	closeNewStandaloneMainwindow(isNewStandaloneWindow, appType, mw);

	KPromeNewDocsPage* pNewDocsPage = static_cast<KPromeNewDocsPage*>(pCurrentPage);
	if (pCurrentTab && pNewDocsPage &&
		promeApp->startupInfo()->isIndependentMode() &&
		!KPromePage::isMatchMode(m_createDocApp[appType]))
	{
		pNewDocsPage->doClose();
		pCurrentTab->tryClose();
	}
}

void KxPromePdfWindowJsApi::initConfig()
{
	m_createDocApp["pdf"] = KPromePluginWidget::pagePdf;
	m_createDocApp["wps"] = KPromePluginWidget::pageWps;
	m_createDocApp["et"] = KPromePluginWidget::pageEt;
	m_createDocApp["wpp"] = KPromePluginWidget::pageWpp;

	if (krt::kcmc::support("PDFCommonSetting"))
		return initConfigPdfPro();

	QString pdfUrl = KDocerUtils::getDomainUrl("pdf");

//TODO(zhangxinzhao):应用配置还不一样
#ifdef Q_OS_DARWIN
	std::map<QString, QString> photo2pdfMap;
	photo2pdfMap.insert(std::pair<QString, QString>("entrance", "Home|NewPDF"));
	photo2pdfMap.insert(std::pair<QString, QString>("from", "Home|NewPDF"));
	photo2pdfMap.insert(std::pair<QString, QString>("standalone", "newpdf"));
	photo2pdfMap.insert(std::pair<QString, QString>("url", pdfUrl + PIC2PDF_COMB));
	m_pluginName["3"] = PluginInfo("photo2pdf", photo2pdfMap, "TabRecommend|image2PDF", EVENTNAME_PIC2PDF);

	std::map<QString, QString> kcad2pdfMap;
	kcad2pdfMap.insert(std::pair<QString, QString>("action", "Convert"));
	kcad2pdfMap.insert(std::pair<QString, QString>("url", pdfUrl + CAD2PDF_COMB));
	m_pluginName["4"] = PluginInfo("pdfcadmutualconverterv2", kcad2pdfMap, "TabRecommend|PDF2Word", EVENTNAME_CAD2PDF);//

	std::map<QString, QString> kpdf2wordv2Map;
	kpdf2wordv2Map["action"] = "ConvertToWord";
	kpdf2wordv2Map["url"] = pdfUrl + PDF2WORD_COMB;
	m_pluginName["1"] = PluginInfo("kpdf2wordv3", kpdf2wordv2Map, "TabRecommend|PDF2Word", EVENTNAME_PDF2WORD);

	std::map<QString, QString> pdf2photoMap;
	pdf2photoMap["url"] = pdfUrl + PDF2PHOTO_COMB;
	m_pluginName["2"] = PluginInfo("kpdf2photo", pdf2photoMap, "TabRecommend|PDF2Image", EVENTNAME_PDF2PHOTO);

	std::map<QString, QString> kMergev2Map;
	kMergev2Map["action"] = "Merge";
	kMergev2Map["url"] = pdfUrl + MERGE_COMB;
	m_pluginName["5"] = PluginInfo("kpdf2wordv3", kMergev2Map, "TabRecommend|PDFMerge", EVENTNAME_PDFMERGE);
	
	std::map<QString, QString> translateAllMap;
	m_pluginName["6"] = PluginInfo("kdoctranslate", translateAllMap, "TabRecommend|TranslationAll", EVENTNAME_DOCTRANSLATOR);
#else
	std::map<QString, QString> photo2pdfMap;
	photo2pdfMap.insert(std::pair<QString, QString>("entrance", "Home|NewPDF"));
	photo2pdfMap.insert(std::pair<QString, QString>("from", "Home|NewPDF"));
	photo2pdfMap.insert(std::pair<QString, QString>("standalone", "pdf_co_newpdf"));
	photo2pdfMap.insert(std::pair<QString, QString>("url", pdfUrl + PIC2PDF_COMB));
	m_pluginName["3"] = PluginInfo("photo2pdf", photo2pdfMap, "TabRecommend|image2PDF", EVENTNAME_PIC2PDF);

	std::map<QString, QString> kcad2pdfMap;
	kcad2pdfMap.insert(std::pair<QString, QString>("action", "Convert"));
	kcad2pdfMap.insert(std::pair<QString, QString>("url", pdfUrl + CAD2PDF_COMB));
	m_pluginName["4"] = PluginInfo("pdfcadmutualconverterv2", kcad2pdfMap, "TabRecommend|PDF2Word", EVENTNAME_CAD2PDF);

	std::map<QString, QString> kpdf2wordv2Map;
	kpdf2wordv2Map["action"] = "ConvertToWord";
	kpdf2wordv2Map["url"] = pdfUrl + PDF2WORD_COMB;
	m_pluginName["1"] = PluginInfo("kpdf2wordv2", kpdf2wordv2Map, "TabRecommend|PDF2Word", EVENTNAME_PDF2WORD);

	std::map<QString, QString> pdf2photoMap;
	pdf2photoMap["url"] = pdfUrl + PDF2PHOTO_COMB;
	m_pluginName["2"] = PluginInfo("kexportimage", pdf2photoMap, "TabRecommend|PDF2Image", EVENTNAME_PDF2PHOTO);

	std::map<QString, QString> kMergev2Map;
	kMergev2Map["action"] = "Merge";
	kMergev2Map["url"] = pdfUrl + MERGE_COMB;
	m_pluginName["5"] = PluginInfo("kpdf2wordv2", kMergev2Map, "TabRecommend|PDFMerge", EVENTNAME_PDFMERGE);
	
	std::map<QString, QString> translateAllMap;
	m_pluginName["6"] = PluginInfo("kdoctranslate", translateAllMap, "TabRecommend|TranslationAll", EVENTNAME_DOCTRANSLATOR);
#endif
}

void KxPromePdfWindowJsApi::initConfigPdfPro()
{
	QString pdfUrl = KDocerUtils::getDomainUrl("pdf");
	std::map<QString, QString> kpdf2wordv2Map;
	kpdf2wordv2Map.insert(std::pair<QString, QString>("action", "Convert"));
	kpdf2wordv2Map.insert(std::pair<QString, QString>("url", pdfUrl + PDF2WORD_COMB));
	m_pluginName["1"] = PluginInfo("kpdf2wordv2", kpdf2wordv2Map, "TabRecommend|PDF2Word", EVENTNAME_PDF2WORD);
	kpdf2wordv2Map["action"] = "ConvertToExcel";
	kpdf2wordv2Map["url"] = pdfUrl + PDF2EXCEL_COMB;
	m_pluginName["2"] = PluginInfo("kpdf2wordv2", kpdf2wordv2Map, "TabRecommend|PDF2Excel", EVENTNAME_PDF2EXCEL);
	kpdf2wordv2Map["action"] = "ConvertToPowerPoint";
	kpdf2wordv2Map["url"] = pdfUrl + PDF2PPT_COMB;
	m_pluginName["3"] = PluginInfo("kpdf2wordv2", kpdf2wordv2Map, "TabRecommend|PDF2PPT", EVENTNAME_PDF2PPT);
	std::map<QString, QString> pdf2photoMap;
	pdf2photoMap["url"] = pdfUrl + PDF2PHOTO_COMB;
	m_pluginName["4"] = PluginInfo("kexportimage", pdf2photoMap, "TabRecommend|PDF2Image", EVENTNAME_PDF2PHOTO);
	kpdf2wordv2Map["action"] = "Merge";
	kpdf2wordv2Map["url"] = pdfUrl + MERGE_COMB;
	m_pluginName["5"] = PluginInfo("kpdf2wordv2", kpdf2wordv2Map, "TabRecommend|PDFMerge", EVENTNAME_PDFMERGE);
	kpdf2wordv2Map["action"] = "Split";
	kpdf2wordv2Map["url"] = pdfUrl + SPLIT_COMB;
	m_pluginName["6"] = PluginInfo("kpdf2wordv2", kpdf2wordv2Map, "TabRecommend|PDFSplit", EVENTNAME_PDFSPLIT);
	std::map<QString, QString> pdfcompressMap;
	m_pluginName["7"] = PluginInfo("batchcompress", pdfcompressMap, "TabRecommend|PDFCompress");
	std::map<QString, QString> photo2pdfMap;
	photo2pdfMap.insert(std::pair<QString, QString>("entrance", "Home|NewPDF"));
	photo2pdfMap.insert(std::pair<QString, QString>("from", "Home|NewPDF"));
	photo2pdfMap.insert(std::pair<QString, QString>("standalone", "pdf_co_newpdf"));
	photo2pdfMap.insert(std::pair<QString, QString>("url", pdfUrl + PIC2PDF_COMB));
	m_pluginName["8"] = PluginInfo("photo2pdf", photo2pdfMap, "TabRecommend|image2PDF", EVENTNAME_PIC2PDF);
	std::map<QString, QString> translateAllMap;
	m_pluginName["9"] = PluginInfo("kdoctranslate", translateAllMap, "TabRecommend|TranslationAll", EVENTNAME_DOCTRANSLATOR);
}

void KxPromePdfWindowJsApi::onNewDocument(const QString& file, const QString& fileKey, QString appType /*= "pdf"*/)
{
	KPromeMainWindow* mw = nullptr;

	bool isNewStandaloneWindow = false;
	mw = findMainWindow(isNewStandaloneWindow);

	if (!mw)
		return;

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;

	KPromeTab* pCurrentTab = nullptr;
	if (mw->tabBar())
	{
		pCurrentTab = mw->tabBar()->currentTab();
	}
	KPromePage* pCurrentPage = mw->centralArea()->currentPage();

	PERFLOGBEGIN2("PrometheusNewDocument",
		toOpenDocFilterStr(m_createDocApp[appType], file).constData());

	bool bClose = (!promeApp->startupInfo()->isIndependentMode() ||
		KPromePage::isMatchMode(m_createDocApp[appType]));
	if (bClose)
	{
		KPromePage* page = contentArea->addPage(m_createDocApp[appType]);
		if (!page)
			return;
		if (pCurrentTab)
		{
			pCurrentTab->setAllowReAttachSubwindow(true);
			pCurrentTab->reAttachSubPage(nullptr);
		}
	}

	QVariantMap infoMap;
	infoMap.insert("from", "newdocspage");
	infoMap.insert("password", fileKey);
	mw->notifyNewFiles(QStringList(file),
		m_createDocApp[appType], KPromePage::LastOneDirectExeOtherProxy, infoMap);

	closeNewStandaloneMainwindow(isNewStandaloneWindow, appType, mw);

	if (bClose)
		emit closeTab();

	KPromeNewDocsPage* pNewDocsPage = static_cast<KPromeNewDocsPage*>(pCurrentPage);
	if (pCurrentTab && pNewDocsPage &&
		promeApp->startupInfo()->isIndependentMode() &&
		!KPromePage::isMatchMode(m_createDocApp[appType]))
	{
		pNewDocsPage->doClose();
		pCurrentTab->tryClose();
	}
}

void KxPromePdfWindowJsApi::onOpenDocument(KPromeMainWindow* mw, const QString& file, const QString& fileKey, QString appType /*= "pdf"*/)
{
	if (file.isEmpty() || !mw)
		return;

	KPromePage* pCurrentPage = NULL;
	QPointer<KPromeTab> pCurrentTab;
	if (mw)
	{
		pCurrentPage = mw->centralArea()->currentPage();
		if (mw->tabBar())
		{
			pCurrentTab = mw->tabBar()->currentTab();
		}
		KPromeNewDocsPage* pNewDocsPage = static_cast<KPromeNewDocsPage*>(pCurrentPage);
		if (NULL != pNewDocsPage)
		{
			for (int i = 0, count = pNewDocsPage->subPages()->count(); i < count; i++)
			{
				KPromeSubPage* pg = pNewDocsPage->subPages()->subPageAt(i);
				pg->setCloseMode(KPromeSubPage::CloseMode_ChangePage);
				pg->close();
			}
			pNewDocsPage->doClose();
			mw->centralArea()->activeCurrentPage();
		}
	}

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;

	int type = KxCreateDocConfig::instance().getAppPageType(appType);
	KPromePluginWidget::WidgetType pageType = (KPromePluginWidget::WidgetType)type;
	QStringList list(file);

	PERFLOGBEGIN2("PrometheusNewDocument",

		toOpenDocFilterStr(pageType, file).constData());
	mw->notifyOpenFiles(QStringList(file),
		pageType, KPromePage::LastOneDirectExeOtherProxy, QVariantMap());

	if (pCurrentPage && pCurrentTab &&
		pCurrentPage->type() == KPromePluginWidget::pageNewDocs &&
		krt::kcmc::support("PdfNewDocsTryClose"))
	{
		pCurrentTab->tryClose();
	}
}

KPromeNewDocsPage* KxPromePdfWindowJsApi::getNewDocsPage() const
{
	KPromeNewDocsPage* newDocsPage = nullptr;
	QObject* o = _window->parent();
	while (o)
	{
		auto page = qobject_cast<KPromePage*>(o);
		if (page)
		{
			newDocsPage = qobject_cast<KPromeNewDocsPage*>(page);
			break;
		}
		o = o->parent();
	}

	return newDocsPage;
}


void KxPromePdfWindowJsApi::runAppByPluginId(const QString& pluginId)
{
	//数据上报
	if (m_pluginName.count(pluginId) != 0)
	{
		QString eventName = m_pluginName[pluginId].mSourceStr;
		if (!eventName.isEmpty())
			KxPdfNewDocHelper::CreateDcInfo(eventName, "source", "pdfnewPage");
	}

	if (pluginId.toInt() == PdfPluginJsId_Pdf2Pic || (krt::kcmc::support("OnlyFocusOnPDF") && pluginId.toInt() == 4))
		onExportPhoto(m_pluginName[QString::number(pluginId.toInt())]);
	else
		onClickPluginApp(m_pluginName[QString::number(pluginId.toInt())]);
}

void KxPromePdfWindowJsApi::onClickPluginApp(PluginInfo pluginInfo)
{
	if (m_proxyPlugin)
	{
		delete m_proxyPlugin;
		m_proxyPlugin = nullptr;
	}
	m_proxyPlugin = new KPdfProxyPugin(this);
	connect(m_proxyPlugin, SIGNAL(onPluginLoadSuccess(QString)), this, SLOT(onPluginLoadSuccess(QString)));
	m_proxyPlugin->setAppID(pluginInfo.pluginName);
	m_proxyPlugin->setAppProperty(pluginInfo.pluginProperty);
	m_proxyPlugin->requestWidget(promeApp->currentPromeMainWindow(), "wpsoffice", "TabNewPDF_Plugin");
	m_proxyPlugin->onTrigger();
}

void KxPromePdfWindowJsApi::onExportPhoto(const PluginInfo& pluginInfo)
{
	if (m_proxyPlugin)
	{
		delete m_proxyPlugin;
		m_proxyPlugin = nullptr;
	}

	m_proxyPlugin = new KProxyPugin_ExportImage(this);
	connect(m_proxyPlugin, SIGNAL(onPluginLoadSuccess(QString)), this, SLOT(onPluginLoadSuccess(QString)));
	m_proxyPlugin->setAppID(pluginInfo.pluginName);
	m_proxyPlugin->setAppProperty(pluginInfo.pluginProperty);
	m_proxyPlugin->requestWidget(promeApp->currentPromeMainWindow(), "wpsoffice", "TabNewPDF_Plugin");
	m_proxyPlugin->onTrigger();
}

void KxPromePdfWindowJsApi::onHandleExtentedFunc(QStringList params)
{
	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return;
	mw->notifyHandleExtendedFunc(params, KPromeStartup::OpenMode_ForcePdf);
}

void KxPromePdfWindowJsApi::newExportPage(QString srcFilePath, QString dstFilePath, QString widgetType, QString p4ValuePre, bool isNewPage/* = true*/, QString appType /*= "pdf"*/)
{
	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	QVariantMap infoMap;
	infoMap.insert("filePath", srcFilePath);
	infoMap.insert("dstfilePath", dstFilePath);
	infoMap.insert("isNewPage", isNewPage);
	infoMap.insert("widgetType", widgetType);
	infoMap.insert("from", "wpsoffice");
	infoMap.insert("p4ValuePre", p4ValuePre);
	mw->notifyExportPdfFile(dstFilePath, m_createDocApp[appType], KPromePage::LastOneDirectExeOtherProxy, infoMap);//显示新建页
}

KPromeMainWindow* KxPromePdfWindowJsApi::findMainWindow(bool& isNewStandaloneWindow)
{
	isNewStandaloneWindow = false;
	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return nullptr;

	if (mw->headerBar() && mw->headerBar()->getHeaderBarType() == KPromeHeaderBarBase::Standalone)
	{
		KPromeMainWindow* standalonewindow = mw->findOrCreateMainWindow(KPromeHeaderBarBase::Standalone, true);
		isNewStandaloneWindow = true;
		return standalonewindow;
	}
	return mw;
}

QByteArray KxPromePdfWindowJsApi::toOpenDocFilterStr(KPromePluginWidget::WidgetType t, const QString& path)
{
	QString type = KPromePluginWidget::convertWidgetType(t);
	return QDir::toNativeSeparators(QDir::cleanPath(
		QString(type % "|" % path % "|" % (isPageLoaded(t) ? "1" : "0")).toLower()))
		.toUtf8().toBase64();
}

bool KxPromePdfWindowJsApi::isPageLoaded(KPromePluginWidget::WidgetType t)
{
	foreach(KPromeMainWindow * mw, promeApp->mainWindows())
	{
		auto page = dynamic_cast<KPromeWpsPage*>(mw->centralArea()->pageByType(t));
		if (page && page->containedWindow() != 0)
			return true;
	}
	return false;
}

void KxPromePdfWindowJsApi::onExportPdfDocument(QString appType/* = "pdf"*/)
{
	QVariantMap params;
	params.insert("showRecentTab", "1");
	params.insert("showCloudTab", "1");
	params.insert("AllowMultiSel", "0");
	params.insert("BusinessFrom", "FD_KNewDocs_ExportPdfDocument_SelectFile");
	QStringList filterType;
	filterType << "wps" << "et" << "wpp";

	_openDocument(appType, params, filterType);
}

void KxPromePdfWindowJsApi::onOpenPromeBrowser(const QString& url)
{
	if (!url.startsWith("http://", Qt::CaseInsensitive) && !url.startsWith("https://", Qt::CaseInsensitive))
		return;
	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return;

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;
	contentArea->addBrowserPage(url);
}

KProxyPugin_ExportImage::KProxyPugin_ExportImage(QObject* parent)
	: KPdfProxyPugin(parent)
	, m_bIsShowSelectFileDlg(false)
{

}

KProxyPugin_ExportImage::~KProxyPugin_ExportImage()
{

}

void KProxyPugin_ExportImage::runApp()
{
	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return;

	//getFilePath
	if (m_bIsShowSelectFileDlg)
		return;
	m_bIsShowSelectFileDlg = true;
//TODO(zhangxinzhao): 文件打开窗口的基建没有做好吗？
#ifdef Q_OS_DARWIN
	QString filePath = QFileDialog::getOpenFileName(mw, "", QDir::homePath() , "PDF(*.pdf)");
	if (filePath.isEmpty())
		return;
#else
	QVariantMap params;
	params.insert("showRecentTab", "1");
	params.insert("showCloudTab", "1");
	params.insert("AllowMultiSel", "0");
	params.insert("BusinessFrom", "FD_KNewDocs_KBatchExportImage_SelectFile");
	QStringList filterType;
	filterType << "pdf";
	QStringList filePathList = promeApp->showSelectFileDialog(mw, params, filterType);

	m_bIsShowSelectFileDlg = false;

	if (filePathList.empty())
		return;
#endif

//TODO(zhangxinzhao): 应用平台配置
#ifndef Q_OS_DARWIN
	KAppContext context;
	context.entrance = Entrance_Newdocs;
	context.src = "newpdf";//首页新建
	QWidget* hostWindow = promeApp->currentPromeMainWindow();
	context.hostWindow = hostWindow;
	context.arguments["action"] = m_param;
	context.arguments["file"] = filePathList.first();
	m_appObj->run(context);
#endif

	QPointer<KPromeTab> pCurrentTab;
	KPromePage* pCurrentPage = mw->centralArea()->currentPage();
	if (mw->tabBar())
	{
		pCurrentTab = mw->tabBar()->currentTab();
	}

	KPromeNewDocsPage* pNewDocsPage = static_cast<KPromeNewDocsPage*>(pCurrentPage);
	if (NULL != pNewDocsPage)
	{
		for (int i = 0, count = pNewDocsPage->subPages()->count(); i < count; i++)
		{
			KPromeSubPage* pg = pNewDocsPage->subPages()->subPageAt(i);
			pg->setCloseMode(KPromeSubPage::CloseMode_ChangePage);
			pg->close();
		}
		pNewDocsPage->doClose();
		mw->centralArea()->activeCurrentPage();
	}

#ifdef Q_OS_DARWIN
	QStringList notifyParams;
	notifyParams << "pdf2photo";
	notifyParams << filePath;
	mw->notifyHandleExtendedFunc(notifyParams, KPromeStartup::OpenMode_ForcePdf);
#endif
	
	if (pCurrentPage && pCurrentTab &&
		pCurrentPage->type() == KPromePluginWidget::pageNewDocs &&
		krt::kcmc::support("PdfNewDocsTryClose"))
	{
		pCurrentTab->tryClose();
	}
}

void KxPromePdfWindowJsApi::isDarkSkin(KxWebViewJSContext& context)
{
	setResult(context, (QVariant)KNewDocDrawHelper::isDarkSkin());
}

void KxPromePdfWindowJsApi::onSkinChanged()
{
	bool isDarkSkin = false;
	if (promeApp && promeApp->promeSkin())
	{
		isDarkSkin = promeApp->promeSkin()->isDarkSkin();
	}
	QVariantMap result;
	result["isDarkSkin"] = isDarkSkin;
	callbackToJS(s_strPdfSkinChangedJsCallBack, formatResult(result));
}

void KxPromePdfWindowJsApi::onAppMgrLoadFinished()
{
	callbackToJS(s_strAppMgrLoadFinishCallBack, "");
}

void KxPromePdfWindowJsApi::newScanItem(KxWebViewJSContext& context)
{
	bool isShow = checkAppValidity(APPID_PDFFROMSCANNER);
	setResult(context, (QVariant)isShow);
}

void KxPromePdfWindowJsApi::translateItem(KxWebViewJSContext& context)
{
	bool isShow = checkAppValidity(APPID_TRANSLATE);
	setResult(context, (QVariant)isShow);
}

void KxPromePdfWindowJsApi::exportPdfItem(KxWebViewJSContext& context)
{
	bool isShow = checkAppValidity(APPID_EXPORTPDF);
	setResult(context, (QVariant)isShow);
}

void KxPromePdfWindowJsApi::OnWebPageLoadFinished(KxWebViewJSContext& context)
{

}

void KxPromePdfWindowJsApi::createBlankDocument(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;
	
	QString blankFileStr = "blank";

	auto tabId = args["switchToRbTab"].toString();
	if (!tabId.isEmpty())
		blankFileStr.append(" /switchToRbTab=").append(tabId);

	onNewDocument(blankFileStr, "", "pdf");
}

void KxPromePdfWindowJsApi::doRunApp(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	QString appId = args["appId"].toString();
	if (appId.isEmpty())
		return;

	runApp(appId);
}

void KxPromePdfWindowJsApi::isAppsValid(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;
	QStringList appIds = args["appIds"].toStringList();
	QVariantMap retMap;
	foreach(QString appId, appIds)
	{
		if (retMap.contains(appId))
			continue;
		retMap.insert(appId, checkAppValidity(appId));
	}
	setResult(context, retMap);
}

void KxPromePdfWindowJsApi::openMyTemplateTab(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	QString params = QString::fromLocal8Bit(context.getJsonValue("params").toLocal8Bit());
	KPromeNewDocsPage* newDocsPage = getNewDocsPage();
	if (newDocsPage)
	{
		newDocsPage->openSpecificTab(KPromePluginWidget::pageMytpl, params);
	}
}
