webpackJsonp([5],{"R+Qh":function(t,e,i){"use strict";var a={data:function(){return{}},props:{item:{type:Object,default:function(){return{}}},index:Number},methods:{openFile:function(){void 0===this.item.is_exit?this.$emit("openFile",this.item):this.$emit("openFile",this.item.filePath,this.item.id,this.item.fileType)}},computed:{show:function(){return void 0!==this.item.is_exit&&!this.item.is_exit}}},s={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"list-item",on:{click:function(e){t.openFile()}}},[i("span",{staticClass:"icon icon-document"}),t._v(" "),i("div",{staticClass:"list-info"},[t.show?i("h5",{class:{"no-name":t.show},attrs:{title:t.item.name}},[t._v(t._s(t.item.name))]):t._e(),t._v(" "),t.show?t._e():i("h5",{class:{"no-h5":!t.item.author&&!t.item.publish_time},attrs:{title:t.item.name}},[t._v(t._s(t.item.name))]),t._v(" "),i("p",[t.item.author?i("span",{staticClass:"name"},[t._v(t._s(t.item.author))]):t._e(),t.item.publish_time?i("span",{staticClass:"time"},[t._v(t._s(t._f("replaceDate")(t.item.publish_time)))]):t._e()])])])},staticRenderFns:[]},n={data:function(){return{}},props:["limit","data","csource","position","servicetype"],computed:{limitCount:function(){return this.limit&&this.data.length&&this.data.length%this.limit!=0?this.limit-this.data.length%this.limit:0}},methods:{openFile:function(t,e,i){void 0==e?this.$emit("openFile",t):this.$emit("openFile",t,e,i)}},components:{Item:i("VU/8")(a,s,!1,null,null,null).exports}},o={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("ul",{staticClass:"m-txtlist"},[t._l(t.data,function(e,a){return i("li",{key:a,staticClass:"m-txtlist-item"},[i("Item",{attrs:{item:e,index:a},on:{openFile:t.openFile}})],1)}),t._v(" "),t._l(t.limitCount,function(t){return[i("li",{staticClass:"m-txtlist-item"})]})],2)},staticRenderFns:[]},r=i("VU/8")(n,o,!1,null,null,null);e.a=r.exports},"fip/":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var a=i("//Fk"),s=i.n(a),n=i("Dd8w"),o=i.n(n),r=i("NYxO"),l=i("mMDT"),c=i("G1qf"),u=i("gEr2"),h=i("cMGX"),m=i("R+Qh"),d=i("UNaj"),p=i("wYMm"),f=i("s0MJ"),g=i("w7XY"),v=i("v8ob"),_=i("59SO"),y={mixins:[d.a],data:function(){return{word:"",searchType:"",statusCode:"",limit:0,query:{},totalPage:0,page:1,data:[],filter:{}}},beforeRouteEnter:function(t,e,i){i(function(e){_.a.sendRecently("show"),v.a.$on(p.g.resizeWindow,e.ajustTplLayout),e.initData(t)})},beforeRouteUpdate:function(t,e,i){this.initData(t),i()},beforeRouteLeave:function(t,e,i){v.a.$off(p.g.resizeWindow,this.ajustTplLayout),i()},methods:o()({},Object(r.b)(["setPreviewShow"]),{initQuery:function(t){t=t||{},this.word=t.word||"",this.query=t},initData:function(t){var e=this;this.filter={},this.$refs.nColorFilter&&this.$refs.nColorFilter.resetColor(),this.$refs.nOrderFilter&&this.$refs.nOrderFilter.resetOrderBy(),this.ajustTplLayout(),this.initQuery(t.query),this.$nextTick(function(){e.page=1,e.getData()})},gotoPage:function(t){t>0&&t<=this.totalPage&&(this.page!=t||1==t)&&(this.page=t,this.getData())},retry:function(){this.getData()},getData:function(){var t=this;this.word=this.word.replace(/\s/g,""),this.getFile().then(function(e){t.data=e.slice((t.page-1)*t.pageSize,t.page*t.pageSize),t.totalPage=Math.ceil((e.length||0)/t.pageSize),t.statusCode=e.length>0?"":p.k.empty,t.statusCode===p.k.empty?_.a.sendRecently("recently_result_empty"):_.a.sendRecently("recently_result_successed")},function(){t.data=[],t.totalPage=0,t.statusCode=p.k.error})},getFile:function(){var t=this.word.replace(/\s/g,"");return t?new s.a(function(e,i){g.a.recentFileSearch({keyWord:t,appName:p.b.name}).then(function(t){t&&e(t),e([])}).catch(function(t){i(t)})}):new s.a(function(t,e){g.a.getRecentFiles({maxCount:-1}).then(function(e){e&&t(e),t([])}).catch(function(t){e(t)})})},ajustTplLayout:function(){this.limit=f.b.getLimit(this.$refs&&this.$refs.nSearch,"menu")},openFile:function(t,e,i){"word"===this.query.searchType?_.a.send("recently_search_page","recently_file_click"):_.a.sendRecently("recently_file_click"),g.a.openRecentFile({fileId:e,fileName:t,fileType:i,appName:p.b.name}).then(function(t){}).catch(function(t){})},addWindowStatusListener:function(){var t=this;window.onresize=function(){t.ajustTplLayout()}}}),computed:o()({},Object(r.c)({appName:"getAppName"}),{pageSize:function(){return 5*this.limit}}),mounted:function(){this.addWindowStatusListener()},components:{GoHome:l.a,Pagination:h.a,DataStatus:c.a,EpTxtList:m.a,SearchEp:u.a},watch:{limit:function(){"recep"==f.b.getRoutePath(this.$route.path)[0]&&this.limit&&(this.ajustTplLayout(),this.page=1,this.getData())}}},w={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"m-container"},[i("div",{directives:[{name:"goto-top",rawName:"v-goto-top"}],ref:"scroller",staticClass:"m-main"},[i("div",{ref:"nSearch",staticClass:"m-main-in"},[i("GoHome"),t._v(" "),i("div",{staticClass:"top-search"},[i("SearchEp",{attrs:{theme:"search-box",defaultValue:t.word,from:"recently_page"}})],1),t._v(" "),i("div",{ref:"fixBar",staticClass:"g-clearfloat g-should-fix-wrapper-1"},[i("div",{staticClass:"g-should-fix",class:{"g-fixed":t.isFixed}},[i("SearchEp",{directives:[{name:"show",rawName:"v-show",value:t.isFixed,expression:"isFixed"}],style:{float:"right",marginTop:"-8px"},attrs:{defaultValue:t.word,from:"recently_page"}})],1)]),t._v(" "),i("div",[i("DataStatus",{attrs:{statusCode:t.statusCode},on:{retry:t.retry}})],1),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.statusCode,expression:"!statusCode"}],staticClass:"theme-list"},[i("EpTxtList",{attrs:{limit:t.limit,data:t.data},on:{openFile:t.openFile}}),t._v(" "),i("Pagination",{attrs:{totalPage:t.totalPage,page:t.page},on:{gotoPage:t.gotoPage}})],1)],1)])])},staticRenderFns:[]};var C=i("VU/8")(y,w,!1,function(t){i("uBw5")},"data-v-7c9578ae",null);e.default=C.exports},uBw5:function(t,e){}});