﻿#ifndef __KPROMETHEUS_NEWDOCSPAGE_H__
#define __KPROMETHEUS_NEWDOCSPAGE_H__

#include <kprometheus/kpromepage.h>
#include <kprometheus/kpromebrowser.h>
#include <ksolite/krecentfile.h>

class KPromeNewDocsTabBar;
class KPromeNewDocsTab;
class KPromeNewDocsStartup;
class KPromeNewDocsTabInfoObj;
class KPromeNewDocsTabBarConfig;
class KPromeNewDocsScrollBar : public QScrollBar
{
	Q_OBJECT
public:
	KPromeNewDocsScrollBar(QWidget* parent);
protected:
	void paintEvent(QPaintEvent* ev) override;
	QSize sizeHint() const override;
};

class KPromeNewDocsPage
	: public KPromePage
{
	Q_OBJECT
public:
	KPromeNewDocsPage(QWidget* parent);
	~KPromeNewDocsPage();

	virtual KPromePluginWidget::WidgetType type() const override;
	virtual QString getPageTitle() const override;
	virtual void load() override;
	virtual bool newDocument(const QString& file = QString(),
		const QVariantList& args = QVariantList(),
		ProxyStrategy strategy = LastOneDirectExeOtherProxy,
		KPromeSubPage* parentSubPage = nullptr,
		const QVariantMap& infoMap = QVariantMap()) override;
	virtual bool openFiles(const QStringList& files,
		const QVariantList& args = QVariantList(),
		ProxyStrategy strategy = LastOneDirectExeOtherProxy,
		KPromeSubPage* parentSubPage = nullptr,
		const QVariantMap& infoMap = QVariantMap(),
		bool bShowFrontMainWin = true) override;
	void notify(const QString& cmd) override;
	void doClose() override;
	void invokeWebPageCallback(int tabType, const QString& param);
	//下载模版正在执行任务个数相关
	int getDownloadTemplateCount();
	//前端设置新建文档时，是否关闭新建页，为true时，不关闭新建页
	bool noClose();
	//关闭页面时检查一次然后重置
	void setNoClose(bool);
	qint64 getKNewDocsTabbarButtonClickedTimeStamp() const;
	void decDownloadTemplateCount();
	void incDownloadTemplateCount();
	//从应用配置调起
	Q_INVOKABLE void setAppContext(const KAppContext& context);
	Q_INVOKABLE void setCurrentTab(const QString& tabId);
	Q_INVOKABLE void setTabType(int tabType);
	Q_INVOKABLE void testNewDocument(int tabType);
	Q_INVOKABLE QString processonFileId() const;
	Q_INVOKABLE QVariantMap getPageTabFileType() const;
	Q_INVOKABLE QVariantMap getPageTabIconInfo() const;
	KPromeNewDocsTabInfoObj* getDocsTabByType(int tabType);
	KPromeNewDocsStartup* getStartupPage() const;
	int getCurrentTabType();
	void openSpecificTab(int tabType, const QString& params);
	static bool isComponentType(int tabType);
	void postKNewdocsLoadEvent(int errorCode);
public:
	void setExpectedTabType(int type);
	void setExpectedUrl(const QString& url);
	int expectedTabType();
	int tabType();
	QString entrance();
	QMap<QString, QString> timeStampMap() const;
	QString entryId() const;

signals:
	void invokeWebPageCallback(KPromePluginWidget::WidgetType, const QString& jsCallBack);
	void sigTabChanged();
	void sigTabInitBrowser();
	void sigloadProgress(int progress);
	void sigPluginLoadFailed();
	void sigPluginLoadSuccess();
	void sigWebLoadfinished();
	void mainResourceVerificationFailed();

protected slots:
	void onAddPageComplete();
	void onTabClicked(int tabType);
	void onRefreshWebWidget(int tabType);
	void onOpenDocument();
	void onCefKeyEvent(Qt::KeyboardModifiers modifyKey, int key, bool keyPress, bool &handle);
	void onOpenProcessOnFile(bool b);
	void onIsOpenProcessonFile(const QString&);
	void onTitleChanged(const QString& title);
	void onSubPageActived();
	void onHideNewDocsPageTabbar();
	void onJumpToSpecificTab(const QString& tabId, const QString& params);
	void onPluginLoad(bool falgs);
	void reloadPlugin(KPromeNewDocsTabInfoObj* pConfigObj, KPromeNewDocsTabBarConfig* pConfig);
	void onShowWidget(const QWidget* widget);
	void onUpdateTitleAndIcon(const QString& title, const QIcon& ic);

private slots:
	void onLoadTimeout();
#if defined(Q_OS_LINUX) && !defined(Q_OS_OHOS) && !defined(Q_OS_ANDROID) && !defined(WEB_OFFICE_ENABLE)
	void onLoginStatusChanged();
#endif

protected:
	virtual KPromeSubPage* createSubPage(KPromeSubPages*) const override;

private:
	void initUIWidget();
	void sendCreateNewPageInfo(const QVariantMap& infoMap);
	void sendVasShellNewCreatePageInfo(int tabType);
	bool isProcessOnType() const;
	bool isWpsFormType() const;
	bool isKDocsType() const;
	bool isKSheetType() const;
	bool isKDbSheetType() const;
	bool isChuangKitType() const;
	bool isQuickCreateType() const;
	bool isUseStartupWidget(const int type) const;
	void paintEvent(QPaintEvent* e);
	void upDateTitleAndIcon(const QString& title, const QString& iconName);
	int getExpectTabType(const QString& appName);
	void notifyToChangeTemplateTab(const QMap<QString, QString>& mapParam);
	void initContentWidget(int tabType);
	void loadPlugin(KPromeNewDocsTabInfoObj* pConfigObj, KPromeNewDocsTabBarConfig* pConfig);
	void initTopTagButton();
	int getDefaultWidgetType();

public:
	static const int s_invalidTabType;

private:
	QScrollArea* 				m_scrollArea = nullptr;
	QString						m_strDefaultTitle;
	QStackedWidget*				m_stackedWidget;
	QVBoxLayout*				m_mainLayout;
	QMap<int, QWidget*>			m_widgetMap;
	QMap<int, QString>			m_typeTitleMap;
	QMap<QString, QString>		m_timeStampMap;
	int							m_tabType;
	int							m_expectedTabType;
	QString						m_expectedUrl;
	bool						m_loaded;
	bool						m_hasOpenProcessOnFile;
	bool						m_isProcessOnAutoNew;
	qint64						m_clickNewTabbarTimeStamp;
	QString						m_processonFileId;
	QString						m_entrance;
	int							m_downloadTemplateCount;
	QString						m_entryId;
	KPromeNewDocsStartup*		m_contentStartupPage = nullptr;
	QString						m_cbParams;
	KPromeNewDocsTabBar*		m_tabBar;
	bool						m_noClose = false;
};

class KPromeNewDocsSubPage: public KPromeSubPage
{
	Q_OBJECT
public:
	KPromeNewDocsSubPage(KPromeSubPages* subPages,  QPointer<KPromeNewDocsPage> page);
	~KPromeNewDocsSubPage();

protected:
	virtual bool getWorkAreaRecoverItem(KPromeWorkAreaRecoverItem* pItem) override;

private:
	QPointer<KPromeNewDocsPage> m_page;
};

class KBlankTemplateRecentRecordHelper : public QObject
{
	Q_OBJECT
public:
	static KBlankTemplateRecentRecordHelper* instance()
	{
		static KBlankTemplateRecentRecordHelper ins;
		return &ins;
	}
private slots:
	void onRecentFileRecordsAdded(const RecordInfoList &recentInfos);
private:
	KBlankTemplateRecentRecordHelper(QObject* parent = nullptr);
	~KBlankTemplateRecentRecordHelper();
};

#endif//__KPROMETHEUS_NEWDOCSPAGE_H__
