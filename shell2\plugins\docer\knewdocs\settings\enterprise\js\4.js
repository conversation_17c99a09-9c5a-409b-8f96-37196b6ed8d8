(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[4],{1306:function(t,e,a){},"193b":function(t,e,a){"use strict";a("acc4")},"267c":function(t,e,a){t.exports=a.p+"images/wps.png"},"3a9b":function(t,e,a){t.exports=a.p+"images/wpp.png"},"4a78":function(t,e,a){"use strict";var s=a("13bc"),i=a("c8fc"),n=a("2f62"),l=a("beeb");const c=a("267c"),o=a("3a9b"),r=a("db6c"),h=function(t){if(t){var e=l["u"][t];if(e){"?"==e.slice(-1)?l["u"].appUrl=e.slice(0,-1):l["u"].appUrl=e;var a=e.indexOf("v3.php/");l["u"].privateUrl=-1!=a?e.slice(0,a+7):l["u"].appUrl.slice(0,-4),l["u"].isSupportIntranetTpl=!0}}},u=function(){s["a"].IsSupportIntranetTemplate().then(t=>{t&&s["a"].getCreateDocUrl().then(t=>{t&&(l["u"].wps=t.url_wps,l["u"].et=t.url_et,l["u"].wpp=t.url_wpp,h(l["e"].name))}).catch(t=>{console.log(t)})}).catch(t=>{console.warn(t)})};u(),e["a"]={computed:{...Object(n["c"])("common",["isEnterprise"])},data:()=>({localTplData:[],customTplInfo:[],customTplData:[],recentlyData:[]}),methods:{_getDefaultPreviewImgSrc(){switch(l["e"].name){case"wps":return c;case"wpp":return o;case"et":return r;default:return o}},_loadMoreData(t,e,a){if(l["u"].isSupportIntranetTpl)return this._getPrivateTplByTag(t,e,a)},_getRecentlyOpenFile(){this._getFile().then(t=>{this.recentlyData=t},()=>{console.error("获取最近打开文档失败")})},_getTplData(){return l["u"].isSupportIntranetTpl?this._getPrivateTpl():this._getLocalTpl()},_getFile(){return new Promise((t,e)=>{s["a"].getRecentFiles({maxCount:-1}).then(e=>{e&&t(e),t([])}).catch(t=>{e(t)})})},_getLocalRecentTpl(t){return s["a"].getRecentUseTemplate({appName:t})},_removeRecentTemplate(t){return s["a"].removeRecentTemplate({templatePath:t})},_getLocalTpl(){return s["a"].getLocalTemplateFileInfo({appName:l["e"].name}).then(t=>(Array.isArray(t)&&t.forEach(t=>{Array.isArray(t.fileList)?t.total_num=t.fileList.length:t&&(t.total_num=0)}),t))},_getCustomTpl(){return s["a"].getCustomTemplateFileInfo({appName:l["e"].name})},_getPrivateTags(){return new Promise(t=>{i["a"].get("pritag").getPrivateTags({data:{mb_app:l["e"].mark},privateUrl:{url:l["u"].privateUrl}}).then(e=>{if("ok"===e.result){let s=0;var a=[];e.data.forEach(t=>{t[0].item.forEach(t=>{if(s++,s>3)return!1;a.push(t)})}),t(a)}})})},_getPrivateTplByTag(t,e=1,a=10){return new Promise(s=>{i["a"].get("pritpl").getPriTplByTags({data:{term:t.k,sort_by:"mix",moban_type:"",sort_way:"down",limit:a,page:e,vip:1},privateUrl:{url:l["u"].appUrl}}).then(e=>{var a={tagName:t.v};"ok"===e.result&&e.data&&0!==e.data.total_num?(a.fileList=e.data.data,a.total_num=e.data.total_num):(a.fileList=[],a.total_num=0),s(a)})})},_getPrivateTpl(){this.localTplData=[];let t=[];return this._getPrivateTags().then(e=>(e.forEach(a=>{t.push(this._getPrivateTplByTag(a).then(t=>{e.forEach(e=>{e.v==t.tagName&&(e.tagName=t.tagName,e.fileList=t.fileList)})}))}),Promise.all(t).then(()=>(this.localTplData=e,e))))},_getCustomOfficialTemplate(){this.customData=[],s["a"]._getCustomOfficialTemplate().then(t=>{t&&t.length>0&&(console.log("自定义模板：",t),this.customData=t)})},_searchPrivateTplData(t,e,a){return i["a"].get("pritpl").searchPriTpl({data:{term:t,page:e,is_push:1,limit:a,vip:1},privateUrl:{url:l["u"].appUrl}}).then(t=>("ok"===t.result&&t.data&&Array.isArray(t.data.moban)&&(t.data.fileList=t.data.moban),t.data))},_searchlocalTplData(t){return s["a"].localTemplateSearch({keyWord:t,appName:l["e"].name}).then(t=>(Array.isArray(t.fileList)?t.total_num=t.fileList.length:t&&(t.total_num=0),t))},_searchTplDataFn(t,e,a){return l["u"].isSupportIntranetTpl?this._searchPrivateTplData(t,e,a):this._searchlocalTplData(t)},_localSearch(t,e,a){return this._searchTplDataFn(t,e,a).then(t=>(console.log("_localSearch: "+JSON.stringify(t)),t))},_formartPrevieImage(t){const e=t=>{t.thumb_img="data:image/png;base64,"+t.preview};return Array.isArray(t)?t.forEach(t=>{t.preview&&e(t)}):t&&t.preview&&e(t),t}}}},"55d6":function(t,e,a){"use strict";a("e485")},"5ab0":function(t,e,a){},"7dc8":function(t,e,a){"use strict";a("8225")},8225:function(t,e,a){},a487:function(t,e,a){"use strict";a("5ab0")},acc4:function(t,e,a){},b7a0:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"stick",rawName:"v-stick"}],ref:"rMainContentCon",staticClass:"mytpl mytpl__container"},[!t.isLogin&&t.getUserInfoFinish?e("div",{staticClass:"mytpl__nologin"},[e("NotLogin")],1):[e("div",{staticClass:"mytpl__body"},[e("div",{staticClass:"mytpl__top"},[e("div",{staticClass:"mytpl__top__line"},[e("DocerUl",{attrs:{selectTab:t.menu,list:t.menuList},on:{change:t.changeMenu}})],1),e("MainTopBar",{attrs:{mark:t.mark,menu:t.menu,showBatchEdit:t.showBatchEdit,isBatchEditing:t.isBatchEditing,selectStatus:t.selectStatus,statusCode:t.statusCode},on:{changeTerm:t.changeTerm,changeShowBatchEdit:t.changeShowBatchEdit,batchDelete:t.batchDelete,changeSelectStatus:t.changeSelectStatus}})],1),e("div",{ref:"nContent",class:["mytpl__content","mytpl__content__"+t.mark]},[t.statusCode?e("DataStatus",{attrs:{statusCode:t.statusCode,module:t.menu},on:{retry:t.getData}}):t._e(),e("List",{staticClass:"mytpl__list",attrs:{list:t.data,maxCount:t.maxCount,limit:t.limit,pageSize:t.pageSize,showDeleteBtn:t.showDeleteBtn,showDeleteFavBtn:"collect"==t.menu,isBatchOperate:t.isBatchEditing,isMyBought:"buy"==t.menu,commonCollectParams:t.commonCollectParams},on:{loadMore:t.loadMore,onClickDelete:t.onClickDelete,onClickDeleteFav:t.onClickDeleteFav,onChangeItemSelectStatus:t.onChangeItemSelectStatus}})],1)])]],2)},i=[],n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"notlogin"},[e("div",{staticClass:"notlogin__main"},[e("SvgIcon",{attrs:{svgName:"placeholder_logout_100"}}),e("p",{staticClass:"notlogin__info"},[t._v("您用过的模板都在这里，登录才能看到喔！")]),e("span",{staticClass:"m-btn m-btn-secondary-blue",on:{click:t.login}},[t._v("立即登录")])],1)])},l=[],c=a("2f62"),o=a("3387"),r={data(){return{}},mounted(){this.setAppComponent("")},methods:{...Object(c["b"])("common",["setAppComponent"]),...Object(c["b"])("user",["checkUserInfo"]),login(){let t={$m_n:"guide_login",$e_t:"button",$e_n:"login",$e_i:10813};o["a"].send("click",t),this.checkUserInfo().catch(()=>{console.log("request allinfo")})}}},h=r,u=(a("caf9"),a("2877")),m=Object(u["a"])(h,n,l,!1,null,"44a2c31f",null),d=m.exports,p=a("a4c7"),g=a("beeb"),_=a("f348"),f=a("a6c6"),v=a("c8fc");const b={wps:1,et:2,wpp:3,pdf:14},C={common:{recent:{serviceName:"user",methodName:"queryMyUsed"},collect:{serviceName:"user",methodName:"queryMyCollected"},buy:{serviceName:"user",methodName:"queryMyBought"},recentSearch:{serviceName:"user",methodName:"queryMyUsed"},collectSearch:{serviceName:"user",methodName:"searchMyCollect"},buySearch:{serviceName:"user",methodName:"searchMyTmp"},delMyUsed:{serviceName:"user",methodName:"delMyUsed"},delCollect:{serviceName:"user",methodName:"delCollect"}}};var y=a("3813"),S=function(){var t=this,e=t._self._c;return e("div",{staticClass:"dul"},[e("ul",{staticClass:"ul",class:[t.ulType]},t._l(t.typeList,(function(a,s){return e("li",{key:a.name+"_"+s,staticClass:"li",class:{active:t.selectTab==a.val},on:{click:function(e){return t.handleClick(a)}}},[e("div",{staticClass:"li__name"},[t._v(t._s(a.name))]),e("div",{staticClass:"li__line"})])})),0)])},w=[],T={name:"docer-ul",data(){return{typeList:this.list}},props:{ulType:{type:String,default:"nomal"},list:{type:Array,default:()=>[{name:"最近使用",val:"recent"},{name:"我的收藏",val:"collect"},{name:"已购资源",val:"buy"}]},selectTab:{type:String,default:""}},methods:{handleClick(t){this.$emit("change",t)}}},D=T,k=(a("7dc8"),Object(u["a"])(D,S,w,!1,null,"c56d6294",null)),$=k.exports,E=a("eff4"),L=function(){var t=this,e=t._self._c;return e("div",{staticClass:"top"},[e("div",{staticClass:"top-bar"},[e("DocerUl",{attrs:{ulType:"radio",list:t.appData,selectTab:t.mark,title:"资源格式"},on:{change:t.changeTerm}}),e("div",{class:["top-batch__"+t.mark]},[t.showBatchEdit?e("BatchEdit",{attrs:{isBatchEditing:t.isBatchEditing,selectStatus:t.selectStatus,statusCode:t.statusCode},on:{batchDelete:t.batchDelete,changeSelectStatus:t.changeSelectStatus,changeShowBatchEdit:t.changeShowBatchEdit}}):t._e()],1)],1)])},B=[],I=function(){var t=this,e=t._self._c;return e("div",{staticClass:"batch-edit"},[t.isBatchEditing?e("div",{staticClass:"batch-edit__show"},[e("div",{directives:[{name:"collect",rawName:"v-collect.click",value:{$m_n:"batch_operation",$e_n:"choose_resource",$e_t:"button"},expression:"{$m_n: 'batch_operation', $e_n: 'choose_resource', $e_t: 'button'}",modifiers:{click:!0}}],staticClass:"batch-edit__item",on:{click:t.changeSelectStatus}},[e("DcSwitch",{attrs:{isMiddle:"middle"==t.selectStatus,isSelect:"all"==t.selectStatus}}),e("span",{staticClass:"batch-edit__show--name"},[t._v("全选")])],1),e("div",{directives:[{name:"collect",rawName:"v-collect.click",value:{$m_n:"batch_operation",$e_n:"batch_delete",$e_t:"button"},expression:"{$m_n: 'batch_operation', $e_n: 'batch_delete', $e_t: 'button'}",modifiers:{click:!0}}],staticClass:"batch-edit__item batch-edit__item__delete",class:{"btn-disable":"no"==t.selectStatus},on:{click:t.batchDelete}},[e("SvgIcon",{staticClass:"batch-edit__svg",attrs:{svgName:"garbage"}}),e("span",{staticClass:"batch-edit__show--name"},[t._v("删除")])],1),e("span",{staticClass:"batch-edit__line"}),e("span",{staticClass:"batch-edit__show--name batch-edit__out",on:{click:function(e){return t.changeShowBatchEdit(!1)}}},[t._v("退出")])]):e("div",{directives:[{name:"collect",rawName:"v-collect.click",value:{$m_n:"navtool",$e_n:"batch_operation",$e_t:"button"},expression:"{$m_n: 'navtool', $e_n: 'batch_operation', $e_t: 'button'}",modifiers:{click:!0}}],staticClass:"batch-edit__hidden",class:{"btn-disable":t.statusCode},on:{click:function(e){return t.changeShowBatchEdit(!0)}}},[e("SvgIcon",{attrs:{svgName:"batch-edit"}}),e("span",{staticClass:"batch-edit__hidden--name"},[t._v("批量操作")])],1)])},x=[],N=a("f2b1"),P=a("10de"),M={name:"batchEdit",props:{isBatchEditing:{type:Boolean,default:!1},selectStatus:{type:String,default:"no"},statusCode:{type:String,default:""}},methods:{changeShowBatchEdit(t){this.$emit("changeSelectStatus","no"),this.$emit("changeShowBatchEdit",t)},changeSelectStatus(){let t="no"===this.selectStatus?"all":"no";this.$emit("changeSelectStatus",t),P["a"].$emit(g["p"].selectAllMyRecentTemplate,t)},batchDelete(){this.$emit("batchDelete")}},components:{DcSwitch:N["a"]}},U=M,O=(a("193b"),Object(u["a"])(U,I,x,!1,null,"5c3c0914",null)),A=O.exports;let j=[{val:"wps",name:"文字"},{val:"wpp",name:"演示"},{val:"et",name:"表格"}];var F={props:["mark","menu","defModeDisabled","isBatchEditing","selectStatus","statusCode","showBatchEdit"],data(){return{}},methods:{changeTerm(t){this.$emit("changeTerm",t)},changeSelectStatus(t){this.$emit("changeSelectStatus",t)},batchDelete(){this.$emit("batchDelete")},changeShowBatchEdit(t){this.$emit("changeShowBatchEdit",t)}},computed:{appData(){return"buy"==this.menu?j.slice(0,3):j}},components:{BatchEdit:A,DocerUl:$}},R=F,z=(a("a487"),Object(u["a"])(R,L,B,!1,null,"22426e6f",null)),q=z.exports,J=a("e400"),W=a("4a78");let K=_["a"].debounce(300);const G=250;var H={mixins:[J["a"],W["a"]],data(){return{statusCode:"",mark:"wps",menu:"recent",page:1,pageSize:0,data:[],getUserInfoFinish:!1,sessionId:null,maxCount:0,limit:0,isBatchEditing:!1,menuList:[{name:"最近使用",val:"recent"}],isLoading:!1}},beforeRouteEnter(t,e,a){a(e=>{e.init(t),_["a"].getUserProfessionFinish.then(()=>{e.getUserInfoFinish=!0,f["a"].$on(g["p"].logout,e.userChange),f["a"].$on(g["p"].login,e.getData),e.$nextTick(()=>{e.ajustTplLayout(),e.getData()})}),f["a"].$on(g["p"].collectChange,e.collectChange),f["a"].$on(g["p"].resizeWindow,e.ajustTplLayout),y["a"].setOriginInfo({scenesEntrance:"myfav",secondPage:"mytpl"})})},beforeRouteUpdate(t,e,a){this.init(t),_["a"].getUserProfessionFinish.then(()=>{this.ajustTplLayout(),this.getData()}),a()},beforeRouteLeave(t,e,a){f["a"].$off(g["p"].collectChange,this.collectChange),f["a"].$off(g["p"].logout,this.userChange),f["a"].$off(g["p"].login,this.getData),f["a"].$off(g["p"].resizeWindow,this.ajustTplLayout),a()},methods:{...Object(c["b"])("preview",["setAppPreview"]),init(t){this.data=[],this.menu=t.query.type||"recent",this.mark=g["e"].name||"wps",this.page=1,this.maxCount=0,this.statusCode=""},userChange(){this.mark=g["e"].name||"wps",this.page=1,this.maxCount=0,this.data=[]},getData(t=!0){this.isLogin&&(t&&(this.data=new Array(this.pageSize).fill({})),this.statusCode="",this.changeShowBatchEdit(!1),this.isLoading=!0,K(()=>{this.getMenuData(t)}))},getMenuData(t){let e={},a="";if(e={limit:this.pageSize,offset:this.pageOffset,mb_app:b[this.mark]||1,is_with_price:1},a=this.menu,"recent"==this.menu){if(this.isEnterprise)return this.getLocalRecentTpl(this.mark);e.file_type="1",e.mb_platform="8",e.is_suffix=1}e=Object.assign(e,{wps:{file_type:"1:5",mb_app:1,mb_platform:8},et:{file_type:1,mb_app:2,mb_platform:8},wpp:{file_type:1,mb_app:3,mb_platform:8,add_preview:1}}[this.mark]||{});let s=C["common"][a],i=()=>v["a"].get(s.serviceName)[s.methodName]({data:e});this.sessionId=_["a"].getRandomKey(),this.renderResult(i,this.sessionId,t)},renderResult(t,e,a){t().then(t=>{if(console.log("接口请求结果",t),this.sessionId==e){if("ok"===t.result){let e=t.data.data||t.data.list||[];this.transformData(e),a?(this.maxCount=t.data.total||t.data.total_num||0,this.data=e):this.data=this.data.concat(e),console.log(this.maxCount,"this.maxCount")}else this.maxCount=0,this.data=[];this.statusCode=this.data.length?"":g["s"].tmpEmpty}}).catch(t=>{console.log(t,"err"),this.maxCount=0,this.data=[],this.statusCode=this.data.length?"":g["s"].error}).finally(()=>{this.isLoading=!1})},getLocalRecentTpl(t){const e=()=>{this.maxCount=this.data?this.data.length:0,this.statusCode=this.data.length?"":g["s"].tmpEmpty};this._getLocalRecentTpl(t).then(t=>{t&&Array.isArray(t)&&t.length>0?this.data=this._formartPrevieImage(t):this.data=[],e(),console.log("recentlyData:"+JSON.stringify(t))}).catch(t=>{this.data=[],e(),console.error("获取最近使用模板失败",t)}).finally(()=>{this.isLoading=!1})},transformData(t){return t.forEach(t=>{t.selected=!1}),t},loadMore(){console.log("搜索页的loadMore"),this.isInterfaceLoading||(this.page++,this.getData(!1))},changeMenu(t){this.menu=t.val,this.page=1,this.data=[],this.statusCode="",this.getData(),this.maxCount=0,o["a"].send("click",{$m_n:"category_area",$e_n:"first_cat",$e_t:"tag",$e_i:10816,first_cat_name:t.name})},collectChange(){console.log("collectChange"),"collect"==this.menu&&(this.page=1,this.data=[],this.maxCount=0,this.getData())},recentChange(){"recent"==this.menu&&(this.page=1,this.data=[],this.maxCount=0,this.getData())},ajustTplLayout(){let[t,e]=_["a"].getScreenDisplay(this.$refs.rMainContentCon,this.mark);console.log(t,e),this.limit!=e&&(this.limit=e),this.pageSize||(this.pageSize=t*e||30)},changeTerm(t){this.changeMark(t.val),this.page=1,this.word="",this.maxCount=0,this.statusCode="",this.ajustTplLayout(),this.getData(),o["a"].send("click",{$m_n:"category_area",$e_n:"second_cat",$e_t:"tag",$e_i:10818,second_cat_name:t.name})},changeMark(t="wps"){this.mark=t},changeShowBatchEdit(t){this.isBatchEditing=t,this.changeSelectStatus("no")},handlerSelectAll(t,e=!1){this.data=t.map(t=>(t.selected=e,t))},batchDelete(t){let e,a=this.data[t];if(this.isEnterprise)return a?e=[a.filePath]:(e=[],this.data.forEach(t=>{t.selected&&e.push(t.filePath)})),void this._removeRecentTemplate(e).then(()=>{this.getData()});if(a&&(e=a.id,o["a"].send("click",{...this.commonCollectParams,$e_i:10823,$e_n:"delete",$e_t:"button",$e_p:t+1})),this.selectedList.length>G)return void this.toast({type:"error",content:`单次最多支持删除${G}个`});let s="recent"===this.menu,i=s?"delMyUsed":"delCollect",n={};e=e||this.selectedList.join(":"),n={data:{mb_ids:e}},v["a"].get("user")[i](n).then(t=>{t&&"ok"==t.result&&(this.page>1&&e&&e.toString().split(["common","library"].includes(this.serviceType)?":":",").length>=this.data.length&&(this.page-=1),this.getData(),s?this.recentChange():this.collectChange())})},changeSelectStatus(t){this.handlerSelectAll(this.data,"all"===t)},onClickDelete(t){let e=this.getItemIndex(t);this.batchDelete(e)},onClickDeleteFav(t){let e=this.getItemIndex(t);this.batchDelete(e)},onChangeItemSelectStatus(t){let e=this.getItemIndex(t);if(e>=0){const a=this.data[e];a.selected=!t.selected,this.$set(this.data,e,a)}},getItemIndex(t){return this.isEnterprise?this.data.findIndex(e=>e.filePath===t.filePath):this.data.findIndex(e=>e.id===t.id)}},computed:{...Object(c["c"])("user",["isLogin","userInfo","isClientLogin"]),...Object(c["c"])("common",["isEnterprise"]),itemWidth(){return g["f"][this.appName||"wps"]},isCommonType(){return["wps","et","wpp","pdf"].includes(this.mark)},totalPage(){return Math.ceil(this.maxCount/this.pageSize)},pageOffset(){return this.data.filter(t=>!!t.id).length},selectedList(){let t=[];return this.data.forEach(e=>{e.selected&&t.push(e.id)}),t},selectStatus(){return this.selectedList.length?this.selectedList.length==this.data.length?"all":"middle":"no"},showDeleteBtn(){return"recent"===this.menu&&!this.isBatchEditing},showBatchEdit(){return!this.isLoading&&("buy"!==this.menu&&this.data.length)},commonCollectParams(){return{$m_n:"resource_list",$e_i:{display:10821,click:10822},first_cat_name:{recent:"最近使用",collect:"我的收藏",buy:"已购资源"}[this.menu],second_cat_name:{wps:"文字",wpp:"演示",et:"表格"}[this.mark],$track:""}}},components:{NotLogin:d,DataStatus:p["a"],DocerUl:$,List:E["a"],MainTopBar:q}},Q=H,V=(a("55d6"),Object(u["a"])(Q,s,i,!1,null,"929a3efe",null));e["default"]=V.exports},caf9:function(t,e,a){"use strict";a("1306")},db6c:function(t,e,a){t.exports=a.p+"images/et.png"},e485:function(t,e,a){}}]);