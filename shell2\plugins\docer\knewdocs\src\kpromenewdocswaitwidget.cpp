﻿#include "stdafx.h"
#include "kpromenewdocswaitwidget.h"
#include <kliteui/klitestyle.h>
#include <public_header/kliteui/uicontrol/klitelabel.h>
#include <public_header/kliteui/koperatorlib.h>
#include <public_header/kliteui/uicontrol/klitebutton.h>
#include <ksolite/kdocercoreitef.h>
#include <public_header/kliteui/uicontrol/kliteprogresswidget.h>
#include "kdocercorelitehelper.h"
namespace {
	const int g_fpcLoadTimeMoudleId = 50824;
	const int g_defaultExpirationTime = 2 * 10 * 1000;
}

KPromeNewdocsLoadingWidget::KPromeNewdocsLoadingWidget(bool bSupport, QWidget* parent) :
	KLiteWidget(parent)
	, m_bSupportDefaultPage(bSupport)
	, m_msTimeout(0)
{
	if (m_bSupportDefaultPage)
		initTimeout();

	initUI();
	initTimer();
}

void KPromeNewdocsLoadingWidget::onStopLoading()
{
	m_timer->stop();
}

void KPromeNewdocsLoadingWidget::onStartLoading()
{
	m_timer->start();
}

void KPromeNewdocsLoadingWidget::initUI()
{
	if (nullptr != m_vb)
		return;
	m_progressBar = new KLiteProgressRingGroup(this, KLiteProgressGroup::Vertical);
	m_progressBar->setSpacing(KLiteStyle::dpiScaled(20));
	m_progressBar->setText(tr("loading"));
	auto ringWidget = qobject_cast<KLiteProgressRing*>(m_progressBar->subControl(KLiteProgressRingGroup::ProgressRing));
	if (ringWidget)
	{
		ringWidget->setBorderWidth(KLiteStyle::dpiScaled(6));
		ringWidget->setRadius(KLiteStyle::dpiScaled(24));
	}
	m_progressBar->setFixedSize(KLiteStyle::dpiScaledSize(105, 88));
	m_vb = new QVBoxLayout(this);
	m_vb->setContentsMargins(KLiteStyle::dpiScaledMargins(0, 0, 0, 0));
	m_vb->addWidget(m_progressBar, 0, Qt::AlignCenter);
}

void KPromeNewdocsLoadingWidget::initTimer()
{
	m_timer = new QTimer(this);
	m_timer->setSingleShot(true);
	m_timer->setInterval(m_msTimeout);

	connect(m_timer, &QTimer::timeout, this, [=]() {
		if (!m_bSupportDefaultPage)
			return;
		auto pWidget = KTik::findParentByType<KPromeNewdocsWaitWidget>(parent());
		if (pWidget)
			emit pWidget->sigLoadTimeout();
		
	});
	onStartLoading();
}

void KPromeNewdocsLoadingWidget::initTimeout()
{
	static int fpcTime = [=]()->int {
		auto pDocerLite = getIKDocerCoreLite();

		if (!pDocerLite || !pDocerLite->isFpcInitFinished())
			return g_defaultExpirationTime;

		QJsonObject timeCfg = pDocerLite->getFpcCombJsonObject(g_fpcLoadTimeMoudleId);
		if (!timeCfg.contains("ExpirationTime"))
			return g_defaultExpirationTime;

		int time = timeCfg.value("ExpirationTime").toInt(g_defaultExpirationTime);
		return time > 0 ? time : g_defaultExpirationTime;
	}();
	m_msTimeout = fpcTime;
}

KPromeNewdocsWaitWidget::KPromeNewdocsWaitWidget(bool bComponentType, QWidget* parent)
	:KLiteWidget(parent)
	,m_bComponentType(bComponentType)
{
	initUI();
}

void KPromeNewdocsWaitWidget::onPluginLoadFailed()
{
	if (nullptr == m_stackWidget || 
		nullptr == m_vbLayout || 
		nullptr == m_loadErrorWidget || 
		nullptr == m_loadingWidget)
		return;

	m_loadingWidget->onStopLoading();
	m_stackWidget->setCurrentWidget(m_loadErrorWidget);
}

void KPromeNewdocsWaitWidget::onWebLoadfinished()
{
	m_loadingWidget->onStopLoading();
}

void KPromeNewdocsWaitWidget::initUI()
{
	if (nullptr != m_vbLayout)
		return;

	applyThemeClass("KNewDocs-WaitWidget");
	setMinimumSize(KLiteStyle::dpiScaledSize(80, 80));
	m_vbLayout = new QVBoxLayout(this);
	m_vbLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(0, 0, 0, 0));
	m_vbLayout->setSpacing(KLiteStyle::dpiScaled(0));

	m_stackWidget = new QStackedWidget(this);
	m_loadingWidget = new KPromeNewdocsLoadingWidget(m_bComponentType, this);
	m_loadErrorWidget = new KPromeNewdocsErrorWidget(this);

	m_stackWidget->addWidget(m_loadingWidget);
	m_stackWidget->addWidget(m_loadErrorWidget);
	m_stackWidget->setCurrentWidget(m_loadingWidget);
	m_vbLayout->addWidget(m_stackWidget);

	connect(m_loadErrorWidget, &KPromeNewdocsErrorWidget::sigLoadRetry, this, [=]() 
		{
			if (nullptr == m_stackWidget ||
				nullptr == m_vbLayout ||
				nullptr == m_loadingWidget)
				return;
			m_stackWidget->setCurrentWidget(m_loadingWidget);
			m_loadingWidget->onStartLoading();
			emit sigLoadRetry();
		}
	);

}

void KPromeNewdocsWaitWidget::paintEvent(QPaintEvent* event)
{
    if (qApp && qApp->usingGlassEffect())
    {
        QPainter p(this);
        p.setCompositionMode(QPainter::CompositionMode_Clear);
        p.fillRect(rect(), Qt::transparent);
        return ;
    }
    QWidget::paintEvent(event);
}

KPromeNewdocsErrorWidget::KPromeNewdocsErrorWidget(QWidget* parent)
	:KLiteWidget(parent)
{
	initUI();
}

void KPromeNewdocsErrorWidget::initUI()
{
	if (nullptr != m_vb)
		return;

	m_vb = new QVBoxLayout(this);
	m_vb->setContentsMargins(KLiteStyle::dpiScaledMargins(0, 11, 0, 11));
	m_errorIconLbl = new KLiteLabel(this);

	QSize sz = KLiteStyle::dpiScaledSize(100, 100);
	QPixmap errorIcon = KDrawHelper::loadIcon("placeholder_nointernet_100").pixmap(sz);
	m_errorIconLbl->setPixmap(errorIcon);
	m_errorIconLbl->setScaledContents(true);
	m_errorIconLbl->setFixedSize(KLiteStyle::dpiScaledSize(100, 100));

	m_textLabel = new KLiteLabel(this);
	m_textLabel->applyThemeClass("KNewDocs-ErrorText");
	m_textLabel->setFixedSize(KLiteStyle::dpiScaledSize(240, 22));

	m_textLabel->setText(tr("failed to load"));
	m_textLabel->setAlignment(Qt::AlignHCenter);

	m_liteBtn = new KLiteButton(this);
	m_liteBtn->applyThemeClass("KLiteButton_Text_Assist");
	m_liteBtn->setFixedSize(KLiteStyle::dpiScaledSize(72, 32));
	m_liteBtn->setText(tr("try again"));

	m_vb->addStretch();
	m_vb->setSpacing(KLiteStyle::dpiScaled(16));
	m_vb->addWidget(m_errorIconLbl, 0, Qt::AlignHCenter);
	m_vb->addWidget(m_textLabel, 1, Qt::AlignHCenter);
	m_vb->addWidget(m_liteBtn, 2, Qt::AlignHCenter);
	m_vb->addStretch();

	connect(m_liteBtn, &KLiteButton::clicked, this, [=]() {
		emit sigLoadRetry();
		}, Qt::UniqueConnection);
}

