﻿#include "stdafx.h"
#include "kxpdfnewdochelper.h"
#include <kprometheus/kpromeapplication.h>
#include <kprometheus/kpromestyle.h>
#include <kprometheus/kpromeinfocollcethelper.h>

class FadeQSpacerItem : public QLayoutItem
{
public:
	int width;
	int height;
	QSizePolicy sizeP;
	QRect rect;
};

QString KxPdfNewDocHelper::toHdpiStyle(const QString& style)
{
	if (style.isEmpty())
	{
		return style;
	}
	qreal scale = KPromeStyle::dpi();
	if (scale < 1.2)
	{
		return style;
	}

	QString tempStyle = style;
	QRegExp rx("\\d+px", Qt::CaseInsensitive);
	rx.setMinimal(true);
	int index = -1;
	while ((index = rx.indexIn(tempStyle, index + 1)) >= 0)
	{
		int capLen = rx.cap(0).length() - 2;
		QString snum = tempStyle.mid(index, capLen);
		snum = QString::number(qRound(snum.toInt() * scale));
		tempStyle.replace(index, capLen, snum);
		index += snum.length();
		if (index > tempStyle.size() - 2)
		{
			break;
		}
	}

	return tempStyle;
}

void KxPdfNewDocHelper::fitHdpiWidget(QWidget *widget)
{
	qreal scale = KPromeStyle::dpi();
	if (scale < 1.2f)
		return;

	if (widget)
	{

		QWidget* parentWid = widget->parentWidget();
		if (widget->inherits("QLineEdit") && parentWid && (parentWid->inherits("QAbstractSpinBox") || parentWid->inherits("QComboBox")))
		{
			return;
		}

		QSizePolicy sizePolicy = widget->sizePolicy();
		QSizePolicy::Policy horizontalPolicy = sizePolicy.horizontalPolicy();
		QSizePolicy::Policy verticalPolicy = sizePolicy.verticalPolicy();

		QRect geometry = widget->geometry();
		int fixW = geometry.width();
		int fixH = geometry.height();

		if (widget->inherits("KToolButton"))
		{
			if (widget->inherits("KMetroPushButton"))
			{
				if (horizontalPolicy != QSizePolicy::Expanding)
				{
					fixW = qRound(geometry.width() * scale);
					widget->setFixedWidth(fixW);
				}

				if (verticalPolicy != QSizePolicy::Expanding)
				{
					fixH = qRound(geometry.height() * scale);
					widget->setFixedHeight(fixH);
				}
			}
		}
		else
		{
			if (horizontalPolicy != QSizePolicy::Expanding && horizontalPolicy != QSizePolicy::Preferred)
			{
				fixW = qRound(geometry.width() * scale);
				widget->setFixedWidth(fixW);
			}

			if (verticalPolicy != QSizePolicy::Expanding && verticalPolicy != QSizePolicy::Preferred)
			{
				fixH = qRound(geometry.height() * scale);
				widget->setFixedHeight(fixH);
			}
		}

		if (!widget->inherits("QDialog"))
		{
			widget->setGeometry(QRect(scale * geometry.topLeft(), QSize(fixW, fixH)));
		}
		QAbstractButton* button = qobject_cast<QAbstractButton*>(widget);
		if (button && !widget->inherits("KxPdfIconButton"))
		{
			button->setIconSize(button->iconSize() * scale);
		}

		QSize maxSize = widget->maximumSize();
		if (maxSize.width() < fixW || maxSize.height() < fixH)
		{
			widget->setMaximumSize(fixW, fixH);
		}

		fitHdpiLayout(widget->layout());
	}
}

void KxPdfNewDocHelper::fitHdpiLayout(QLayout *layout)
{
	if (!layout)
		return;
	qreal scale = KPromeStyle::dpi();
	if (scale < 1.2f)
		return;

	auto margin = layout->contentsMargins();
	layout->setContentsMargins(qRound(margin.left() * scale), qRound(margin.top() * scale), qRound(margin.right() * scale), qRound(margin.bottom() * scale));
	layout->setSpacing(qRound(layout->spacing() * scale));

	int itemCount = layout->count();
	for (int index = 0; index < itemCount; ++index)
	{
		auto item = layout->itemAt(index);
		if (auto spacerItem = item->spacerItem())
		{
			QSize size = spacerItem->sizeHint();
#if QT_VERSION >= 0x050000
			QSizePolicy sizeP = spacerItem->sizePolicy();
#else
			auto fakeSpacerItem = reinterpret_cast<_FakeQSpacerItem*>(spacerItem);
			QSizePolicy sizeP = fakeSpacerItem->sizeP;
#endif
			// 仅处理这两种情况
			if (layout->inherits("QHBoxLayout") || layout->inherits("QVBoxLayout") || layout->inherits("QGridLayout"))
			{
				spacerItem->changeSize(qRound(size.width() * scale), qRound(size.height() * scale), sizeP.horizontalPolicy(), sizeP.verticalPolicy());
			}
		}
		else if (auto widgetItem = item->widget())
		{
			fitHdpiWidget(widgetItem);
		}
		else if (auto layoutItem = item->layout())
		{
			fitHdpiLayout(layoutItem);
		}
	}
}

void KxPdfNewDocHelper::CreateDcInfo(const QString &eventName, const QString &key, const QString &value)
{
	promeApp->getCloudCollectInfoMgr()->SendPdfGeneralEvent(eventName, key, value);
}

void KxPdfNewDocHelper::CreatePDFDcInfo(const QString &eventName, 
										QHash<QString, QString> &customArgs, quint32 retryCount, quint32 timeout)
{
	promeApp->getCloudCollectInfoMgr()->sendPdfCollectInfo(eventName, customArgs, retryCount, timeout);
}
