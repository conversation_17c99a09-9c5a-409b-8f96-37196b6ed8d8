﻿#ifndef __PLUGINS_KNEWDOCS_STDAFX_H__
#define __PLUGINS_KNEWDOCS_STDAFX_H__

#define WIN32_LEAN_AND_MEAN

#include <QtCore>
#include <QtGui>
#include <QtWidgets/QtWidgets>
#include <krt/krt.h>
#include <kfc.h>
#include <kfc/variant.h>
#include <ksolite/ksolite.h>
#include <kxshare/kxshare.h>
#include <krt/krt.h>
#include "ksolite/kappmgr.h"
#include <kso/shell.h>
#include <kso/shell/shell_tlb_old.h>
#include <kxshare/kxshare.h>
#undef DllMain
#include <algorithm>
#ifndef SIMPIMPL
#define SIMPIMPL															\
	{ return S_OK; }
#endif

#ifndef NOTIMPL
#define NOTIMPL																\
	{ REPORT_ONCEA("Not Implement!!!"); return E_NOTIMPL; }
#endif

#undef K_ASSERT
#define K_ASSERT Q_ASSERT
#undef ASSERT
#define ASSERT Q_ASSERT

#ifdef Q_OS_UNIX
typedef long WPARAM;
typedef long LPARAM;
typedef long LRESULT;
typedef void* HWND;
typedef int32_t HRESULT;
typedef int BOOL;
typedef unsigned int  UINT;
typedef uint16_t WORD;
typedef uint32_t DWORD;
typedef char16_t WCHAR;
typedef const WCHAR* LPCWSTR;
typedef unsigned char BYTE;
typedef void* HANDLE;
#define S_OK ((HRESULT)0x00000000L)
#define S_FALSE ((HRESULT)0x00000001L)
#define QT_NO_NETWORKINTERFACE
#endif

#include <kcomctl/tik.h>
#include "knewdochelper.h"
#include <public_header/kliteui/kdrawhelper.h>
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include "utility/knewdocshelper.h"
#include <kprometheus/kpromeskin.h>
#include <kprometheus/kpromeapplication.h>
#include <kprometheus/kpromecloudsvrproxy.h>

#define KPluginName "knewdocs"
#define KPluginNameW L"knewdocs"
#define STR_KNEWDOC_CLICKEVENTNAME "docer_knewdocx_click"
#define PROME_NEW_DOCS_TABBAR_NAME "KNewDocs-Tabbar"

#include "ksolite/webapi/ksojscorelite.h"
#include "api/jsapi/ksojscore/ksojscore_h.h"
#include "api/jsapi/jsapiservice/jsapiservice_h.h"
#include "api/jsapi/jsapiservice/jsribbon.h"
#include "api/jsapi/ipc/ipc_event.h"
#include "ksolite/webapi/wpsextention.h"
#include "gen-cpp/kdcservices_types.h"
#endif // __PLUGINS_KNEWDOCS_STDAFX_H__
