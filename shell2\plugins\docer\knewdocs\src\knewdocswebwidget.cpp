﻿#include "stdafx.h"
#include "knewdocswebwidget.h"
#include "src/kpromenewdocsstartup.h"
#include "src/knewdocjsapi.h"
#include "src/kpromenewdocspage.h"
#include "ksolite/kxjsonhelper.h"
#include "ksolite/jsapiutilities/util/kjsapiutil.h"
#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"
#include "src/tabbar/kpromenewdocstabbarconfig.h"
#include "kdocerwebpageperfhelper.h"
#include "ksolite/webapi/kxjsapibrowserwidget_p.h" 

#include "api/jsapi/jsonhelp.h"
#include <kprometheus/kpromestartupcounter.h>
#include <kprometheus/kpromecentralarea.h>
#include <kprometheus/kpromemainwindow.h>
#include <thread>
#include <string>
#if defined(Q_OS_WIN)
#include "ksolite/ksoprereadimages.h"
#endif // end if defined(Q_OS_WIN)
#include "utilities/path/module/kcurrentmod.h"
#include "kappesstoolbox/toolboxproxy.h"
#include "ksolite/ksupportutil.h"
#include "kprometheus/kprometab.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "krt/kconfigmanagercenter.h"

#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"

namespace
{
	constexpr const char* g_unknown = "unknown";
	constexpr const char* g_undefined = "undefined";

	QString getAccountType()
	{
		if (!kcoreApp || !kcoreApp->getAccountSDK())
			return g_unknown;

		auto account = kcoreApp->getAccountSDK()->getAccount();
		if (!account)
			return g_unknown;

		kacctoutsdk::AccountType accountType = account->getAccountType();
		if (kacctoutsdk::AccountType::Company == accountType)
			return "company";
		else if (kacctoutsdk::AccountType::Personal == accountType)
			return "personal";
		else if (kacctoutsdk::AccountType::Combination == accountType)
			return "combination";

		return g_unknown;
	}
}
////////////////////////////////////////////////////////////////////////////////////////////////////

KNewDocsWebWidget::KNewDocsWebWidget(int tabType, KPromeNewDocsStartup* parent)
	: KDocerCommonWebWidget(parent, "knewdocs")
	, m_startup(parent)
	, m_tabType(tabType)
{
	KNewDocsHelper::newCloudDocSwitchRegisterNotifyObj(this, QLatin1String("onNewCloudDocSwitchStatus"));
}

KNewDocsWebWidget::~KNewDocsWebWidget()
{
	KNewDocsHelper::newCloudDocSwitchUnregisterNotifyObj(this);
}

KxWebViewImplementationType KNewDocsWebWidget::webviewType()
{
	return KXWEBVIEW_IMPL_TYPE_CEF;
}

ksolite::KxCommonJsApi* KNewDocsWebWidget::initJsObject()
{
	QObject* jsapi = nullptr;
	if (m_tabType == KPromePluginWidget::pageDocerPdf)
		m_startup->onCreatingPdfApi(getWebview(), &jsapi);
	else
		m_startup->onCreatingApi(getWebview(), &jsapi);
	connect(jsapi, SIGNAL(webScriptReady()), this, SLOT(onScriptReady()));
	connect(jsapi, SIGNAL(jsScriptReady()), this, SLOT(onJsScriptReady()));
	return qobject_cast<ksolite::KxCommonJsApi*>(jsapi);
}

void KNewDocsWebWidget::onBrowserCreated()
{
	if (!getWebview() || !getWebview()->getWebView())
		return;
	connect(this, SIGNAL(cefKeyEvent(Qt::KeyboardModifiers, int, bool, bool&)),
		m_startup, SIGNAL(cefKeyEvent(Qt::KeyboardModifiers, int, bool, bool&)),
		Qt::UniqueConnection);

	if (KNewDocsHelper::isSupportDefaultPage(m_tabType))
	{
		connect(getWebview()->getWebView(), SIGNAL(mainResourceVerificationFailed(const QString&)), this, SIGNAL(mainResourceVerificationFailed(const QString&)));
		connect(getWebview()->getWebView(), SIGNAL(renderProcAbnormalTerminate(int)), this, SLOT(onRenderProcAbnormalTerminate(int)));
	}

	connect(promeApp->cloudSvrProxy(), SIGNAL(loginStatusChanged()), this, SLOT(onInjectInfo()), Qt::QueuedConnection);
	
	KDocerCommonWebWidget::onBrowserCreated();
}

void KNewDocsWebWidget::onInjectInfo()
{
	if (nullptr == getWebview())
		return;

	QString script;
	IKDocerAccount* docerAccount = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		docerAccount = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;
	if (docerAccount)
		isLogined = docerAccount->isLogined();
	if (isLogined)
	{
		auto accountSdk = kcoreApp->getAccountSDK();
		if (nullptr == accountSdk)
			return;
		auto account = accountSdk->getAccount();
		if (nullptr == account)
			return;
		auto obj = qobject_cast<kacctoutsdk::KAccountSdkProxy*>(sender());
		if (nullptr != obj)
			disconnect(accountSdk, &kacctoutsdk::KAccountSdkProxy::accountInfoReady,
				this, &KNewDocsWebWidget::onInjectInfo);

		if (!account->isReady())
		{
			connect(accountSdk, &kacctoutsdk::KAccountSdkProxy::accountInfoReady,
				this, &KNewDocsWebWidget::onInjectInfo, Qt::UniqueConnection);
			return;
		}
		if(docerAccount && docerAccount->isCompanyAccount())
			script = QString("window.__KNEWDOCS__.companyUserCategory = '%1'").arg(KNewDocsHelper::getCompanyUserCategory());
	}
	else
	{
		script = QString("window.__KNEWDOCS__.companyUserCategory = %1").arg(g_undefined);
	}

	getWebview()->executeJavaScript(script);
}

void KNewDocsWebWidget::onBrowserException(qint32 value)
{
	onRenderProcAbnormalTerminate(value);
	KDocerCommonWebWidget::onBrowserException(value);
}

void KNewDocsWebWidget::onWebViewPopupWindow(const KxWebViewClickedContext& context)
{
	if (!context.url.isValid() || context.url.host().isEmpty())
		return;

	if (ksolite::isSupported(ksolite::IntranetVersion7))
	{
		openPromeBrowser(context.url.toString());
	}
}

void KNewDocsWebWidget::onLoadedFinished(bool bOk)
{
	QString errMsg = m_webView ? m_webView->getCurrentErrorMessage() : QString();
	KxLoggerLite::writeInfo(KPluginNameW, QString(
		"onLoadedFinished status: %1, message: %2").arg(bOk).arg(errMsg).toStdWString());
	m_profiler.cefLoadDone();
	if (!bOk)
		m_profiler.webLoadFailed(errMsg);

	if (ksolite::isSupported(ksolite::IntranetVersion7))
	{
		showWebWidget();
	}

	if (bOk &&(m_bLoadErrorPage || !KPromeNewDocsTabBarConfig::getInstance()->getInfoObj(m_tabType)->waitJs()))
	{
		showWebWidget();

		// 非四组件加载错误，不进行前端回调和埋点上报
		if (KNewDocsHelper::isSupportDefaultPage(m_tabType))
		{
			errorPageCallback();
			postLoadEvent();
		}
		return;
	}
	if (!bOk && m_webView)
	{

		if (KNewDocsHelper::isSupportDefaultPage(m_tabType) &&
			!QFileInfo(QUrl(mainUrl()).toLocalFile()).exists())
		{
			m_loadErrorCode = KNewdocsloadErrorCode::MainResourceMiss;
			KxLoggerLite::writeInfo(KPluginNameW,
				QString("【description】:%1 does not exist!,【solution】：Load default errorPage!")
				.arg(mainUrl())
				.toStdWString());
		}

		reportSRELoadPageFailed();
		if (m_bLoadErrorPage)
		{
			KxLoggerLite::writeInfo(L"KNewDocsWebWidget", L"load error page failed.");
			return;
		}
		loadErrorPage();
		m_bLoadErrorPage = true;
#ifdef Q_OS_DARWIN
		// ones:#1092462
		// Mac新建错误页第二次不会再次进入onLoadedFinished，基建通知似乎被做了防抖处理
		// 临时处理：直接通知错误页变更文案
		if (KNewDocsHelper::isSupportDefaultPage(m_tabType))
		{
			errorPageCallback();
			postLoadEvent();
		}
#endif //!Q_OS_DARWIN
	}

	prepareComponent();
	sendDocCreateCloudSwitchInfo();
}

void KNewDocsWebWidget::prepareComponent()
{
#	if defined(Q_OS_WIN)
	/*
	* 新建页加载完成，若
	* 新建的文档是四组件之一则预读它们。
	*/
	LPCWSTR appName = NULL;
	switch (m_tabType)
	{
	case KPromePluginWidget::pageDocerWps:
		appName = L"wps";
		break;
	case KPromePluginWidget::pageDocerEt:
		appName = L"et";
		break;
	case KPromePluginWidget::pageDocerWpp:
		appName = L"wpp";
		break;
	case KPromePluginWidget::pageDocerPdf:
		appName = L"pdf";
		break;
	}

	if (appName)
	{
		std::thread([appName]()
		{
			const std::wstring office6(
				QString(krt::dirs::officeInstallRoot() +
					QDir::separator() + "office6").toStdWString());

			KSOLITE_USE_NAMESPACE

			KxLoggerLite::writeInfo(L"KPromeNewDocsPreread", 
				L"new template page is displayed. begin preread component.");

			PrereadComponent(appName, TRUE, office6.c_str(),
				RES_MGR_REG_PATH);

			PrereadComponent(appName, FALSE, office6.c_str(),
				RES_MGR_REG_PATH);

			KxLoggerLite::writeInfo(L"KPromeNewDocsPreread",
				L"preread component complete.");
		}).detach();
	}
#	endif // end if defined(Q_OS_WIN)
}

QUrl KNewDocsWebWidget::getErrorPage()
{
	// 加载错误页面的时候取消签名失败的信号槽，避免通用错误页无签名一直重载页面
	if (getWebview() && getWebview()->getWebView())
	{
		disconnect(getWebview()->getWebView(), 
			SIGNAL(mainResourceVerificationFailed(const QString&)), 
			this, SIGNAL(mainResourceVerificationFailed(const QString&)));
	}

	if (KNewDocsHelper::isSupportDefaultPage(m_tabType)
		&& QFileInfo(QUrl(KNewDocsHelper::componentErrorPage()).toLocalFile()).exists())
	{
		QUrl url = KNewDocsHelper::componentErrorPage();
		QUrlQuery query;
		query.addQueryItem("app", KPromeNewDocsTabInfoObj::convertTabIntType(m_tabType));
		url.setQuery(query);
		return url;
	}

	return  KDocerCommonWebWidget::getErrorPage();
}

bool KNewDocsWebWidget::openPromeBrowser(const QString& url)
{
	if (!promeApp)
		return false;

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return false;

	KPromeCentralArea* contentArea = mw->centralArea();
	if (contentArea)
	{
		contentArea->addBrowserPage(url, false);
		return true;
	}
	return false;
}

// 此处需要特殊处理一下金山文档的cookie缺失
void KNewDocsWebWidget::addSpecialCookies(const QString& domainName, const QVariantMap& varintMap)
{
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;

	if (account)
		isLogined = account->isLogined();

	if (domainName.isEmpty() ||
		!isLogined ||
		!m_webView)
		return;

	QString domainUrl = KDocerUtils::getDomainUrl(domainName);
	if (domainUrl.isEmpty())
		return;

	QString domain = KDocerUtils::getDomainFormUrl(domainUrl);
	if (domain.isEmpty())
		return;

	for (auto it = varintMap.constBegin(); it != varintMap.constEnd(); ++it)
	{
		QString cookieName = it.key();
		if(cookieName.isEmpty())
			continue;
		QString cookieValue = it.value().toString();
		if(cookieValue.isEmpty())
			continue;
		
		QNetworkCookie cookie(cookieName.toUtf8(), cookieValue.toUtf8());
		cookie.setExpirationDate(QDateTime::currentDateTime().addDays(7));
		cookie.setDomain(domain);
		cookie.setPath("/");
		cookie.setHttpOnly(true);
		cookie.setSecure(true);
		m_webView->deleteCookie(domainUrl, cookieName, nullptr);
		m_webView->setCookie(domainUrl, cookie, nullptr);
	}
}

void KNewDocsWebWidget::sendDocCreateCloudSwitchInfo()
{
	if (m_appId == "knewdocs_pageDocerPdf")
		return;

	if (!promeApp || !promeApp->cloudService() || !promeApp->getDcInfo())
		return;

	QString switchStatus;
	IKNewCloudDocSwitch* newCloudDocSwitch = promeApp->cloudService()->getNewCloudDocSwitch();
	if (!newCloudDocSwitch || !newCloudDocSwitch->isNewCloudDocSwitchEnabled())
		return;
	
	switchStatus = newCloudDocSwitch->isNewCloudDocSwitchOpened() ? "open" : "close";

	QString comp;
	if (m_appId == "knewdocs_pageDocerWps")
		comp = "wps";
	else if (m_appId == "knewdocs_pageDocerWpp")
		comp = "wpp";
	else if (m_appId == "knewdocs_pageDocerEt")
		comp = "et";

	QHash<QString, QString> args;
	args.insert("motion", "show");
	args.insert("status", switchStatus);
	args.insert("source", "create_new");
	args.insert("identity", getAccountType());
	args.insert("comp", comp);
	KDocerUtils::postGeneralEvent(QLatin1String("doc_create_cloud_switch"), args);
}

void KNewDocsWebWidget::errorPageCallback()
{
	QVariantMap paramsMap;
	paramsMap.insert("errorCode", (int)m_loadErrorCode);
	paramsMap.insert("msg", convertLoadErrorCode2String(m_loadErrorCode));
	callbackToJS("onKNewdocsLoadDefaultErrorPage", JsonHelper::convertVariantToString(paramsMap));
}

void KNewDocsWebWidget::postLoadEvent()
{
	auto page = KTik::findParentByType<KPromeNewDocsPage>(parent());
	if (!page)
		return;
	page->postKNewdocsLoadEvent(m_loadErrorCode);
}

void KNewDocsWebWidget::reportSRELoadPageFailed()
{
	SREMonitorInfo info;
	info.m_fnCategory = QLatin1String("sre_app_knewdocs_load_errorpage");
	QString pageType = KPromeNewDocsTabInfoObj::convertTabIntType(m_tabType);//组件页名称
	info.m_fnName = pageType;
	info.m_params = QMap<QString, QString>
	{
		{QLatin1String("page_type"), pageType},
		{QLatin1String("plugin_version"), docer::base::KCurrentModule::getFileVersion()},
		{QLatin1String("main_url"), mainUrl()},
		{QLatin1String("load_error_code"), QString::number((int)m_loadErrorCode)},
		{QLatin1String("load_error_reason"), convertLoadErrorCode2String(m_loadErrorCode)},
		{QLatin1String("load_duration"), QString::number(QDateTime::currentMSecsSinceEpoch() - m_startTimeStamp)}
	};
	SREMonitorPost(info);
}

void KNewDocsWebWidget::updateAccessControlAllowAllOrigin(const QString strUrl)
{
	//允许开启跨域的scheme白名单列表
	static QStringList s_allowSchemes =
	{
		"file"
	};

	QString strScheme = QUrl(strUrl).scheme();

	if (getWebview())
		getWebview()->setAccessControlAllowAllOrigin(s_allowSchemes.contains(strScheme));
}

void KNewDocsWebWidget::showWebWidget()
{
	KDocerCommonWebWidget::showWebWidget();
	qint64 timeStampe = QDateTime::currentMSecsSinceEpoch();
	auto totalCost = timeStampe - m_startTimeStamp;
	
	static bool s_bHasRecordTime = false;
	if (!s_bHasRecordTime)
	{
		KPromeStartupCounter::instance().recordNewDocsPageAppName(m_appId);
		KPromeStartupCounter::instance().recordNewDocsPageTimeCost(totalCost);
	}
	s_bHasRecordTime = true;
	if (krt::kcmc::support("TemplateLibFixedTab")
	&& this->property("tab_type").isValid()
	&& this->property("tab_type").toString() == "templateLib_fixed_tab")
	{
		resetTemplateLibTabTitle();
	}
}

void KNewDocsWebWidget::onScriptReady()
{
	showWebWidget();
	postLoadEvent();
}

void KNewDocsWebWidget::updateCookies()
{
	addCookies("home");
	addCookies("kdocs");
	addSpecialCookies("kdocs", QVariantMap{ {"wps_endcloud","1"} });
}

void KNewDocsWebWidget::initWebView()
{
	if (!getWebview())
		return;

#ifdef Q_OS_DARWIN
	getWebview()->setBrowserNativeEmbed(true, false);
	if (KCocoaCommon::applicationIsInDarkMode())
	{
		QString backgroudColor = KDrawHelper::getColorFromTheme("KNewDocsWebWidget",  KDrawHelper::Prop_Background).name();
		getWebview()->setDrawBackgroundColor(backgroudColor);
	}
#endif
	updateAccessControlAllowAllOrigin(mainUrl());
	getWebview()->setPopupPolicy(KxWebViewPopupPolicy(true, this, false));
	connect(getWebview(), SIGNAL(cefPopupWindow(const KxWebViewClickedContext&)),
		this, SLOT(onWebViewPopupWindow(const KxWebViewClickedContext&)));

	KDocerCommonWebWidget::initWebView();
}

void KNewDocsWebWidget::onKeyBoardEvent(int modifierkey, int key, int eventType)
{
	KDocerCommonWebWidget::onKeyBoardEvent(modifierkey, key, eventType);
	
	const int MODIFIERKEY_CONTROL = 1 << 2;
	const int MODIFIERKEY_IS_KEY_PAD = 1 << 9;
	Qt::KeyboardModifiers QtModifierkey = Qt::NoModifier;
	bool bHandled = false;
#ifdef Q_OS_DARWIN
	if (modifierkey == JSApiCef::EVENTFLAG_COMMAND_DOWN
		|| modifierkey == (MODIFIERKEY_IS_KEY_PAD | JSApiCef::EVENTFLAG_COMMAND_DOWN))
#else
	if (modifierkey == MODIFIERKEY_CONTROL
		|| modifierkey == (MODIFIERKEY_IS_KEY_PAD | MODIFIERKEY_CONTROL))
#endif
	{
#ifdef Q_OS_DARWIN
		QtModifierkey |= Qt::MetaModifier;
#else
		QtModifierkey |= Qt::ControlModifier;
#endif
		if (key == 'N')
		{
			emit cefKeyEvent(QtModifierkey, key, eventType == 0, bHandled);
		}
	}
	else if(KNewDocsHelper::isSupportCtrlAltAKey())
	{
		const int MODIFIERKEY_ALT = 1 << 3;
		switch (modifierkey)
		{
		case MODIFIERKEY_CONTROL | MODIFIERKEY_ALT:
		case MODIFIERKEY_IS_KEY_PAD | MODIFIERKEY_CONTROL | MODIFIERKEY_ALT:
#ifdef Q_OS_MACOS
			QtModifierkey |= Qt::MetaModifier;
#else
			QtModifierkey |= Qt::ControlModifier;
#endif
			QtModifierkey |= Qt::AltModifier;
			break;
		default: 
			 break;
		}

		if ((QtModifierkey.testFlag(Qt::ControlModifier) || QtModifierkey.testFlag(Qt::MetaModifier)) &&
			QtModifierkey.testFlag(Qt::AltModifier) && key == Qt::Key_A)
		{
			emit cefKeyEvent(QtModifierkey, key, eventType == 0, bHandled);
		}
	}
}

QWidget* KNewDocsWebWidget::createWaitingWidget()
{
	return nullptr;
}

void KNewDocsWebWidget::invokeWebPageCallback(const QString& param)
{
	if (!param.isEmpty())
	{
		QPointer<KNewDocsWebWidget> spThis(this);
		auto doCallBackTask = [=]() {
			if (spThis)
				spThis->callbackToJS("onWpsPushUIEvent", param);
		};
		if (m_bScriptReady)
			doCallBackTask();
		else
			m_onScriptReadyTasks << doCallBackTask;
	}
}

void KNewDocsWebWidget::setStartTimeStamp(qint64 timeStamp)
{
	m_startTimeStamp = timeStamp;
}

void KNewDocsWebWidget::setErrorPage(KNewdocsloadErrorCode errorCode)
{
	if (!m_webView)
		return;
	m_loadErrorCode = errorCode;
	loadErrorPage();
	m_bLoadErrorPage = true;
}

QString KNewDocsWebWidget::convertLoadErrorCode2String(KNewdocsloadErrorCode errorCode)
{
	QMetaEnum metaEnum = QMetaEnum::fromType<KNewdocsloadErrorCode>();
	return metaEnum.valueToKey(errorCode);
}

KxWebViewContainer* KNewDocsWebWidget::createWebViewContainer()
{
	return new KxWebViewContainer(this, KXWEBVIEW_IMPL_TYPE_CEF_GPU_PROCESS_MODE, "");
}

void KNewDocsWebWidget::resetTemplateLibTabTitle()
{
	KPromeNewDocsPage *page = KTik::findParentByType<KPromeNewDocsPage>(this);
	if (!page)
		return;

	KPromeNewDocsTabBarConfig* pConfig = KPromeNewDocsTabBarConfig::getInstance();
	if (nullptr == pConfig)
		return;

	KPromeSubPage *subPage = page->initialSubPage();
	if (!subPage)
		return;

	KPromeTab *tab = nullptr;
	tab = subPage->getTab();
	if (!tab)
		return;

	if (page->entrance() == "templateLib_fixed_tab")
	{
		KPromeNewDocsTabInfoObj *pConfigObj = pConfig->getInfoObj(KPromeNewDocsTabInfoObj::NewDocTabType::pageTemplateLib);
		subPage->setWindowTitle(pConfigObj->text());
		tab->setIcon(pConfigObj->icon());
		tab->update();
	}
}

void KNewDocsWebWidget::init(const QString& initUrl, const QString& appID, bool bInitWaitingWidget /*= true*/, KxWebViewCefJsApiFlag jsapiFlag /*= KXWEBVIEW_CEF_CEFQUERY*/)
{
	updateAccessControlAllowAllOrigin(initUrl);
	KDocerCommonWebWidget::init(initUrl, appID, bInitWaitingWidget, jsapiFlag);
}

void KNewDocsWebWidget::reload(const QString& reloadUrl, const QString& appID)
{
	updateAccessControlAllowAllOrigin(reloadUrl);
	KDocerCommonWebWidget::reload(reloadUrl, appID);
}

void KNewDocsWebWidget::onJsScriptReady()
{
	m_bScriptReady = true;
	foreach(auto doTask, m_onScriptReadyTasks)
		doTask();
	m_onScriptReadyTasks.clear();
	m_profiler.scriptLoadDone();
}

void KNewDocsWebWidget::onNewCloudDocSwitchStatus(const QVariantMap& paramsMap)
{
	if (paramsMap.isEmpty() ||
		!m_jsApi ||
		!m_jsApi->eventHandler())
		return;

	QVariantMap resultMap;
	resultMap.insert("callstatus", "ok");
	resultMap.insert("result", paramsMap);
	m_jsApi->eventHandler()->broadcastEventToCurrentWebview("event.newdoccloudstatus.change",
		JsonHelper::convertVariantToString(resultMap));
}

void KNewDocsWebWidget::onRenderProcAbnormalTerminate(int status)
{
	if (!m_startup || !KPromeNewDocsPage::isComponentType(m_tabType))
		return;
	
	m_loadErrorCode = KNewdocsloadErrorCode::CefRenderAbnormal;
	m_startup->onOpenDocument("", "", "newBlankDocument", KPromeNewDocsTabInfoObj::convertTabIntType(m_tabType));

	postLoadEvent();
	KxLoggerLite::writeInfo(KPluginNameW, QString("【description】:CefRenderAbnormal,【solution】：Create a new document directly!").toStdWString());
}


