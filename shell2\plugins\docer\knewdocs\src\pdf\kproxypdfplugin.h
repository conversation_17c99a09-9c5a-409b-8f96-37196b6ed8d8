﻿#ifndef __KPDFPROXYPUGIN_H__
#define __KPDFPROXYPUGIN_H__

#include "ksolite/kappmgr.h"
#include "kpromeglobal.h"
#include "kpdfpluginloaddialog.h"

class KPdfProxyPugin : public QObject
{
	Q_OBJECT
	Q_PROPERTY(QString appID READ appID WRITE setAppID)

public:
	KPdfProxyPugin(QObject* parent);
	~KPdfProxyPugin();

	QString appID() const;
	void setAppID(const QString& appID);
	void setAppProperty(std::map<QString, QString> pluginPropertyMap);
	void onTrigger();
	void requestWidget(QWidget* parent, QString from = "wpsoffice", QString p0Value = QString());

protected:
	void setupAppObj();
	void setAppObjPorperty();
	virtual void runApp();
	QString getUrl(const QString& url);
	void openUrl(const QString& url);
	QString getEntrance() const;

protected slots:
	void onLoadSuccess();
	void onLoadFailed();
	void onLoadCanceled();
	void onShowPopupFailedWindow();
	void onOpenUrl();
	void loadProgressUpdate(double progress);
signals:
	void onPluginLoadSuccess(QString appId);
	void onpluginJsIdProgress(QString pluginJsId, double progress);
protected:
	QString m_appID;
	QString m_source;
	KAppObj* m_appObj;
	std::map<QString, QString> m_pluginPropertyMap;
	QVariantMap m_params;
	bool m_hasTriggered;

	QString m_param;
	QWidget* m_parentWidget;
	KPluginLoadFailedWidget* m_failWindow;
	KPluginLoadWindow* m_loadWidget;
};

#endif//__KNWEDOCS_PROXY_PLUGIN_H__
