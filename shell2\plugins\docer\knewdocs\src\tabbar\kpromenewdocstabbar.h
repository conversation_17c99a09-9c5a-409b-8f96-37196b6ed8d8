﻿#ifndef __KPROMETHEUS_NEW_DOCS_TAB_BAR_H__
#define __KPROMETHEUS_NEW_DOCS_TAB_BAR_H__

#include "kpromenewdocstab.h"
#include "kprometheus/kpromepluginwidget.h"

class KPromeNewDocsTab;

class KTabSeperator : public QWidget
{
	Q_OBJECT
public:
	KTabSeperator(QWidget* parent = nullptr);
private slots:
	void onSkinChanged();
};

class KPromeNewDocsTabBar : public QWidget
{
	Q_OBJECT
	Q_PROPERTY(QColor movingAreaBkColor READ movingAreaBkColor WRITE setMovingAreaBkColor)
	Q_PROPERTY(QRect moveArea READ moveArea WRITE setMoveArea)

public:
	KPromeNewDocsTabBar(QWidget* parent);
	~KPromeNewDocsTabBar();

	void initTab();
	QColor movingAreaBkColor();
	int getCurrentTabType();
	KPromeNewDocsTab* getCurrentSelecteTab();
	KPromeNewDocsTab* getTabByType(int tabType);
	void setCurrentTab(int tabType);

	void setMouseHoverIgnore(bool);
	bool ignoreMouseHover() const;

	void updateNewDocsTabBar();
	void clearUnReadMessageCount(int tabType);
	void updateUnReadMessageCount(int tabType);
	void setUnReadMessageCount(int tabType, int count);
	int getUnReadMessageCount(int tabType);

protected:
	virtual void paintEvent(QPaintEvent* ev) override;

signals:
	void tabClicked(int pageType);
	void refreshWebWidget(int pageType);
	void tabTitleChanged(const QString& title, const QIcon& ic);

private slots:
	void onTabClicked(int tabType);
	void onAnimFinished();
	void onCurrentWorkspaceChanged(const QString& curId, const QString& oldId);
	void onUserInfoChange();
private:
	void initUIWidget();
	void setMovingAreaBkColor(QColor color);
	QRect moveArea();
	void setMoveArea(const QRect& rt);
	// 需求：新建页共享文件夹入口tab需要动态设置可见性，企业空间需隐藏，个人空间展示
	void setCloudFolderShareVisible(bool visible);
	void setCloudFolderShareTabVisible(KPromeNewDocsTab* tab, int tabType, bool visible);
	bool isCurrentWorkspacePersonal();
	bool showShareFolderTab();

private:
	int m_currentTabType;
	int m_borderHeight;
	QHBoxLayout* m_docsLayout;
	QMap<int, KPromeNewDocsTab*> m_tabMap;
	QMap<int, QColor> m_typeColorMap;
	bool m_bIgnoreMouseHover;
	QColor m_curMovingTabBkColor;
	QPropertyAnimation* m_movingAreaBkColorAnimation;
	QRect m_movingArea;
	QPropertyAnimation* m_movingAreaAnimation;
	bool m_bMoving;
};

#endif//__KPROMETHEUS_NEW_DOCS_TAB_BAR_H__
