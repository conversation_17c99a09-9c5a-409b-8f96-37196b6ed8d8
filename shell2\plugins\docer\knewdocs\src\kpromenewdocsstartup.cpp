﻿#include "stdafx.h"
#include "kpromenewdocsstartup.h"
#include "knewdochelper.h"
#include "knewdocjsapi.h"
#include <kprometheus/kpromepluginwidgetfactory.h>
#include <kprometheus/kpromeapplication.h>
#include <kprometheus/kpromemainwindow.h>
#include <kprometheus/kprometab.h>
#include <kprometheus/kprometabbar.h>
#include <kprometheus/kpromebrowser.h>
#include <kprometheus/kpromecentralarea.h>
#include <kprometheus/kpromebrowser.h>
#include <kprometheus/kpromewpspage.h>
#include <kprometheus/kpromeheaderbar.h>
#include <kprometheus/kpromecloudsvrproxy.h>
#include <kprometheus/kpromestartupcounter.h>
#include <kprometheus/kpromecloudfilemgr.h>
#include <ksolite/kwebview.h>
#include <ksolite/kdcinfoc.h>
#include <ksolite/kxjsonhelper.h>
#include <ksolite/webapi/kxjsapibrowserwidget.h>
#include <ksolite/kdocer/kdocerjsapiserviceloader.h>
#include <perftool/perftool.h>
#include "utility/knewdocshelper.h"
#include "kpromenewdocspage.h"
#include "pdf/kpromepdfwindow.h"
#include <auth/productinfo.h>
#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"
#include "kdocerwebpageperfhelper.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include "utilities/path/module/kcurrentmod.h"
#include "tabbar/kpromenewdocstabbarconfig.h"
#include "src/knewdocswebwidget.h"
#include <ksolite/kdocercoreitef.h>
#include "ksolite/ksecdocutilsex.h"
#include "ksolite/ksupportutil.h"
#include "ksolite/kdomainmgr.h"
#include "knewdocs_features.h"
#include "krt/kconfigmanagercenter.h"
#include "kdocercorelitehelper.h"
#ifdef Q_OS_WIN
#include <wininet.h>
#endif

#define normalTemplate "/res/blanktemplate/normal.pptx"
#define aiTemplate "/res/aitemplate/aitemplate.pptx"

namespace
{
#ifdef Q_OS_MACOS
#define WM_LBUTTONUP			0x0202
#endif

#undef PROME_NEW_DOCS_TABBAR_NAME
#define PROME_NEW_DOCS_TABBAR_NAME			"KPromeNewDocsTabBarNew"

const char* const g_strKnewdocs = "window.__KNEWDOCS__";
const char* const g_strIsDarkSkin = "isDarkSkin";
const char* const g_strGetAppInfo = "getAppInfo";
const char* const g_strQuerySupportInterface = "querySupportInterface";
const char* const g_strGetKLMEntryId = "getKLMEntryId";
const char* const g_strGetNewDocsEntrance = "getNewDocsEntrance";
const char* const g_strUserId = "userId";
const char* const g_strSkuKey = "skuKey";
const char* const g_strIsEnterprise = "isEnterprise";
const char* const g_strTabText = "tabText";
const char* const g_strGetCurrentSkinName = "getCurrentSkinName";
const char* const g_strShowTemplateTab = "showTemplateTab";
const char* const g_strClientType = "clientType";
constexpr const char* g_strShowSideBar = "showSideBar";
#ifdef Q_OS_LINUX
const char* const g_strGetFontInfo = "getFontInfo";
#endif
#ifdef Q_OS_DARWIN
constexpr qint64 g_timeoutThresholdId = 55930;
#else
constexpr qint64 g_timeoutThresholdId = 49750;
#endif
constexpr const char* g_timeoutThreshold = "timeoutThreshold";
constexpr const char* g_strPageEntryControlStatus = "pageEntryControlStatus";
constexpr int g_checkTransferStateInterval = 1000;
constexpr const char* g_strSupportYunTemplate = "supportYunTemplate";

}

namespace
{
bool isPageLoaded(KPromePluginWidget::WidgetType t)
{
	foreach(KPromeMainWindow* mw, promeApp->mainWindows())
	{
		auto page = dynamic_cast<KPromeWpsPage*>(mw->centralArea()->pageByType(t));
		if (page && page->containedWindow() != 0)
			return true;
	}
	return false;
}

QByteArray toOpenDocFilterStr(KPromePluginWidget::WidgetType t, const QString& path)
{
	QString type = KPromePluginWidget::convertWidgetType(t);
	return QDir::toNativeSeparators(QDir::cleanPath(
		QString(type % "|" % path % "|" % (isPageLoaded(t) ? "1" : "0")).toLower()))
		.toUtf8().toBase64();
}

std::string toByteArray(const QString& str)
{
	QByteArray _str = str.toUtf8();
	return std::string(_str.constData(), _str.count());
}

QString fromByteArray(const std::string& str)
{
	QByteArray _str(str.data(), str.size());
	return QString::fromUtf8(_str);
}

std::vector<std::string> qStrListToStrList(const QStringList qStrList)
{
	std::vector<std::string> strList;
	foreach(const QString & file, qStrList)
	{
		QByteArray _str = file.toUtf8();
		strList.push_back(std::string(_str.constData(), _str.count()));
	}
	return strList;
}

}

KPromeNewDocsWaitingWidget::KPromeNewDocsWaitingWidget(QWidget* parent)
	: QWidget(parent)
	, m_label(nullptr)
	, m_movie(nullptr)
	, m_darkMovie(nullptr)
{
	setObjectName("KPromeNewDocsWaitingWidget");
	setWindowFlags(Qt::FramelessWindowHint);
	setAutoFillBackground(true);
	m_label = new QLabel(this);
	auto sz = KLiteStyle::dpiScaledSize(80, 80);
	m_label->setFixedSize(sz);
	m_movie = new QMovie(":/gif/newdoc-loading-data.gif", QByteArray(), this);
	m_darkMovie = new QMovie(":/gif/newdoc-loading-data-dark.gif", QByteArray(), this);
	m_movie->setScaledSize(sz);
	m_darkMovie->setScaledSize(sz);

	resetBackground();

	if (promeApp && promeApp->promeSkin())
		connect(promeApp->promeSkin(), SIGNAL(skinChanged()), this, SLOT(onSkinChanged()));
}

void KPromeNewDocsWaitingWidget::resizeEvent(QResizeEvent* e)
{
	int x = geometry().width() / 2 - m_label->width() / 2;
	int y = geometry().height() / 2 - m_label->height() / 2;
	m_label->move(x, y);
}

bool KPromeNewDocsWaitingWidget::event(QEvent* e)
{
	if (e->type() == QEvent::Show)
	{
		if (m_label && m_label->movie())
			m_label->movie()->start();
	}
	else if (e->type() == QEvent::Hide)
	{
		if (m_label && m_label->movie())
			m_label->movie()->stop();
	}

	return QWidget::event(e);
}

void KPromeNewDocsWaitingWidget::resetBackground()
{
	KNewDocDrawHelper::setBackgroundPattle(this);

	if (m_movie)
		m_movie->stop();

	if (m_darkMovie)
		m_darkMovie->stop();

	if (m_label)
	{
		m_label->setMovie(KNewDocDrawHelper::isDarkSkin() ? m_darkMovie : m_movie);

		if (m_label->movie())
			m_label->movie()->start();
	}
}


void KPromeNewDocsWaitingWidget::onSkinChanged()
{
	resetBackground();
}

KPromeNewDocsStartup::KPromeNewDocsStartup(QWidget* parent)
	: KPromePluginWidget(parent)
	, m_pParent(parent)
	, m_nLoadDuration(0)
	, m_browser(nullptr)
	, m_bOnlineWebPage(false)
	, m_bRunOnce(false)
	, m_layout(nullptr)
	, m_netTestSocket(nullptr)
{
	if (ksolite::isSupported(ksolite::IntranetVersion7))
	{
		connect(&m_checkUploadStatusTimer, &QTimer::timeout, this,
			&KPromeNewDocsStartup::onGetUploadStateTimerTimeout);
	}
}

void KPromeNewDocsStartup::paintEvent(QPaintEvent* ev)
{
	KNewDocDrawHelper::drawCommonBackground(this);
}

void KPromeNewDocsStartup::resizeEvent(QResizeEvent* event)
{
	if (m_webwidget)
		m_webwidget->resize(event->size());

	if (m_browser)
		m_browser->resize(event->size());

	KPromePluginWidget::resizeEvent(event);
}

QString KPromeNewDocsStartup::getModuleName(QString strPageType)
{
	return QString("knewdocs_%1").arg(strPageType);
}

void KPromeNewDocsStartup::getFileLocParamFromUrl(const QString& url, QString& driveId, QString& parentId)
{
	QString filePath = url;
	int pos = filePath.lastIndexOf("?");
	filePath = filePath.left(pos);

	pos = filePath.lastIndexOf("/");
	if (pos > filePath.size() - 1)
		return;

	parentId = filePath.mid(pos + 1);
	filePath = filePath.left(pos);

	pos = filePath.lastIndexOf("/");
	if (pos > filePath.size() - 1)
		return;

	driveId = filePath.mid(pos + 1);
}

void KPromeNewDocsStartup::openV7TemplateDocument(const QString& from, const QString& filePath, const QString& browserType, const QString& callBack)
{
	QVariantMap result;
	result["callstatus"] = "ok";
	result["result"] = false;
	QString driveId, parentId;
	getFileLocParamFromUrl(m_expectedUrl, driveId, parentId);
	if (driveId.isEmpty() || parentId.isEmpty())
	{
		emit sigV7NewDoc(callBack, result);
		return;
	}

	if (kcoreApp && kcoreApp->kdcService())
	{
		if (from == "openLocalFile")
		{
			//上传模板文件到云端
			_uploadFiles(QStringList() << filePath, QString(), parentId, driveId, "", callBack);
			_startGetUploadState();
			return;
		}
		else if (from == "newBlankDocument")
		{
			//新建文件,然后打开
			kdc::CreateFileArg arg;
			arg.source = "knewdocs";
			arg.parentId = toByteArray(parentId);
			arg.groupId = "";
			arg.fileType = kdc::FileTypeFilter::File;
			arg.fileName = toByteArray(getNewCreateFileNameByType(browserType));
			arg.extraParam["driveId"] = toByteArray(driveId);
			arg.extraParam["onNameConflict"] = "rename";
			arg.extraParam["needDownload"] = "0";

			QString requestId = kcoreApp->kdcService()->kdcAsyncCall("createFile", &arg, this, "onCreateFileFinished");
			if (!callBack.isEmpty() && !requestId.isEmpty())
			{
				m_v7NewBlankDocCallBacks.insert(requestId, callBack);
				return;
			}
		}
	}
	emit sigV7NewDoc(callBack, result);
}

void KPromeNewDocsStartup::onOpenV7Document(const QString& fileId, const QString& fileName, const QString& cloudPath)
{
	QVariantMap cloudFileInfo;
	cloudFileInfo.insert("fileid", fileId);
	cloudFileInfo.insert("fileName", fileName);
	cloudFileInfo.insert("doctype", cloudPath);
	cloudFileInfo.insert("csource", "knewdocs");
	if (promeApp && promeApp->cloudFilesMgr())
	{
		promeApp->cloudFilesMgr()->downloadAndOpen(cloudFileInfo);
	}

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);

	if (!mw)
		return;

	KPromeNewDocsPage* newDocsPage = qobject_cast<KPromeNewDocsPage*>(m_pParent);
	if (!newDocsPage)
		return;

	KPromeTab* newDocsTab = nullptr;
	if (mw->tabBar())
	{
		newDocsTab = mw->tabBar()->findTab(newDocsPage->initialSubPage());
	}

	//获取当前页面下载数量是否为0，如果为0，则可关闭当前页面
	int downloadTempalteCount = 0;
	if (KPromeNewDocsPage* page = qobject_cast<KPromeNewDocsPage*>(m_pParent))
	{
		downloadTempalteCount = page->getDownloadTemplateCount();
	}
	//当前页面要关闭的情况是当前page页面正在下载数为0
	bool bClose = (downloadTempalteCount == 0);
	newDocsPage->setNoClose(false);

	if (bClose)
		emit closeTab();

	if (newDocsTab && bClose)
	{
		newDocsPage->doClose();
		newDocsTab->tryClose();
	}
}

QString KPromeNewDocsStartup::getTargetUrlArgsType(const QString& browserType)
{
	static QMap<QString, QString> s_targetUrlTypeMap =
	{
		{"pageDocerWps", "doc"},
		{"pageDocerWpp", "ppt"},
		{"pageDocerEt", "xls"},
		{"pageDocerPdf", "pdf"},
		{"widgetWpsForm", "form"},
		{"pageKOtl", "otl"},
		{"pageKDbSheet", "dbt"},
		{"pageKSheet", "ksheet"}
	};
	return s_targetUrlTypeMap.value(browserType);
}

KPromeNewDocsStartup::~KPromeNewDocsStartup()
{
	QString moduleName = getModuleName(QString::number(m_browserType));
}

void KPromeNewDocsStartup::initBrowser(int type)
{
	m_layout = new QStackedLayout(this);
	m_layout->setSpacing(0);
	m_layout->setMargin(0);
	connect(&m_browserCreator, SIGNAL(createSucceeded(KPromeBrowserWidgetBase*)),
		this, SLOT(onCreateSucceeded(KPromeBrowserWidgetBase*)));

	m_initBrowserTime = QDateTime::currentMSecsSinceEpoch();
	m_browserType = type;
	QString strPageType = KPromeNewDocsTabInfoObj::convertTabIntType(m_browserType);
	QString moduleName = getModuleName(strPageType);
	::CreatePromeBrowserWidget(this, &m_browserCreator,
		KPromeBrowserType::WPSApp, PromeBrowserProcessToken(), "kprome_new_docs_startup");
	emit sigInitBrowser();
}

void KPromeNewDocsStartup::initKNewDocsBrowser(int type)
{
	m_initBrowserTime = QDateTime::currentMSecsSinceEpoch();
	m_browserType = type;
	auto newDocsPage = KTik::findParentByType<KPromeNewDocsPage>(parent());
	QString strPageType = KPromeNewDocsTabInfoObj::convertTabIntType(m_browserType);
	QString startupUrl = KxCreateDocConfig::instance().getCreateDocUrl(strPageType);
	if (ksolite::isSupported(ksolite::IntranetVersion7) && _canShowOnlineTemplate() &&
		isOnlineWebPage())
	{
		//v7可以展示模板页的话，就展示模板页
		QString domainUrl = promeApp->getDomainMgr()->getDomainUrl("root");
		if (!domainUrl.endsWith('/'))
			domainUrl.append('/');
		QString argsType = getTargetUrlArgsType(strPageType);
		startupUrl = QString("%1c/businessKdocs/index.html#/business/recommend/0/0?type=%2").arg(domainUrl).arg(argsType);
	}

	if (!m_expectedUrl.isEmpty()
		&& QUrl(m_expectedUrl).isValid())
	{
		startupUrl = m_expectedUrl;
	}

	QString moduleName = getModuleName(strPageType);
	
		if (!m_webwidget)
		{
			m_webwidget = new KNewDocsWebWidget(type, this);

			connect(m_webwidget, &KNewDocsWebWidget::loadFinished, this, [=]() 
				{
					emit sigShowWidget(this);
				});
			connect(m_webwidget, &KNewDocsWebWidget::mainResourceVerificationFailed, this, [=](const QString& url) 
				{
					if (!newDocsPage)
						return;
					emit newDocsPage->mainResourceVerificationFailed();
				}
			);

			QVariantMap mapJsObj;
			mapJsObj.insert(g_strKnewdocs, buildKNewDocsJsParams(type));
			m_webwidget->injectJsObject(startupUrl, mapJsObj);

			m_webwidget->setStartTimeStamp(m_initBrowserTime);
			m_webwidget->resize(newDocsPage->size()); //先设置Page大小，后面会进行resize，防止出现窗口过小，页面闪烁的问题
			m_webwidget->init(startupUrl, moduleName);
			emit sigInitBrowser();
		}
		else
			changeEntry(type);

		if (m_webwidget)
		{
			if (KxWebViewContainer* webView = m_webwidget->getWebview())
				webView->setBrowserTraceInfo(KxWebViewStartTrace::ActionStart, m_initBrowserTime);
		}
}

void KPromeNewDocsStartup::changeEntry(int type)
{
	if (!m_webwidget)
		return;
	m_browserType = type;
	QJsonObject jsonObject;
	QJsonObject paramObject;
	paramObject.insert("entry", KNewDocsHelper::getComponentName(type));
	jsonObject.insert("action", "changeentry");
	jsonObject.insert("param", paramObject);

	if (m_webwidget)
		m_webwidget->invokeWebPageCallback(JsonHelper::convertQJsonToString(jsonObject));
}

void KPromeNewDocsStartup::loadErrorPage(KNewDocsWebWidget::KNewdocsloadErrorCode errorCode)
{
	if (!m_webwidget)
		return;
	m_webwidget->setErrorPage(errorCode);
}

void KPromeNewDocsStartup::refreshUrl(int tabType, bool bForce /* = false*/, const QString& url /* = QString() */)
{
	if (krt::kcmc::support("TemplateLibFixedTab")
		&& m_webwidget
		&& ksolite::isSupported(ksolite::IntranetVersion7))
	{
		reloadNewDocsWebWidget(tabType);
		return;
	}
	QString strPageType = KPromeNewDocsTabInfoObj::convertTabIntType(tabType);
	QString startupUrl = !url.isEmpty() ? url : KxCreateDocConfig::instance().getCreateDocUrl(strPageType);
	QString moduleName = getModuleName(strPageType);
	if (m_webwidget)
	{
		m_browserType = tabType;
		
		QString curUrl = m_webwidget->mainUrl();
		if (bForce || curUrl != startupUrl)
		{
			m_webwidget->reload(startupUrl, moduleName);
		}
	}
	else if (m_browser)
	{
		QString curUrl = m_browser->getCurrentUrl();
		if (bForce || curUrl != startupUrl)
		{
			if (m_browser->isCefReady())
				m_browser->loadURL(startupUrl);
			else
				m_browser->setStartupUrl(startupUrl);
		}
	}
}

void KPromeNewDocsStartup::setExpectedUrl(const QString& url)
{
	m_expectedUrl = url;
}

QString KPromeNewDocsStartup::getBrowserType()
{
	return 	KPromeNewDocsTabInfoObj::convertTabIntType(m_browserType);
}

KPromePluginWidget::WidgetType KPromeNewDocsStartup::getBrowserRawType()
{
	return (KPromePluginWidget::WidgetType)m_browserType;
}

void KPromeNewDocsStartup::showWebWidget()
{
	if (m_browser)
	{
		if (m_layout->currentWidget() != m_browser || !m_browser->isVisible())
		{
			if (auto newDocsPage = KTik::findParentByType<KPromeNewDocsPage>(parent()))
			{
				qint64 currentTimeStampe = QDateTime::currentMSecsSinceEpoch();
				KxLoggerLite::writeInfo(L"KPromeNewDocsStartup", QString("show webWidget, create Browser %1 and load Url(including create Browser) time Coast is %2, total time is %3")
					.arg(m_browserCreatedTime)
					.arg(currentTimeStampe - m_initBrowserTime)
					.arg(currentTimeStampe - newDocsPage->getKNewDocsTabbarButtonClickedTimeStamp()).toStdWString());
			}
		}
		{
			m_browser->setVisible(true);
			m_layout->setCurrentWidget(m_browser);
		}
	}
	else if (m_webwidget)
	{
		KxLoggerLite::writeInfo(L"KPromeNewDocsStartup", QString("show webWidget, load Url(including create Browser) time Coast is %1")
			.arg(QDateTime::currentMSecsSinceEpoch() - m_initBrowserTime).toStdWString());
		m_webwidget->showWebWidget();
	}
}

void KPromeNewDocsStartup::createWaitingWidget()
{
#ifdef Q_OS_DARWIN
	if (m_layout)
	{
		auto waitingWidget = new KPromeNewDocsWaitingWidget(this);
		m_layout->addWidget(waitingWidget);
		m_layout->setCurrentWidget(waitingWidget);
	}
#endif // Q_OS_DARWIN
}

void KPromeNewDocsStartup::setOnlineWebPage(bool bOnline)
{
	m_bOnlineWebPage = bOnline;
}

bool KPromeNewDocsStartup::isOnlineWebPage()
{
	return m_bOnlineWebPage;
}

void KPromeNewDocsStartup::invokeWebPageCallback(const QString& param)
{
	if (m_webwidget)
		m_webwidget->invokeWebPageCallback(param);
}

void KPromeNewDocsStartup::openDocument(const QString& file, const QString& fileKey, const QString& from)
{
	emit signalOpenDocument(file, fileKey, from);
}

void KPromeNewDocsStartup::setFrom(const QString &from)
{
	m_from = from;
}

QString KPromeNewDocsStartup::getFrom()
{
	return m_from;
}

void KPromeNewDocsStartup::reloadNewDocsWebWidget(int type)
{
	m_initBrowserTime = QDateTime::currentMSecsSinceEpoch();
	m_browserType = type;
	auto newDocsPage = KTik::findParentByType<KPromeNewDocsPage>(parent());
	QString strPageType = KPromeNewDocsTabInfoObj::convertTabIntType(m_browserType);
	QString startupUrl = KxCreateDocConfig::instance().getCreateDocUrl(strPageType);
	if (ksolite::isSupported(ksolite::IntranetVersion7) && _canShowOnlineTemplate() &&
	    isOnlineWebPage())
	{
		//v7可以展示模板页的话，就展示模板页
		QString domainUrl = promeApp->getDomainMgr()->getDomainUrl("root");
		if (!domainUrl.endsWith('/'))
			domainUrl.append('/');
		startupUrl = domainUrl + "c/businessKdocs/index.html";

		if (!m_expectedUrl.isEmpty()
		    && QUrl(m_expectedUrl).isValid())
		{
			startupUrl = m_expectedUrl;
		}
	}

	QVariantMap mapJsObj;
	mapJsObj.insert(g_strKnewdocs, buildKNewDocsJsParams(type));
	m_webwidget->injectJsObject(startupUrl, mapJsObj);

	m_webwidget->setStartTimeStamp(m_initBrowserTime);
	m_webwidget->resize(newDocsPage->size()); //先设置Page大小，后面会进行resize，防止出现窗口过小，页面闪烁的问题
	m_webwidget->setProperty("tab_type", m_from);
	m_webwidget->reload(startupUrl, getModuleName(strPageType));
}

QVariantMap KPromeNewDocsStartup::buildKNewDocsJsParams(int type)
{
	m_initBrowserTime = QDateTime::currentMSecsSinceEpoch();
	m_browserType = type;
	auto newDocsPage = KTik::findParentByType<KPromeNewDocsPage>(parent());
	QVariantMap mapJson;
	mapJson.insert(g_strIsDarkSkin, KNewDocDrawHelper::isDarkSkin());
	mapJson.insert(g_strGetAppInfo, KNewDocsHelper::getAppInfo());
	mapJson.insert(g_strQuerySupportInterface, KNewDocsHelper::querySupportInterface());
	mapJson.insert(g_strGetKLMEntryId, KNewDocsHelper::getPageEntryId(newDocsPage));
	mapJson.insert(g_strGetNewDocsEntrance, KNewDocsHelper::getPaySourceEntrance(newDocsPage));
	QString userId;
	QString skuKey;
	bool isCompanyAccount = false;
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	if (account)
	{
		userId = account->getUserId();
		skuKey = account->getMerchandiseSkus().keys().join(";");
		isCompanyAccount = account->isCompanyAccount();
	}

	mapJson.insert(g_strUserId, userId);
	mapJson.insert(g_strSkuKey, skuKey);
	mapJson.insert(g_strIsEnterprise, isCompanyAccount);
	mapJson.insert(g_strShowSideBar, KNewDocsHelper::showSideBar());
#ifdef Q_OS_LINUX
	mapJson.insert(g_strGetFontInfo, QString("'%1'").arg(KNewDocsHelper::getFontInfo()));
#endif
	QString tabText;
	if (auto pCfgObj = newDocsPage->getDocsTabByType(type))
		tabText = pCfgObj->text();
	mapJson.insert(g_strTabText, tabText);
	KSkin *skin = qApp->findChild<KSkin *>();
	mapJson.insert(g_strGetCurrentSkinName, skin ? skin->getSkinInfo().m_skinName : "");
	QJsonObject jsonObj = KPromeNewDocsTabBarConfig::getInstance()
		                      ? KPromeNewDocsTabBarConfig::getInstance()->getFpcCombData()
		                      : QJsonObject();
	bool showTemplateTab = false;
	if (krt::kcmc::support("ShowTemplateTab"))
	{
		if (account)
			showTemplateTab = account->isCompanyAccount();
	}
	else
		showTemplateTab = jsonObj.value(g_strShowTemplateTab).toBool(true);
	mapJson.insert(g_strShowTemplateTab, showTemplateTab);
	mapJson.insert(g_strClientType, krt::product::versionIdentifier());
	// 新建上云的状态
	mapJson.insert("isNewCloudDocsSwitchEnabled", KNewDocsHelper::isNewCloudDocSwitchEnabled());
	mapJson.insert("isNewCloudDocSwitchOpened", KNewDocsHelper::isNewCloudDocSwitchOpened());
	mapJson.insert("isNewCloudDocSwitchChangeable", KNewDocsHelper::isNewCloudDocSwitchChangeable());
	mapJson.insert("isThisDeviceFirstOpen", KNewDocsHelper::isThisDeviceFirstOpen());

	if (account && account->isCompanyAccount())
		mapJson.insert("companyUserCategory", KNewDocsHelper::getCompanyUserCategory());

	QJsonObject controlStatusJsonObj;
	KNewDocsHelper::getCurrentPageEntryControlStatus(m_browserType, controlStatusJsonObj);
	if (!controlStatusJsonObj.isEmpty())
		mapJson.insert(g_strPageEntryControlStatus, controlStatusJsonObj);

	//新建首页超时阈值
	auto docerCoreLite = getIKDocerCoreLite();
	if (docerCoreLite && docerCoreLite->isFpcInitFinished())
	{
		QJsonObject fpcCombJsonObj = docerCoreLite->getFpcCombJsonObject(g_timeoutThresholdId);
		mapJson.insert(g_timeoutThreshold, fpcCombJsonObj);
	}

	if (ksolite::isSupported(ksolite::IntranetVersion7))
	{
		mapJson.insert(g_strSupportYunTemplate, true);
	}

	return mapJson;
}

void KPromeNewDocsStartup::onLoadFinished(bool ok)
{
	if (!m_browser)
		return;
	QString strPageType = KPromeNewDocsTabInfoObj::convertTabIntType(m_browserType);
	QString moduleName = getModuleName(strPageType);
	QString startupUrl = KxCreateDocConfig::instance().getCreateDocUrl(strPageType);
	KxLoggerLite::writeInfo(L"KPromeNewDocsStartup", QString("load url %1 end, timeStamp is %2")
		.arg(startupUrl).arg(QDateTime::currentMSecsSinceEpoch()).toStdWString());
	if (ok)
	{
		if (m_layout && m_browser)
		{
			m_layout->setCurrentWidget(m_browser);
			if (krt::kcmc::support("PromeNewDocsSkeleton"))
				emit sigShowWidget(this);
		}
		PERFLOGEND("PrometheusNewDocsTabChange");
		PERFLOGEND("PrometheusNewDocsTabLoad");
	}

	if (!m_bRunOnce)
	{
		m_bRunOnce = true;
		m_nLoadDuration = QDateTime::currentMSecsSinceEpoch() - m_initBrowserTime;
	}
#if WPS_FEATURE(cocoa_menubar)
	if (auto mw = promeApp->findRelativePromeMainWindow(this))
	{
		if (!mw->hasEditMenu())
			mw->addEditMenu();
	}
#endif
}

void KPromeNewDocsStartup::onCreateFileFinished(bool bOk, const QString& requestId, const string& strResult)
{
	QString strCallBack = m_v7NewBlankDocCallBacks.value(requestId);
	QVariantMap contextResult;
	contextResult["callstatus"] = "ok";
	contextResult["result"] = false;
	if (kcoreApp && kcoreApp->kdcService())
	{
		kdc::CreateFileResult result = kcoreApp->kdcService()->deserializeResult<kdc::CreateFileResult>(strResult);
		if (result.state != kdc::ResultState::Succeeded)
		{
			QString errCode = QString::fromStdString(result.errInfo.errResult);
			QString errMsg = QString::fromStdString(result.errInfo.errString);
			contextResult["errMsg"] = errMsg;
			contextResult["errCode"] = errCode;
			contextResult["operaResult"] = "error";
			m_v7NewBlankDocCallBacks.remove(requestId);
		}
		else
		{
			QString fileId = fromByteArray(result.itemInfo.fileId);
			QString fileName = fromByteArray(result.itemInfo.name);
			QString cloudPath = fromByteArray(result.itemInfo.cache);
			onOpenV7Document(fileId, fileName, cloudPath);
			contextResult["fileId"] = fileId;
			contextResult["operaResult"] = "ok";
		}
	}
	emit sigV7NewDoc(strCallBack, contextResult);
}

void KPromeNewDocsStartup::onGetCloudFileInfoFinished(bool bOk, const QString& requestId, const string& strResult)
{
	if (kcoreApp && kcoreApp->kdcService())
	{
		kdc::GetCloudFileInfoResult result = kcoreApp->kdcService()->deserializeResult<kdc::GetCloudFileInfoResult>(strResult);
		if (result.state != kdc::ResultState::Succeeded)
			return;

		QString fileId = fromByteArray(result.itemInfo.fileId);
		QString fileName = fromByteArray(result.itemInfo.name);
		QString cloudPath = fromByteArray(result.itemInfo.cache);
		onOpenV7Document(fileId, fileName, cloudPath);
	}
}

void KPromeNewDocsStartup::onGetUploadStateTimerTimeout()
{
	_getUploadState();
}

void KPromeNewDocsStartup::onGetBatchUploadFileListState(bool bOk, const QString& uuid, const KRpcResponse& res)
{
	QSet<QString> uploadingFiles = m_uploadingFiles;
	foreach(QString file, uploadingFiles)
	{
		QString fileInfo = res.get(file);
		onV7NotifyUploadFileListState(bOk, file, fileInfo);

		QString fileId = res.get(file + "/fileId");
		if (!fileId.isEmpty())
		{
			m_uploadingFiles.remove(file);

			kdc::GetCloudFileInfoArg arg;
			arg.fileId = toByteArray(fileId);
			arg.groupId = "";
			if (kcoreApp && kcoreApp->kdcService())
			{
				kcoreApp->kdcService()->kdcAsyncCall("getCloudFileInfo", &arg, this, "onGetCloudFileInfoFinished");
			}
		}
	}

	if (m_uploadingFiles.isEmpty())
	{
		_stopGetUploadState();
	}
}

void KPromeNewDocsStartup::onJsInit()
{
	QString strPageType = KPromeNewDocsTabInfoObj::convertTabIntType(m_browserType);
	QString moduleName = getModuleName(strPageType);
}

void KPromeNewDocsStartup::onCreateSucceeded(KPromeBrowserWidgetBase* browser)
{
	m_browserCreatedTime = QDateTime::currentMSecsSinceEpoch() - m_initBrowserTime;
	KxLoggerLite::writeInfo(L"KPromeNewDocsStartup", QString("initBrowser tabType %1 end, time Coast is %2")
		.arg(m_browserType).arg(m_browserCreatedTime).toStdWString());

	m_browser = browser;
	auto newdocsPage = KTik::findParentByType<KPromeNewDocsPage>((parent()));
	if (newdocsPage)
	{
		this->resize(newdocsPage->size());
		m_browser->resize(this->size());
	}

	KNewDocDrawHelper::setBackgroundPattle(m_browser);

	m_layout->addWidget(browser);

	if (krt::kcmc::support("IsEulerOSInHwpad"))
	{
		QWidget* pParent = parentWidget();
		QByteArray name("KPromeCentralArea");
		while (pParent)
		{
			const QMetaObject* pMeta = pParent->metaObject();
			if (pMeta && 0 == name.compare(pMeta->className()))
				break;

			pParent = pParent->parentWidget();
		}

		if (pParent)
			m_browser->resize(pParent->size().width() - 1, pParent->size().height() - 1);
	}

	if (m_browserType == pageDocerPdf)//pdf首页
	{
		connect(m_browser, SIGNAL(creatingApi(KxWebViewContainer*, QObject**)),
			this, SLOT(onCreatingPdfApi(KxWebViewContainer*, QObject**)),
			Qt::DirectConnection);
	}
	else
	{
		connect(browser, SIGNAL(creatingApi(KxWebViewContainer*, QObject**)),
			this, SLOT(onCreatingApi(KxWebViewContainer*, QObject**)),
			Qt::DirectConnection);
	}
	connect(browser, SIGNAL(cefKeyEvent(Qt::KeyboardModifiers, int, bool, bool&)),
		this, SIGNAL(cefKeyEvent(Qt::KeyboardModifiers, int, bool, bool&)),
		Qt::UniqueConnection);

	QString strPageType = KPromeNewDocsTabInfoObj::convertTabIntType(m_browserType);
	QString startupUrl = KxCreateDocConfig::instance().getCreateDocUrl(strPageType);
	QString moduleName = getModuleName(strPageType);

	browser->setToolbarVisible(false);
	browser->setStartupUrl(startupUrl);
	browser->setPolicy(POLICY_USE_CONTEXT_MENU, false);
	browser->setPolicy(POLICY_FULL_SCREEN, false); // 473480
	browser->setPolicy(POLICY_ENABLE_DRAG_AND_DROP, false);
	//browser->setPolicy(POLICY_DISABLE_DEFAULT_KEYEVENT, true);
	connect(browser, SIGNAL(loadFinished(bool)), this, SLOT(onLoadFinished(bool)));
	browser->init();

	if (browser->webView())
		browser->webView()->setFocusPolicy(KXWEBVIEW_TAKE_FOCUS); //#458112 新建页面cef需主动获取焦点
#ifdef Q_OS_MACOS
	// bug#370833、370828
    browser->setBackgroundColor(KDrawHelper::isDarkSkin() ? QColor("#1f2021") : QColor("#f7f7f7"));
	m_browser->setDelayShow(50);
#endif
}

void KPromeNewDocsStartup::onCreatingApi(KxWebViewContainer* webview, QObject** api)
{
	*api = new KxNewDocJsApi(this, webview);
	if (ksolite::KxCommonJsApi* pJsAPi = qobject_cast<ksolite::KxCommonJsApi*>(*api))
		pJsAPi->init();

	bool connected = false;

	connected = connect(*api, SIGNAL(openDocument(const QString&, const QString&, const QString&, const QString&, const QString&)), this, SLOT(onOpenDocument(const QString&, const QString&, const QString&, const QString&, const QString&)));
	ASSERT(connected);

	connected = connect(*api, SIGNAL(openRecentDocument(const QString&, const QString&)), this, SLOT(onOpenRecentDocument(const QString&, const QString&)));
	ASSERT(connected);

	connected = connect(*api, SIGNAL(closeTab()), this, SIGNAL(closeTab()), Qt::QueuedConnection);
	ASSERT(connected);

	connected = connect(*api, SIGNAL(downloadUpdate(bool)), this, SIGNAL(downloadUpdate(bool)));
	ASSERT(connected);

	connected = connect(*api,SIGNAL(openPromeBrowser(const QString&)),this,SLOT(onOpenPromeBrowser(const QString&)));
	ASSERT(connected);

	connected = connect(*api, SIGNAL(execOfficialDoc(const QString&)), this, SLOT(onExecOfficialDoc(const QString&)));
	ASSERT(connected);

	
	connected = connect(*api, SIGNAL(jsInit()), this, SLOT(onJsInit()));
	ASSERT(connected);

	connected = connect(this, SIGNAL(sigV7NewDoc(const QString&, const QVariantMap&)), *api, SLOT(onV7NewDoc(const QString&, const QVariantMap&)));
	ASSERT(connected);
	
#ifdef Q_OS_MACOS
	connect(webview,
			SIGNAL(chromeWidgetWinMessage(unsigned int, unsigned int, unsigned int, unsigned int)),
			this,
			SLOT(onChromeWidgetWinMessage(unsigned int, unsigned int, unsigned int, unsigned int)));
#endif
}


void KPromeNewDocsStartup::onCreatingPdfApi(KxWebViewContainer* webview, QObject** api)
{
	*api = new KxPromePdfWindowJsApi(this, webview);
	if (krt::kcmc::support("OnlyFocusOnPDF"))
	{
		webview->setMainResourceVerification(false);
	}
	bool connected = false;
	connected = connect(*api, SIGNAL(closeTab()), this, SIGNAL(closeTab()));
	ASSERT(connected);
}

void KPromeNewDocsStartup::onChromeWidgetWinMessage(unsigned int hwnd, unsigned int message, unsigned int wparam, unsigned int lparam)
{
#if WPS_FEATURE(cocoa_menubar)
	if (message != WM_LBUTTONUP)
		return;
	
	if (!promeApp)
		return;
	
	if (auto mw = promeApp->findRelativePromeMainWindow(this))
	{
		if (!mw->hasEditMenu())
			mw->addEditMenu();
	}
#endif
}

void KPromeNewDocsStartup::onV7NotifyUploadFileListState(bool bOk, const QString &filePath, const QString &fileInfo)
{
	QString callBack = m_v7NewLocalDocCallBacks.value(filePath, "");
	QJsonDocument fileDoc = QJsonDocument::fromJson(fileInfo.toUtf8());
	if (!fileDoc.isObject())
		return;

	QVariantMap contextResult;
	contextResult["callstatus"] = "ok";
	contextResult["result"] = false;

	QJsonObject fileObj = fileDoc.object();
	QString uploadState = fileObj.value("state").toString();
	if (uploadState.compare("success", Qt::CaseInsensitive) == 0)
	{
		QString strFileId = fileObj.value("fileId").toString();
		contextResult["operaResult"] = "ok";
		contextResult["fileId"] = strFileId;
		emit sigV7NewDoc(callBack, contextResult);
		m_v7NewLocalDocCallBacks.remove(filePath);
	}
	else if (uploadState.compare("fail", Qt::CaseInsensitive) == 0)
	{
		QString strErrCode = fileObj.value("errCode").toString();
		QString strErrMsg = fileObj.value("errmsg").toString();
		contextResult["errMsg"] = strErrMsg;
		contextResult["errCode"] = strErrCode;
		contextResult["operaResult"] = "error";
		emit sigV7NewDoc(callBack, contextResult);
		m_v7NewLocalDocCallBacks.remove(filePath);
		m_uploadingFiles.remove(filePath);
	}
}


KPromeMainWindow * KPromeNewDocsStartup::loadMainWindow(KPromePluginWidget::WidgetType plgType,
	KPromePluginWidget::WidgetType &currentPageType, 
	const QString &from,
	bool &bcreateNew)
{
	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return nullptr;

	if (!mw->centralArea())
		return nullptr;
	
	KPromePage* pCurrentPage = mw->centralArea()->currentPage();
	
	bool isstandalone = mw->headerbarType() == KPromeHeaderBarBase::Standalone;

	//如果是Wpp格式且是多组件模式。不要创建新的窗口。因为接收IPC的窗口是当前窗口。
	//IPC会有ADD信息。导致会关闭窗口。目前只发现WPP在下载模板有的模板有这种现象。并不是所有的都有。
	bool donotcraete = isstandalone
		&& promeApp->isPromeShelllessMode()
		&& (currentPageType == KPromePluginWidget::pageDocerWpp || currentPageType == KPromePluginWidget::pageWpp) && (from != "newBlankDocument");

	if (donotcraete)
	{
		if (KPromeStandaloneWindowHeaderbar* bar = qobject_cast<KPromeStandaloneWindowHeaderbar*>(mw->headerBar()))
		{
			bar->setCreateHeaderBarFrom(KPromeStandaloneWindowHeaderbar::CreateHeaderBarFrom_StartupOpenDoc);
		}
	}

	//如果是多组件模式，并且是符合文档类型，创建新的独立窗口打开文档。
	if (KPromePage::isMatchMode(plgType))
	{
		KPromeMainWindow* newMw = nullptr;
		if (isstandalone)
		{
			newMw = mw->findOrCreateMainWindow(KPromeHeaderBarBase::Standalone, true);
		}
		if (newMw && newMw->centralArea())
		{
			pCurrentPage = newMw->centralArea()->currentPage();
			if (!pCurrentPage)
			{
				KPromePage* pPage = newMw->centralArea()->pageByType(plgType);
				if (!pPage)
					pPage = newMw->centralArea()->addPage(plgType);

				if (pPage)
				{
					newMw->centralArea()->setCurrentPage(pPage);
					pCurrentPage = pPage;
					mw = newMw;
					bcreateNew = true;
				}
				else
				{
					newMw->close();
				}
			}
		}

	}

	if (pCurrentPage)
	{
		currentPageType = pCurrentPage->type();
	}
	return mw;
}


bool KPromeNewDocsStartup::_canShowOnlineTemplate()
{
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;
	if (account)
		isLogined = account->isLogined();
	if (ksolite::isSupported(ksolite::IntranetVersion7) && 
		_networkIsOk() && 
		isLogined &&
		_hasOnlineTemplatePrivilege())
	{
		return true;
	}
	return false;
}

bool KPromeNewDocsStartup::_networkIsOk()
{
#ifdef Q_OS_WIN
	DWORD dwState = 0;
	BOOL bIsConnected = ::InternetGetConnectedState(&dwState, 0);
	return bIsConnected == 1;
#else
	QString server = krt::kcmc::value("DomainCfgs", "CfgV7Url");
	QUrl url(server);
	if (!m_netTestSocket)
		m_netTestSocket = new QTcpSocket;
	m_netTestSocket->connectToHost(url.host(), 80);
	bool ret = m_netTestSocket->waitForConnected(1000);
	m_netTestSocket->abort();
	return ret;
#endif
}

bool KPromeNewDocsStartup::_hasOnlineTemplatePrivilege()
{
	kacctoutsdk::KAccountSdkProxy* pAccountSdk = kcoreApp->getAccountSDK();
	if (!pAccountSdk)
		return false;

	kacctoutsdk::IAccount* pAccount = pAccountSdk->getAccount();
	if (!pAccount)
		return false;

	kacctoutsdk::IPrivileges* pPrivileges = pAccount->getPrivileges();
	if (!pPrivileges)
		return false;

	QString privilegeId = "template.enable";
	QStringList privilegeIds;
	privilegeIds.append(privilegeId);

	kacctoutsdk::PrivilegeInfos privilgeResult;
	pPrivileges->getPrivileges(privilegeIds, privilgeResult, true);
	return !privilgeResult.isEmpty();
}

QString KPromeNewDocsStartup::getNewCreateFileNameByType(const QString& browserType)
{
	QString fileName;
	if (browserType == "pageDocerWps")
		fileName = QObject::tr("NewWps").append(".docx");
	else if (browserType == "pageDocerWpp")
		fileName = QObject::tr("NewWpp").append(".pptx");
	else if (browserType == "pageDocerEt")
		fileName = QObject::tr("NewEt").append(".xlsx");

	return fileName;
}

void KPromeNewDocsStartup::_uploadFiles(const QStringList& curFileList, const QString& groupId, const QString& parentId, const QString& driveId, const QString& fileId, const QString& callBack)
{
	if (curFileList.isEmpty())
		return;

	kdc::UploadCloudFilesArg arg;
	arg.groupId = toByteArray(groupId);
	arg.parentId = toByteArray(parentId);
	arg.fileId = toByteArray(fileId);
	arg.driveId = toByteArray(driveId);

	foreach(const QString& filePath, curFileList)
	{
		kdc::UploadFileParam fileParam;
		fileParam.filePath = toByteArray(filePath);
		fileParam.nameConflictMode = "overwrite";
		fileParam.showFinishedDetail = true;
		arg.fileList.push_back(fileParam);
	}

	if (kcoreApp && kcoreApp->kdcService())
		kcoreApp->kdcService()->kdcAsyncCall("uploadCloudFiles", &arg, this, QString());

	_addFileListToUploadingList(curFileList, callBack);
}

void KPromeNewDocsStartup::_startGetUploadState()
{
	if (!m_checkUploadStatusTimer.isActive())
	{
		m_checkUploadStatusTimer.start(g_checkTransferStateInterval);
	}
}

void KPromeNewDocsStartup::_getUploadState()
{
	QStringList arg = (QStringList() << QStringList(m_uploadingFiles.toList()).join("|"));
	if (kcoreApp && kcoreApp->kdcService())
		kcoreApp->kdcService()->asyncCall("getBatchUploadFileListState", arg,
			this, "onGetBatchUploadFileListState");
}

void KPromeNewDocsStartup::_stopGetUploadState()
{
	m_checkUploadStatusTimer.stop();
}

void KPromeNewDocsStartup::_addFileListToUploadingList(const QStringList& fileList, const QString& callBack)
{
	foreach(const QString & path, fileList)
	{
		int count = 0;
		QString targetPath = path;
		while (m_uploadingFiles.contains(targetPath))
		{
			targetPath = path + "&" + QString::number(++count);
		}
		m_v7NewLocalDocCallBacks.insert(targetPath, callBack);
		m_uploadingFiles.insert(targetPath);
	}
}

void KPromeNewDocsStartup::onOpenDocument(const QString& file,const QString& fileKey, const QString& from, const QString& browserType, const QString& callBack)
{
	if (ksolite::isSupported(ksolite::IntranetVersion7)
		&& !m_expectedUrl.isEmpty())
	{
		openV7TemplateDocument(from, file, browserType, callBack);
		return;
	}

	int tartgetType = KPromeNewDocsTabInfoObj::convertTabIntType(browserType);
	if (m_browserType == -1 || tartgetType == -1)
		return;

	QString filename = file;
	int type = filename.isEmpty() ?
		KxCreateDocConfig::instance().getDocerType(tartgetType) :
		KxCreateDocConfig::instance().getDocumentType(filename);

	if (file.isEmpty() && tartgetType == KPromePluginWidget::pageDocerPdf)
	{
		type = KxCreateDocConfig::instance().getDocerType(tartgetType);
		filename = "blank";
	}

	KPromePluginWidget::WidgetType plgType = (KPromePluginWidget::WidgetType)(type);
	KPromePluginWidget::WidgetType currentPageType = plgType;
	bool bCreateNew = false;
	KPromeMainWindow* mw = loadMainWindow(plgType, currentPageType, from, bCreateNew);

	if (!mw)
		return;

	if (!mw->centralArea())
		return;
	
	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;

	KPromeNewDocsPage* newDocsPage = qobject_cast<KPromeNewDocsPage *>(m_pParent);
	if(!newDocsPage)
		return;

	KPromeTab* newDocsTab = nullptr;
	if (mw->tabBar() && m_from != "templateLib_fixed_tab") // 存在在模版库打开其他类型文件时，不关闭当前tab的问题，原因是拿不到pageType
	{
		newDocsTab = mw->tabBar()->findTab(newDocsPage->initialSubPage());
	}

#ifndef Q_OS_DARWIN
	PERFLOGBEGIN2("PrometheusNewDocument",
		toOpenDocFilterStr((KPromePluginWidget::WidgetType)type, filename).constData());
#endif

	//获取当前页面下载数量是否为0，如果为0，则可关闭当前页面
	int downloadTempalteCount = 0;
	if(KPromeNewDocsPage* page = qobject_cast<KPromeNewDocsPage *>(m_pParent))
		downloadTempalteCount = page->getDownloadTemplateCount();
	//当前页面要关闭的情况是当前page页面正在下载数为0
	bool bClose = (downloadTempalteCount == 0) && 
		(!promeApp->startupInfo()->isIndependentMode() || KPromePage::isMatchMode(plgType)) && 
		!newDocsPage->noClose();
	newDocsPage->setNoClose(false);

	bool bPageWpsFromDiffrentComponent =
		(KPromePluginWidget::pageWps == plgType) &&
		(promeApp->isOfficialIndepentComponent() != (tartgetType == pageDocerOfficial) &&
		krt::kcmc::support("OfficialComponent")); // 普通文字组件新建公文,或者公文组件新建普通文字文档

	if (bClose &&
		promeApp->startupInfo()->isIndependentMode() &&
		bPageWpsFromDiffrentComponent)
	{
		bClose = false;
	}

	if (bClose)
	{
		KPromePage* page = contentArea->addPage(plgType);
		if (!page)
			return;

		if (mw->tabBar() && m_from == "templateLib_fixed_tab")	// 从固定标签应用的模版，应该创建一个新的页面显示，新建空白文档，需要判断是否是从固定标签中创建的空白文档
		{
			newDocsTab = mw->tabBar()->addTabPage(0, page->createSubPage(page->subPages()));
		}

		if (newDocsTab)
		{
			bool bMutexMultiMainWnd = false;
			promeApp->getMultiMainWindowStatus(&bMutexMultiMainWnd, nullptr);
			if (!bMutexMultiMainWnd || 0 == page->subPages()->count())
			{
				newDocsTab->setAllowReAttachSubwindow(true);
				newDocsTab->reAttachSubPage(nullptr);
			}
		}
	}

	bool changeFocusTab = true;
	if(currentPageType != KPromePluginWidget::pagePdf && KPromePage::isDocumentPage(currentPageType))
		changeFocusTab = false;

	QVariantMap infoMap;
	infoMap.insert("from", "newdocspage");
	infoMap.insert("password", fileKey);
	//是否需要切换当前tab页

	// 上云开关：非模板，三组件适用
	if (from.compare(QLatin1String("newBlankDocument")) == 0 &&
	   (plgType == KPromePluginWidget::pageWps ||
		plgType == KPromePluginWidget::pageWpp ||
		plgType == KPromePluginWidget::pageEt))
	{
		infoMap.insert("isNewCloudFile", KNewDocsHelper::isNewCloudDocSwitchOpened());
		infoMap.insert("newCloudDocSource", "create_new");
	}

	KPromePage::ProxyStrategy proxyStrategy = changeFocusTab ? 
		KPromePage::LastOneDirectExeOtherProxy : KPromePage::ProxySubPage;

	if (plgType == KPromePluginWidget::pagePdf && filename != "blank")
		mw->notifyOpenFiles(QStringList(filename), plgType, proxyStrategy, infoMap, tartgetType == pageDocerOfficial);
	else
		mw->notifyNewFiles(QStringList(filename), plgType, proxyStrategy, infoMap, tartgetType == pageDocerOfficial);

	if (bClose && m_from != "templateLib_fixed_tab")
	// 不是固定标签页创建的，要关闭标签 @warning：1. 存在从固定标签页新建空白页；2.从固定标签页应用文档
	{
		emit closeTab();
	}

	if (newDocsTab &&
		promeApp->startupInfo()->isIndependentMode() &&
		(!KPromePage::isMatchMode(plgType) || bPageWpsFromDiffrentComponent) && (downloadTempalteCount == 0))
	{
		newDocsPage->doClose();
		newDocsTab->tryClose();
	}

	if (!bCreateNew)
	{
		KPromeMainWindow* currentMw = promeApp->findRelativePromeMainWindow(this);
		if (currentMw && 
			currentMw->headerbarType() == KPromeHeaderBarBase::Standalone &&
			!KPromePage::isMatchMode(plgType) && 
			promeApp->startupInfo()->isIndependentMode())
		{
			currentMw->close();
		}
	}
}

void KPromeNewDocsStartup::onOpenRecentDocument(const QString& fileName,const QString& appName)
{
	if (m_browserType == -1)
		return;

	KPromeMainWindow* mainwWindow = promeApp->findRelativePromeMainWindow(this);
	if (!mainwWindow)
		return;

	KPromeCentralArea* contentArea = mainwWindow->centralArea();
	if (!contentArea)
		return;

	int type = KxCreateDocConfig::instance().getAppPageType(appName);
	KPromePluginWidget::WidgetType pageType = (KPromePluginWidget::WidgetType)type;
	QStringList list(fileName);
#ifndef Q_OS_DARWIN
	PERFLOGBEGIN2("PrometheusNewDocument",

	toOpenDocFilterStr(pageType, fileName).constData());
#endif

	// 多组件模式下，每个进程只对应一个appType，如果进程的appType与要打开/新建文档的type对不上，
	// 打开文档会失败，这种情况下需要走通过新建进程打开文档的逻辑。
	// mainwWindow->notifyOpenFiles()中会自行判断是否为多组件模式，以及是否需要新建进程打开文档。
	mainwWindow->notifyOpenFiles(QStringList(fileName),
		pageType, KPromePage::LastOneDirectExeOtherProxy, QVariantMap());
}

void KPromeNewDocsStartup::onExecOfficialDoc(const QString& data)
{
	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return;

	int type = KxCreateDocConfig::instance().getDocerType(m_browserType);
	KPromePluginWidget::WidgetType pageType = (KPromePluginWidget::WidgetType)type;
	if (KPromePluginWidget::pageWps == pageType)
	{
		KPromePage *pCurrentPage = mw->centralArea()->currentPage();
		KPromeTab* pCurrentTab = nullptr;
		if(mw->tabBar())
		{
			pCurrentTab = mw->tabBar()->currentTab();
		}
		KPromeNewDocsPage* pNewDocsPage = static_cast<KPromeNewDocsPage*>(pCurrentPage);
		if (pNewDocsPage)
			pNewDocsPage->doClose();
		if (pCurrentTab)
			pCurrentTab->tryClose();
	}

	if (data.isEmpty())
		return;

	mw->notifyKsoWebStartup(KPromePluginWidget::pageWps, data);
}

void KPromeNewDocsStartup::onSubPageActived()
{
	if (m_browser)
		m_browser->notifyMovingOrResizing();
	else if (m_webwidget && m_webwidget->getWebview())
		m_webwidget->getWebview()->notify(KXWEBVIEW_NOTIFY_MOVE_OR_RESIZE_START);
}

void KPromeNewDocsStartup::onOpenPromeBrowser(const QString& url)
{
	KxCreateDocConfig::instance().openPromeBrowser(url, true);
}

DECLARE_PLUGIN_WIDGET_FACTORY(pageDocerWps, KPromeNewDocsStartup);
DECLARE_PLUGIN_WIDGET_FACTORY(pageDocerOfficial, KPromeNewDocsStartup);
DECLARE_PLUGIN_WIDGET_FACTORY(pageDocerEt, KPromeNewDocsStartup);
DECLARE_PLUGIN_WIDGET_FACTORY(pageDocerWpp, KPromeNewDocsStartup);
DECLARE_PLUGIN_WIDGET_FACTORY(pageDocerPdf, KPromeNewDocsStartup);
DECLARE_PLUGIN_WIDGET_FACTORY(pageMytpl, KPromeNewDocsStartup);
