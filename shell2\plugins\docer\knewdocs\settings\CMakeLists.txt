set(assets_enterprise_pdf_css_files
	enterprise/pdf/css/app.css
	)
	
set(assets_enterprise_pdf_js_files
	enterprise/pdf/js/0.js
	enterprise/pdf/js/1.js
	enterprise/pdf/js/2.js
	enterprise/pdf/js/3.js
	enterprise/pdf/js/app.js
	enterprise/pdf/js/manifest.js
	enterprise/pdf/js/vendor.js
	)
	
set(assets_enterprise_pdf_img_files
	enterprise/pdf/images/advertisement.svg
	enterprise/pdf/images/pdf_ad_icon.svg
	enterprise/pdf/images/pdf_combine.svg
	enterprise/pdf/images/pdf_new_blank_file.svg
	enterprise/pdf/images/pdf_new_file.svg
	enterprise/pdf/images/pdf_new_scan_file.svg
	enterprise/pdf/images/pdf_open_file.svg
	enterprise/pdf/images/pdf_split.svg
	
	enterprise/pdf/images/pdf2excel.svg
	enterprise/pdf/images/pdf2photo.svg
	enterprise/pdf/images/pdf2ppt.svg
	enterprise/pdf/images/pdf2word.svg
	enterprise/pdf/images/pdfcompress.svg
	enterprise/pdf/images/photo2pdf.svg
	enterprise/pdf/images/translateAll.svg
	)

set(enterprise_pdf_root_files
	enterprise/pdf/index.html
	enterprise/pdf/config.ini
	)
	

	
set(assets_pdf_css_files
	pdf/css/app.css
	)

set(assets_pdf_img_files
	pdf/images/pdf_ad_icon.svg
	pdf/images/pdf_combine.svg
	pdf/images/pdf_new_blank_file.svg
	pdf/images/pdf_new_file.svg
	pdf/images/pdf_new_scan_file.svg
	pdf/images/pdf_open_file.svg
	pdf/images/pdf_split.svg
	pdf/images/pdf2excel.svg
	pdf/images/pdf2photo.svg
	pdf/images/pdf2ppt.svg
	pdf/images/pdf2word.svg
	pdf/images/pdfcompress.svg
	pdf/images/photo2pdf.svg
	pdf/images/advertisement.svg
	pdf/images/translateAll.svg
	)

set(pdf_root_files
	pdf/index.html
	pdf/config.ini
	)
	
set(assets_pdf_js_files
	pdf/js/0.js
	pdf/js/1.js
	pdf/js/2.js
	pdf/js/3.js
	pdf/js/app.js
	pdf/js/manifest.js
	pdf/js/vendor.js
	)


wps_package(knewdocssettings)
	# personal
	# 由于非个人版的不介入，先注释
	# wps_add_frontend_resource(
	#	error-net.html
	#	LOCATION ${WPS_FRONTEND_DIRECTORY}/kwebpc_knewdocs
	#	BUILD_DEST_PATH office6/addons/knewdocs/res/componenterrorpage
	# )
	wps_add_frontend_resource(
		kwebpc_knewdocs
		BUILD_DEST_PATH office6/addons/knewdocs/res/personal
		)

	wps_add_resources(
		config.ini
		BUILD_DEST_PATH office6/addons/knewdocs/res/
		)
if(NOT OS_DARWIN)
	wps_add_resources(
		${assets_enterprise_pdf_img_files}
		${assets_enterprise_pdf_js_files}
		${assets_enterprise_pdf_css_files}
		${enterprise_pdf_root_files}
		BUILD_DEST_PATH office6/addons/knewdocs/res
		)

	wps_add_resources(
		${assets_enterprise_ct_img_files}
		${assets_enterprise_ct_js_files}
		${assets_enterprise_ct_css_files}
		${assets_enterprise_ct_fonts_files}
		${enterprise_ct_root_files}
		BUILD_DEST_PATH office6/addons/knewdocs/res
		)
		
	wps_add_resources(
		${assets_pdf_img_files}
		BUILD_DEST_PATH office6/addons/knewdocs/res
		WPS_ADD_INSTALL LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/res/pdf/images"
		)
	wps_add_resources(
		${assets_pdf_js_files}
		BUILD_DEST_PATH office6/addons/knewdocs/res
		WPS_ADD_INSTALL LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/res/pdf/js"
		)
	wps_add_resources(
		${assets_pdf_css_files}
		BUILD_DEST_PATH office6/addons/knewdocs/res
		WPS_ADD_INSTALL LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/res/pdf/css"
		)
	wps_add_resources(
		${pdf_root_files}
		BUILD_DEST_PATH office6/addons/knewdocs/res
		WPS_ADD_INSTALL CATEGORY LINUX_COMMUNITY LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/res"
		)
	wps_add_frontend_resource(
		kwebpdf_newdoc
		BUILD_DEST_PATH office6/addons/knewdocs/res/pdf_standalone
	)
endif()

	wps_add_frontend_resource(
		kwebwps_onlinekotl
		BUILD_DEST_PATH office6/addons/knewdocs/res/kotl
	)

	wps_add_frontend_resource(
		kwebnew_file_v3
		BUILD_DEST_PATH office6/addons/knewdocs/res/enterprise
		WPS_ADD_INSTALL SETOUTPATH "office6/addons/knewdocs/res/enterprise"
	)

	wps_add_frontend_resource(
		kwebnew_file_v3_2019
		BUILD_DEST_PATH office6/addons/knewdocs/res/enterpriseOld
		WPS_ADD_INSTALL SETOUTPATH "office6/addons/knewdocs/res/enterpriseOld"
	)
wps_end_package()
