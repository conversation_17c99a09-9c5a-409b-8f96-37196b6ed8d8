﻿#include "stdafx.h"
#include "knewdocjsapi.h"
#include "knewdocjsapihelper.h"
#include "knewdochelper.h"
#include "utility/kxsettings.h"
#include "utility/knewdocshelper.h"
#include "kpromenewdocsstartup.h"
#include <kprometheus/kpromemainwindow.h>
#include <kprometheus/kpromepage.h>
#include <kprometheus/kpromecentralarea.h>
#include <kprometheus/kprometab.h>
#include <kprometheus/kprometabbar.h>
#include <kprometheus/kpromecloudfilemgr.h>
#include <kprometheus/kpromecloudsvrproxy.h>
#include <kprometheus/kpromeinfocollcethelper.h>
#include <kprometheus/kpromeskin.h>
#include <ksolite/kdcinfoc.h>
#include <ksolite/kwebview.h>
#include <kliteui/kfilefilter.h>
#include <krt/dirs.h>
#include <krt/kentrycontrol/kentrycontrolproxy.h>
#include "kdocerbasehelper.h"
#include "ksolite/kpath.h"
#include <auth/productinfo.h>
#include <utilities/path/module/kcurrentmod.h>
#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"
#include "kdocertoolkitlite/kdocercommonhttpaccess.h"
#include <ksolite/kpluginmanager.h>
#include <ksolite/kplugin.h>
#include <ksolite/db.h>
#include <kdocertoolkitlite/kcustompropshelper.h>
#include <kprometheus/kpromestartupcounter.h>
#include <ksolite/kskinimageschemehandler.h>
#include <ksolite/kcoreapplication.h>
#include "kprometheus/kpromeevents.h"
#include "kdocerprocessonlib/kprocessonserversdk.h"
#include "kdocerbasehelper.h"
#include "ugcresource/cloudfile/kxcloudfileproxylib.h"
#include "ksolite/ikdcsvrproxy.h"
#ifdef Q_OS_LINUX
#include <sys/statfs.h>
#endif
#ifdef Q_OS_DARWIN
#include <sys/mount.h>
#endif

#include "kpromenewdocspage.h"
#include <kcomctl/tik.h>
#include "jsapi/handlers/kdocerjsutilhandler.h"
#include "tabbar/kpromenewdocstabbarconfig.h"
#include "ksolite/kernel/kcoreapplication.h"
#include "ksolite/kdomainmgr/kdomainmgr.h"
#include <ksolite/knetworkstate.h>
#include <kdocerfunctional.h>
#include "ksolite/kconfigcentersettings.h"
#include <ksolite/ksupportutil.h>
#include "kclouddocs/kclouddocspagepreload.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "krt/kconfigmanagercenter.h"

void CreateDocSendCollectInfo(QString p2, QString p3, QString p4 = QString(), QString p5 = QString(), QString p6 = QString());

namespace
{
	constexpr const char* g_methodResult = "result";
	constexpr const char* g_templateTableName = "localstorage_template";
	constexpr const char* g_downloadCanceledCallback = "downloadCanceled";
	constexpr const char* g_skinChangedCallback = "onSkinChanged";
	
	constexpr const char* ReturnCode = "error_code";
	constexpr const char* Data = "data";
	constexpr const char* NetworkErrorCode = "network_error_code";
	constexpr const char* NetworkStartTime = "network_start_time";
	constexpr const char* NetworkEndTime = "network_end_time";

	constexpr const char* s_strDownloadProgressJsCallBack = "updateDownloadPercent";
	constexpr const char* s_strDownloadSuccessJsCallBack = "downloadComplete";
	constexpr const char* s_strDownloadErrorJsCallBack = "downloadFailed";
	constexpr const char* g_copilotEntryPlugin = "kcopilotentrylite";
	constexpr const char* g_copilotEntryDll = "kcopilotentrylite";
	constexpr const char* g_knewdocsNewAiEntryId = "wpp_knewdocsNewAi_visible";
	constexpr const char* g_unknown = "unknown";
	constexpr const char* g_newDosPageIdentifier = "newdocspage";
	constexpr const char* g_downloadFont = "downloadFont";

	constexpr const char* g_str_event_getworkspacestatus_finish = "event.getworkspacestatus.finish";
	constexpr const char* g_str_event_entrystatus_update = "event.entrystatus.update";
	constexpr const char* g_str_src = "knewdocs";

	constexpr unsigned int INT_DISK_FREE_SIZE = 300000;
	
	constexpr const char* const k_openFrom = "from";
	constexpr const char* const k_fileId = "File_ID";
	constexpr const char* const k_isUnusePrecreatedPage = "is_unuse_precreated";

	enum ErrorCode
	{
		SUCCESS = 0,
		INVALID_JSON,
		EMPTY_FUNCTION,
		FUNCTION_NOT_EXSIT,
		PARAM_COUNT_UNMATCH,
		LOST_PARAM_NAME,
		INVALID_PARAM_VALUE,
		NETWORK_ERROR,
		NETWORK_TIMEOUT,
		UNKNOW_ERROR
	};
	
	const QString getLocalPath(const QString& url, const QString& name)
	{
		QDir dir(krt::dirs::downloadTemplates());
		if (!dir.exists())
			dir.mkpath(dir.path());
		QString filePath = url;
		int pos = filePath.indexOf("?");
		if (pos > 0)
			filePath.truncate(pos);

		QString fSuffix = QFileInfo(filePath).suffix();
		QString suffix = fSuffix.isEmpty() ? "" : "." + QFileInfo(filePath).suffix();
		if (name.right(suffix.length()) == suffix)
			suffix = "";

		return QDir::toNativeSeparators(dir.path() + "/" + QFileInfo(filePath).baseName() + "/" + name + suffix);
	}

	KxWebViewSchemeHandler* getSchemeHandler()
	{
		static KSkinImageSchemeHandler handler;
		return &handler;
	}

	QString getTartgetBrowserType(const QString& argsType)
	{
		if (argsType.isEmpty())
			return QString();
		if (argsType == "wps")
			return KPromeNewDocsTabInfoObj::convertTabIntType(KPromePluginWidget::pageDocerWps);
		else if (argsType == "wpp")
			return KPromeNewDocsTabInfoObj::convertTabIntType(KPromePluginWidget::pageDocerWpp);
		else if (argsType == "et")
			return KPromeNewDocsTabInfoObj::convertTabIntType(KPromePluginWidget::pageDocerEt);
		else if (argsType == "pdf")
			return KPromeNewDocsTabInfoObj::convertTabIntType(KPromePluginWidget::pageDocerPdf);
		else if (argsType == "form")
			return KPromeNewDocsTabInfoObj::convertTabIntType(KPromePluginWidget::widgetWpsForm);
		else if (argsType == "otl")
			return KPromeNewDocsTabInfoObj::convertTabIntType(KPromeNewDocsTabInfoObj::pageKOtl);
		else if (argsType == "dbt")
			return KPromeNewDocsTabInfoObj::convertTabIntType(KPromeNewDocsTabInfoObj::pageKDbSheet);
		else if (argsType == "ksheet")
			return KPromeNewDocsTabInfoObj::convertTabIntType(KPromeNewDocsTabInfoObj::pageKSheet);
		return QString();
	}

	QString getTargetArgsType(const QString& browserType)
	{
		if (browserType.isEmpty())
			return QString();
		if (browserType == "pageDocerWps")
			return "wps";
		else if (browserType == "pageDocerWpp")
			return "wpp";
		else if (browserType == "pageDocerEt")
			return "et";
		else if (browserType == "pageDocerPdf")
			return "pdf";
		else if (browserType == "widgetWpsForm")
			return "form";
		else if (browserType == "pageKOtl")
			return "otl";
		else if (browserType == "pageKDbSheet")
			return "dbt";
		else if (browserType == "pageKSheet")
			return "ksheet";
		return QString();
	}

	bool isPCSupportDOPFunc()
	{
		static bool bIsSupport([]() -> bool
			{
				return krt::product::getFuncSwitchState("isPCSupportDOPFunc", true);
			}());

		return bIsSupport;
	}

	QString getAccountType()
	{
		if (!kcoreApp || !kcoreApp->getAccountSDK())
			return g_unknown;

		auto account = kcoreApp->getAccountSDK()->getAccount();
		if (!account)
			return g_unknown;

		kacctoutsdk::AccountType accountType = account->getAccountType();
		if (kacctoutsdk::AccountType::Company == accountType)
			return "company";
		else if (kacctoutsdk::AccountType::Personal == accountType)
			return "personal";
		else if (kacctoutsdk::AccountType::Combination == accountType)
			return "combination";

		return g_unknown;
	}

	QString appendExtendParam(const QString& url, const QString& from)
	{
		if (url.isEmpty())
		{
			return url;
		}

		QUrl targetUrl(url);
		if (!targetUrl.isValid())
		{
			return QString();
		}

		QUrlQuery urlQuery(targetUrl.query());
		urlQuery.addQueryItem("createEndTime",
			QString::number(QDateTime::currentMSecsSinceEpoch()));
		if(urlQuery.hasQueryItem(k_openFrom))
			urlQuery.addQueryItem("from2", from);
		else
			urlQuery.addQueryItem(k_openFrom, from);
		targetUrl.setQuery(urlQuery);
		return targetUrl.toString();
	}

	const KWorkspaceCompanyInfo* getWorkspaceCompanyInfo()
	{
		if (nullptr == kcoreApp || nullptr == kcoreApp->getAccountSDK())
			return nullptr;

		if (nullptr == kcoreApp->getAccountSDK()->getAccount())
			return nullptr;

		if (nullptr == promeApp->cloudSvrProxy())
			return nullptr;

		auto account = kcoreApp->getAccountSDK()->getAccount();
		auto cloudsvr = promeApp->cloudSvrProxy();

		return cloudsvr->getWorkspaceCompanyInfo(account->getCompanyId());
	}

	void writeDownloadFontConfig(bool downloadFont)
	{
		//未主动下载字体时无需处理
		if (!downloadFont)
			return;

		QVariantMap mapConfig;
		mapConfig.insert("entryName", "knewdocs_theme_template");
		mapConfig.insert("triggerTimeMs", QDateTime::currentMSecsSinceEpoch());
		mapConfig.insert("maxIntervalMs", 15000);

		KCCPluginsSettings plgSettings(true);
		plgSettings.beginGroup("kdocercore");
		QString strConfig = JsonHelper::variantMapSerialize(mapConfig);
		plgSettings.setValue("ondocumentadded", strConfig, true);
		plgSettings.endGroup();
	}

	KPromePage* findExistingCloudDocsPage(KPromeCentralArea* area)
	{
		if (!area)
			return nullptr;

		QList<KPromePage*> pages = area->pages();
		for (const auto& page : pages)
		{
			if (!page || KPromePluginWidget::pageKDocsPreview != page->type())
				continue;

			if (page->property(k_isUnusePrecreatedPage).toBool()
				&& page->property(k_fileId).toString().isEmpty())
			{
				page->setProperty(k_isUnusePrecreatedPage, false);
				KxLoggerLite::writeInfo(L"knewdocs",
					QString("findExistingCloudDocsPage is_precreated_unuse").arg(__FUNCTION__).toStdWString());
				return page;
			}
		}

		return nullptr;
	}

	bool isOnlineDocSupport(const QString& suffix)
	{
		static QSet<QString> extraOnlineDocSuffixTable{ "otl", "ksheet", "dbt" };
		return extraOnlineDocSuffixTable.contains(suffix);
	}
}

// ============================================================================
ksolite::KxCommonJsApi* createCreateDocJsApiFunc(KxWebViewContainer* webView)
{
	return new KxNewDocJsApiBase(webView, true);
}

KxNewDocJsApiBase::KxNewDocJsApiBase(KxWebViewContainer* webView, bool bNewWindow /*= false*/)
	: KNewDocPageJsApiBase(webView)
	, m_loginTimeStamp(0)
	, m_bNewWindow(bNewWindow)
{
	KxNewDocJsApiHelper::instance().addApi(this);
	m_cookieJar = new KxNetworkCookieJar(this->m_network.data());
	this->m_network->setCookieJar(m_cookieJar);

	if (auto p = kcoreApp->cloudService())
		connect(p, SIGNAL(userInfoChange(int)), this, SLOT(onUserInfoChange(int)));

	if (webView)
	{
		KxWebViewScheme scheme = {
			"skinimg",
			"",
			getSchemeHandler()
		};
		webView->addCustomScheme(scheme);
	}
}

KxNewDocJsApiBase::~KxNewDocJsApiBase()
{
	KxNewDocJsApiHelper::instance().removeApi(this);
}

void KxNewDocJsApiBase::boardcastCustomMessage(KxWebViewJSContext& context)
{
	KxNewDocJsApiHelper::instance().boardcastCustomMessage(this, context);
}

QWidget* KxNewDocJsApiBase::widgetParent()
{
	QWidget* webView = m_container->getWebView();
	if (webView)
		return webView->window();
	else
		return promeApp->currentPromeMainWindow();
}

ksolite::KxCommonWebDialog* KxNewDocJsApiBase::createNewUrlWidget(QWidget* parent, int nWidth, int nHeight, bool bModal, bool bCloseBtn)
{
	return new ksolite::KxCommonWebDialog(createCreateDocJsApiFunc, parent, nWidth, nHeight, bModal, bCloseBtn);
}

void KxNewDocJsApiBase::httpGet(KxWebViewJSContext& context)
{
	m_cookieJar->removeAllCookies();

	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	QNetworkRequest netRequest = getNetRequest(args, QNetworkAccessManager::GetOperation);
	QNetworkReply* netReply = m_network->get(netRequest);
	httpAction(netReply, args, "get");
}

void KxNewDocJsApiBase::httpPost(KxWebViewJSContext& context)
{
	m_cookieJar->removeAllCookies();

	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	QByteArray postData;
	if (args.value("bRawData", false).toBool())
	{
		postData = args.value("data").toString().toUtf8();
	}
	else
	{
		QMap<QString, QVariant> mapData = args.value("data").toMap();
		postData = organizePostData(mapData);
	}

	QNetworkRequest netRequest = getNetRequest(args, QNetworkAccessManager::PostOperation);
	QNetworkReply* netReply = m_network->post(netRequest, postData);

	httpAction(netReply, args, "post");
}

void KxNewDocJsApiBase::httpAction(QNetworkReply* netReply, const QVariantMap& args, const QString& httpMethod/* = "get"*/)
{
	QString callback = args.value("callback").toString();
	QString type = args.value("type", "json").toString();
	int timeout = args.value("timeout").toInt();
	KDocerCommonHttpAccess* http = new KDocerCommonHttpAccess(netReply, callback, type, timeout);
	http->setHttpMethod(httpMethod);
	connect(http, SIGNAL(done(KDocerCommonHttpAccess*)), this, SLOT(onHttpAccessDone(KDocerCommonHttpAccess*)));

	addHttpAccess(http);
	http->go();
}

quint64 KxNewDocJsApiBase::getDiskSize(const QString& strDirName)
{
#ifdef Q_OS_WIN
	if (!QDir(strDirName).exists())
		return 0u;

	ULARGE_INTEGER lFreeSpace = {0};
	BOOL bRet = ::GetDiskFreeSpaceExW(krt::utf16(strDirName), &lFreeSpace, NULL, NULL);
	K_ASSERT(bRet);
	return lFreeSpace.QuadPart;
#else
	struct statfs x;
	QByteArray path = strDirName.toLocal8Bit();
	if (statfs(path.constData(), &x) == 0)
	{
		return (quint64)x.f_bsize * (quint64)x.f_bavail;
	}
	return 0;
#endif
}

void KxNewDocJsApiBase::onUserInfoChange(int)
{
	m_loginTimeStamp = QDateTime::currentMSecsSinceEpoch();
}

void KxNewDocJsApiBase::onVipInfoChange(const QVariantMap& newVipInfo)
{
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;
	if (account)
		isLogined = account->isLogined();
	if (!isLogined)
		return;
	qint64 t = QDateTime::currentMSecsSinceEpoch();
	if (abs(t - m_loginTimeStamp) > 1000)
	{
		QString strInfor;
		for (auto it = newVipInfo.begin(); it != newVipInfo.end(); ++it)
		{
			if (it.key() == "byPush")
				continue;
			strInfor += it.key();
			strInfor += it.value().toString();
		}

		if (m_lastVipInfo != strInfor)
		{
			callbackToJS("onVipInfoChange", "");
			m_lastVipInfo = strInfor;
		}
	}
}

void KxNewDocJsApiBase::getResouceDownloadDiskInfo(KxWebViewJSContext& context)
{
	QString downloadDir = krt::dirs::downloadTemplates();
	quint64 diskSize = getDiskSize(downloadDir);
	QVariantMap variantMap;
	variantMap["path"] = downloadDir;
	variantMap["size"] = QString::number(diskSize);
	variantMap["full"] = "1";
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty())
	{
		qlonglong resSize = args.value("size").toLongLong();
		if (diskSize <= (quint64)resSize || diskSize < INT_DISK_FREE_SIZE)
		{
			variantMap["full"] = "0";

			//埋点
			QString resId = args.value("id").toString();
			QHash<QString, QString> args;
			args.insert("mod_name", kcoreApp->applicationName());
			args.insert("app_id", KPluginName);
			args.insert("down_result", "error");
			args.insert("down_error_msg","disk size is full");
			args.insert("res_id", resId);
			args.insert("res_type","template");
			args.insert("res_size", QString::number(resSize));
			args.insert("samplerate", "1.0");
			args.insert("disk_size", QString::number(diskSize));
			KDocerUtils::postGeneralEvent("docer_common_res_download", args, 0, 0, KDC_All);
		}
	}
	setResult(context, variantMap);
}

QString KxNewDocJsApiBase::getLocalStoragePath() const
{
	if (!m_bNewWindow)
		return KDocerUtils::getLocalStoragePath("newdocs");
	return KDocerUtils::getLocalStoragePath(QString());
}

void KxNewDocJsApiBase::onHttpAccessDone(KDocerCommonHttpAccess* httpAccess)
{
	handleHttpAccess(httpAccess);
	releaseHttpAccess(httpAccess);
}

void KxNewDocJsApiBase::addHttpAccess(KDocerCommonHttpAccess* httpAccess)
{
	if (!httpAccess)
		return;

	if (!m_vecHttpAccess.contains(httpAccess))
		m_vecHttpAccess.push_back(httpAccess);
}

void KxNewDocJsApiBase::releaseHttpAccess(KDocerCommonHttpAccess* httpAccess)
{
	int index = m_vecHttpAccess.indexOf(httpAccess);
	if (index != -1)
	{
		m_vecHttpAccess.remove(index);
		httpAccess->deleteLater();
	}
}

void KxNewDocJsApiBase::handleHttpAccess(KDocerCommonHttpAccess* httpAccess)
{
	if (!httpAccess)
		return;

	QString callback = httpAccess->callback();
	if (callback.isEmpty())
		return;

	ErrorCode errorcode = SUCCESS;
	if (httpAccess->isTimeout())
		errorcode = NETWORK_TIMEOUT;
	if (httpAccess->isNetworkError())
		errorcode = NETWORK_ERROR;

	if (httpAccess->type() == "json")
	{
		QJsonObject retObj;
		QJsonObject resultObj;
		QJsonObject dataObj = JsonHelper::convertByteArrayToQJson(httpAccess->rawResult());
		if (dataObj.isEmpty())
		{
			QJsonArray dataArray = JsonHelper::convertByteArrayToQJsonArray(httpAccess->rawResult());
			if (dataArray.isEmpty())
			{
				errorcode = INVALID_JSON;
			}	
			else
			{
				resultObj[Data] = dataArray;
			}
		}
		else
		{
			resultObj[Data] = dataObj;
		}
		resultObj[ReturnCode] = errorCodeString(errorcode);
		resultObj[NetworkErrorCode] = httpAccess->networkErrorCode();
		resultObj[NetworkStartTime] = httpAccess->getTimeStamp();
		resultObj[NetworkEndTime] = httpAccess->getEndTimeStamp();
		retObj["result"] = resultObj;
		QString result = JsonHelper::convertQJsonToString(retObj);
		KxLoggerLite::writeInfo(L"knewdocs", result.toStdWString());
		callbackToJS(callback, result);
	}
	else
	{
		if (errorcode == SUCCESS)
		{
			callbackToJSRaw(callback, httpAccess->rawResult());
		}
		else
		{
			QVariantMap result;
			result.insert(ReturnCode, errorCodeString(errorcode));
			result.insert(NetworkErrorCode, httpAccess->networkErrorCode());
			result.insert(NetworkStartTime, httpAccess->getTimeStamp());
			result.insert(NetworkEndTime, httpAccess->getEndTimeStamp());
			callbackToJS(callback, formatResult(g_methodResult, result));
		}
	}
}

void KxNewDocJsApiBase::isDarkSkin(KxWebViewJSContext& context)
{
	setResult(context, (QVariant)KNewDocDrawHelper::isDarkSkin());
}

void KxNewDocJsApiBase::getCurrentSkinName(KxWebViewJSContext& context)
{
	QVariantMap resultMap;
	resultMap.insert("skinName", KNewDocsHelper::getNonAdaptedWebSkinName());
	context.Result["result"] = JsonHelper::variantMapToJsonValue(resultMap);
}

void KxNewDocJsApiBase::openDocerPage(KxWebViewJSContext& context)
{
	QString action, param;
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty())
	{
		action = args.value("action").toString();
		QVariantMap variantMapParam = args.value("params", "").toMap();
		QJsonDocument docParam = QJsonDocument::fromVariant(QVariant(variantMapParam));
		QByteArray byteParam = docParam.toJson();
		param = QString(byteParam);
	}
	if (action.isEmpty())
		return;

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(widgetParent());
	if (!mw)
		return;

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;

	KPromePage* page = nullptr;
	auto pages = contentArea->pages();
	for (int i = 0; i < pages.size(); ++i)
	{
		if (KPromePluginWidget::pageDocer == pages.at(i)->type())
		{
			page = pages.at(i);
			break;
		}
	}

	if (nullptr == page)
		page = contentArea->addPage(KPromePluginWidget::pageDocer);

	if (nullptr == page)
		return;

	contentArea->setCurrentPage(page);
	page->newDocument();

	QMetaObject::invokeMethod(page, "commonDocerPageShowDetail"
		, Qt::QueuedConnection
		, Q_ARG(QString, action)
		, Q_ARG(QString, param));
}

// ============================================================================
KxNewDocJsApi::KxNewDocJsApi(KPromeNewDocsStartup* window, KxWebViewContainer* webView)
	: KxNewDocJsApiBase(webView)
	, m_window(window)
{
	connect(window, SIGNAL(resizeWindow()), this, SLOT(onResizeWindow()));
	connect(promeApp->promeSkin(), SIGNAL(skinChanged()), this, SLOT(onSkinChanged()));
	connect(promeApp->cloudSvrProxy(), &KPromeCloudSvrProxy::sigGetWorkspaceStatusFinished,
		this, &KxNewDocJsApi::notifyGetWorkspaceStatusFinished);
	connect(promeApp->cloudSvrProxy(), &KPromeCloudSvrProxy::loginStatusChanged,
		this, &KxNewDocJsApi::notifyEntryStatusUpdate);

	initCopilotEntry();
}

KxNewDocJsApi::~KxNewDocJsApi()
{
	clearWatchToEntry();
	clearAllDownloadTask();
}

void KxNewDocJsApi::notifyGetWorkspaceStatusFinished(bool bCurWorkspaceChanged,
		const QString& requestId,
		bool isCompanyAccountInMultiAccount,
		const QString& loginSource)
{
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	
	if (requestId.isEmpty() || !account || !account->isCompanyAccount())
		return;

	QString eventName(g_str_event_getworkspacestatus_finish);
	QJsonObject result;
	const KWorkspaceCompanyInfo* info = getWorkspaceCompanyInfo();
	if (nullptr != info)
	{
		result["url"] = info->logo;
		result["companyName"] = info->name;
		result["companyId"] = info->id;
	}
	result["eventName"] = eventName;
	eventHandler()->broadcastEventToCurrentWebview(
		eventName,
		JsonHelper::convertQJsonToString(result));
}

void KxNewDocJsApi::onV7NewDoc(const QString& callBack, const QVariantMap& result)
{
	QString tarResult = formatResult(result);
	if (!callBack.isEmpty())
		callbackToJS(callBack, tarResult, false);
}

void KxNewDocJsApi::addWatchToEntry(KxWebViewJSContext& context)
{
	clearWatchToEntry();
	QVariantMap paramsMap = parseContextArgs(context);
	QStringList entryPositions = paramsMap.value("entryPositions").toStringList();
	for (const QString& entryPosition : entryPositions)
	{
		krt::kentrycontrol::addWatch({ entryPosition, this, "notifyUpdate" });
		m_entryWatchList[entryPosition] = krt::kentrycontrol::isEntryNeedHide(entryPosition);
	}
	context.setDefaultPublishResult(true);
}

void KxNewDocJsApi::clearWatchToEntry()
{
	for (const auto& entry : m_entryWatchList.keys())
		krt::kentrycontrol::removeWatch({ entry, this, "notifyUpdate" });

	m_entryWatchList.clear();
}

void KxNewDocJsApi::notifyUpdate(bool isControled, const QString& entryPosition, const QString& reason)
{
	if (m_entryWatchList.isEmpty() || !m_entryWatchList.contains(entryPosition))
		return;

	bool enableNotifyUpdate = false;
	for (const auto& key : m_entryWatchList.keys())
	{
		if (m_entryWatchList[key] != krt::kentrycontrol::isEntryNeedHide(key))
		{
			enableNotifyUpdate = true;
			break;
		}
	}

	if (enableNotifyUpdate)
		notifyEntryStatusUpdate();
}

void KxNewDocJsApi::notifyEntryStatusUpdate()
{
	if (m_entryWatchList.isEmpty())
		return;
	
	if (nullptr == promeApp)
		return;

	auto accountSdk = promeApp->getAccountSDK();

	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;
	if (account)
		isLogined = account->isLogined();
	if (nullptr == accountSdk || (accountSdk->isSwitching() && !isLogined))
		return;

	QJsonObject controlStatus;
	for (const auto& key : m_entryWatchList.keys())
	{
		bool hideStatus = krt::kentrycontrol::isEntryNeedHide(key);
		m_entryWatchList[key] = hideStatus;
		controlStatus[key] = hideStatus;
	}

	auto handler = eventHandler();
	if (nullptr != handler)
		handler->broadcastEventToCurrentWebview(
			g_str_event_entrystatus_update, JsonHelper::convertQJsonToString(controlStatus));
}

void KxNewDocJsApi::init()
{
	createLocalTemplateTable();
	KDocerCommonJsApi::init();
}

void KxNewDocJsApi::getRecommendedFiles(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QList<QVariant> result;

	if (!args.isEmpty())
	{
		int fileLen = args.value("maxCount", 10).toInt();
		QString fileType = args.value("appName", m_window->getBrowserType()).toString();
		fileType = fileType.toLower();

		promeApp->recentFileProxy()->loadRecentRecords(Record_AllFile);
		//最多取前100条记录
		RecordInfoList fileList = promeApp->recentFileProxy()->recentRecords(Record_AllFile, 100);
		auto currentComponentType = m_window->getBrowserRawType();
		KPromePluginWidget::WidgetType widgetType = KPromePluginWidget::promePluginWidgetEnd;
		switch (currentComponentType)
		{
		case KPromePluginWidget::pageDocerEt:
			widgetType = KPromePluginWidget::pageEt;
			break;
		case KPromePluginWidget::pageDocerWpp:
			widgetType = KPromePluginWidget::pageWpp;
			break;
		case KPromePluginWidget::pageDocerWps:
			widgetType = KPromePluginWidget::pageWps;
			break;
		default:
			break;
		}
		QStringList openFileTabTextList = KxNewDocJsApiHelper::instance().openFileTabText(this, widgetType);
		int resultcnt = 0;
		foreach(const RecordInfo file, fileList)
		{
			if (fileType != "all" && !(fileType.contains(file.m_appType)))
				continue;
			QMap<QString, QVariant> propMap;
			//propMap["id"] = file.m_fileId;
			propMap["name"] = file.m_fileName;
			propMap["isCurrentOpen"] = openFileTabTextList.contains(file.m_fileName) ? "1" : "0";
			int time = file.m_fileAccessTime.toMSecsSinceEpoch() / 1000;
			propMap["time"] = time;
			result.append(propMap);

			resultcnt++;
			if (resultcnt >= fileLen)
				break;
		}
	}

	//发送的明文：[{"name":"这里传文件名，过滤掉路径","time":数字（秒）},{"name":"这里传文件名，过滤掉路径","time":数字（秒）},....]
	//在此基础上整体加密
	QString rawValue = "[]";
	if (!result.isEmpty())
	{
		Json::Value jsonValue = ConvertJsonHelper::variantToJson(result);
		rawValue = ConvertJsonHelper::jsonSerialize(jsonValue);
	}
	QVariantMap retMap;
	auto ikdocerBase = getIKDocerBase();
	if (ikdocerBase)
		ikdocerBase->encryptString(rawValue, retMap);
	setResult(context, retMap);
}

void KxNewDocJsApi::userProfession(KxWebViewJSContext& context)
{
	KxNewDocsSettings newfileSettings;
	setResult(context, newfileSettings.value("profession", "other"));
}

void KxNewDocJsApi::newBlankDocument(KxWebViewJSContext& context)
{
	const auto args = parseContextArgs(context);
	newBlankDocument(args, context.getValue("callback", "").toString());
}

void KxNewDocJsApi::openRecentFile(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty())
	{
		const QString& fileName  = args.value("fileName").toString();
		const QString& fileId    = args.value("fileId").toString();
		int fileType			 = args.value("fileType").toInt();
		const QString& appName   = args.value("appName").toString();

		KPromeMainWindow *mainWindow = promeApp->findRelativePromeMainWindow(this);
		KPromePage *pCurrentPage = NULL;
		QPointer<KPromeTab> pCurrentTab;
		if (mainWindow)
		{
			pCurrentPage = mainWindow->centralArea()->currentPage();
			if (mainWindow->tabBar())
				pCurrentTab = mainWindow->tabBar()->currentTab();

			if (fileType == LocalFile && !QFile::exists(fileName))
			{
				QString str = tr("Unable to open \"%1\".\nThis file may have been renamed, deleted or moved.")
									.arg(fileName);
				QMessageBox::warning(mainWindow, tr("WPS Office"), str);
				return;
			}

			KPromeNewDocsPage* pNewDocsPage = static_cast<KPromeNewDocsPage*>(pCurrentPage);
			if (!pNewDocsPage)
			{
				for(int index = 0, count = pNewDocsPage->subPages()->count(); index < count; index++)
				{
					KPromeSubPage *promeSubPage = pNewDocsPage->subPages()->subPageAt(index);
					promeSubPage->setCloseMode(KPromeSubPage::CloseMode_ChangePage);
					promeSubPage->close();
				}
				pNewDocsPage->doClose();
				mainWindow->centralArea()->activeCurrentPage();
			}
		}

		if (fileType == CloudFile && userLogined())
		{
			QVariantMap cloudFileInfo;
			cloudFileInfo.insert("fileid", fileId);
			cloudFileInfo.insert("fileName", fileName);
			cloudFileInfo.insert("historyid", "");
			cloudFileInfo.insert("from", "newdocspage");
			cloudFileInfo.insert("appName", appName);
			if (mainWindow)
				promeApp->cloudFilesMgr()->downloadAndOpen(cloudFileInfo, mainWindow);
			if (userLogined())
				promeApp->recentFileProxy()->updateRecentRoamingFiles(TRUE);
		}
		else if(!fileName.isEmpty())
		{
			emit openRecentDocument(fileName, appName);
		}

		if (pCurrentPage && pCurrentTab &&
			pCurrentPage->type() == KPromePluginWidget::pageNewDocs &&
			krt::kcmc::support("ColsePageNewDocs"))
		{
			pCurrentTab->tryClose();
		}
	}
}

void KxNewDocJsApi::openLocalFile(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QString callBack = context.getValue("callback", "").toString();
	if (!args.isEmpty())
	{
		QString filePath = args.value("filePath").toString();
		int mod = args.value("mod").toInt();
		openLocalFile(filePath, mod == 1, callBack);
		addRecentUseTempalte(filePath);
	}
}

void KxNewDocJsApi::downloadTemplate(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);

	//下载字体列表
	QVariantList fontList = args.value("fontList").toList();
	bool downloadFont = KxCreateDocInstance::instance().downloadFontList(fontList);

	QVariantList list = args.value("params").toList();

	//重置一次，不然会在多组件下载的时候，显示成上一次完成的100
	callbackToJS(s_strDownloadProgressJsCallBack, formatResult(0));
	for (auto i = list.constBegin();i != list.constEnd(); ++i)
	{
		QVariantMap item_map = i->toMap();

		QString url = item_map["url"].toString();
		QString name = item_map["name"].toString();
		name = name.replace(QRegExp(QString::fromLatin1("[\\\\/:*?\"<>|]")), "");
		QString md5 = item_map["md5"].toString();
		QString templateId = item_map["id"].toString();
		QStringList subUrls = item_map["subUrls"].toStringList();
		qlonglong size = item_map["size"].toLongLong();
		QString downloadKey = item_map["download_key"].toString();

		DownloadArgs downloadData;
		downloadData.commonArgs.plgName = KPluginName;
		downloadData.commonArgs.plgVer = docer::base::KCurrentModule::getFileVersion();
		downloadData.saveArgs.bUnzip = false;
		downloadData.saveArgs.bCheckMd5 = false;
		downloadData.resourceArgs.md5 = md5;
		downloadData.resourceKey = "template";
		downloadData.resourceArgs.id = templateId;
		downloadData.resourceArgs.resSize = size;

		downloadData.resourceArgs.downloadKey = downloadKey;

		QVariantMap originalPropsMap = item_map["addCustomProps"].toMap();
		const QString strDocerFrontend = "KSODocerFrontend";
		const QString strDocerFrontendTemp = "KSODocerFrontendTemp";
		if (originalPropsMap.contains(strDocerFrontend))
		{
			downloadData.resourceArgs.templateCustomProps.insert(strDocerFrontend, originalPropsMap.value(strDocerFrontend).toString());
		}

		if (originalPropsMap.contains(strDocerFrontendTemp))
		{
			downloadData.resourceArgs.templateCustomProps.insert(strDocerFrontendTemp, originalPropsMap.value(strDocerFrontendTemp).toString());
		}

		QString filekey;
		if (item_map.contains("filekey"))
		{
			filekey = item_map["filekey"].toString();
			filekey = KxCreateDocConfig::instance().buildBeautifyTemplatePassword(filekey);
		}

		QString decodeUrl = QUrl::fromPercentEncoding(url.toUtf8());
		QString decodeName = QUrl::fromPercentEncoding(name.toUtf8());
		if (decodeUrl.isEmpty() || decodeName.isEmpty())
			continue;

		downloadData.saveArgs.saveFilePath= getLocalPath(decodeUrl, decodeName);
		downloadData.resourceArgs.urls << url << subUrls;
		QString id = downloadTemplate(downloadData, filekey, downloadFont);

		if (!id.isEmpty())
		{
			//返回的ID为模板ID
			setResult(context, templateId);
			auto newDocsPage = getNewDocsPage();
			if(newDocsPage)
				newDocsPage->incDownloadTemplateCount();
			m_downloadfileIdSet.insert(templateId);
		}
	}
}

void KxNewDocJsApi::onResizeWindow()
{
	callbackToJS("setIsFullScreen", formatResult(m_window->isMaximized()));
}

void KxNewDocJsApi::insertOnlineResource(const ResourceInfo& resourceInfo)
{
	auto resInfoMap = resourceInfo.extInfo.value("resInfo").toMap();
	if (resInfoMap.isEmpty())
		return;
	QString resourcePath = resInfoMap.value("resourcePath").toString();
	if (resourcePath.isEmpty())
	{
		KxLoggerLite::writeInfo(L"knewdocs",
			QString("File: %1 not found, when trying to open it.").arg(resourcePath).toStdWString());
		return;
	}

	openLocalFile(resourcePath, "");
}

void KxNewDocJsApi::onDownloadProgress(const QString& id, int nProgress)
{
	if (!m_downloadTaskfileIdMap.contains(id) || ksolite::isSupported(ksolite::IntranetVersion7))
		return;
	int nPercent = std::min(nProgress, 99);
	QVariantMap result;
	result["progress"] = nPercent;
	result["id"] = m_downloadTaskfileIdMap[id];

	{
		callbackToJS(s_strDownloadProgressJsCallBack, formatResult(result));
		KxNewDocJsApiHelper::instance().notifyDownloadProgress(this, m_downloadTaskfileIdMap[id], nPercent);
	}
}

void KxNewDocJsApi::cancelDownload(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty())
	{
		auto newDocsPage = getNewDocsPage();
		if(newDocsPage)
			newDocsPage->decDownloadTemplateCount();
		//可以有多个下载任务
		QString templateId = args.value("id").toString();
		QString downloadId = KxNewDocJsApiHelper::instance().getDownloadID(templateId);
		if (!downloadId.isEmpty())
		{
			KDocerResNetworkSDK::getInstance().cancelDownload(downloadId);
			KxNewDocJsApiHelper::instance().notifyCancelDownload(this, templateId);
			KxNewDocJsApiHelper::instance().removeByResId(templateId);
		}
		m_downloadfileIdSet.remove(templateId);
	}
}

void KxNewDocJsApi::onDownloadSuccess(const QString& id, const QString& fileName)
{
	auto newDocsPage = getNewDocsPage();
	if(!newDocsPage)
		return;
	if(!m_downloadTaskfileIdMap.contains(id))
		return;
	QString templateId = m_downloadTaskfileIdMap[id];
	QString strCallBack = m_callbackMap.take(id);
	if (!ksolite::isSupported(ksolite::IntranetVersion7))
	{
		QVariantMap result;
		result["id"] = templateId;
		result["progress"] = 100;
		QString strResult = formatResult(result);
		callbackToJS(s_strDownloadSuccessJsCallBack, strResult);
		callbackToJS(s_strDownloadProgressJsCallBack, strResult);
	}

	newDocsPage->decDownloadTemplateCount();

	QString fileKey = m_fileIdKeysMap.contains(templateId) ? m_fileIdKeysMap[templateId] : "";
	bool downloadFont = sender() ? sender()->property(g_downloadFont).toBool() : false;
	if (m_customPropsMap.contains(templateId))
	{
		auto customPropsArgs = m_customPropsMap[templateId];
		QHash<QString, QString> customProps;
		foreach(auto key, customPropsArgs.keys())
			customProps.insert(key, customPropsArgs[key].toString());
		QPointer<KxNewDocJsApi> spThis(this);
		std::shared_ptr<QString> spFileKey;
		if (!fileKey.isEmpty())
		{
			spFileKey = std::make_shared<QString>();
			*spFileKey = fileKey;
		}
		kdocer::KCustomPropsHelper::asyncModifyCustomProps(
			fileName, KPluginName, customProps,
			[=](const QString& originFilePath, const QString& outputFilePath)
			{
				if (spThis)
				{
					if (spFileKey && !spFileKey->isEmpty())
					{
						spThis->openLocalFile(outputFilePath, *spFileKey);
					}
					else
						spThis->openLocalFile(outputFilePath, true);

					writeDownloadFontConfig(downloadFont);
				}

			},
			[=](const QString& originFilePath, const kdocer::KModifyCustomPropsErrorInfo& errInfo)
			{
				if (spThis)
				{
					if (spFileKey && !spFileKey->isEmpty())
					{
						spThis->openLocalFile(originFilePath, *spFileKey);
					}
					else
						spThis->openLocalFile(originFilePath, true);

					writeDownloadFontConfig(downloadFont);
				}
			}
			);
	}
	else
	{
		if (!fileKey.isEmpty())
		{
			openLocalFile(fileName, fileKey);
		}
		else
			openLocalFile(fileName, true, strCallBack);

		writeDownloadFontConfig(downloadFont);
	}

	KxNewDocJsApiHelper::instance().notifyDownloadSuccess(this, templateId, fileName);

	KxNewDocJsApiHelper::instance().removeByTaskId(id);

	if (m_downloadfileIdSet.contains(templateId))
	{
		m_downloadfileIdSet.remove(templateId);
	}
	
	if (m_customPropsMap.contains(templateId))
	{
		m_customPropsMap.remove(templateId);
	}
}

void KxNewDocJsApi::onDownloadError(const QString& id, DownloadFailInfo info)
{
	QVariantMap result;
	if(!m_downloadTaskfileIdMap.contains(id))
		return;
	QString templateId = m_downloadTaskfileIdMap[id];

	if (!ksolite::isSupported(ksolite::IntranetVersion7))
	{
		result["id"] = templateId;
		result["reason"] = info.errorMsg;
		callbackToJS(s_strDownloadErrorJsCallBack, formatResult(result));
	}
	else
	{
		result["callstatus"] = "ok";
		result["result"] = false;
		QString strResult = formatResult(result);
		QString callBack = m_callbackMap.take(id);
		callbackToJS(callBack, strResult, false);
	}
	
	KxNewDocJsApiHelper::instance().removeByTaskId(id);

	{

		KxNewDocJsApiHelper::instance().notifyDownloadError(this, m_downloadTaskfileIdMap[id], info);
		auto newDocsPage = getNewDocsPage();
		if(newDocsPage)
			newDocsPage->decDownloadTemplateCount();
	}
	if(m_downloadfileIdSet.contains(templateId))
		m_downloadfileIdSet.remove(templateId);
	if (m_customPropsMap.contains(templateId))
	{
		m_customPropsMap.remove(templateId);
	}
}

void KxNewDocJsApi::onThumbnailGenerated(int id,
	const QString& fileName, const QString& callback, const QByteArray& thumbnail)
{
	callbackToJSRaw(callback, thumbnail);
}

void KxNewDocJsApi::onSkinChanged()
{
	//需废弃
	QVariantMap result;
	result["isDarkSkin"] = KNewDocDrawHelper::isDarkSkin();
	callbackToJS(g_skinChangedCallback, formatResult(result));
}

void KxNewDocJsApi::newBlankDocument(const QVariantMap& args, const QString& callBack)
{
	//接promes打开文件

	// 快速新建的对应的browser调用jsapi对其进行转换
	QString tartgetType = m_window->getBrowserType();
	int newType = m_window->getBrowserRawType();

	QString argsType = args.value("fileType").toString();
	if (!argsType.isEmpty())
	{
		QString type = getTartgetBrowserType(argsType);
		if (!type.isEmpty())
		{
			tartgetType = type;
			newType = KPromeNewDocsTabInfoObj::convertTabIntType(tartgetType);
		}
	}
	
	if ((newType == KPromePluginWidget::pageDocerWps ||
		newType == KPromePluginWidget::pageDocerEt ||
		newType == KPromePluginWidget::pageDocerWpp ||
		newType == KPromePluginWidget::pageDocerPdf) &&
		!promeApp->isPromeShelllessMode())
		KPromeStartupCounter::instance().recordNewFileBegin(KPromeStartupCounter::AppType(newType - KPromePluginWidget::pageDocerWps));

	QString fileName;
	QString fileType = tartgetType.toLower();
	if (fileType.contains("wpp"))
	{
		if (!krt::kcmc::support("DefaultTemplatePath"))
		{
#ifdef Q_OS_MACOS
			// Mac企业版和Mac个人版使用的模板保持一致
			QDir dir(krt::dirs::resources());
			QString templatePath("addons/knewdocs/res/blanktemplate/normal_mac.pptx");
			if (dir.exists(templatePath))
				fileName = dir.absoluteFilePath(templatePath);
#else
			fileName = krt::i18n::getFilePath(WPP_PRO_PROMETHEUS_NEWFILE);
#endif
		}
		else
		{
			const QString blankTemplateDirPath = docer::base::KCurrentModule::getModuleResourceDirPath().append(QLatin1String("/res/blanktemplate/"));
			const QString strColor = args.value("color").toString();
			bool bAI = args.value("ai", false).toBool();
			QString blankTemplateFileName = "normal.pptx";
			static QHash<QString, QString> s_blankNewTemplateMap = []() -> QHash<QString, QString>
			{
				QHash<QString, QString> ret;
#ifdef Q_OS_DARWIN
				ret["white"] = "normal_mac.pptx";
				ret["black"] = "normal_black_mac.pptx";
				ret["gray"] = "normal_gray_mac.pptx";
#else
				ret["white"] = "normal.pptx";
				ret["black"] = "normal_black.pptx";
				ret["gray"] = "normal_gray.pptx";
#endif //!Q_OS_DARWIN
				return ret;
			}();

			if(s_blankNewTemplateMap.contains(strColor))
			{
				QString tmpFileName = s_blankNewTemplateMap[strColor];
				if(QFile::exists(blankTemplateDirPath + tmpFileName))
					blankTemplateFileName = tmpFileName;
			}
			if (bAI)
			{
				if (auto sharedMemory = getIKDocerSharedMemory())
					sharedMemory->setLargeSharedMemory("docer_ai_memory", Memory_1M, "knewdocs", "useCopilot", "1");
				QString infoMap = JsonHelper::convertVariantToString(args.value("useCopilotInfoMap")).toUtf8().toHex();
				if (auto sharedMemory = getIKDocerSharedMemory())
				{
					sharedMemory->setLargeSharedMemory("docer_ai_memory", Memory_1M, "knewdocs", "useCopilotInfoMap", infoMap);
					sharedMemory->setLargeSharedMemory("docer_ai_memory", Memory_1M, "knewdocs", "autoTriggerEntryAI", "1");
				}
				
				//AI入口新建演示模板时，强制用白色模板
#ifdef Q_OS_DARWIN
				blankTemplateFileName = "normal_mac.pptx";
#else
				blankTemplateFileName = "normal.pptx";
#endif // !Q_OS_DARWIN
			}
			else
			{
				if(KNetworkStateMaintainer::isConnectedToInternet())
				{
					if (auto sharedMemory = getIKDocerSharedMemory())
						sharedMemory->setLargeSharedMemory("beautify_change_theme_memory", Memory_1K, "knewdocs", "trigggerChangeTheme", "1");
				}
			}
			fileName = blankTemplateDirPath + blankTemplateFileName;
			ksolite::toNativeSeparators(fileName);
		}
	}
	else if (fileType.compare("pagedocerwps") == 0)
	{
		bool bAI = args.value("ai", false).toBool();
		if (bAI)
		{
			if (auto sharedMemory = getIKDocerSharedMemory())
			{
				sharedMemory->setLargeSharedMemory("docer_ai_memory", Memory_1M, "knewdocs", "useCopilot", "1");
				QString infoMap = JsonHelper::convertVariantToString(args.value("useCopilotInfoMap")).toUtf8().toHex();
				sharedMemory->setLargeSharedMemory("docer_ai_memory", Memory_1M, "knewdocs", "useCopilotInfoMap", infoMap);
				KxLoggerLite::writeInfo(L"kappcenter",
					QString("%1 set shared memory %2")
					.arg(__FUNCTION__).arg(infoMap).toStdWString());
			}
		}
	}
	else if (fileType.compare("pagedoceret") == 0)
	{
		bool bAI = args.value("ai", true).toBool();
		if (bAI)
		{
			if (auto sharedMemory = getIKDocerSharedMemory())
				sharedMemory->setLargeSharedMemory("docer_ai_memory", Memory_1M, "knewdocs", "useCopilot", "1");
			QString infoMap = JsonHelper::convertVariantToString(args.value("useCopilotInfoMap")).toUtf8().toHex();
			if (auto sharedMemory = getIKDocerSharedMemory())
				sharedMemory->setLargeSharedMemory("docer_ai_memory", Memory_1M, "knewdocs", "useCopilotInfoMap", infoMap);
			KxLoggerLite::writeInfo(L"kappcenter",
				QString("%1 set shared memory %2")
				.arg(__FUNCTION__).arg(infoMap).toStdWString());
		}
	}

	if (0 == fileType.compare(QLatin1String("pagedocerofficial"), Qt::CaseInsensitive))
	{
		const static bool bOfficialDocUseUOF =
			krt::kcmc::support("OfficialDocUseUOF") && krt::kcmc::support("WPSUof");

		const static QString strTemplatesPath = krt::dirs::officeHome() % QDir::separator() % "templates" %
			QDir::separator() % "wps" % QDir::separator() % krt::i18n::language();

		QString strAppDataOfficialNormal = strTemplatesPath + QDir::separator() + QString("OfficialNormal%1").arg(
			bOfficialDocUseUOF ? ".uott" : ".dotm");

		if (!QFile::exists(strAppDataOfficialNormal))
		{
			QString strSource = QString("templates/officialtemplate/OfficialNormal%1").arg(
				bOfficialDocUseUOF ? ".uott" : ".dotm");

			QDir dir;
			if (!dir.exists(strTemplatesPath))
				dir.mkpath(strTemplatesPath);

			krt::i18n::copyFileTo(strSource, strAppDataOfficialNormal); // 从安装目拷贝到appdata
		}

		fileName = strAppDataOfficialNormal;
	}

	emit openDocument(fileName,"", "newBlankDocument", tartgetType, callBack);
}

void KxNewDocJsApi::openLocalFile(const QString& file, bool copy, const QString& callBack)
{
	//接promes打开文件
	emit openDocument(file, "", "openLocalFile", m_window->getBrowserType(), callBack);
}

void KxNewDocJsApi::openLocalFile(const QString& filePath, const QString& fileKey)
{
	//接promes打开文件
	emit openDocument(filePath, fileKey, "openLocalFile", m_window->getBrowserType(), "");
}

QString KxNewDocJsApi::downloadTemplate(const DownloadArgs& downloadData, const QString& fileKey, bool downloadFont, const QString& callBack)
{
	const QString& localPath = downloadData.saveArgs.saveFilePath;
	//fix bug:291989 若文件以及存在，则直接打开文件
	QFileInfo fi(localPath);
	if (fi.exists())
	{
		if (!ksolite::isSupported(ksolite::IntranetVersion7))
		{
			QVariantMap result;
			result["progress"] = 100;
			result["id"] = downloadData.resourceArgs.id;
			callbackToJS(s_strDownloadProgressJsCallBack, formatResult(result));
			callbackToJS(s_strDownloadSuccessJsCallBack, formatResult(result));
		}

		if (downloadData.resourceArgs.templateCustomProps.isEmpty())
		{
			if (fileKey.isEmpty())
				openLocalFile(localPath, true, callBack);
			else
				openLocalFile(localPath, fileKey);
			
			writeDownloadFontConfig(downloadFont);
		}
		else
		{
#ifdef Q_OS_WIN
			QHash<QString, QString> customProps;
			for (auto constIter = downloadData.resourceArgs.templateCustomProps.constBegin(); 
				constIter != downloadData.resourceArgs.templateCustomProps.constEnd(); ++constIter)
				customProps.insert(constIter.key(), constIter.value().toString());
			QPointer<KxNewDocJsApi> spThis(this);
			std::shared_ptr<QString> spFileKey;
			if (!fileKey.isEmpty())
			{
				spFileKey = std::make_shared<QString>();
				*spFileKey = fileKey;
			}
			kdocer::KCustomPropsHelper::asyncModifyCustomProps(
				localPath, KPluginName, customProps,
				[=](const QString& originFilePath, const QString& outputFilePath)
				{
					if (spThis)
					{
						if (spFileKey && !spFileKey->isEmpty())
							spThis->openLocalFile(outputFilePath, *spFileKey);
						else
							spThis->openLocalFile(outputFilePath, true);

						writeDownloadFontConfig(downloadFont);
					}
				},
				[=](const QString& originFilePath, const kdocer::KModifyCustomPropsErrorInfo& errInfo)
				{
					if (spThis)
					{
						if (spFileKey && !spFileKey->isEmpty())
							spThis->openLocalFile(originFilePath, *spFileKey);
						else
							spThis->openLocalFile(originFilePath, true);

						writeDownloadFontConfig(downloadFont);
					}
				}
				);
#endif
		}
		
		return "";
	}

	DownloadArgs downloadArgs = downloadData;
	downloadArgs.resourceArgs.urls = downloadData.resourceArgs.urls;
	downloadArgs.saveArgs.saveFilePath = localPath;
	downloadArgs.saveArgs.installFilePath = QString();
	downloadArgs.methodInfo.option = DirectGetOperation;
	const QString& id = KDocerResNetworkSDK::getInstance().startDownload(downloadArgs,
		bindContext(this, [=](const QString& downloadId, DownloadSuccessInfo info) {
			onDownloadSuccess(downloadId, info.path);
		}),
		bindContext(this, [=](const QString& downloadId, int nProgress) {
			onDownloadProgress(downloadId, nProgress);
		}),
		bindContext(this, [=](const QString& downloadId, DownloadFailInfo info) {
			onDownloadError(downloadId, info);
		}));

	if (!fileKey.isEmpty())
		m_fileIdKeysMap.insert(downloadData.resourceArgs.id, fileKey);
	m_downloadTaskfileIdMap.insert(id, downloadData.resourceArgs.id);
	if (!downloadData.resourceArgs.templateCustomProps.isEmpty())
	{
		m_customPropsMap.insert(downloadData.resourceArgs.id, QJsonObject::fromVariantMap(downloadData.resourceArgs.templateCustomProps));
	}

	return id;
}

void KxNewDocJsApi::clearAllDownloadTask()
{

}

void KxNewDocJsApi::openSpecificTab(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if(args.isEmpty())
		return;
	QString tabType = args.value("tabType", "wps").toString();
	int type = KPromePluginWidget::pageDocerWps;
	if (tabType == "wps")
		type = KPromePluginWidget::pageDocerWps;
	else if (tabType == "wpp")
		type = KPromePluginWidget::pageDocerWpp;
	else if (tabType == "et")
		type = KPromePluginWidget::pageDocerEt;
	else if (tabType == "chuangkit")
		type = KPromePluginWidget::widgetChuangKit;
	else
	{
		if (KPromeNewDocsTabBarConfig::getInstance())
		{
			auto tabObj = KPromeNewDocsTabBarConfig::getInstance()->getInfoObjById(tabType);
			if (tabObj)
				type = tabObj->getTabType();
		}
	}

	QString params = QString::fromLocal8Bit(context.getJsonValue("params").toLocal8Bit());
	KPromeNewDocsPage* newDocsPage = getNewDocsPage();
	if (newDocsPage)
	{
		newDocsPage->openSpecificTab(type, params);
	}
}

void KxNewDocJsApi::getTabText(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	int tabType = args.value("tabType").toInt();
	QString tabText, titleText;
	KPromeNewDocsPage* newDocsPage = getNewDocsPage();
	if (newDocsPage)
	{
		auto pCfgObj = newDocsPage->getDocsTabByType(tabType);
		if (pCfgObj)
		{
			tabText = pCfgObj->text();
			titleText = pCfgObj->webTitleText();
		}
	}
	QVariantMap result;
	result["tabText"] = tabText;
	result["titleText"] = titleText;
	setResult(context, result);
}

void KxNewDocJsApi::openMyTemplateTab(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if(args.isEmpty())
		return;

	QString params = QString::fromLocal8Bit(context.getJsonValue("params").toLocal8Bit());
	KPromeNewDocsPage* newDocsPage = getNewDocsPage();
	if (newDocsPage)
	{
		newDocsPage->openSpecificTab(KPromePluginWidget::pageMytpl, params);
	}
}

void KxNewDocJsApi::notifyDownloadProgress(const QString& templateId, int nPercent)
{
	if (m_downloadfileIdSet.contains(templateId))
	{
		QVariantMap result;
		result["id"] = templateId;
		result["progress"] = nPercent;
		callbackToJS(s_strDownloadProgressJsCallBack, formatResult(result));
	}
}

void KxNewDocJsApi::notifyCancelDownload(const QString& templateId)
{
	if(m_downloadfileIdSet.contains(templateId))
	{
		QVariantMap result;
		result["id"] = templateId;
		callbackToJS(g_downloadCanceledCallback, formatResult(result));
		m_downloadfileIdSet.remove(templateId);
		auto newDocsPage = getNewDocsPage();
		if(newDocsPage)
			newDocsPage->decDownloadTemplateCount();
	}
	if (m_customPropsMap.contains(templateId))
	{
		m_customPropsMap.remove(templateId);
	}
}

void KxNewDocJsApi::notifyDownloadSuccess(const QString& templateId, const QString& fileName)
{
	if(m_downloadfileIdSet.contains(templateId))
	{
		QVariantMap result;
		result["id"] = templateId;
		callbackToJS(s_strDownloadSuccessJsCallBack, formatResult(result));
		m_downloadfileIdSet.remove(templateId);
		auto newDocsPage = getNewDocsPage();
		if(newDocsPage)
			newDocsPage->decDownloadTemplateCount();
	}
	if (m_customPropsMap.contains(templateId))
	{
		m_customPropsMap.remove(templateId);
	}
}

void KxNewDocJsApi::notifyDownloadError(const QString& templateId, KDownloadErrorInfo info)
{
	if (m_downloadfileIdSet.contains(templateId))
	{
		QVariantMap result;
		result["id"] = templateId;
		result["reason"] = info.description;
		callbackToJS(s_strDownloadErrorJsCallBack, formatResult(result));
		m_downloadfileIdSet.remove(templateId);
		auto newDocsPage = getNewDocsPage();
		if(newDocsPage)
			newDocsPage->decDownloadTemplateCount();
	}
	if (m_customPropsMap.contains(templateId))
	{
		m_customPropsMap.remove(templateId);
	}
}

void KxNewDocJsApi::getFloderAllTemplateFiles(
	const QString& path,
	const QStringList& nameFilters,
	const QSettings& tranSet,
	QList<QVariant>& fileNames,
	bool bNeedTranslate/* = true*/,
	QDir::SortFlags sortFlag/* = QDir::NoSort*/)
{
	QFileInfoList tmpdirs = 
		QDir(path).entryInfoList(nameFilters, QDir::AllDirs | QDir::Files | QDir::NoDotAndDotDot, sortFlag);

	foreach (QFileInfo fileInfo, tmpdirs)
	{
		if (fileInfo.isDir())
		{
			getFloderAllTemplateFiles(fileInfo.absoluteFilePath(), nameFilters, tranSet, fileNames);
		}
		else
		{
			QString fileName = fileInfo.fileName();
			if (!fileName.isEmpty())
			{
				QMap<QString, QVariant> fileItem;
				fileItem["filePath"] = fileInfo.absoluteFilePath();
				QString tempFileName = fileName.left(fileName.lastIndexOf("."));
				fileItem["name"] = bNeedTranslate ? tranSet.value(tempFileName, tempFileName).toString() : tempFileName;//界面显示的文件名称
				QString previewPNG = QDir::toNativeSeparators(fileInfo.absolutePath() + "/" + tempFileName + ".png");
				QFile thumbFile(previewPNG);
				if (thumbFile.open(QIODevice::ReadOnly))
				{
					fileItem["preview"] = thumbFile.readAll().toBase64().data();
					thumbFile.close();
				}
				fileNames.append(fileItem);
			}
		}
	}
}

void KxNewDocJsApi::templateTabSort(const QString& appName, QStringList& inputList, QStringList& outputList)
{
	QSettings tabsortSet(QString("%1/%2/tabsort.ini").arg(krt::i18n::getFilePath("templates")).arg(appName), QSettings::IniFormat);
	tabsortSet.setIniCodec("UTF-8");
	tabsortSet.beginGroup("sort");

	QMap<int, QString> sortedMap;
	foreach(QString path, inputList)
	{
		QString strPath = path.replace(QString("\\"), QString("/"));
		QString pathName = QFileInfo(strPath).fileName();
		int sortIndex = tabsortSet.value(pathName, "-1").toInt();
		if (sortIndex != -1)
			sortedMap.insert(sortIndex, path);
	}
	outputList = sortedMap.values();

	tabsortSet.endGroup();
}

bool KxNewDocJsApi::exceptByOfficialfilter(const QString& strFile)
{
	static QStringList listSuffix;
	if (listSuffix.isEmpty())
	{
		bool bOfficialDocUseUOF = krt::kcmc::support("OfficialDocUseUOF") && krt::kcmc::support("WPSUof");

		if (bOfficialDocUseUOF)
			listSuffix.append("uot");
		else
			listSuffix << "wps" << "wpt";
	}

	QString strFileSuffix = QFileInfo(strFile).suffix();
	if (!listSuffix.contains(strFileSuffix, Qt::CaseInsensitive))
		return true;

	return false;
}


bool KxNewDocJsApi::switchToPageByFileId(const QString& fileId)
{
	if (fileId.isEmpty())
		return false;

	if (!promeApp)
		return false;

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return false;

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return false;

	bool bOpened = false;
	int cnt = contentArea->pageCount();
	for (int i = 0; i < cnt; ++i)
	{
		KPromePage* page = contentArea->pageAt(i);
		if (!page)
			continue;

		if (KPromePluginWidget::pageKDocsPreview == page->type())
		{
			QString openedFileID = page->property("File_ID").toString();
			if (!openedFileID.isEmpty() && openedFileID == fileId)
			{
				contentArea->setCurrentPage(page);
				KPromeSubPage* subPage = page->initialSubPage();
				if (subPage)
					subPage->activate();
				mw->showToFront();
				bOpened = true;
				break;
			}
		}
	}
	return bOpened;
}

void KxNewDocJsApi::contextStateRsp(KxWebViewJSContext& context,
	bool reslut, const std::string& errMsg)
{
	context.Result["callstatus"] = reslut ? "ok" : "error";
	context.Result["errormsg"] = errMsg;
}

void KxNewDocJsApi::IsSupportIntranetTemplate(KxWebViewJSContext& context)
{
	bool bResult = krt::kcmc::support("DeployIntranetTemplateService");
	setResult(context, QVariant::fromValue(bResult));
}

void KxNewDocJsApi::getLocalTemplateFileInfo(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QList<QVariant> result;

	const QString& appName = args.value("appName").toString();
	QStringList nameFilters;
	if (!getFilterType(appName, nameFilters, false))
		return;

	if (!args.isEmpty() && !krt::kcmc::support("DeployIntranetTemplateService"))
	{
		QStringList tempList;
		getTemplatesPath(appName, tempList, false);

		QStringList templatePaths;
		templatePaths.reserve(tempList.size());
		templateTabSort(appName, tempList, templatePaths);

		QSettings tranSet(QString("%1/%2/display.ini").arg(krt::i18n::getFilePath("templates")).arg(appName), QSettings::IniFormat);
		tranSet.setIniCodec("UTF-8");
		tranSet.beginGroup("Display");

		result.reserve(templatePaths.size());
		foreach(QString path, templatePaths)
		{
			QMap<QString, QVariant> item;
			QString strTempPath = path.replace(QString("\\"), QString("/"));
			QString pathName = QFileInfo(strTempPath).fileName();
			QString strTabName;
			strTabName = tranSet.value(pathName, pathName).toString();
			QList<QVariant> fileNames;
			getFloderAllTemplateFiles(strTempPath, nameFilters, tranSet, fileNames);

			item["tagName"] = strTabName;
			item["filePath"] = strTempPath;
			item["fileList"] = fileNames;
			result.append(item);
		}
	}

	if (krt::kcmc::support("GeneralTemplateInHomePage"))
	{
		QSettings tranSet(QString("%1/%2/display.ini").arg(krt::i18n::getFilePath("templates")).arg(appName), QSettings::IniFormat);
		tranSet.setIniCodec("UTF-8");
		tranSet.beginGroup("Display");

		QString gtp = QString("%1/%2/%3/%4")
				.arg(QDir::homePath())
				.arg(".local/share/Kingsoft/office6/templates")
				.arg(appName)
				.arg(krt::i18n::uiLanguage());

		QMap<QString, QVariant> item;
		QList<QVariant> fileNames;
		getFloderAllTemplateFiles(gtp, nameFilters, tranSet, fileNames);
		item["tagName"] = tr("General");
		item["filePath"] = gtp;
		item["fileList"] = fileNames;
		result.prepend(item);
	}

	if (result.isEmpty())
	{
		QMap<QString, QVariant> item;
		item["tagName"] = QVariant();
		item["filePath"] = QVariant();
		item["fileList"] = QVariant();
		result.append(item);
	}

	setResult(context,result);
}

void KxNewDocJsApi::getCreateDocUrl(KxWebViewJSContext& context)
{
	QVariantMap result;
	setResult(context, result);
}


static bool recentThan(const RecordInfo& left, const RecordInfo& right)
{
	QDateTime leftDateTime = left.m_fileAccessTime;
	QDateTime rightDateTime = right.m_fileAccessTime;
	if (leftDateTime.isValid() && rightDateTime.isValid())
	{
		return leftDateTime > rightDateTime;
	}
	return false;
}

void KxNewDocJsApi::getRecentFiles(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QList<QVariant> result;

	if (!args.isEmpty())
	{
		int fileLen = args.value("maxCount").toInt();
		QString fileType = m_window->getBrowserType();
		fileType = fileType.toLower();

		RecordInfoList fileList = promeApp->recentFileProxy()->recentRecords(LocalFile, fileLen);
		IKDocerAccount* account = nullptr;
		auto kdocercoreLite = getIKDocerCoreLite();
		if (kdocercoreLite)
			account = kdocercoreLite->getIKDocerAccount();
		bool isLogined = false;
		if (account)
			isLogined = account->isLogined();
		if (isLogined)
			promeApp->recentFileProxy()->updateRecentRoamingFiles(TRUE);

		std::sort(fileList.begin(), fileList.end(), recentThan);

		int resultcnt = 0;
		foreach(const RecordInfo file, fileList)
		{
			if (fileType == QLatin1String("pagedocerofficial"))
			{
				if (exceptByOfficialfilter(file.m_fileName))
					continue;
			}
			else if (!fileType.contains(file.m_appType))
			{
				continue;
			}
			IKDocerAccount* account = nullptr;
			auto kdocercoreLite = getIKDocerCoreLite();
			if (kdocercoreLite)
				account = kdocercoreLite->getIKDocerAccount();
			bool isLogined = false;

			if (account)
				isLogined = account->isLogined();
			if (!isLogined && file.m_fileType != LocalFile)
				continue;

			QMap<QString, QVariant> propMap;
			propMap["id"]            = file.m_fileId;
			propMap["name"]          = file.m_fileName;
			if (file.m_fileType == CloudFile)
				propMap["filePath"]  = file.m_cloudPath + QDir::separator() + file.m_fileName;
			else
				propMap["filePath"]  = file.m_fileAbsPath;
			propMap["publish_time"]  = file.m_fileAccessTime.toStringEx("yyyy-MM-dd hh:mm");

			propMap["is_exit"]	     = file.m_isExist;
			if (!file.m_isNetworkFile && file.m_fileType != CloudFile)
				propMap["is_exit"]	 = QFileInfo(file.m_fileAbsPath).exists();

			propMap["fileType"]      = (int)file.m_fileType;
			result.append(propMap);
			resultcnt++;
			if (fileLen != -1 && resultcnt >= fileLen)
				break;
		}
	}
	setResult(context, result);
}

void KxNewDocJsApi::getNewDocRegeditValue(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QVariantMap result;
	if (!args.isEmpty())
	{
		QString queryKey = args.value("key").toString();
		KxNewDocsSettings newDocsSettings;
		QString value = newDocsSettings.value(queryKey, "").toString();
		result["value"] = value;
	}
	setResult(context, result);
}

HRESULT KxNewDocJsApi::getTemplatesPath(const QString& appName, QStringList& list, bool bForSearch){
	bool bOfficialDockerPage = false;
	bool bWps = (appName.compare("wps") == 0);

	static bool bOfficialComponentEnable = krt::kcmc::support("OfficialComponent");
	if (bOfficialComponentEnable && bWps)
	{
		QString strPageType = m_window->getBrowserType().toLower();
		bOfficialDockerPage = (strPageType == QLatin1String("pagedocerofficial"));
	}

	QStringList paths = krt::i18n::getFilePaths(QString("templates/%1").arg(appName));
	foreach(QString path, paths)
	{
		QFileInfoList tmpdirs = QDir(path).entryInfoList(QDir::AllDirs | QDir::NoDotAndDotDot);
		foreach (QFileInfo fi, tmpdirs)
		{
			QString strPath = fi.absoluteFilePath();

			if (bOfficialComponentEnable && bForSearch && bWps)
			{
				if (strPath.contains("GB9704_2012_Templates"))
				{
					if (!bOfficialDockerPage)
						continue;
				}
				else
				{
					if (bOfficialDockerPage)
						continue;
				}
			}

			if (strPath.contains("GB9704_electronic_document_templates"))
			{
				const static QStringList s_gbbasicPrivilegeIds = { "gbbasicfunction"};
				const static QStringList s_gbofficialPrivilegeIds = { "gbofficialfunction" };
				if (!KNewDocsHelper::hasPrivileges(s_gbbasicPrivilegeIds) &&
					(!krt::product::isGBOfficial() || !KNewDocsHelper::hasPrivileges(s_gbofficialPrivilegeIds)))
					continue;
			}
			else if (strPath.contains("GB9704_electronic_document_printing_templates"))
			{
				const static QStringList s_gbOfficialPrivilegeIds = { "gbofficialfunction" };
				if (!krt::product::isGBOfficial() || !KNewDocsHelper::hasPrivileges(s_gbOfficialPrivilegeIds))
					continue;
			}
			else if (strPath.contains("GJB5100A_electronic_document_printing_templates"))
			{
				const static QStringList s_gjbOfficialPrivilegeIds = { "gjbofficialfunction" };
				if (!krt::product::isGJBOfficial() || !KNewDocsHelper::hasPrivileges(s_gjbOfficialPrivilegeIds))
					continue;
			}

			list.append(strPath);
		}
	}

	if (bForSearch)
	{
		static bool bOfficialViewEnable = krt::kcmc::support("OfficialViewEnable");

		if ((bOfficialViewEnable && !bOfficialComponentEnable) ||
			(bOfficialComponentEnable && bOfficialDockerPage))
		{
			QDir dir(krt::dirs::officeHome() + "/templates/officialtemplate");
			if (dir.exists())
				list.append(dir.absolutePath());

			dir.setPath(krt::dirs::officeHome() + "/templates/importofficial");
			if (dir.exists())
				list.append(dir.absolutePath());

			dir.setPath(krt::i18n::getFilePath("templates") + "/officialtemplate/oem");
			if (dir.exists())
				list.append(dir.absolutePath());
		}
	}

	return S_OK;
}


bool KxNewDocJsApi::getFilterType(const QString& appName, QStringList& list, bool bForSearch)
{
	if (!appName.compare("wps", Qt::CaseInsensitive))
	{
		list<<"*.wpt"<<"*.dot"<<"*.dotm"<<"*.dotx";	//wps的模板

		if (bForSearch &&
			krt::kcmc::support("OfficialViewEnable") &&
			krt::kcmc::support("OfficialDocUseUOF") &&
			krt::kcmc::support("WPSUof") ||
			krt::kcmc::support("OfficialFakeUOT"))
		{
			list << "*.uot";
		}
	}
	else if (!appName.compare("wpp", Qt::CaseInsensitive))
	{
		list<<"*.dpt"<<"*.pot" << "*.potx" << "*.potm";	//wpp的模板
	}

	else if (!appName.compare("et", Qt::CaseInsensitive))
	{

		list<<"*.ett"<<"*.xlt" << "*.xltx" << "*.xltm";	//et的模板
	}
	else
	{
		return false;
	}
	return true;
}

void KxNewDocJsApi::recentFileSearch(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QList<QVariant> result;

	if (!args.isEmpty())
	{
		const QString &searchText = args.value("keyWord").toString();
		const QString &appName = args.value("appName").toString();

		QString fileType = m_window->getBrowserType();
		fileType = fileType.toLower();

		RecordInfoList fileList = promeApp->recentFileProxy()->recentRecords(LocalFile, -1);
		std::sort(fileList.begin(), fileList.end(), recentThan);

		foreach(const RecordInfo file, fileList)
		{
			if (fileType == QLatin1String("pagedocerofficial"))
			{
				if (exceptByOfficialfilter(file.m_fileName))
					continue;
			}
			else if (!fileType.contains(file.m_appType))
			{
				continue;
			}
			IKDocerAccount* account = nullptr;
			auto kdocercoreLite = getIKDocerCoreLite();
			if (kdocercoreLite)
				account = kdocercoreLite->getIKDocerAccount();
			bool isLogined = false;

			if (account)
				isLogined = account->isLogined();
			if (!isLogined && file.m_fileType != LocalFile)
				continue;

			if (file.m_fileName.contains(searchText, Qt::CaseInsensitive))
			{
				QMap<QString, QVariant> propMap;
				propMap["id"] = file.m_fileId;
				propMap["name"] = file.m_fileName;
				if (file.m_fileType == CloudFile)
					propMap["filePath"] = file.m_cloudPath + QDir::separator() + file.m_fileName;
				else
					propMap["filePath"] = file.m_fileAbsPath;
				propMap["publish_time"] = file.m_fileAccessTime.toStringEx("yyyy-MM-dd hh:mm");

				propMap["is_exit"] = file.m_isExist;
				if (!file.m_isNetworkFile && file.m_fileType != CloudFile)
				{
					propMap["is_exit"] = QFileInfo(file.m_fileAbsPath).exists();
				}

				propMap["fileType"] = (int)file.m_fileType;
				result.append(propMap);
			}
		}
	}
	setResult(context, result);
}

void KxNewDocJsApi::localTemplateSearch(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	QVariantMap result;
	QList<QVariant> itemList;
	int itemCount = 0;
	if (!args.isEmpty() && !krt::kcmc::support("DeployIntranetTemplateService"))
	{
		const QString &searchText = args.value("keyWord").toString();
		const QString &appName = args.value("appName").toString();
		if (!searchText.isEmpty())
		{
			QStringList nameFilters;
			if (!getFilterType(appName, nameFilters, true))
				return;

			QStringList templatePaths;
			getTemplatesPath(appName, templatePaths, true);

			QSettings tranSet(QString("%1/%2/display.ini").arg(krt::i18n::getFilePath("templates")).arg(appName), QSettings::IniFormat);
			tranSet.setIniCodec("UTF-8");
			tranSet.beginGroup("Display");

			foreach(QString path, templatePaths)
			{
				QMap<QString, QVariant> item;
				QString strTempPath = path.replace(QString("\\"), QString("/"));
				QString pathName = QFileInfo(strTempPath).fileName();

				QList<QVariant> fileNames;
				getFloderAllTemplateFiles(strTempPath, nameFilters, tranSet, fileNames);
				foreach(QVariant item, fileNames)
				{
					QMap<QString, QVariant> fileItem = item.value<QMap<QString, QVariant>>();
					const QString& itemName = fileItem["name"].toString();
					if (itemName.contains(searchText, Qt::CaseInsensitive))
					{
						itemCount++;
						itemList.append(item);
					}
				}
			}
		}
	}
	result["count"]	= itemCount;
	result["fileList"] = itemList;
	setResult(context, result);
}

void KxNewDocJsApi::execOfficialDoc(KxWebViewJSContext& context)
{
	QString data;
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty())
		data = args.value("data").toString();
	emit execOfficialDoc(data);
}

void KxNewDocJsApi::getOemSupport(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;
	QString strKey = args.value("key").toString();
	bool bValue = krt::product::isSupported(strKey);
	if (ksolite::isSupported(ksolite::IntranetVersion7)
		&& strKey == "CustomTemplate")
	{
		QString argsType = args.value("type").toString();
		if (argsType != "wps" && argsType != "wpp" && argsType != "et")
		{
			bValue = false;
		}
	}

	setResult(context, QVariant::fromValue(bValue));
}

void KxNewDocJsApi::getCfgsFileData(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;
	QString strFileName = args.value("File").toString();
	if (strFileName.isEmpty())
		return;
	QString strPath = krt::dirs::officeConfigs() + krt::dirs::separator() + strFileName;
	QFile file(strPath);
	if (!file.open(QIODevice::ReadOnly))
		return;
	QString strData = QString::fromUtf8(file.readAll().data());
	file.close();
	setResult(context, QVariant::fromValue(strData));
}

void KxNewDocJsApi::setTemplateDownDir(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if(!args.isEmpty())
	{
		KxPathProvider::setTemplateDownDir(args.value("path", "").toString());
		setResult(context, QVariant(1));
	}
	else
	{
		setResult(context, QVariant(0));
	}
}

void KxNewDocJsApi::getTemplateDownDir(KxWebViewJSContext& context)
{
	QString strTemplatePath = KxPathProvider::getTemplateDownDir(KxPathProvider::getKsoTemplateDir().path());
	strTemplatePath.replace(QString("\\"),QString("\\\\"));
	quint64 nSize = getDiskSize(strTemplatePath);

	QVariantMap variantMap;
	variantMap["path"] = strTemplatePath;
	variantMap["size"] = QString::number(nSize);
	setResult(context, variantMap);
}

void KxNewDocJsApi::getDefaultTemplateDownDir(KxWebViewJSContext& context)
{
	QVariant value = KxPathProvider::getKsoTemplateDir().path().toUtf8().data();
	setResult(context,value);
}

void KxNewDocJsApi::getEncryptString(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;
	QString strKey = args.value("key").toString();
	QVariantMap retMap;
	if (!strKey.isEmpty())
	{
		auto ikdocerBase = getIKDocerBase();
		if (ikdocerBase)
			ikdocerBase->encryptString(strKey, retMap);
	}
	setResult(context, retMap);
}

void KxNewDocJsApi::getCustomOfficialTemplate(KxWebViewJSContext& context)
{
	QList<QVariant> result;

	if (krt::kcmc::support("OfficialViewEnable"))
	{
		QStringList strListSuffix;
		if (krt::kcmc::support("OfficialDocUseUOF") && krt::kcmc::support("WPSUof") ||
			krt::kcmc::support("OfficialFakeUOT"))
		{
			strListSuffix.append("*.uot");
		}
		else
		{
			strListSuffix.append("*.dotx");
			strListSuffix.append("*.wpt");
		}

		QString strTemplatePath = krt::dirs::officeHome() + "/templates/officialtemplate";
		getFloderAllTemplateFiles(strTemplatePath, strListSuffix, QSettings(), result, false, QDir::Time);

		strTemplatePath = krt::dirs::officeHome() + "/templates/importofficial";
		getFloderAllTemplateFiles(strTemplatePath, strListSuffix, QSettings(), result, false, QDir::Time);

		strTemplatePath = krt::i18n::getFilePath("templates") + "/officialtemplate/oem";
		getFloderAllTemplateFiles(strTemplatePath, strListSuffix, QSettings(), result, false, QDir::Time);
	}

	setResult(context, result);
}

void KxNewDocJsApi::getCustomTemplateFileInfo(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty() || !krt::kcmc::support("CustomTemplate"))
		return;

	const QString& appName = args.value("appName").toString();
	QStringList nameFilters;
	if (!getFilterType(appName, nameFilters, false))
		return;

	QString customTemplatePath = krt::i18n::getFilePath("templates") + QDir::separator() + appName + QDir::separator() + "customtemplate";

	QDir dir(customTemplatePath);
	dir.cdUp();
	QSettings tranSet(dir.filePath("display.ini"), QSettings::IniFormat);
	tranSet.setIniCodec("UTF-8");
	tranSet.beginGroup("Display");
	QString tagName = tranSet.value("customtemplate", "customtemplate").toString();

	QList<QVariant> tmpResult;
	getFloderAllTemplateFiles(customTemplatePath, nameFilters, QSettings(), tmpResult, false, QDir::Time);

	QMap<QString, QVariant> item;
	item["tagName"] = tagName;
	item["filePath"] = customTemplatePath;
	item["fileList"] = tmpResult;

	QList<QVariant> result;
	result.append(item);
	setResult(context, result);
}

void KxNewDocJsApi::getNationalOfficialTemplate(KxWebViewJSContext& context)
{
	QList<QVariant> result;

	if (krt::kcmc::support("OfficialViewEnable"))
	{
		QString strTemplatePath = krt::i18n::getFilePath("templates") + "/wps/GB9704_2012_Templates";

		QDir dir(strTemplatePath);
		dir.cdUp();

		QSettings settingsName(dir.filePath("display.ini"), QSettings::IniFormat);
		settingsName.setIniCodec("UTF-8");
		settingsName.beginGroup("Display");

		QString strOfficialTemplateSuffix;
		if (krt::kcmc::support("OfficialDocUseUOF") && krt::kcmc::support("WPSUof") || krt::kcmc::support("OfficialFakeUOT"))
			strOfficialTemplateSuffix = "*.uot";
		else
			strOfficialTemplateSuffix = "*.dotx";

		getFloderAllTemplateFiles(strTemplatePath, QStringList(strOfficialTemplateSuffix), settingsName, result);
	}

	setResult(context, result);
}


void KxNewDocJsApi::registPushUIEventHandler(KxWebViewJSContext& context)
{
	emit jsScriptReady();
	QMetaObject::invokeMethod(this, "doInitCopilotEntry", Qt::QueuedConnection);
}

void KxNewDocJsApi::openPromeResumePage(KxWebViewJSContext& context)
{
	if (auto hanlder = docerUtilHandler())
		hanlder->doOpenPromePluginPage(context, "kpromeresume", "pageResume");
}

void KxNewDocJsApi::openByCloudDocsPage(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	const QString url = args.value("url").toString();
	if (url.isEmpty())
		return;

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(widgetParent());
	if (!mw)
		return;
	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;

	const QString fileId = args.value("fileId").toString();
	if (!fileId.isEmpty())
	{
		int cnt = contentArea->pageCount();
		for (int i = 0; i < cnt; ++i)
		{
			KPromePage* page = contentArea->pageAt(i);
			Q_ASSERT(page);
			if (KPromePluginWidget::pageKDocsPreview == page->type())
			{
				QString openedFileID = page->property("File_ID").toString();
				if (!openedFileID.isEmpty() && openedFileID == fileId)
				{
					contentArea->setCurrentPage(page);
					KPromeSubPage* subPage = page->initialSubPage();
					if (subPage)
						subPage->activate();
					mw->showToFront();
					return;
				}
			}
		}
	}

	KPromePage* page = contentArea->addPage(KPromePluginWidget::pageKDocsPreview);
	if (!page)
		return;

	const QString suffix = args.value("suffix").toString();
	if (KFileFilter::instance()->isSupportExt(filterWps, suffix))
		page->notify(QString("file_type:") + QString::number(KPromePluginWidget::pageWps));
	else if (KFileFilter::instance()->isSupportExt(filterEt, suffix))
		page->notify(QString("file_type:") + QString::number(KPromePluginWidget::pageEt));
	else if (KFileFilter::instance()->isSupportExt(filterWpp, suffix))
		page->notify(QString("file_type:") + QString::number(KPromePluginWidget::pageWpp));
	else if (KFileFilter::instance()->isSupportExt(filterPdf, suffix))
		page->notify(QString("file_type:") + QString::number(KPromePluginWidget::pagePdf));

	page->notify(url);
	contentArea->setCurrentPage(page);
	page->newDocument(QString(), QVariantList(),
		KPromePage::LastOneDirectExeOtherProxy);
	if (fileId != "-1")
		page->setProperty("File_ID", fileId);
}

void KxNewDocJsApi::openChuangKitPage(KxWebViewJSContext& context)
{
	if (auto hanlder = docerUtilHandler())
	{
		// 设置属性，用于调用handler中的方法时只关闭有此处新建的。
		KPromePage* promePage = KTik::findParentByType<KPromePage>(this);
		if (nullptr == promePage)
			return;

		if (promeApp)
		{
			if (KPromeOpenFileSourceInfoCollectMgr *fileSrcInfoColMgr = promeApp->getOpenFileSourceInfoCollectMgr())
			{
				QVariantMap infoMap;
				infoMap.insert("openSource", QLatin1String(g_newDosPageIdentifier));
				fileSrcInfoColMgr->sendDocOpenSourceInfo("chuangkit", "online", infoMap);
			}
		}
		promePage->setProperty("closeAfterOpen", true);
		hanlder->doOpenPromePluginPage(context, "kpromechuangkit", "pageChuangKit");
	}
}

void KxNewDocJsApi::getCorpSpecialGroupId(KxWebViewJSContext& context)
{
	if (!m_proxyExLib)
		m_proxyExLib = new KxCloudFileProxyExLib(this);
	
	const QVariantMap args = parseContextArgs(context);
	QString callback = args.value("callback", "").toString();
	KQingIpcLoginCache loginCache;
	if (auto cloudsvr = kcoreApp->cloudService())
		cloudsvr->getUserLoginInfo(&loginCache);
	
	QPointer<KxNewDocJsApi> spThis(this);
	
	m_proxyExLib->getCorpSpecialGroupId(loginCache.companyID, [=](const QString& groupId) {
		if (!spThis)
			return;
		QVariantMap result, variantMap;
		variantMap["groupid"] = groupId;
		result["callstatus"] = "ok";
		result["result"] = variantMap;
		callbackToJS(callback, JsonHelper::convertVariantToString(result));
	});
}


void KxNewDocJsApi::getCorpId(KxWebViewJSContext& context)
{
	QVariantMap variantMap;
	variantMap["corpid"] = KxCreateDocConfig::instance().getCurrentWorkspaceId();
	setResult(context, variantMap);
}


void KxNewDocJsApi::getKLMEntryId(KxWebViewJSContext& context)
{
	QVariantMap variantMap;
	variantMap["entry_id"] = KNewDocsHelper::getPageEntryId(KTik::findParentByType<KPromeNewDocsPage>(this));
	setResult(context, variantMap);
}


void KxNewDocJsApi::isSupportDOPFunc(KxWebViewJSContext& context)
{
	context.Result["callstatus"] = "ok";
	context.Result["state"] = isPCSupportDOPFunc();
}


void KxNewDocJsApi::openDocsOnlineFile(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return contextStateRsp(context, false, "args isEmpty");

	const QString fileId = args.value("fileId").toString();
	const QString cId = args.value("cId").toString();
	const QString officeType = args.value("officeType").toString();
	const QString fileName = args.value("filename").toString();
	quint64 startTime = args.value("startTime").toULongLong() ;
	if (fileId.isEmpty() || officeType.isEmpty())
		return contextStateRsp(context, false, "param isEmpty");

	if (switchToPageByFileId(fileId))
		return contextStateRsp(context, true, "");

	if (!promeApp)
		return contextStateRsp(context, false, "internal error");

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return contextStateRsp(context, false, "internal error");

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return contextStateRsp(context, false, "internal error");

	if (mw->headerbarType() != KPromeHeaderBarBase::Standalone)
	{
		KPromeNewDocsPage* newDocsPage = KTik::findParentByType<KPromeNewDocsPage>(this);
		if (nullptr == newDocsPage)
			return;

		KPromeSubPage* subPage = newDocsPage->initialSubPage();
		if (nullptr == subPage)
			return;
		KPromeTab* tab = subPage->getTab();
		if (nullptr == tab)
			return;
		tab->setAllowReAttachSubwindow(true);
		tab->reAttachSubPage(nullptr);
	}

	KPromePage* page = findExistingCloudDocsPage(contentArea);
	if (!page)
		page = contentArea->addPage(KPromePluginWidget::pageKDocsPreview);

	if (!page)
		return contextStateRsp(context, false, "internal error");

	QString url;
	if (kcoreApp && kcoreApp->getDomainMgr())
		url = kcoreApp->getDomainMgr()->getDomainUrl("kdocs_api_spec");
	if (url.isEmpty())
		return contextStateRsp(context, false, "kdocs host isEmpty");
	url.append(QString("/l/%1").arg(cId.isEmpty() ? fileId : cId));
	{
		QUrl targetUrl(url);
		if (!targetUrl.isValid())
		{
			return contextStateRsp(context, false, "url not valid");
		}
		QUrlQuery urlQuery(targetUrl);
		urlQuery.addQueryItem("localId", QString());
		urlQuery.addQueryItem("officeType", officeType);
		urlQuery.addQueryItem("openEnv", "hybrid");
		urlQuery.addQueryItem("startTime", QString::number(startTime));
		urlQuery.addQueryItem("newFile", "true");
		urlQuery.addQueryItem("documentTitle",
			QUrl::toPercentEncoding(QFileInfo(fileName).completeBaseName()));
		urlQuery.addQueryItem("from", "knewdocs-openDocsOnlineFile");
		urlQuery.addQueryItem("createEndTime",
			QString::number(QDateTime::currentMSecsSinceEpoch()));
		targetUrl.setQuery(urlQuery);
		url = targetUrl.toString();
	}

	QVariantMap variantMap;
	variantMap["url"] = url;
	variantMap["suffix"] = officeType;
	variantMap["fileId"] = fileId;
	variantMap["fileName"] = fileName;
	QGenericArgument argument;
	if (promeBrowserModuleCallProxy(krt::fromUtf16(__X("openFileWithKDocsDrive")),
		Q_ARG(QVariantMap, variantMap), argument))
	{
		return contextStateRsp(context, true, "");
	}

	if (KPromeOpenFileSourceInfoCollectMgr *fileSrcInfoColMgr = promeApp->getOpenFileSourceInfoCollectMgr())
	{
		QVariantMap infoMap;
		infoMap.insert("openSource", QLatin1String(g_newDosPageIdentifier));
		fileSrcInfoColMgr->sendDocOpenSourceInfo(fileName, "online", infoMap);
	}

	page->setProperty("fileName", fileName);
	page->notify("suffix:" + officeType);
	page->notify(url);
	contentArea->setCurrentPage(page);
	page->newDocument(QString(), QVariantList(),
		KPromePage::LastOneDirectExeOtherProxy);
	if (fileId != "-1")
		page->setProperty("File_ID", fileId);

	contextStateRsp(context, true, "");
	emit closeTab();
}


void KxNewDocJsApi::newDocsOnlineFile(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return contextStateRsp(context, false, "args isEmpty");

	const QString templateId = args.value("templateId").toString();
	const QString officeType = args.value("officeType").toString();
	const QString groupId = args.value("groupId").toString();
	const QString parentId = args.value("parentId").toString();
	if (officeType.isEmpty())
		return contextStateRsp(context, false, "officeType isEmpty");

	if (!promeApp)
		return contextStateRsp(context, false, "internal error");

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return contextStateRsp(context, false, "internal error");

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return contextStateRsp(context, false, "internal error");

	if (mw->headerbarType() != KPromeHeaderBarBase::Standalone)
	{
		KPromeNewDocsPage* newDocsPage = KTik::findParentByType<KPromeNewDocsPage>(this);
		if (nullptr == newDocsPage)
			return;

		KPromeSubPage* subPage = newDocsPage->initialSubPage();
		if (nullptr == subPage)
			return;

		KPromeTab* tab = subPage->getTab();
		if (nullptr == tab)
			return;

		tab->setAllowReAttachSubwindow(true);
		tab->reAttachSubPage(nullptr);
	}

	KPromePage* page = contentArea->addPage(KPromePluginWidget::pageKDocsPreview);
	if (!page)
		return contextStateRsp(context, false, "internal error");

	QString url;
	if (kcoreApp && kcoreApp->getDomainMgr())
		url = kcoreApp->getDomainMgr()->getDomainUrl("kdocs");
	if (url.isEmpty())
		return contextStateRsp(context, false, "kdocs host isEmpty");

	qint64 nowTime = QDateTime::currentMSecsSinceEpoch();
	url = url.append(
		QString("/office/new/%1?localId=&officeType=%1&templateId=%2"
			"&startTime=%3&groupId=%4&parentId=%5&openEnv=hybrid")
		.arg(officeType).arg(templateId).arg(nowTime).arg(groupId).arg(parentId));

	page->notify("suffix:" + officeType);
	page->notify(url);
	contentArea->setCurrentPage(page);
	page->newDocument(QString(), QVariantList(),
		KPromePage::LastOneDirectExeOtherProxy);

	contextStateRsp(context, true, "");
	emit closeTab();
}

void KxNewDocJsApi::openProcessonPage(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;

	openProcessonPage(args);
}


void KxNewDocJsApi::openProcessonPage(const QVariantMap& variantMap)
{
	QString fileId = variantMap.value("fileId").toString();
	QString groupId = variantMap.value("groupId").toString();
	QString parentId = variantMap.value("parentId").toString();
	QString pageType = variantMap.value("pageType").toString();
	QString action = variantMap.value("action").toString();
	if (fileId.isEmpty() || groupId.isEmpty()
		|| parentId.isEmpty() || pageType.isEmpty())
		return;

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return;

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;

	KPromePage* cloudDocPage = contentArea->pageByType(KPromePluginWidget::pageCloudDocs);
	if (cloudDocPage)
		QMetaObject::invokeMethod(cloudDocPage, "refresh");

	QJsonObject pageInfo;
	pageInfo.insert("fileid", fileId);
	pageInfo.insert("groupid", groupId);
	pageInfo.insert("parentid", parentId);
	pageInfo.insert("newPage", true);
	pageInfo.insert("action", action);
	QString strPageInfo = JsonHelper::convertQJsonToString(pageInfo);

	KPromePluginWidget::WidgetType widgetType = KPromePluginWidget::pageProcesson_Flow;
	if (pageType == "mind")
		widgetType = KPromePluginWidget::pageProcesson_Mind;
	KPromePage* page = contentArea->addPage(widgetType, strPageInfo);
	if (!page)
		return;

	if (KPromeOpenFileSourceInfoCollectMgr *fileSrcInfoColMgr = promeApp->getOpenFileSourceInfoCollectMgr())
	{
		QVariantMap infoMap;
		infoMap.insert("openSource", QLatin1String(g_newDosPageIdentifier));
		fileSrcInfoColMgr->sendDocOpenSourceInfo(pageType, "online", infoMap);
	}

	page->openFiles(QStringList() << fileId << groupId);

	emit closeTab();
}

void KxNewDocJsApi::newProcessOnFile(KxWebViewJSContext& context)
{
	enum class NewPoFileCodes
	{
		Success = 0,
		NotLogin,
		JsonError,
		CloudRequestError,
		CloudSpaceFullError
	};
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;
	if (account)
		isLogined = account->isLogined();
	if (!isLogined)
	{
		QVariantMap result;
		result["code"] = (int)NewPoFileCodes::NotLogin;
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "user not logined";
		context.Result["result"] = JsonHelper::variantMapToJsonValue(result);
		return;
	}

	QVariantMap contextArgs = parseContextArgs(context);

	QString name = contextArgs.value("name").toString();
	QString category = contextArgs.value("category", "0").toString();
	QString templ = contextArgs.value("template").toString();
	QString defTempId = contextArgs.value("def_temp_id").toString();
	QString type = QFileInfo(name).suffix();

	if (name.isEmpty() || type.isEmpty())
	{
		QVariantMap result;
		result["code"] = (int)NewPoFileCodes::JsonError;
		context.Result["callstatus"] = "error";
		context.Result["errormsg"] = "input json invalid.";
		context.Result["result"] = JsonHelper::variantMapToJsonValue(result);
		return;
	}

	QVariantMap params;
	params.insert("processon_template", templ);
	params.insert("processon_category", category);
	params.insert("name", name);
	params.insert("pageType", type == "pom" ? "flow" : "mind");
	params.insert("action", contextArgs.value("action").toString());

	QMap<QString, QString> otherArgs;
	if (!defTempId.isEmpty())
		otherArgs.insert("processon_def_temp_id", defTempId);

	std::shared_ptr<DownloadFailInfo> spErroInfo = std::make_shared<DownloadFailInfo>();
	std::shared_ptr<QVariantMap> spParams = std::make_shared<QVariantMap>();
	(*spParams) = params;

	QPointer<KxNewDocJsApi> spThis(this);
	auto _onReject = [=](docer::base::closure resolve, docer::base::closure reject)
	{
		if (!spThis)
			return;

		KxWebViewJSContext cpContext = context;
		QVariantMap result;
		int nCode = (int)NewPoFileCodes::CloudRequestError;
		if (spParams && spParams->contains("errorCode"))
			nCode = spParams->value("errorCode").toInt();

		result["code"] = nCode;
		result["errorInfo"] = spErroInfo->toVariantMap();
		cpContext.Result["callstatus"] = "error";
		cpContext.Result["errormsg"] = "cloud server api request error.";
		cpContext.Result["result"] = JsonHelper::variantMapToJsonValue(result);
		spThis->publishResult(cpContext);

		std::wstring logInfo = QString("newProcessOnFile::Promise _onRequestError. %1").arg(JsonHelper::variantMapSerialize(result)).toStdWString();
		KxLoggerLite::writeInfo(L"knewdocs", logInfo);

		reject();
	};

	QPointer<docer::base::KPromise> spPromise = docer::base::KPromise::make(
		[=](docer::base::closure resolve, docer::base::closure reject) {
			KxLoggerLite::writeInfo(L"knewdocs", L"newProcessOnFile::Promise requestGroupId.");

			docer_processon::requestGroupId(
				[=](const QString& groupId, int companyId, const QByteArray& ba)
				{
					(*spParams)["groupId"] = groupId;
					resolve();
				},
				[=](DownloadFailInfo info)
				{
					*spErroInfo = info;
					reject();
				}
				);
		}
	)->then(
		[=](docer::base::closure resolve, docer::base::closure reject) {
			KxLoggerLite::writeInfo(L"knewdocs", L"newProcessOnFile::Promise requestNewFile.");

			auto newParams = *spParams;
			docer_processon::requestNewFile(
				newParams["groupId"].toString(), newParams["name"].toString(),
				newParams["processon_category"].toString(), newParams["processon_template"].toString(), otherArgs,
				[=](const QString& fileId, const QByteArray& ba)
				{
					(*spParams)["fileId"] = fileId;
					(*spParams)["parentId"] = "0";
					resolve();
				},
				[=](DownloadFailInfo info)
				{
					*spErroInfo = info;
					auto jo = QJsonDocument::fromJson(info.content).object();
					if (jo.value("result").toString().contains("SpaceFull", Qt::CaseInsensitive))
					{
						(*spParams)["errorCode"] = (int)NewPoFileCodes::CloudSpaceFullError;
					}
					reject();
				}
				);
		}, _onReject
	)->then(
		docer::base::mustSuccess([=]() {
			if (!spThis)
				return;
			std::wstring logInfo = QString("newProcessOnFile::Promise openFiles. %1").arg(JsonHelper::variantMapSerialize(*spParams)).toStdWString();
			KxLoggerLite::writeInfo(L"knewdocs", logInfo);

			openProcessonPage(*spParams);
			}), _onReject
	);

		spPromise->run();
}

void KxNewDocJsApi::newBlankOfficialDocument(KxWebViewJSContext& context)
{
	auto filePath = docer::base::KCurrentModule::getModuleResourceDirPath().append(QLatin1String("/res/blanktemplate/blank_official.docx"));
	auto formattedFilePath = QDir::toNativeSeparators(filePath);
	if (!QFile::exists(formattedFilePath))
	{
		QVariantMap result;
		result["result"] = false;
		result["errormsg"] = "file not exist.";
		setResult(context, result);
		return;
	}

	QString tartgetType = m_window->getBrowserType();
	emit openDocument(formattedFilePath, "", "newBlankDocument", tartgetType, "");

	QVariantMap result;
	result["result"] = true;
	result["errormsg"] = "success";
	setResult(context, result);
}

void KxNewDocJsApi::getLocalTemplateClassify(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty())
	{
		QString strKey = args.value("key").toString();
		QString strValue;
		QString strStamp;
		QVariantMap result;
		if (m_localstorage->webStorageGet(strKey, strValue, strStamp))
		{
			bool isNeedAdd = false;
			if (strValue.isEmpty())
			{
				strValue = "all,wps,et,wpp";
				isNeedAdd = true;
			}
			if (isNeedAdd)
				m_localstorage->webStorageAdd(strKey, strValue, QString());
			result["key"] = strKey;
			result["value"] = strValue;
		}
		setResult(context, result);
	}
}

void KxNewDocJsApi::removeRecentTemplate(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty() && m_localstorage->db())
	{
		QList<QVariant> result;
		QJsonArray templatePathArray = args["templatePath"].toJsonArray();
		for (int index = 0; index < templatePathArray.size(); ++index)
		{
			QString templatePath = templatePathArray[index].toString();
			QFileInfo fileInfo(templatePath);
			if (!fileInfo.exists())
				continue;

			QMap<QString, QVariant> item;
			QString templateName = fileInfo.baseName();
			QString strCondition = QString("templateName='%1'").arg(m_localstorage->db()->toSQLSafeStr(templateName));
			item[templatePath] = m_localstorage->db()->deleteItem(g_templateTableName, strCondition);
			result.push_back(item);
		}
		setResult(context, result);
	}
}

void KxNewDocJsApi::createLocalTemplateTable()
{
	if (m_localstorage->db())
	{
		bool bExist = false;
		if (!m_localstorage->db()->isTableExist(g_templateTableName, bExist))
			return;

		if (!bExist)
		{
			m_localstorage->db()->createTable(
									g_templateTableName,
									"Id TEXT PRIMARY KEY COLLATE NOCASE NOT NULL, "
									"templateName TEXT NOT NULL , "
									"templatePath TEXT NOT NULL , "
									"templateType TEXT NOT NULL , "
									"templateUseTime TEXT");
		}
	}
}

void KxNewDocJsApi::getRecentUseTemplate(KxWebViewJSContext &context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (!args.isEmpty() && m_localstorage->db())
	{
		ksolite::KDBQuery dbQuery;
		if (!m_localstorage->db()->getItems(g_templateTableName, dbQuery))
			return;

		const QString& appName = args.value("appName").toString();
		QSettings wpsTranSet(QString("%1/%2/display.ini").arg(krt::i18n::getFilePath("templates")).arg("wps"), QSettings::IniFormat);
		wpsTranSet.setIniCodec("UTF-8");
		wpsTranSet.beginGroup("Display");

		QSettings etTranSet(QString("%1/%2/display.ini").arg(krt::i18n::getFilePath("templates")).arg("et"), QSettings::IniFormat);
		etTranSet.setIniCodec("UTF-8");
		etTranSet.beginGroup("Display");

		QSettings wppTranSet(QString("%1/%2/display.ini").arg(krt::i18n::getFilePath("templates")).arg("wpp"), QSettings::IniFormat);
		wppTranSet.setIniCodec("UTF-8");
		wppTranSet.beginGroup("Display");

		QList<QVariant> result;
		while (!dbQuery.eof())
		{
			QMap<QString, QVariant> item;

			QString tempateType = dbQuery.getStringField("templateType", "");
			if (tempateType.compare(appName, Qt::CaseInsensitive) != 0)
			{
				dbQuery.nextRow();
				continue;
			}

			QString templateName = dbQuery.getStringField("templateName", "");
			if (tempateType.compare("wps", Qt::CaseInsensitive) == 0)
				templateName = wpsTranSet.value(templateName, templateName).toString();
			else if (tempateType.compare("et", Qt::CaseInsensitive) == 0)
				templateName = etTranSet.value(templateName, templateName).toString();
			else
				templateName = wppTranSet.value(templateName, templateName).toString();

			item["name"] = templateName;
			item["filePath"] = dbQuery.getStringField("templatePath", "");
			item["templateType"] = tempateType;
			item["templateUseTime"] = dbQuery.getStringField("templateUseTime", "");

			QFileInfo fileInfo(item["filePath"].toString());
			QString previewPNG = QDir::toNativeSeparators(fileInfo.absolutePath() + "/" + fileInfo.baseName() + ".png");
			QFile thumbFile(previewPNG);
			if (thumbFile.open(QIODevice::ReadOnly))
			{
				item["preview"] = thumbFile.readAll().toBase64().data();
				thumbFile.close();
			}
			result.push_back(item);
			dbQuery.nextRow();
		}
		wpsTranSet.endGroup();
		etTranSet.endGroup();
		wppTranSet.endGroup();

		setResult(context, result);
	}
}

void KxNewDocJsApi::addRecentUseTempalte(const QString& filePath)
{
	if (!filePath.isEmpty() && m_localstorage->db())
	{
		QFileInfo fileInfo(filePath);
		if (!fileInfo.exists())
			return;

		QString templateName = fileInfo.baseName();
		if (templateName.isEmpty())
			return;

		QString templateType = getTemplateTypeBySuffix(fileInfo.suffix());
		QString strCondition = QString("templateName='%1'").arg(m_localstorage->db()->toSQLSafeStr(templateName));

		ksolite::KDBQuery dbQuery;
		if (!m_localstorage->db()->getItem(g_templateTableName, dbQuery, strCondition))
			return;

		if (dbQuery.eof())
		{
			QString strFields = "Id,templateName,templatePath,templateType,templateUseTime";
			QString strValues = QString("'%1','%2','%3','%4','%5'"
				).arg(m_localstorage->db()->toSQLSafeStr(QUuid::createUuid().toString())
				).arg(m_localstorage->db()->toSQLSafeStr(templateName)
				).arg(m_localstorage->db()->toSQLSafeStr(filePath)
				).arg(m_localstorage->db()->toSQLSafeStr(templateType)
				).arg(m_localstorage->db()->toSQLSafeStr(QDateTime::currentDateTime().toStringEx("yyyy-MM-dd hh:mm")));
			m_localstorage->db()->insertItem(g_templateTableName, strFields, strValues);
		}
		else
		{
			QString strFieldsAndValue = QString("templatePath='%1',templateType='%2', templateUseTime='%3'"
				).arg(m_localstorage->db()->toSQLSafeStr(filePath)
				).arg(m_localstorage->db()->toSQLSafeStr(templateType)
				).arg(m_localstorage->db()->toSQLSafeStr(QDateTime::currentDateTime().toStringEx("yyyy-MM-dd hh:mm")));
			QString strCondition = QString("templateName='%1'").arg(m_localstorage->db()->toSQLSafeStr(templateName));
			m_localstorage->db()->updateItem(g_templateTableName, strFieldsAndValue, strCondition);
		}
	}
}

void KxNewDocJsApi::getKDocerAiTemplate(KxWebViewJSContext& context)
{
	QVariantMap paramsMap = parseContextArgs(context);
	paramsMap.insert("fileType", "wps");
	paramsMap.insert("ai", true);
	if (auto newdocsPage =KTik::findParentByType<KPromeNewDocsPage>(m_window))
	{
		newdocsPage->setNoClose(true);
	}
	newBlankDocument(paramsMap);
	QVariantMap result;
	result["result"] = true;
	result["errormsg"] = "success";
	setResult(context, result);
}


void KxNewDocJsApi::closePage(KxWebViewJSContext& context)
{
	emit closeTab();

	QVariantMap result;
	result["result"] = true;
	result["errormsg"] = "success";
	setResult(context, result);
}


void KxNewDocJsApi::setNewCloudDocSwitchStatus(KxWebViewJSContext& context)
{
	QVariantMap paramsMap = parseContextArgs(context);
	QVariantMap result;
	if (!paramsMap.contains("switchStatus"))
	{
		result.insert("result", true);
		result.insert("errormsg", "params is empty");
		setResult(context, result);
		return;
	}
	bool switchStatus = paramsMap.value("switchStatus", false).toBool();
	KNewDocsHelper::setNewCloudDocSwitchStatus(switchStatus);
	QString comp = paramsMap.value("comp", QString()).toString();

	result.insert("result", true);
	result.insert("errormsg", "success");
	setResult(context, result);

	QHash<QString, QString> args;
	args.insert("operation", [=]() ->QString
		{
			if (true == switchStatus)
				return QLatin1String("switch_click_open");
			else
				return QLatin1String("switch_click_close");
		}());
	args.insert("motion", "click");
	args.insert("status", switchStatus ? "open" : "close");
	args.insert("source", "create_new");
	args.insert("identity", getAccountType());
	args.insert("comp", comp);
	KDocerUtils::postGeneralEvent(QLatin1String("doc_create_cloud_switch"), args);

	QHash<QString, QString> params;
	params.insert("motion", "switch");
	params.insert("scene", "switch");
	params.insert("identity", getAccountType());
	params.insert("switch_status", switchStatus ? "1" : "0");
	KDocerUtils::postGeneralEvent(QLatin1String("doc_create_cloud_status"), params);
}

void KxNewDocJsApi::navigateToStartPage(KxWebViewJSContext& context)
{

	bool bNaigate = false;
	QVariantMap result;

	struct KBlockRequestWorkspaceStatus
	{
		KBlockRequestWorkspaceStatus()
		{
			if (promeApp && promeApp->cloudSvrProxy())
			{
				promeApp->cloudSvrProxy()->addRequestWorkspaceStatusBlock();
			}
		}
		~KBlockRequestWorkspaceStatus()
		{
			if (promeApp && promeApp->cloudSvrProxy())
			{
				promeApp->cloudSvrProxy()->releaseRequestWorkspaceStatusBlock();
			}
		}
	};

	KBlockRequestWorkspaceStatus blockRequestWorkspaceStatus;

	do 
	{
		KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
		if (!mw)
		{
			result.insert("errormsg", "can't find mainwindow");
			break;
		}

		KPromeCentralArea* centralArea = mw->centralArea();
		if (!centralArea)
		{
			result.insert("errormsg", "can't find centralArea");
			break;
		}

		KPromeSubPage* subPage = centralArea->startPageProxySubPage();
		if (subPage)
			subPage->activate();

		KPromePage* promePage = centralArea->pageByType(KPromePluginWidget::pageStartPage);

		if (!promePage)
		{
			result.insert("errormsg", "can't find promePage");
			break;
		}

		centralArea->setCurrentPage(promePage);
		promePage->notify("navigationStartPageCloudDocsLocation");
		bNaigate = true;
		result.insert("errormsg", "success");

	} while (false);

	result.insert("result", bNaigate);
	setResult(context, result);

}

void KxNewDocJsApi::getCompanyLogoInfo(KxWebViewJSContext& context)
{
	QVariantMap result;
	bool bRet = false;
	const KWorkspaceCompanyInfo* info = getWorkspaceCompanyInfo();
	if (nullptr != info)
	{
		result["url"] = info->logo;
		result["companyName"] = info->name;
		result["companyId"] = info->id;
		bRet = true;
	}
	result["result"] = true;
	result["errormsg"] = bRet ? "success" : "error";
	setResult(context, result);
}

void KxNewDocJsApi::changeDocType(KxWebViewJSContext& context)
{
	//服务端提供参数
	QVariantMap paramsMap = parseContextArgs(context);
	QString argsType = paramsMap.value("type", "").toString();
	QString tabType = getTartgetBrowserType(argsType);

	bool ret = false;
	do
	{
		KPromeNewDocsPage* page = KTik::findParentByType<KPromeNewDocsPage>(this);
		if (nullptr == page)
			break;

		KPromeSubPage* subPage = page->initialSubPage();
		if (nullptr == subPage)
			break;

		KPromeTab* tab = subPage->getTab();
		if (nullptr == tab)
			break;

		//修改Tab显示的文字，图标
		KPromeNewDocsTabBarConfig* pConfig = KPromeNewDocsTabBarConfig::getInstance();
		if (nullptr == pConfig)
			break;

		KPromeNewDocsTabInfoObj *pConfigObj;
		if (m_window->getFrom() == "templateLib_fixed_tab") // 存在部分系统，固定模版库tab被刷新导致图标丢失
		{
			pConfigObj = pConfig->getInfoObj(KPromeNewDocsTabInfoObj::NewDocTabType::pageTemplateLib);
		}
		else
		{
			int rawTabType = KPromeNewDocsTabInfoObj::convertTabIntType(tabType);
			pConfigObj = pConfig->getInfoObj(rawTabType);
		}

		if (nullptr == pConfigObj)
			break;

		subPage->setWindowTitle(pConfigObj->text());
		if (nullptr != tab)
		{
			tab->setIcon(pConfigObj->icon());
			tab->update();
		}
		ret = true;
	} while (false);

	setResult(context, QVariant::fromValue(ret));
}

void KxNewDocJsApi::getInitialDocType(KxWebViewJSContext& context)
{
	//客户端提供参数
	QString browserType;
	if (m_window)
	{
		browserType = m_window->getBrowserType();
	}

	QString type = getTargetArgsType(browserType);
	setResult(context, QVariant::fromValue(type));
}

void KxNewDocJsApi::showLocalTemplates(KxWebViewJSContext& context)
{
	//服务端提供参数
	QVariantMap paramsMap = parseContextArgs(context);
	QVariantMap result;
	QString localTemplateType = paramsMap.value("type", "").toString();

	if (localTemplateType != "wps" && localTemplateType != "wpp"
		&& localTemplateType != "et")
	{
		setResult(context, QVariant::fromValue(false));
		return;
	}

	bool ret = false;
	do
	{
		KPromeMainWindow* mw = promeApp->currentPromeMainWindow();
		if (!mw)
			break;

		KPromeCentralArea* centralArea = mw->centralArea();
		if (!centralArea)
			break;

		auto page = centralArea->addPage(KPromePluginWidget::pageNewDocs);
		if (!page)
			break;

		QVariantMap infoMap;
		infoMap.insert("from", "v7jscb-template");
		infoMap.insert("appName", localTemplateType.toStdString().data());
		ret = page->newDocument(QString(), QVariantList(),
			KPromePage::LastOneDirectExeOtherProxy,
			nullptr,
			infoMap);
	} while (false);
	
	setResult(context, QVariant::fromValue(ret));
}

void KxNewDocJsApi::useTemplateFile(KxWebViewJSContext& context)
{
	//服务端提供参数
	QVariantMap paramsMap = parseContextArgs(context);
	QString fileName = paramsMap.value("fileName", "").toString();
	QString md5 = paramsMap.value("sha256", "").toString();
	QString downloadUrl = paramsMap.value("downloadUrl", "").toString();
	qint64 size = paramsMap.value("fileSize", 0).toLongLong();
	QString templateId = paramsMap.value("templateId", "").toString();
	QString callback = context.getValue("callback", "").toString();

	fileName = fileName.replace(QRegExp(QString::fromLatin1("[\\\\/:*?\"<>|]")), "");
	DownloadArgs downloadData;
	downloadData.commonArgs.plgName = KPluginName;
	downloadData.commonArgs.plgVer = docer::base::KCurrentModule::getFileVersion();
	downloadData.saveArgs.bUnzip = false;
	downloadData.saveArgs.bCheckMd5 = false;
	downloadData.resourceArgs.md5 = md5;
	downloadData.resourceKey = "template";
	downloadData.resourceArgs.id = templateId;
	downloadData.resourceArgs.resSize = size;

	QString decodeUrl = QUrl::fromPercentEncoding(downloadUrl.toUtf8());
	QString decodeName = QUrl::fromPercentEncoding(fileName.toUtf8());
	downloadData.saveArgs.saveFilePath = getLocalPath(decodeUrl, decodeName);
	downloadData.resourceArgs.urls << downloadUrl;

	if (ksolite::isSupported(ksolite::IntranetVersion7))
	{
		appendDownloadArgHeaders(downloadData);
	}

	QString id = downloadTemplate(downloadData, "", false, callback);

	if (!id.isEmpty())
	{
		m_callbackMap.insert(id, callback);
		auto newDocsPage = getNewDocsPage();
		if (newDocsPage)
			newDocsPage->incDownloadTemplateCount();
		m_downloadfileIdSet.insert(templateId);
	}
}

void KxNewDocJsApi::getDisplayDocType(KxWebViewJSContext &context)
{
	//客户端提供参数
	QString browserType = "wps;et;wpp";
	setResult(context, QVariant::fromValue(browserType));
}

void KxNewDocJsApi::prepareCloudDocsPage(KxWebViewJSContext& context)
{
	if (!m_window)
		return;

	QVariantMap paramsMap = parseContextArgs(context);
	QString from = paramsMap.value(k_openFrom, "knewdocs").toString();

	QVariantMap preloadParams;
	preloadParams["entry"] = from;
	kclouddocspagepreload::precreatedCloudDocsPage(m_window, preloadParams);
}

void KxNewDocJsApi::openBrowserPage(KxWebViewJSContext& context)
{
	QVariantMap paramsMap = parseContextArgs(context);
	QString url = paramsMap.value("url", "").toString();
	if (!QUrl(url).isValid())
	{
		return contextStateRsp(context, false, "url not valid");
	}
	bool bUrlToolbarVisible = paramsMap.value("urlToolbarVisible", true).toBool();
	QString fileName = paramsMap.value("filename", "").toString();
	url = appendExtendParam(url, "knewdocs-openBrowserPage");

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (nullptr == mw)
		return;
	KPromeCentralArea* contentArea = mw->centralArea();
	if (nullptr == contentArea)
		return;

	if (mw->headerbarType() != KPromeHeaderBarBase::Standalone)
	{
		KPromeNewDocsPage* page = KTik::findParentByType<KPromeNewDocsPage>(this);
		if (nullptr == page)
			return;

		KPromeSubPage* subPage = page->initialSubPage();
		if (nullptr == subPage)
			return;

		KPromeTab* tab = subPage->getTab();
		if (nullptr == tab)
			return;

		tab->setAllowReAttachSubwindow(true);
		tab->reAttachSubPage(nullptr);
	}

	if (KPromeOpenFileSourceInfoCollectMgr *fileSrcInfoColMgr = promeApp->getOpenFileSourceInfoCollectMgr())
	{
		QVariantMap infoMap;
		infoMap.insert("openSource", QLatin1String(g_newDosPageIdentifier));
		fileSrcInfoColMgr->sendDocOpenSourceInfo(fileName, "online", infoMap);
	}

	QFileInfo fileInfo(fileName);
	if (isOnlineDocSupport(fileInfo.suffix()))
	{
		KPromePage* page = findExistingCloudDocsPage(contentArea);
		if (!page)
			page = contentArea->addPage(KPromePluginWidget::pageKDocsPreview);

		page->setProperty("fileName", fileName);
		page->notify(url);
		contentArea->setCurrentPage(page);
		page->newDocument(QString(), QVariantList(),
			KPromePage::LastOneDirectExeOtherProxy);
	}
	else
	{
		contentArea->addBrowserPage(url, bUrlToolbarVisible,
			KPromePluginWidget::pageBrowser, nullptr, QVariantMap(), fileName);
	}

	emit closeTab();
}
void KxNewDocJsApi::watchCopilotEntryState(QObject* entry)
{
	notifyCopilotEntryState();

	bool ok = connect(entry, SIGNAL(sigEntryVisibleChanged(const QString&, bool)),
		this, SLOT(onAIEntryVisibleChanged(const QString &, bool)), Qt::UniqueConnection);
	ASSERT(ok);

	ok = connect(entry, SIGNAL(sigEntryEnabledChanged(bool)),
		this, SLOT(onAIEntryEnabledChanged(bool)), Qt::UniqueConnection);
	ASSERT(ok);
}

void KxNewDocJsApi::onPluginInstanceObjectRegisted(const QString& name)
{
	if (name != g_copilotEntryDll)
		return;

	auto* entry = KPluginManager::getPluginInstanceObject(g_copilotEntryDll);
	ASSERT(entry);
	if (entry)
		watchCopilotEntryState(entry);

	QObject::disconnect(KPluginManager::getInstance(), &KPluginManager::pluginInstanceObjectRegisted,
		this, &KxNewDocJsApi::onPluginInstanceObjectRegisted);
}

void KxNewDocJsApi::onAIEntryVisibleChanged(const QString&, bool)
{
	notifyCopilotEntryState();
}

void KxNewDocJsApi::onAIEntryEnabledChanged(bool enable)
{
	notifyCopilotEntryState();
}

void KxNewDocJsApi::notifyCopilotEntryState()
{
	auto* entry = KPluginManager::getPluginInstanceObject(g_copilotEntryDll);
	if (entry)
	{
		QJsonObject retObj;
		retObj["action"] = "notifyCopilotEntryState";
		QJsonObject retParam;
		retParam["enable"] = entry->property("isEntryEnabled").toBool();
		bool bVisible = false;
		QMetaObject::invokeMethod(entry, "getEntryVisible", Qt::DirectConnection,
			Q_RETURN_ARG(bool, bVisible), Q_ARG(const QString&, g_knewdocsNewAiEntryId));
		retParam["visible"] = bVisible;
		retObj["param"] = retParam;
		QString strCallBack = "onWpsPushUIEvent";
		QString strRet = JsonHelper::convertQJsonToString(retObj);
		callbackToJS(strCallBack, strRet);

		KxLoggerLite::writeInfo(L"KxNewDocJsApi",
			QString("%1:%2").arg(__FUNCTION__).arg(strRet).toStdWString());
	}
}

void KxNewDocJsApi::initCopilotEntry()
{
	if (!KPluginManager::getInstance())
	{
		KxLoggerLite::writeInfo(L"KxNewDocJsApi",
			QString("%1 KPluginManager::getInstance is nullptr").arg(__FUNCTION__).toStdWString());
		return;
	}

	QString path = KPluginManager::getInstance()->getPluginPath(g_copilotEntryPlugin);
	QDir pathDir(path);
	if (!pathDir.exists())
		path = krt::dirs::frameworks() + QDir::separator() + g_copilotEntryPlugin;
	path += QDir::separator();
	path += g_copilotEntryDll;

#ifdef Q_OS_DARWIN
	//QLibrary.load() -> sys_load()并不支持Mac下的动态库自动添加.framework后缀，导致入口管理插件加载失败
	path.append(QLatin1String(".framework"));
#endif // Q_OS_DARWIN

	QLibrary library(path);
	if (!library.load())
	{
		KxLoggerLite::writeInfo(L"KxNewDocJsApi",
			QString("%1 load kcopilotentrylite error: %2")
			.arg(__FUNCTION__).arg(library.errorString()).toStdWString());
	}
}

void KxNewDocJsApi::doInitCopilotEntry()
{
	if (auto* entry = KPluginManager::getPluginInstanceObject(g_copilotEntryDll))
	{
		watchCopilotEntryState(entry);
		retriveAiTips();
	}
	else
	{
		if (auto* mgr = KPluginManager::getInstance())
		{
			connect(mgr, &KPluginManager::pluginInstanceObjectRegisted,
				this, &KxNewDocJsApi::onPluginInstanceObjectRegisted);
		}
	}
}

QString KxNewDocJsApi::getTemplateTypeBySuffix(const QString& buffix)
{
	static QVector<QString> s_wpsType;
	if (s_wpsType.isEmpty())
	{
		s_wpsType.append("wpt");
		s_wpsType.append("dot");
		s_wpsType.append("dotm");
		s_wpsType.append("dotx");
		s_wpsType.append("uot");
	}

	static QVector<QString> s_etType;
	if (s_etType.isEmpty())
	{
		s_etType.append("ett");
		s_etType.append("xlt");
		s_etType.append("xltx");
		s_etType.append("xltm");
	}

	static QVector<QString> s_wppType;
	if (s_wppType.isEmpty())
	{
		s_wppType.append("dpt");
		s_wppType.append("pot");
		s_wppType.append("potx");
		s_wppType.append("potm");
	}

	if (s_wpsType.contains(buffix))
		return "wps";

	if (s_etType.contains(buffix))
		return "et";

	if (s_wppType.contains(buffix))
		return "wpp";

	return "";
}

void KxNewDocJsApi::retriveAiTips()
{
	if (!m_window)
	{
		return;
	}

	KPromePluginWidget::WidgetType eRawType = m_window->getBrowserRawType();
	QString appName;
	switch (eRawType)
	{
	case KPromePluginWidget::pageDocerWps:
		appName = "wps";
		break;
	case KPromePluginWidget::pageDocerWpp:
		appName = "wpp";
		break;
	case KPromePluginWidget::pageDocerEt:
		appName = "et";
		break;
	case KPromePluginWidget::pageDocerPdf:
		appName = "pdf";
		break;
	default:
		break;
	}

	if (auto* entry = KPluginManager::getPluginInstanceObject(g_copilotEntryDll))
	{
		QMetaObject::invokeMethod(entry, "retriveAiTips", Qt::DirectConnection,
			Q_ARG(const QString&, appName));
	}
}

bool KxNewDocJsApi::hasOfficialFunctionPrivilege()
{
	kacctoutsdk::KAccountSdkProxy* pAccountSdk = kcoreApp->getAccountSDK();
	if (!pAccountSdk)
		return false;

	kacctoutsdk::IAccount* pAccount = pAccountSdk->getAccount();
	if (!pAccount)
		return false;

	kacctoutsdk::IPrivileges* pPrivileges = pAccount->getPrivileges();
	if (!pPrivileges)
		return false;

	QStringList privilegeIds;
	privilegeIds.append("gbofficialfunction");
	privilegeIds.append("gjbofficalfunction");

	kacctoutsdk::PrivilegeInfos privilgeResult;
	pPrivileges->getPrivileges(privilegeIds, privilgeResult);
	return !privilgeResult.isEmpty();
}

bool KxNewDocJsApi::appendDownloadArgHeaders(DownloadArgs& downloadData)
{
	if (kcoreApp && kcoreApp->kdcService())
	{
		KdcLoginCache info;
		kcoreApp->kdcService()->getUserLoginInfo(&info);
		downloadData.commonArgs.headers["Cookie"] = QString("wps_sid=%1").arg(info.wpsKey);
		return true;
	}
	return false;
}
