#include "stdafx.h"
#include "kmobilescanmanager.h"
#include "kmobilescanjshandler.h"
#include "ktransfertask.h"
#include "kmobilescanjsobject.h"
#include <krt/dirs.h>
#include <ksolite/webapi/wpsextention.h>
#include "ksolite/kcoreapplication.h"
#include "ksolite/kdomainmgr.h"
#include "ksolite/kdcinfoc.h"
#include <ksolite/kpath.h>
#include <krt/kconfigmanagercenter.h>

#ifndef _FIX_IOS_TODO
#include <QPrinter>
#endif

#include <qnetworkaccessmanager.h>
#include <qnetworkcookiejar.h>
#include <qnetworkcookie.h>
#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"

namespace
{
	QString getUserId()
	{
		auto service = kcoreApp ? kcoreApp->cloudService() : nullptr;
		if (!service || !service->isLogined())
			return "";
		KQingIpcLoginCache loginCache;
		service->getUserLoginInfo(&loginCache);
		return loginCache.userID;
	}

	QString getPictureDir()
	{
		QString path;
		QString picturesPath = krt::dirs::officeHome();
		if (!picturesPath.isEmpty())
		{
			path = picturesPath.append(QDir::separator())
				.append("pdf")
				.append(QDir::separator())
				.append(getUserId())
				.append(QDir::separator());
			if (!QDir().exists(path))
				ksolite::makePath(path);
		}
		return path;
	}

	QString loadNewTempDocPath(const QString& name)
	{
		int documentNum = 1;
		QString qstrFName = name + QString::number(documentNum) + QString(".pdf");
		QString qstrPath = []() -> QString
		{
			QString path;
			QString userDir = krt::dirs::officeHome();
			QString tempdir = QDir::separator() + QLatin1String("pdf") + QDir::separator() + QLatin1String("temp");
			if (krt::kcmc::support("UseResourcePDFOnly"))
				path = QDir(userDir + QDir::separator() + ".." + tempdir).absolutePath();
			else
				path = QDir(userDir + tempdir).absolutePath();

			if (!QDir().exists(path))
			{
				if (!QDir().mkpath(path))
					return QString();
#ifdef  Q_OS_WIN
				::SetFileAttributesW(krt::utf16(path), FILE_ATTRIBUTE_HIDDEN);
#endif
			}
			return path;
		}();
		QString qstrFPath = qstrPath + QDir::separator() + qstrFName;
		QFile newFile(qstrFPath);
		while (newFile.exists())
		{
			documentNum++;
			qstrFName = name + QString::number(documentNum) + QString(".pdf");
			qstrFPath = qstrPath + QDir::separator() + qstrFName;
			newFile.setFileName(qstrFPath);
		}
		return qstrFPath;
	}
}

// KMobileScanManager
KMobileScanManager* KMobileScanManager::getMobileScanManager()
{
	static KMobileScanManager manager(nullptr);
	return &manager;
}

KMobileScanManager::KMobileScanManager(QObject* parent)
	: QObject(parent)
{
	getTransferTask().listen("km_scan");
	connect(&getTransferTask(), &KTransferTask::sigDownloadStateChange,
		this, &KMobileScanManager::onDownloadStateChange);
}

KMobileScanManager::~KMobileScanManager()
{
}

void KMobileScanManager::init()
{
	m_listFile.clear();
	initJsApiObj();
}

bool KMobileScanManager::initJsApiObj()
{
	static bool isInit = []
	{
		constexpr const char* const k_scanApiObjName = "MobileScan";
		KWpsExtentionAPIMgr::getInstance()->AddObject(k_scanApiObjName,
			KJCExtendJsEventObj<KMobileScanJSObject>::CreateObject); TS_OBJTYPE(KMobileScanJSObject);
		return true;
	}();
	return isInit;
}

void KMobileScanManager::sendGeneralEvent(const QString& eventName, const QVariantMap& arg)
{
	QHash<QString, QString> params;
	for (const auto& key : arg.keys())
		params.insert(key, arg.value(key).toString());
	if (kcoreApp && kcoreApp->getDcInfo())
		kcoreApp->getDcInfo()->postGeneralEvent(eventName, params);
}

void KMobileScanManager::onMobileScanFinish(const QVariantMap& arg)
{
#ifndef _FIX_IOS_TODO
	m_listFile.clear();
	bool bCreateAllSuccess = true;
	for (auto it = arg.begin(); it != arg.end(); ++it)
	{
		QString tmpPath = loadNewTempDocPath(KMobileScanManager::tr("unnamed"));
		m_listFile.push_back(tmpPath);
		QPrinter pdfPrinter(QPrinter::PrinterResolution);
		pdfPrinter.setOutputFormat(QPrinter::PdfFormat);
		pdfPrinter.setOutputFileName(tmpPath);
		pdfPrinter.setPageSize(QPrinter::Custom);

		QStringList imgs = it.value().toStringList();
		QString strImgPath = imgs.at(0);
		QPixmap pixmap = QPixmap(strImgPath);
		if (pixmap.isNull())
		{
			bCreateAllSuccess = false;
			break;
		}
		auto funPixmapSize = [](const QPixmap& img) -> QSize
		{
			int picWidth = img.width();
			int picHeight = img.height();
			int nXDpi = img.physicalDpiX();
			int nYDpi = img.physicalDpiY();
			return QSize(picWidth * (krt::logicalDpiX() / nXDpi), picHeight * (krt::logicalDpiY() / nYDpi));
		};

		pdfPrinter.setFullPage(TRUE);
		QSize pixSize = funPixmapSize(pixmap);
		pdfPrinter.setPageSize(QPageSize(pixSize, QPageSize::Point));

		QPainter pagePrinter;
		pagePrinter.begin(&pdfPrinter);
		int nImgNum = 0;
		do
		{
			pagePrinter.drawPixmap(0, 0, pixSize.width(), pixSize.height(), pixmap);
			if (++nImgNum < imgs.size())
			{
				strImgPath = imgs.at(nImgNum);
				pixmap = QPixmap(strImgPath);
				if (pixmap.isNull())
				{
					bCreateAllSuccess = false;
					break;
				}
				pixSize = funPixmapSize(pixmap);
				pdfPrinter.setFullPage(TRUE);
				pdfPrinter.setPageSize(QPageSize(pixSize, QPageSize::Point));
				pdfPrinter.newPage();
			}
		} while (nImgNum < imgs.size());
		pagePrinter.end();
		if (!bCreateAllSuccess)
			break;
	}
	onGenerateFinish(bCreateAllSuccess);
#endif
}

void KMobileScanManager::onGenerateFinish(bool bAllSuccess)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return;
	apiHandle->createScanPdfCallback(bAllSuccess);
}

QStringList& KMobileScanManager::getScanfiles()
{
	return m_listFile;
}

void KMobileScanManager::asynTempSpaceDownload(const QVariantMap& arg)
{
	QString strFileId = arg["fileId"].toString();
	QString strGroupId = arg["groupId"].toString();
	QString strFSize = arg["fileSize"].toString();
	auto pictruePath = getPictureDir() + strGroupId + "_" + strFileId;
	getTransferTask().asynTempSpaceDownload(strFileId, strFSize, pictruePath);
}

void KMobileScanManager::cancelAllTempSpaceDownload()
{
	getTransferTask().asynCancelAllTransfer();
}

void KMobileScanManager::onDownloadStateChange(const QVariantMap& argMap)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return;
	if (argMap["type"].toInt() == (int)KTransferTask::KSpaceType::NearField)
		apiHandle->nearFieldFileNoticeCallback(argMap);
	else
		apiHandle->tempSpaceDownloadCallback(argMap);

	if (argMap["status"].toInt() == (int)KTransferTask::TaskStatus::Finish)
	{
		m_listTempPic.push_back(argMap["path"].toString());
	}
}

void KMobileScanManager::clear()
{
	for (const auto& it : m_listTempPic)
	{
		QFile f(it);
		f.remove();
	}
	m_listTempPic.clear();
}

void KMobileScanManager::onSizeChange(const QVariantMap& arg)
{
	int nWidth = arg["width"].toInt();
	int nHeight = arg["height"].toInt();
	emit sigSizeChange(nWidth, nHeight);
}

void KMobileScanManager::onNearFieldFileTaskNotice(const QVariantMap& arg)
{
	getTransferTask().nearFieldFileTaskNotice(arg);
}

void KMobileScanManager::onNetWorkRequest(const QVariantMap& arg)
{
	QString html = arg["url"].toString();
	QString body = arg["body"].toString();
	if (html.isEmpty())
		return;
	QNetworkAccessManager* pMgr = new QNetworkAccessManager(this);
	QNetworkRequest req(html);
	req.setRawHeader("Content-Type", "Application/json");
	kacctoutsdk::IAuthProvider* authProvider = nullptr;

	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	if (account)
		authProvider = account->getAuthProvider();
	if (authProvider)
	{
		authProvider->attachAuthCookiesAndHeadersToRequest(&req,
			authProvider->convertToQString(QNetworkAccessManager::Operation::PostOperation), QUrl(html));
	}
	QNetworkReply* pReply = pMgr->post(req, body.toLocal8Bit());
	QObject::connect(pReply, &QNetworkReply::finished, this, [pReply]()
		{
			if (pReply == nullptr)
				return;
			QString response = pReply->readAll();
			KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
			if (!apiHandle)
				return;
			QVariantMap arg;
			arg["data"] = response;
			apiHandle->netWorkRequestCallback(arg);
		});
}
