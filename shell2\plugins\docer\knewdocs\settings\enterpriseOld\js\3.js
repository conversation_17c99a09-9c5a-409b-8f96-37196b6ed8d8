webpackJsonp([3],{"/hx9":function(t,e,i){"use strict";var s=i("wYMm"),a=i("s0MJ"),n={data:function(){return{}},props:{item:{type:Object,default:function(){return{}}},imgStyle:{type:Object,default:function(){return{}}},index:Number,cropSuffix:String},methods:{preview:function(){this.$emit("preview",this.index)}},computed:{Url:function(){var t=this.item.thumb_big_url;if(this.item.thumb_big_url)return this.item.thumb_big_url=this.item.thumb_big_url.replace(/\/720x1020.png/g,""),0!=this.item.thumb_big_url.indexOf("/upload")&&0!=this.item.thumb_big_url.indexOf("upload")||(t=s.f.img1tplUrl),0!=this.item.thumb_big_url.indexOf("/storage")&&0!=this.item.thumb_big_url.indexOf("storage")||(t=s.f.img1fileUrl),t+this.item.thumb_big_url+this.cropSuffix;var e=Object(a.d)(this.item.filePath);try{return i("SkEa")("./"+e+".svg")}catch(t){return"error"}}}},r={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"list-item",style:t.imgStyle},[i("div",{staticClass:"list-img",style:t.imgStyle,on:{click:function(e){t.preview()}}},[i("img",{style:t.imgStyle,attrs:{src:t.Url,alt:""}})]),t._v(" "),i("h4",{staticClass:"list-tt",attrs:{title:t.item.name},on:{click:function(e){t.preview()}}},[t._v(" "+t._s(t._f("toSubStr")(t.item.name))+" ")])])},staticRenderFns:[]},o=i("VU/8")(n,r,!1,null,null,null);e.a=o.exports},"6tkF":function(t,e){},bbPv:function(t,e,i){"use strict";var s=i("Dd8w"),a=i.n(s),n=i("/hx9"),r=i("NYxO"),o=i("wYMm"),l=i("w7XY"),c=i("Dzwp"),u={data:function(){return{}},props:["limit","data","csource","position","servicetype","imgStyle"],methods:{preview:function(t){this.$emit("preview",t)},buyDocerMember:function(){this.$emit("collectBuyMember"),c.a.setBtnPos("view_icon"),window.gotoTag({type:"member",servicetype:this.servicetype||"monthcard",csource:this.csource||"newfile",position:this.position||"newfile"})}},computed:a()({},Object(r.c)({appName:"getAppName"}),{listStyle:function(){return this.imgStyle?{width:this.imgStyle.width,height:parseInt(this.imgStyle.height,10)+27+"px"}:{}},cropSuffix:function(){var t=o.d.list[this.appName];return"wpp"==this.appName&&(t="218x210"),"/"+t+"."+("webkit"!==l.a.runtime?"webp":"jpg")},limitCount:function(){return this.limit&&this.data.length&&this.data.length%this.limit!=0?this.limit-this.data.length%this.limit:0}}),components:{EpItem:n.a}},m={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("ul",{staticClass:"m-list"},[t._l(t.data,function(e,s){return i("li",{key:s,staticClass:"m-list-item",staticStyle:{"margin-bottom":"30px"},style:t.listStyle},[i("EpItem",{attrs:{item:e,index:s,cropSuffix:t.cropSuffix,imgStyle:t.imgStyle},on:{preview:t.preview}})],1)}),t._v(" "),t._l(t.limitCount,function(e){return[i("li",{staticClass:"m-list-item",staticStyle:{"margin-bottom":"30px"},style:t.listStyle})]})],2)},staticRenderFns:[]},h=i("VU/8")(u,m,!1,null,null,null);e.a=h.exports},jpPr:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=i("mvHQ"),a=i.n(s),n=i("Dd8w"),r=i.n(n),o=i("NYxO"),l=i("mMDT"),c=i("G1qf"),u=i("59SO"),m={data:function(){return{mode:"thumb",selectionLeft:0}},props:["from"],computed:{selectionStyle:function(){return{transform:"translateX("+this.selectionLeft+"px)"}}},methods:{filter:function(t,e){this.mode=t,this.collectChange("thumb"==this.mode?"thumb":"list"),this.$emit("filter",t),e&&e.currentTarget&&(this.selectionLeft=e.currentTarget.offsetLeft)},collect:function(){this.from&&u.a.send.apply(null,[this.from].concat(Array.prototype.slice.apply(arguments)))},collectChange:function(t){this.collect("change_view_click",t)}}},h={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"m-filter-mode"},[i("div",{staticClass:"selection",style:t.selectionStyle}),t._v(" "),i("a",{staticClass:"filter-item",class:{active:"thumb"==t.mode},attrs:{href:"javascript:void(0)"},on:{click:function(e){t.filter("thumb",e)}}},[i("span",{staticClass:"icon icon-thumb"})]),t._v(" "),i("a",{staticClass:"filter-item",class:{active:"menu"==t.mode},attrs:{href:"javascript:void(0)"},on:{click:function(e){t.filter("menu",e)}}},[i("span",{staticClass:"icon icon-menu"})])])},staticRenderFns:[]},p=i("VU/8")(m,h,!1,null,null,null).exports,f=i("cMGX"),d=i("bbPv"),g={data:function(){return{}},props:{item:{type:Object,default:function(){return{}}},index:Number},methods:{preview:function(){this.$emit("preview",this.index)}}},v={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"list-item",on:{click:function(e){t.preview()}}},[i("span",{staticClass:"icon icon-document"}),t._v(" "),i("div",{staticClass:"list-info"},[i("h5",{attrs:{title:t.item.name}},[t._v(t._s(t.item.name))]),t._v(" "),i("p",[i("span",{staticClass:"name"},[t._v(t._s(t.item.author))]),i("span",{staticClass:"time"},[t._v(t._s(t._f("replaceDate")(t.item.publish_time)))])])])])},staticRenderFns:[]},_=i("VU/8")(g,v,!1,null,null,null).exports,b=i("Dzwp"),y={data:function(){return{}},props:["limit","data","csource","position","servicetype"],methods:{preview:function(t){this.$emit("preview",t)},buyDocerMember:function(){this.$emit("collectBuyMember"),b.a.setBtnPos("view_icon"),window.gotoTag({type:"member",servicetype:this.servicetype||"monthcard",csource:this.csource||"newfile",position:this.position||"newfile"})}},computed:{limitCount:function(){return this.limit&&this.data.length&&this.data.length%this.limit!=0?this.limit-this.data.length%this.limit:0}},components:{Item:_}},w={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("ul",{staticClass:"m-txtlist"},[t._l(t.data,function(e,s){return i("li",{staticClass:"m-txtlist-item"},[i("Item",{attrs:{item:e,index:s},on:{preview:t.preview,buyDocerMember:t.buyDocerMember}})],1)}),t._v(" "),t._l(t.limitCount,function(t){return[i("li",{staticClass:"m-txtlist-item"})]})],2)},staticRenderFns:[]},x=i("VU/8")(y,w,!1,null,null,null).exports,C=i("UNaj"),S=i("wYMm"),P=i("s0MJ"),j=i("n1nN"),D=i("v8ob"),T={mixins:[C.a],data:function(){return{word:"",term:"",searchType:"",statusCode:"",limit:0,query:{},totalPage:0,page:1,data:[],filter:{},mode:"thumb"}},beforeRouteEnter:function(t,e,i){i(function(e){u.a.sendOffice("show"),D.a.$on(S.g.resizeWindow,e.ajustTplLayout),e.initData(t)})},beforeRouteUpdate:function(t,e,i){this.initData(t),i()},beforeRouteLeave:function(t,e,i){D.a.$off(S.g.resizeWindow,this.ajustTplLayout),i()},methods:r()({},Object(o.b)(["setPreviewShow"]),{initQuery:function(t){"more"==(t=t||{}).searchType?this.word="":this.word=t.word,this.term=t.word,this.query=t},initData:function(t){var e=this;this.filter={},this.ajustTplLayout(),this.initQuery(t.query),this.$nextTick(function(){e.page=1,e.getData()})},gotoPage:function(t){t>0&&t<=this.totalPage&&(this.page!=t||1==t)&&(this.page=t,this.getData())},retry:function(){this.getData()},getData:function(){var t=this;this.statusCode=S.k.loading,j.a.get("docertpl").getTemplateByTags({data:{term:this.term,sort_by:"mix",moban_type:1,sort_way:"down",limit:this.pageSize+2,page:this.page,vip:0},appType:{appName:S.b.name}}).then(function(e){"ok"===e.result&&e.data&&0!==e.data.total_num?(t.data=e.data.moban.slice(2),t.totalPage=Math.ceil(e.data.total_num/(t.pageSize+2)),t.statusCode=t.data.length>2?"":S.k.empty):(t.data=[],t.totalPage=0,t.statusCode=S.k.empty)}).catch(function(e){t.statusCode=S.k.error})},getFilterMenu:function(t){this.mode=t||"thumb",this.ajustTplLayout()},ajustTplLayout:function(){this.limit=P.b.getLimit(this.$refs&&this.$refs.nSearch,this.mode)},preview:function(t){var e=JSON.parse(a()(this.data));u.a.sendOffice("office_template_click"),this.setPreviewShow({data:e,index:t,collect:{from:"office_page",module:"office"},official:S.l.enable})},addWindowStatusListener:function(){var t=this;window.onresize=function(){t.ajustTplLayout()}}}),mounted:function(){this.addWindowStatusListener()},computed:r()({},Object(o.c)({appName:"getAppName"}),{pageSize:function(){return 5*this.limit}}),components:{GoHome:l.a,Pagination:f.a,DataStatus:c.a,EpList:d.a,TxtList:x,MenuFilter:p},watch:{limit:function(){"officialmb"==P.b.getRoutePath(this.$route.path)[0]&&this.limit&&(this.ajustTplLayout(),this.page=1,this.getData())}}},k={render:function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"m-container"},[i("div",{directives:[{name:"goto-top",rawName:"v-goto-top"}],ref:"scroller",staticClass:"m-main"},[i("div",{ref:"nSearch",staticClass:"m-main-in"},[i("GoHome"),t._v(" "),i("div",{ref:"fixBar",staticClass:"top-filter g-clearfloat g-should-fix-wrapper"},[i("div",{staticClass:"g-should-fix",class:{"g-fixed":t.isFixed}},[i("MenuFilter",{staticClass:"g-l top-filter-item",attrs:{from:"office_page"},on:{filter:t.getFilterMenu}})],1)]),t._v(" "),i("div",[i("DataStatus",{attrs:{statusCode:t.statusCode},on:{retry:t.retry}})],1),t._v(" "),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.statusCode,expression:"!statusCode"}],staticClass:"theme-list"},["thumb"==t.mode?i("EpList",{attrs:{limit:t.limit,data:t.data},on:{preview:t.preview}}):t._e(),t._v(" "),"menu"==t.mode?i("TxtList",{attrs:{limit:t.limit,data:t.data},on:{preview:t.preview}}):t._e(),t._v(" "),i("Pagination",{attrs:{totalPage:t.totalPage,page:t.page},on:{gotoPage:t.gotoPage}})],1)],1)])])},staticRenderFns:[]};var L=i("VU/8")(T,k,!1,function(t){i("6tkF")},"data-v-355352ee",null);e.default=L.exports}});