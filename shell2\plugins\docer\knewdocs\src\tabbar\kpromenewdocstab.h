﻿#ifndef __KPROMETHEUS_NEW_DOCS_TAB_H__
#define __KPROMETHEUS_NEW_DOCS_TAB_H__

class KPromeNewDocsTabBar;
class KPromeNewDocsTabInfoObj;
class KPromeStyleOptionNewDocsTab;

class KPromeNewDocsTab : public QWidget
{
	Q_OBJECT
public:
	KPromeNewDocsTab(KPromeNewDocsTabBar* parent);
	~KPromeNewDocsTab();

	void initByTabInfo(KPromeNewDocsTabInfoObj* tabInfoObj);

	bool getSelectedStatus();
	void setSelectedStatus(bool selected);

	void clearUnReadMessageCount();
	void updateUnReadMessageCount();
	int getUnReadMessageCount();
	void setUnReadMessageCount(int num);
	void saveRedPointNum();

	QString getStyleName();
	bool isEntrance();

	QString getRelyPlugin();
	bool isRelyPluginExit();

	QString getTitleText();
	QString collect();
	QIcon getIcon();

	virtual QSize sizeHint() const override;
	virtual QSize minimumSizeHint() const override;
	virtual void setVisible(bool visible) override;

protected:
	virtual void paintEvent(QPaintEvent* ev) override;
	virtual void mousePressEvent(QMouseEvent* ev) override;

private:
	void initStyleOption(KPromeStyleOptionNewDocsTab* option) const;

signals:
	void tabClicked(int tabType);

private:
	bool						m_bSelected;
	bool						m_bGhostIsComing;
	bool						m_bNeedUnReadPoint;
	int							m_unReadMessageCount;
	KPromeNewDocsTabBar*		m_parentTabBar;
	KPromeNewDocsTabInfoObj*	m_tabInfoObj;
	bool						m_bDcInfoSended;
};

#endif //__KPROMETHEUS_NEW_DOCS_TAB_H__
