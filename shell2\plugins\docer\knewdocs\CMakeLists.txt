wps_package(knewdocs SHARED BUILD_DEST_PATH office6/addons/knewdocs)
	wps_add_definitions(
		__SHELL_MODULE__
		WPS_BOOST_THREAD_DISABLED
		PROMETHEUS_APP
		)
	wps_include_directories(
		.
		../
		src
		../../include
		Coding/shell_bundle/shell2/include
		Coding/plugin_bundle/shell2/plugins/include
		Coding/plugin_bundle/shell2/plugins/docer
		Coding/plugin_bundle/shell2/plugins/docer/include
		Coding/shell_bundle/shell2/include/kprometheus
		Coding/core_bundle/include
		Coding/core_bundle/framework
		Coding/core_bundle/office/include
		Coding/misc_bundle/3rdparty
		Coding/misc_bundle/3rdparty/sqlite
		../../../../3rdparty/ksqlite
		../../../../3rdparty/ksqlite/sqlite
		../../../../3rdparty/ksqlite/cppsqlite

		Coding/api_bundle/api/jsapi/jsapiservice
		Coding/misc_bundle/support/include
		${PROJECT_BINARY_DIR}/plugin_bundle/shell2/plugins/kwpslive/kdcthrift/kdcservices
		${PROJECT_BINARY_DIR}/plugin_bundle/shell2/plugins/kwpslive/thriftipc/kqingservices
		Coding/shell_bundle/shell2/common
	)
	wps_use_packages(
		curl
		thrift
		zlib
		Qt5Widgets
		Qt5Xml
		Qt5Network
		jsoncpp
		WIN(zip-utils)
		WIN(libzip)
		LINUX(libsafec)
		Qt5PrintSupport
		)
	
	wps_add_features(
		cocoa_menubar
	)

	wps_add_sources(
		PCH stdafx.h stdafx.cpp
		QT_AUTOMOC
		src/kpromenewdocspage.cpp
		src/kpromenewdocspage.h
		src/tabbar/kpromenewdocstabbarconfig.cpp
		src/tabbar/kpromenewdocstabbarconfig.h
		src/tabbar/kpromenewdocstabbar.cpp
		src/tabbar/kpromenewdocstabbar.h
		src/tabbar/kpromenewdocstab.h
		src/tabbar/kpromenewdocstab.cpp
		src/main.cpp
		src/utility/kxsettings.cpp
		src/utility/kxsettings.h
		src/utility/knewdocshelper.cpp
		src/utility/knewdocshelper.h
		src/knewdocswebwidget.cpp
		src/knewdocswebwidget.h
		src/knewdochelper.cpp
		src/knewdochelper.h
		src/knewdocjsapi.cpp
		src/knewdocjsapi.h
		src/knewdocpagejsapibase.cpp
		src/knewdocpagejsapibase.h
		src/knewdocjsapihelper.cpp
		src/knewdocjsapihelper.h
		src/kpromenewdocsstartup.h
		src/kpromenewdocsstartup.cpp
		src/kpromenewdocswaitwidget.cpp
		src/kpromenewdocswaitwidget.h
		src/pdf/kpromepdfwindow.h
		src/pdf/kpromepdfwindow.cpp
		src/pdf/kxpdfnewdochelper.h
		src/pdf/kxpdfnewdochelper.cpp
		src/pdf/kpdfpluginloaddialog.h
		src/pdf/kpdfpluginloaddialog.cpp
		src/pdf/kproxypdfplugin.h
		src/pdf/kproxypdfplugin.cpp
		src/pdf/mobilescan/kxmobilescanwidget.h
		src/pdf/mobilescan/kxmobilescanwidget.cpp
		src/pdf/mobilescan/ktransfertask.h
		src/pdf/mobilescan/ktransfertask.cpp
		src/pdf/mobilescan/kmobilescanmanager.h
		src/pdf/mobilescan/kmobilescanmanager.cpp
		src/pdf/mobilescan/kmobilescanjsobject.h
		src/pdf/mobilescan/kmobilescanjsobject.cpp
		src/pdf/mobilescan/kmobilescanjshandler.h
		src/pdf/mobilescan/kmobilescanjshandler.cpp

		src/knewdocs.def
		)
	include(${CMAKE_SOURCE_DIR}/plugin_bundle/shell2/plugins/ksoftbus/ksoftbusbusiness.cmake)
	wps_add_sources(
		QT_AUTOMOC
		../../include/kclouddocs/kclouddocspagepreload.h
		../../include/kclouddocs/kclouddocspagepreload.cpp
	)

	wps_link_packages(
		kbase
		kdownload
		kprometheus
		krt
		ksolite
		LINUX(
			kshell
		)
		sqlite
		ksqlite3
		algdiag
		thriftasyncgencpp
		kdcgencpp
		kdocerjsapi_lite
		kdocertoolkitlite
		docermodulelib
		kdocerlegacy
		DARWIN(kfpccomb)
		kdocerprocessonlib
		kdocerresourcelib_lite
		docerugcresource
		)
	if(OS_WIN)
		wps_link_Ws2_32()
		wps_link_wldap32()
		wps_link_crypt32()
	endif()
	if (OS_LINUX)
		wps_link_dl()
	endif()
	wps_custom_compile(lrelease
		INPUT mui/zh_CN/ts/knewdocs.ts mui/zh_CN/ts/knewdocsresource.ts
		OUTPUT office6/addons/knewdocs/mui/zh_CN/knewdocs.qm
		WPS_ADD_INSTALL CATEGORY PERSONAL LINUX_COMMUNITY LINUX_DOMESTIC LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/mui/zh_CN"
	)

	wps_custom_compile(lrelease
		INPUT mui/zh_TW/ts/knewdocs.ts mui/zh_TW/ts/knewdocsresource.ts
		OUTPUT office6/addons/knewdocs/mui/zh_TW/knewdocs.qm
		WPS_ADD_INSTALL CATEGORY PERSONAL LANGUAGE zh_TW SETOUTPATH "office6/addons/knewdocs/mui/zh_TW"
	)

	wps_custom_compile(lrelease
		INPUT mui/ja_JP/ts/knewdocs.ts mui/ja_JP/ts/knewdocsresource.ts
		OUTPUT office6/addons/knewdocs/mui/ja_JP/knewdocs.qm
		WPS_ADD_INSTALL CATEGORY PERSONAL PROFESSIONAL LANGUAGE ja_JP SETOUTPATH "office6/addons/knewdocs/mui/ja_JP"
	)

	wps_custom_compile(lrelease
		INPUT mui/zh_HK/ts/knewdocs.ts mui/zh_HK/ts/knewdocsresource.ts
		OUTPUT office6/addons/knewdocs/mui/zh_HK/knewdocs.qm
		WPS_ADD_INSTALL CATEGORY PERSONAL PROFESSIONAL LANGUAGE zh_HK SETOUTPATH "office6/addons/knewdocs/mui/zh_HK"
	)

	wps_custom_compile(
		qrc
		INPUT mui/default/icons_svg.qrc
		OUTPUT office6/addons/knewdocs/mui/default/icons_svg.data
		WPS_ADD_INSTALL CATEGORY PERSONAL LINUX_COMMUNITY LINUX_DOMESTIC LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/mui/default"
	)
	wps_add_resources(
		res/kuip/knewdoctabbar.json
		res/kuip/knewdoctabbar.kui
		WPS_ADD_INSTALL CATEGORY PERSONAL LINUX_COMMUNITY LINUX_DOMESTIC LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/res/kuip"
	)
	wps_add_resources(
		mui/default/theme/knewdocs.kuip
		WPS_ADD_INSTALL CATEGORY PERSONAL LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/mui/default/theme"
	)

	wps_add_resources(
		res/blanktemplate/normal.pptx
		res/blanktemplate/normal_black.pptx
		res/blanktemplate/normal_gray.pptx
		res/blanktemplate/blank_official.docx
		BUILD_DEST_PATH office6/addons/knewdocs
		WPS_ADD_INSTALL CATEGORY PERSONAL LINUX_COMMUNITY LINUX_DOMESTIC LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/res/blanktemplate"
	)

	if(OS_DARWIN)
		wps_add_resources(
			res/blanktemplate/normal_mac.pptx
			res/blanktemplate/normal_black_mac.pptx
			res/blanktemplate/normal_gray_mac.pptx
			BUILD_DEST_PATH office6/addons/knewdocs
			WPS_ADD_INSTALL CATEGORY PERSONAL LINUX_COMMUNITY LINUX_DOMESTIC LANGUAGE zh_CN SETOUTPATH "office6/addons/knewdocs/res/blanktemplate"
		)
	endif()

	wps_add_resources(
		cfg.ini
		BUILD_DEST_PATH office6/addons/knewdocs/res/
	)
	wps_add_install(SETOUTPATH "office6/addons/knewdocs")
	wps_declare_fileinfo(
		FILE_DESCRIPTION "knewdocs"
		)
wps_end_package()

wps_add_subdirectory(settings)
