﻿#ifndef __KNEWDOCS_KPROMEPDFWINDOW_H__
#define __KNEWDOCS_KPROMEPDFWINDOW_H__

#include <ksolite/kcommonwebwidget.h>
#include <ksolite/kcommonwebapi.h>
#include <kdownload/kdownload.h>
#include <QNetworkCookieJar>
#include <kprometheus/kpromepluginwidget.h>
#ifndef Q_OS_DARWIN
#include <ksolite/kexportpdfservice.h>
#endif //Q_OS_DARWIN
#include <kprometheus/pluginload/kproxyplugin.h>
#include "knewdocjsapi.h"
#include "ksolite/kappmgr/kappobject.h"
#include "kproxypdfplugin.h"
#include "kdocerresnetwork.h"

class KPromeNewDocsStartup;

class KPromeNewDocsPage;
class KPromeMainWindow;

typedef struct PluginInfo {
	QString pluginName;
	std::map<QString, QString>pluginProperty;
	QString p4Value;
	QString mSourceStr;
	PluginInfo()
		: pluginName("")
		, p4Value("")
		, mSourceStr("")
	{
		pluginProperty.clear();
	}
	//~PluginInfo();
	PluginInfo(QString pluginName, std::map<QString, QString>pluginProperty, QString p4Value)
	{
		this->pluginName = pluginName;
		this->pluginProperty = pluginProperty;
		this->p4Value = p4Value;
	}
	PluginInfo(QString pluginName, std::map<QString, QString>pluginProperty, QString p4Value, QString sourceStr)
	{
		this->pluginName = pluginName;
		this->pluginProperty = pluginProperty;
		this->p4Value = p4Value;
		this->mSourceStr = sourceStr;
	}
}PluginInfo;
typedef struct LabelData
{
	QString banner;
	QString labelId;
	QString action; // pay_mode, dialog_mode, jump_mode
	QString link; //
	QString result;

	LabelData& operator = (const LabelData& rhs)
	{
		if (this == &rhs)
		{
			return *this;
		};
		labelId = rhs.labelId;
		action = rhs.action;
		link = rhs.link;
		result = rhs.result;

		return *this;
	}

	bool isValid()
	{
		return !labelId.isEmpty();
	}

}LabelData;

class KxPromePdfWindowJsApi : public KxNewDocJsApiBase
{
	Q_OBJECT
public:
	explicit KxPromePdfWindowJsApi(KPromeNewDocsStartup* window, KxWebViewContainer* webView);
	~KxPromePdfWindowJsApi();

	typedef enum PdfPluginJsId
	{
		PdfPluginJsId_None = 0,
		PdfPluginJsId_BlankPdf = 1,//空白PDF
		PdfPluginJsId_Open = 2,//打开
		PdfPluginJsId_NewFromPicture = 3, //从图片新建
		PdfPluginJsId_NewFromCADFile = 4,//从CAD文件新建
		PdfPluginJsId_NewFromFile = 5,//从文件新建
		PdfPluginJsId_NewFromWebPage = 6,//从网页新建
		PdfPluginJsId_NewFromScanner = 7,//从扫描仪新建
		PdfPluginJsId_NewFromMobileScan = 8, //从手机扫描新建
	}PdfPluginJsId;

	typedef enum PdfRecommendationFunctionJsId
	{
		PdfPluginJsId_Pdf2word = 1,//pdf转word
		PdfPluginJsId_Pdf2Pic = 2,//pdf转图片
	}PdfRecommendationFunctionJsId;

public:
	void notifyCancelDownload(const QString& templateId);
	void notifyDownloadProgress(const QString& templateId, int nPercent);
	void notifyDownloadSuccess(const QString& templateId, const QString& fileName);
	void notifyDownloadError(const QString& templateId, kdocerresnetwork::DownloadFailInfo info);

protected:
	virtual QWidget* widgetParent() override;
	void newBlankDocument();
	void openDocument();
	void newPdfFromFile();
	void newPdfFromScan();
	void newFromWebPage();
	bool checkAppValidity(const QString& appId);
private slots:
	void registPushUIEventHandler(KxWebViewJSContext& context);
	void openPdfUnifyPayDlg(KxWebViewJSContext& context);
	void onScriptReady();
	void clicktRecommendationFunction(KxWebViewJSContext& context);
	void navigateOnNewWindow(KxWebViewJSContext& context);
	
	//模板相关
	void downloadTemplate(KxWebViewJSContext& context);
	void cancelDownload(KxWebViewJSContext& context);
	void onDownloadProgress(const QString& templateId, int nProgress);
	void onDownloadSuccess(const QString& templateId, const QString& filePath);
	void onDownloadError(const QString& templateId, kdocerresnetwork::DownloadFailInfo info);
	void onPluginLoadSuccess(QString appId);
	void onOpenPromeBrowser(const QString&);
	void onSkinChanged();
	void onAppMgrLoadFinished();
	//判断是否暗黑皮肤
	void isDarkSkin(KxWebViewJSContext& context);
	void newlyBuildPlugInUnit(KxWebViewJSContext& context);
	void newScanItem(KxWebViewJSContext& context);
	void translateItem(KxWebViewJSContext& context);
	void exportPdfItem(KxWebViewJSContext& context);
	void OnWebPageLoadFinished(KxWebViewJSContext& context);
	void createBlankDocument(KxWebViewJSContext& context); //产品已经去掉了跳转tab的需求，但以防后续需要，暂时保留此jsapi，需要PDF组件代码支持

	void doRunApp(KxWebViewJSContext& context);
	void isAppsValid(KxWebViewJSContext& context);
	void openMyTemplateTab(KxWebViewJSContext& context);

	void newBlankDocument(KxWebViewJSContext& context);
	void newPdfFromFile(KxWebViewJSContext& context);
	void newPdfFromScan(KxWebViewJSContext& context);
	void openDocument(KxWebViewJSContext& context);

private:
	void closeNewStandaloneMainwindow(bool isNew, QString appType, KPromeMainWindow* mw);
	void _openDocument(QString appType, QVariantMap params, QStringList filterType);

	void initConfig();
	void initConfigPdfPro();
	void onNewDocument(const QString& file, const QString& fileKey, QString appType = "pdf");
	QByteArray toOpenDocFilterStr(KPromePluginWidget::WidgetType t, const QString& path);
	bool isPageLoaded(KPromePluginWidget::WidgetType t);
	void onExportPdfDocument(QString appType = "pdf");
	void onOpenDocument(KPromeMainWindow* mw, const QString& file, const QString& fileKey, QString appType = "pdf");
	KPromeNewDocsPage* getNewDocsPage() const;
	void runAppByPluginId(const QString& pluginId);
	void onClickPluginApp(PluginInfo pluginInfo);
	void onExportPhoto(const PluginInfo& pluginInfo);
	void onHandleExtentedFunc(QStringList params);
	void newExportPage(QString srcFilePath, QString dstFilePath, QString widgetType, QString p4ValuePre = QString(), bool isNewPage = true, QString appType = "pdf");
	KPromeMainWindow* findMainWindow(bool& isNewStandaloneWindow);
	QString downloadTemplate(QString& url, QString& name, const QString& templateId);
	QString getFileType(const QString& fileName);//返回组件名
	void runApp(const QString& appId, const std::map<QString, QString>& appProperties = std::map<QString, QString>());
	void newPdfFromMobileScan();

signals:
	void closeTab();

private:
	KPromeNewDocsStartup* _window;
	QMap<QString, KPromePluginWidget::WidgetType> m_createDocApp;
	QMap<QString, PluginInfo> m_pluginName;
	KPdfProxyPugin* m_proxyPlugin;
	bool m_bIsShowSelectFileDlg;//判断是否打开选择文件对话框，避免连续响应
	bool						m_isPageReady;
	int							m_currentMessageCnt;
	QSet<QString>				m_downloadfileIdSet;
	bool						m_bScriptReady;
	QList<docer::base::closure>		m_onScriptReadyTasks;
};

//////////////////////////////////////////////////////////////////////////

class KProxyPugin_ExportImage : public KPdfProxyPugin
{
	Q_OBJECT
public:
	explicit KProxyPugin_ExportImage(QObject* parent);
	~KProxyPugin_ExportImage();

protected:
	void runApp() override;

private:
	bool m_bIsShowSelectFileDlg;//判断是否打开选择文件对话框，避免连续响应
};

#endif // __KNEWDOCS_KPROMEPDFWINDOW_H__