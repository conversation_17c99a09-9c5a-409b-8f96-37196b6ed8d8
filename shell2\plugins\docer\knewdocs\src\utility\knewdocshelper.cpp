﻿#include "stdafx.h"
#include "knewdocshelper.h"
#include "utilities/path/module/kcurrentmod.h"
#include "knewdochelper.h"
#include "kpromenewdocspage.h"
#include <kprometheus/kpromemainwindow.h>
#include <kprometheus/kpromecentralarea.h>
#include "src/tabbar/kpromenewdocstabbarconfig.h"
#include "ksolite/kpluginmanager.h"
#include "ksolite/kdomainmgr.h"
#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"
#include "src/utility/kxsettings.h"
#include "ksolite/cloudservice/knewclouddocswitchintf.h"
#include "ksolite/cloudservice/kcloudsvrproxy.h"
#include <ksolite/kconfigcentersettings.h>
#include <ksolite/kpluginconfig.h>
#include <kdocertoolkitlite/kdocerutilslite.h>
#include <ksolite/kdocer/kdocercoreitef.h>
#include <krt/kentrycontrol/kentrycontrolproxy.h>
#include <krt/kconfigmanagercenter.h>

#define STR_KLM "klm"
#define STR_ELEMENT_NAME "element_name"
#define STR_ELEMENT_TYPE "element_type"
#define STR_ELEMENT_POSITION "element_position"
#define STR_ACT "act"
#define STR_MODULE_NAME "module_name"
#define STR_SECOND_MODULE "second_module_name"
#define STR_FUNC "func"
#define STR_FUNC_VERSION "func_version"
#define STR_FIRST_ENTRY "first_entry"
#define STR_PAGE_NAME "page_name"
#define RESUMEEDITOR_PLUGIN_NAME "kpromeresume"
#define g_strLibraryApp "docerlibrary"

namespace {
	constexpr const char* g_str_company_user_platform = "category";
	
	IKNewCloudDocSwitch* getNewCloudDocSwicth()
	{
		if (KCloudSvrProxy* pCloudSvrProxy = promeApp->cloudSvrProxy())
			return pCloudSvrProxy->getNewCloudDocSwitch();
		return nullptr;
	}

	QString getItemKeyPrefix(const QString& item, const QString& id = QString())
	{
		QString key = "wpsoffice/plugins/khyperion/companyInfoCache";
		if (id.isEmpty())
		{
			key += QString("/%1").arg(item);
		}
		else
		{
			key += QString("/%1/%2").arg(id).arg(item);
		}
		return key;
	}

	QString getItemConfig(const QString& item, const QString& id = QString())
	{
		QString key = getItemKeyPrefix(item, id);
		QVariant value = krt::kcc::flushRead(key, QVariant(), krt::kcc::ReadOptions());
		return value.toString();
	}
}

QString KNewDocsHelper::getTabbarConfigFilePath()
{
	static QString s_configFilePath = 
		[]()->QString
	{
		QString webResFilePath = KxPathProvider::getWebResDir() % QLatin1String("/kuip/knewdoctabbar.json");
		if (QFile::exists(webResFilePath))
			return webResFilePath;
		return docer::base::KCurrentModule::getModuleResourceDirPath() % QLatin1String("/res/kuip/knewdoctabbar.json");
	}();
	return s_configFilePath;
}

QString KNewDocsHelper::getPaySourceComponent(KPromeNewDocsPage* page)
{
	if (!page)
		return "wps";

	static QHash<int, QString> s_tabTypeMapPageComponent = []()
	{
		QHash<int, QString> t;
		t[KPromePluginWidget::pageDocerPdf] = "pdf";
		t[KPromePluginWidget::pageMytpl] = "mytpl";
		t[KPromePluginWidget::pageDocerWps] = "wps";
		t[KPromePluginWidget::pageDocerEt] = "et";
		t[KPromePluginWidget::pageDocerWpp] = "wpp";
		t[KPromeNewDocsTabInfoObj::newdocWidgetDocerKDocs] = "kdocs";
		t[KPromePluginWidget::widgetChuangKit] = "ckt";
		t[KPromePluginWidget::widgetProcesson_Flow] = "flow";
		t[KPromePluginWidget::widgetProcesson_Mind] = "mind";
		t[KPromePluginWidget::widgetWpsForm] = "form";
		t[KPromePluginWidget::widgetCloudFolderShare] = "share";
		t[KPromeNewDocsTabInfoObj::pageKOtl] = "kotl";
		t[KPromeNewDocsTabInfoObj::pageKSheet] = "ksheet";
		return t;
	}();

	return s_tabTypeMapPageComponent[page->getCurrentTabType()];
}

QString KNewDocsHelper::getPayConfig(KPromeNewDocsPage* page)
{
	const QString defaultConfig = "docer_newfile19";
	if (!page)
		return defaultConfig;

	static QHash<int, QString> s_tabTypeMapPayConfig = []()
	{
		QHash<int, QString> t;
		t[KPromePluginWidget::widgetChuangKit] = "docer_ckt_8344d71d0c";
		t[KPromePluginWidget::widgetProcesson_Flow] = "docer_flow_tree_cb6cdfdd16";
		t[KPromePluginWidget::widgetProcesson_Mind] = "docer_mind_map_cb6cdfdd16";
		return t;
	}();

	auto tabType = page->getCurrentTabType();
	if (s_tabTypeMapPayConfig.contains(tabType))
		return s_tabTypeMapPayConfig[tabType];

	return defaultConfig;
}

QString KNewDocsHelper::getPayCSource(KPromeNewDocsPage* page)
{
	if (!page)
		return "";

	static QHash<int, QString> s_tabTypeMapPaySource = []()
	{
		QHash<int, QString> t;
		t[KPromePluginWidget::widgetProcesson_Flow] = "pc_docer_flow_tree_cb6cdfdd16";
		t[KPromePluginWidget::widgetProcesson_Mind] = "pc_docer_mind_map_cb6cdfdd16";
		return t;
	}();

	auto tabType = page->getCurrentTabType();
	if (s_tabTypeMapPaySource.contains(tabType))
		return s_tabTypeMapPaySource[tabType];

	return "";
}

QString KNewDocsHelper::getPaySourceUserType()
{
	if (auto cloudSvr = promeApp->cloudSvrProxy())
	{
		if(KPromeCloudSvrProxy::isVipTypeSVip(cloudSvr->getVipType()))
			return "v40";
		if (KPromeCloudSvrProxy::isVipTypeDocer(cloudSvr->getVipType()))
			return "v12";
		if (KPromeCloudSvrProxy::isVipTypeWpsVip(cloudSvr->getVipType()))
			return "v20";
		if (KPromeCloudSvrProxy::isVipTypeNomal(cloudSvr->getVipType()))
			return "v10";
	}
	return "null";
}

QString KNewDocsHelper::getPaySourceEntrance(KPromeNewDocsPage* page)
{
	if (!page)
		return "";

	return page->entrance();
}

QString KNewDocsHelper::getPageEntryId(KPromeNewDocsPage* page)
{
	if (!page)
		return "";
	return page->entryId();
}

QString KNewDocsHelper::getMemberIdentity()
{
	QStringList lstUserTypes;
	if (promeApp && promeApp->cloudSvrProxy())
		lstUserTypes = promeApp->cloudSvrProxy()->getRawUserTypeList();
	return lstUserTypes.join("#");
}

void KNewDocsHelper::sendDocerPayAction(const QString& entryId, const QString& payKey, const QString& workbord)
{
	QHash<QString, QString> content;
	content["dev_t"] = "pc";
	content["pay_key"] = payKey;
	content["entry_id"] = entryId;
	content["function"] = "newfile";
	content["belong_func"] = "";
	content["mb_id"] = "";
	content["zt_id"] = "";
	content["workboard"] = workbord;
	KDocerUtils::postGeneralEvent("docer_pay_action", content);
}

KPromeNewDocsPage* KNewDocsHelper::getCurrentNewDocPage()
{
	if (auto mw = promeApp->currentPromeMainWindow())
	{
		if (auto centralArea = mw->centralArea())
		{
			if(auto page = centralArea->currentPage())
				return qobject_cast<KPromeNewDocsPage*>(page);
		}
	}
	return nullptr;
}

QString KNewDocsHelper::getPageNameInfo(int tabType)
{
	auto pageNameInfo = QString("create[%1]").arg(getPageName(tabType));
	return pageNameInfo;
}

QString KNewDocsHelper::getPageName(int tabType)
{
	static QHash<int, QString> s_tabTypeMapPageName = []()
	{
		QHash<int, QString> t;
		t[KPromePluginWidget::pageDocerPdf] = "pdf";
		t[KPromePluginWidget::pageMytpl] = "my_docer";
		t[KPromePluginWidget::pageDocerWps] = "wps";
		t[KPromePluginWidget::pageDocerEt] = "et";
		t[KPromePluginWidget::pageDocerWpp] = "wpp";
		t[KPromeNewDocsTabInfoObj::newdocWidgetDocerKDocs] = "kdocs";
		t[KPromePluginWidget::widgetChuangKit] = "chuangkit";
		t[KPromePluginWidget::widgetProcesson_Flow] = "flowchart";
		t[KPromePluginWidget::widgetProcesson_Mind] = "brainmap";
		t[KPromePluginWidget::widgetWpsForm] = "form";
		t[KPromePluginWidget::widgetCloudFolderShare] = "shared_folder";
		t[KPromeNewDocsTabInfoObj::pageKOtl] = "kotl";
		t[KPromeNewDocsTabInfoObj::pageKSheet] = "ksheet";
		return t;
	}();
	if (s_tabTypeMapPageName.contains(tabType))
		return s_tabTypeMapPageName[tabType];
	return "x";
}

QString KNewDocsHelper::getPageName(KPromeNewDocsPage* page)
{
	if (!page)
		return getPageName(KPromeNewDocsTabInfoObj::INVALID_NEWDOCS_TAB);
	return getPageName(page->getCurrentTabType());
}

QString KNewDocsHelper::getComponentName(int tabType)
{
	QString componentName = "";
	switch (tabType)
	{
	case KPromePluginWidget::pageDocerEt:
		componentName = "et";
		break;
	case KPromePluginWidget::pageDocerWpp:
		componentName = "wpp";
		break;
	case KPromePluginWidget::pageDocerWps:
		componentName = "wps";
		break;
	case KPromePluginWidget::pageMytpl:
		componentName = "my";
		break;
	}
	return componentName;
}

QVariantMap KNewDocsHelper::getAppInfo()
{
	static QVariantMap info;
	if (info.isEmpty())
	{
		info["guid"] = KDcInfoCDetail::getGuid();
		info["uuid"] = KDcInfoCDetail::getUuid();
		info["hdid"] = KDcInfoCDetail::getHdid();
		info["dist"] = KDcInfoCDetail::getMC();
		info["channelid"] = KDcInfoCDetail::getMC();
		info["version"] = KDcInfoCDetail::getVersion();
#ifdef Q_OS_DARWIN
		info["appstore"] = krt::product::isAppStore();
		info["name"] = "wpsoffice_mac";
		if (krt::product::isAppStore())
		{
			info["origin"] = "store";
		}
		else
		{
			info["origin"] = "official";
		}
#else
		info["name"] = kcoreApp->applicationName();
#endif

		QString platform = QLatin1String("unknown");
#ifdef Q_OS_WIN
		platform = QLatin1String("win");
#elif defined Q_OS_DARWIN
		platform = QLatin1String("mac");
#elif defined Q_OS_OHOS
		platform = QLatin1String("ohos");
#elif defined Q_OS_LINUX
		platform = QLatin1String("linux");
#endif
		info["platform"] = platform;
	}
	return info;
}

#ifdef Q_OS_LINUX
QString KNewDocsHelper::getFontInfo()
{
	QFontInfo fontInfo(KDrawHelper::getFontFromThemeWithDpi("KNewDocs-WaitText", "text"));
	return fontInfo.family();
}
#endif

bool KNewDocsHelper::showSideBar()
{
	if (kcoreApp && kcoreApp->getDomainMgr() && kcoreApp->getDomainMgr()->isIntranet())
	{
		return false;
	}
	bool bHideSideBar = (!krt::kcmc::support("ShowSideBarWhenOffline") || !krt::kcmc::support("CommonDocTemplate"))
						&& !krt::kcmc::enable("CloudDocOnline")
						&& !krt::kcmc::enable("CloudDocs");
	return !bHideSideBar;
}

bool KNewDocsHelper::canAddSupportInterface(const QString& pluginName)
{
	bool bSupportInteface = false;

#if defined(_DEBUG) || defined(PLUGIN_TEST)
	bSupportInteface = true;
#endif

	if (!bSupportInteface)
		bSupportInteface = KDocerUtils::isPluginCanUse(pluginName);

	if (!bSupportInteface)
	{
		bSupportInteface = kcoreApp->getPluginMgr() &&
			kcoreApp->getPluginMgr()->getPlugin(pluginName);
	}
	return bSupportInteface;
}

QStringList KNewDocsHelper::querySupportInterface()
{
	static QStringList s_SupprtInterface;
	if (s_SupprtInterface.isEmpty())
	{
		if (canAddSupportInterface(RESUMEEDITOR_PLUGIN_NAME))
			s_SupprtInterface.append("resumeeditor");
		s_SupprtInterface.append("localstoragedb");
		s_SupprtInterface.append("newdocsLibrary");
		s_SupprtInterface.append("addcustomprops");
		s_SupprtInterface << "caiji";
		s_SupprtInterface << "libraryapppage";
		s_SupprtInterface << "getAppInfo";
		s_SupprtInterface << "getAppInfoPdf";
		s_SupprtInterface << "poAiGenEntryPop";
		s_SupprtInterface << "poNewMiddlePage";
		do
		{
			if (!KAppMgr::getInstance() || !KAppMgr::getInstance()->getApp(g_strLibraryApp))
				break;
			auto appObj = KAppMgr::getInstance()->getApp(g_strLibraryApp);
			QString pageType = appObj->getProp("pageType").toString();
			if (pageType == "pageDocer")
			{
				QString plgName = appObj->getProp("pluginName").toString();
				QString path;
				if (!kpluginconfig::hasPlugin(plgName, path))
					KDocerUtils::findLocalPlugin(plgName, path);
				QDir dir;
				if (QFileInfo(path).isDir())
					dir = QDir(path);
				else
					dir = QDir(QFileInfo(path).absoluteDir());
				if (!dir.exists())
					break;
				if (!QFileInfo(dir.filePath(QString("%1.json").arg(g_strLibraryApp))).exists())
					break;
			}
			else if (pageType != "pageDocerThirdApp")
				break;
			s_SupprtInterface << "libraryapp";
		} while (false);
	}
	return s_SupprtInterface;
}

QVariantMap KNewDocsHelper::getUserLoginInfo()
{
	bool bLogined = false;
	QVariantMap ckResult;
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	if (account && account->isLogined())
	{
		bLogined = true;
		QString userId = account->getUserId();
		ckResult["userid"] = userId;
		ckResult["userTypes"] = KDocerUtils::getMemberIdentityStr().split("#");
	}
	ckResult["logined"] = bLogined;
	return ckResult;
}

bool KNewDocsHelper::isNewCloudDocSwitchEnabled()
{
#if defined(Q_OS_DARWIN) || defined(Q_OS_OHOS) || defined(X_LINUX_DESKTOP)
	//Mac、OHOS、Linux桌面平台暂不支持新建空白上云流程，待端云的客户端同学实现
	//屏蔽前端入口
	return false;
#endif

	if (IKNewCloudDocSwitch* pNewCloudDocsSwitch = getNewCloudDocSwicth())
		return pNewCloudDocsSwitch->isNewCloudDocSwitchEnabled();
	return false;
}

bool KNewDocsHelper::isNewCloudDocSwitchOpened()
{
	if (IKNewCloudDocSwitch* pNewCloudDocsSwitch = getNewCloudDocSwicth())
		return pNewCloudDocsSwitch->isNewCloudDocSwitchOpened();
	return false;
}

bool KNewDocsHelper::isNewCloudDocSwitchChangeable()
{
	if (IKNewCloudDocSwitch* pNewCloudDocsSwitch = getNewCloudDocSwicth())
		return pNewCloudDocsSwitch->isNewCloudDocSwitchChangeable();
	return false;
}

void KNewDocsHelper::setNewCloudDocSwitchStatus(bool bOpen)
{
	if (IKNewCloudDocSwitch* pNewCloudDocsSwitch = getNewCloudDocSwicth())
		pNewCloudDocsSwitch->setNewCloudDocSwitchStatus(bOpen);
}

// 仅用作新建上云的引导推荐，作为是否第一次新建页面打开的标志, 若清理了配置中心的数据，默认为false
bool KNewDocsHelper::isThisDeviceFirstOpen()
{
	KCCPluginsSettings settings;
	settings.beginGroup(KPluginName);
	bool bFirstOpen = settings.value("isThisDeviceFirstOpen", true).toBool();
	if (true == bFirstOpen)
		settings.setValue("isThisDeviceFirstOpen", false);
	return bFirstOpen;
}

QMap<QString, QString> KNewDocsHelper::getNewdocsTimeStamp(KPromeNewDocsPage* page)
{
	if (page)
		return page->timeStampMap();
	return QMap<QString, QString>();
}

void KNewDocsHelper::newCloudDocSwitchRegisterNotifyObj(QObject* obj, const QString& slotName)
{
	if (!obj || slotName.isEmpty())
		return;
	if (IKNewCloudDocSwitch* pNewCloudDocsSwitch = getNewCloudDocSwicth())
	{
		pNewCloudDocsSwitch->registerNotifyObj(obj, slotName);
	}
}

void KNewDocsHelper::newCloudDocSwitchUnregisterNotifyObj(QObject* obj)
{
	if (!obj)
		return;
	if (IKNewCloudDocSwitch* pNewCloudDocsSwitch = getNewCloudDocSwicth())
	{
		pNewCloudDocsSwitch->unregisterNotifyObj(obj);
	}
}

bool KNewDocsHelper::hasPrivileges(const QStringList& privilegeIds)
{
	kacctoutsdk::KAccountSdkProxy* pAccountSdk = kcoreApp->getAccountSDK();
	if (!pAccountSdk)
		return false;

	kacctoutsdk::IAccount* pAccount = pAccountSdk->getAccount();
	if (!pAccount)
		return false;

	kacctoutsdk::IPrivileges* pPrivileges = pAccount->getPrivileges();
	if (!pPrivileges)
		return false;

	kacctoutsdk::PrivilegeInfos privilgeResult;
	kacctoutsdk::PrivilegeStatus status = pPrivileges->getPrivileges(privilegeIds, privilgeResult);
	return !privilgeResult.isEmpty();
}

QString KNewDocsHelper::componentErrorPage()
{
	if (krt::kcmc::support("WpsNewDocsDefaultPage"))
	{
		return QDir::fromNativeSeparators("file:///" % docer::base::KCurrentModule::getModuleResourceDirPath()
			% "/res/personal/error-net.html");
	}

	return QString();
}

bool KNewDocsHelper::isSupportDefaultPage(int tabType)
{
	// 目前仅Mac和winPersonal支持了新建兜底功能
	// pdf独立版以及linux，win企业版都暂未适配
	if (krt::kcmc::support("WpsNewDocsDefaultPage") && KPromeNewDocsPage::isComponentType(tabType))
		return true;

	return false;
}

QString KNewDocsHelper::getCompanyUserCategory()
{
	QString userId;

	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	if (account)
		userId = account->getUserId();

	return getItemConfig(g_str_company_user_platform, userId);
}

void KNewDocsHelper::getCurrentPageEntryControlStatus(int pageType, QJsonObject& jsonObj)
{
	QString appName;
	switch (pageType)
	{
	case KPromePluginWidget::WidgetType::pageDocerWps:
	case KPromePluginWidget::WidgetType::pageDocerWpp:
	case KPromePluginWidget::WidgetType::pageDocerEt:
	case KPromePluginWidget::WidgetType::pageDocerPdf:
		appName = getPageName(pageType);
		break;
	case KPromePluginWidget::WidgetType::widgetProcesson_Flow:
		appName = "processon"; 
		break;
	case KPromePluginWidget::WidgetType::widgetProcesson_Mind:
		appName = "mind";
		break;
	default:
		break;
	}
	if (!appName.isEmpty())
	{
		QString entryName = "template/docer/" % appName;
		jsonObj[entryName] = krt::kentrycontrol::isEntryNeedHide(entryName);
	}
}

QString KNewDocsHelper::getNonAdaptedWebSkinName()
{
	QString skinName;
	do
	{
		if (!qApp)
			break;

		KSkin* skin = qApp->findChild<KSkin*>();
		if (!skin)
			break;

		skinName = KDrawHelper::isDarkSkin() ? KSkin::getDefaultDarkSkinName() :
			skin->getSkinInfo().m_skinName;
	} while (false);
	return skinName;
}

bool KNewDocsHelper::isSupportCtrlAltAKey()
{
	auto isSupportCtrlAltAKeyFunc = []()->bool
	{
		QString strShortCutKey = QString("wpsoffice/common/kprometheus/") % QString("isSupportCtrlAltAKey");
		return krt::kcc::flushRead(strShortCutKey, false, krt::kcc::LocalConfig).toBool();
	};

	static bool bSupportCtrlAltAKeyEnabled = isSupportCtrlAltAKeyFunc();
	return bSupportCtrlAltAKeyEnabled;
}