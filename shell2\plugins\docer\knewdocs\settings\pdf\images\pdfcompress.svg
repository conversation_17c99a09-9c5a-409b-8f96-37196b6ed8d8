<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.4 (67378) - http://www.bohemiancoding.com/sketch -->
    <title>PDF压缩</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="64" height="64" rx="6"></rect>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="#01" transform="translate(-871.000000, -1454.000000)">
            <g id="1366768" transform="translate(214.000000, 602.000000)">
                <g id="#01">
                    <g id="推荐模版" transform="translate(275.000000, 191.000000)">
                        <g id="分组-7" transform="translate(3.000000, 304.000000)">
                            <g id="PDF压缩" transform="translate(347.000000, 324.000000)">
                                <g transform="translate(32.000000, 33.000000)">
                                    <mask id="mask-2" fill="white">
                                        <use xlink:href="#path-1"></use>
                                    </mask>
                                    <use id="Mask" fill="#ED5E5E" xlink:href="#path-1"></use>
                                    <g id="金山PDF压缩logo" mask="url(#mask-2)">
                                        <g transform="translate(3.000000, 3.000000)">
                                            <rect id="矩形" fill="#ED5E5E" fill-rule="evenodd" x="0" y="0" width="58" height="58" rx="16"></rect>
                                            <path d="M24.1176471,11 L24.1176471,14.5789474 L27.7647059,14.5789474 C28.7718133,14.5789474 29.5882353,13.7777727 29.5882353,12.7894737 L29.5882353,11 L33.2010712,11 L33.2010712,20.6631579 C33.2010712,22.2444363 34.5073463,23.5263158 36.1187183,23.5263158 L46,23.5263158 L46,42.1368421 C46,43.7181205 44.6937249,45 43.0823529,45 L17.9176471,45 C16.3062751,45 15,43.7181205 15,42.1368421 L15,13.8631579 C15,12.2818795 16.3062751,11 17.9176471,11 L24.1176471,11 Z M20.4705882,14.5789474 C19.4634808,14.5789474 18.6470588,15.380122 18.6470588,16.3684211 C18.6470588,17.3567201 19.4634808,18.1578947 20.4705882,18.1578947 L24.1176471,18.1578947 L24.1176471,14.5789474 L20.4705882,14.5789474 Z M24.1176471,18.1578947 L24.1176471,21.7368421 L27.7647059,21.7368421 C28.7718133,21.7368421 29.5882353,20.9356674 29.5882353,19.9473684 C29.5882353,18.9590695 28.7718133,18.1578947 27.7647059,18.1578947 L24.1176471,18.1578947 Z M20.4705882,21.7368421 C19.4634808,21.7368421 18.6470588,22.5380169 18.6470588,23.5263158 L18.6470588,25.3157895 L24.1176471,25.3157895 L24.1176471,21.7368421 L20.4705882,21.7368421 Z M22.2941176,36.0526316 L22.2941176,39.6315789 C22.2941176,40.6198779 23.1105397,41.4210526 24.1176471,41.4210526 C25.1247545,41.4210526 25.9411765,40.6198779 25.9411765,39.6315789 L25.9411765,36.0526316 L22.2941176,36.0526316 Z M18.6470588,25.3157895 L18.6470588,34.2631579 C18.6470588,35.2514568 19.4634808,36.0526316 20.4705882,36.0526316 L27.7647059,36.0526316 C28.7718133,36.0526316 29.5882353,35.2514568 29.5882353,34.2631579 L29.5882353,27.1052632 C29.5882353,26.1169642 28.7718133,25.3157895 27.7647059,25.3157895 L18.6470588,25.3157895 Z M22.2941176,28.8947368 L25.9411765,28.8947368 L25.9411765,32.4736842 L22.2941176,32.4736842 L22.2941176,28.8947368 Z M35.0588235,11 L46,21.7368421 L36.5176471,21.7368421 C35.7119611,21.7368421 35.0588235,21.0959024 35.0588235,20.3052632 L35.0588235,11 Z" id="合并形状" fill="#FFFFFF" fill-rule="nonzero"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>