﻿#include "stdafx.h"
#include "knewdocpagejsapibase.h"
#include "knewdochelper.h"
#include "ksolite/kpluginmgr.h"
#include "ksolite/kxjsonhelper.h"
#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"

#include "ugcresource/kdocerugcresourcehandler.h"
#include "utilities/path/module/kcurrentmod.h"
#include "kpromenewdocspage.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "ksolite/kwebview/jsapi/kxwebviewjsapidef.h"
#include "ksolite/kwebview/jsapi/apihandler/kxpluginhandler.h"
#include "ksolite/kdocercoreitef.h"
#include "kdocercorelitehelper.h"
namespace
{
	#define RESUMEEDITOR_PLUGIN_NAME "kpromeresume"
	const char* const g_strLibraryApp = "docerlibrary";
	#define PARAM_CSOURCE "csource"
	#define PARAM_POSITION "position"
	#define METHOD_RESULT "result"
}

class KxNewDocPluginHandler : public KxJSApiPluginHandler
{
public:
	KxNewDocPluginHandler(QObject* parent, KxWebViewAsynJSObject* rootJsObject)
		: KxJSApiPluginHandler(parent, rootJsObject)
	{}
public:
	virtual void getAppInfo(KxWebViewJSContext& context) override
	{
		const QVariantMap contextArgs = parseContextArgs(context);
		QVariantMap resultMap;
#ifdef X_LINUX_DESKTOP
		resultMap.insert(g_str_callstatus, g_str_error);
		resultMap.insert(g_str_errormsg, "app not support");
		context.Result[METHOD_RESULT] = JsonHelper::variantMapToJsonValue(resultMap);
		return;
#endif

		if (contextArgs.isEmpty())
		{
			resultMap.insert(g_str_callstatus, g_str_error);
			resultMap.insert(g_str_errormsg, "contextArgs is empty");
			context.Result[METHOD_RESULT] = JsonHelper::variantMapToJsonValue(resultMap);
			return;
		}

		QString appId = contextArgs.value("pluginName", "").toString();
		if (appId.isEmpty())
		{
			resultMap.insert(g_str_callstatus, g_str_error);
			resultMap.insert(g_str_errormsg, "appId is empty");
			context.Result[METHOD_RESULT] = JsonHelper::variantMapToJsonValue(resultMap);
			return;
		}

		KAppMgr* appMgr = KAppMgr::getInstance();
		if (appMgr == nullptr)
		{
			resultMap.insert(g_str_callstatus, g_str_error);
			resultMap.insert(g_str_errormsg, "appMgr is null");
			context.Result[METHOD_RESULT] = JsonHelper::variantMapToJsonValue(resultMap);
			return;
		}

		KAppObj* appObj = appMgr->getApp(appId);
		if (appObj == nullptr)
		{
			resultMap.insert(g_str_callstatus, g_str_error);
			resultMap.insert(g_str_errormsg, "appObj not Valid");
			context.Result[METHOD_RESULT] = JsonHelper::variantMapToJsonValue(resultMap);
			return;
		}

		//base info
		AppMgr::AppBaseInfo baseInfo = appObj->getAppBaseInfo();

		resultMap.insert("appId", baseInfo.appId);
		resultMap.insert("entryId", baseInfo.entryId);
		resultMap.insert("appName", baseInfo.appName);
		resultMap.insert("appDesc", baseInfo.appDesc);
		resultMap.insert("action", baseInfo.action);
		resultMap.insert("webAppUrl", baseInfo.webAppUrl);
		resultMap.insert("appCenterIconName", baseInfo.appCenterIconName);
		resultMap.insert("appCenterIconUrl", baseInfo.appCenterIconUrl);
		resultMap.insert("appStarIconName", baseInfo.appStarIconName);
		resultMap.insert("appStarIconUrl", baseInfo.appStarIconUrl);
		resultMap.insert("quickAccessIconName", baseInfo.quickAccessIconName);
		resultMap.insert("appSearchIconName", baseInfo.appSearchIconName);
		resultMap.insert("appMarketIconName", baseInfo.appMarketIconName);
		resultMap.insert("appMarketIconUrl", baseInfo.appMarketIconUrl);
		resultMap.insert("isAIApp", appObj->getProp("isAIApp").toBool());

		//extend info
		resultMap.insert("extends", appObj->extends());

		//custom info
		const QVariantList queryList = contextArgs.value("queryList", "").toList();
		for (auto itor = queryList.constBegin(); itor != queryList.cend(); ++itor)
		{
			const QString queryKey = itor->toString();
			if (!queryKey.isEmpty())
				resultMap.insert(queryKey, appObj->getProp(queryKey.toUtf8()).toString());
		}

		//schema info
		const QString strSceneType = contextArgs.value("sceneType").toString();
		const QString action = contextArgs.value("action").toString();
		AppMgr::AppSceneType sceneType = convertAppSceneType(strSceneType);

		if (sceneType != AppMgr::InvalidSceneType)
		{
			QSharedPointer<KAppScheme> appScheme = appObj->getAppScheme(sceneType, action);
			if (appScheme)
			{
				QVariantMap schemaInfo;
				const QStringList propKeys = appScheme->getPropKeys();
				for (auto itor = propKeys.constBegin(); itor != propKeys.cend(); ++itor)
					schemaInfo.insert(*itor, appScheme->getProp(*itor));

				//match icon
				QStringList iconCollect({ "appCenterIcon", "appMarketIcon", "appStarIcon", "quickAccessIcon" });
				for (auto itor = iconCollect.constBegin(); itor != iconCollect.constEnd(); itor++)
				{
					const QString iconName = schemaInfo.value(*itor).toString();
					const QStringList iconUrl = appScheme->getIconUrlList(iconName);

					if (!iconUrl.isEmpty())
						schemaInfo[*itor] = iconUrl;
				}

				resultMap.insert("schema", schemaInfo);
			}
		}

		context.Result[METHOD_RESULT] = JsonHelper::variantMapToJsonValue(resultMap);
		context.Result["pluginName"] = appId.toUtf8().data();
		context.Result["version"] = appObj->getProp("version").toString().toUtf8().data();
		context.Result["name"] = appObj->getProp("name").toString().toUtf8().data();
		context.Result["description"] = appObj->getProp("description").toString().toUtf8().data();
		context.Result["pluginExist"] = appObj->checkIsCanLoadInLocal();
	}

	AppMgr::AppSceneType convertAppSceneType(const QString& appSceneType)
	{
		if (appSceneType.compare("AppCenter", Qt::CaseInsensitive) == 0)
			return AppMgr::AppCenter;
		else if (appSceneType.compare("RibbonTab", Qt::CaseInsensitive) == 0)
			return AppMgr::RibbonTab;
		else if (appSceneType.compare("RightMenu", Qt::CaseInsensitive) == 0)
			return AppMgr::RightMenu;
		else if (appSceneType.compare("FileMenu", Qt::CaseInsensitive) == 0)
			return AppMgr::FileMenu;
		else if (appSceneType.compare("ObjectFloatMenu", Qt::CaseInsensitive) == 0)
			return AppMgr::ObjectFloatMenu;
		else if (appSceneType.compare("Startpage", Qt::CaseInsensitive) == 0)
			return AppMgr::Startpage;
		else if (appSceneType.compare("Recommend", Qt::CaseInsensitive) == 0)
			return AppMgr::Recommend;
		else if (appSceneType.compare("Taskpane", Qt::CaseInsensitive) == 0)
			return AppMgr::Taskpane;

		return AppMgr::InvalidSceneType;
	}
};

KNewDocPageJsApiBase::KNewDocPageJsApiBase(KxWebViewContainer* webView)
	: KDocerCommonJsApi(webView, nullptr)
{
	KNewDocPageJsApiHelper::getInstance()->addApi(this);
}

KNewDocPageJsApiBase::~KNewDocPageJsApiBase()
{
	KNewDocPageJsApiHelper::getInstance()->removeApi(this);
}

void KNewDocPageJsApiBase::initHandlers()
{
	KDocerCommonJsApi::initHandlers();
	setHandler("common.ugcresource", new KDocerUgcResourceHandler(this, this));
	setHandler("common.plugin", new KxNewDocPluginHandler(this, this));
}

void KNewDocPageJsApiBase::getNewDocsEntrance(KxWebViewJSContext& context)
{
	QString entrance = KNewDocsHelper::getPaySourceEntrance(getNewDocsPage());
	setResult(context, QVariant::fromValue(entrance));
}

void KNewDocPageJsApiBase::getNewdocsTimeStamp(KxWebViewJSContext& context)
{
	QVariantMap result;
	QMap<QString, QString> timeMap = KNewDocsHelper::getNewdocsTimeStamp(getNewDocsPage());
	for (auto iter = timeMap.constBegin(); iter != timeMap.constEnd(); ++iter)
	{
		if (iter.key().isEmpty() || iter.value().isEmpty())
			continue;
		result.insert(iter.key(),iter.value());
	}
	setResult(context, result);
}

KPromeNewDocsPage* KNewDocPageJsApiBase::getNewDocsPage()
{
	return KTik::findParentByType<KPromeNewDocsPage>(this);
}

void KNewDocPageJsApiBase::querySupportInterface(KxWebViewJSContext& context)
{
	QStringList supportInterfance = KNewDocsHelper::querySupportInterface();
	setResult(context, supportInterfance);
	emit jsInit();
}

QString KNewDocPageJsApiBase::getSkinConfigPath() const
{
	return KxCreateDocConfig::instance().getSkinConfigPath();
}

void KNewDocPageJsApiBase::openDocerUnifyPayDlg(KxWebViewJSContext& context)
{
	const QVariantMap& args = parseContextArgs(context);
	if (args.isEmpty())
		return;
	//启动稻壳支付的500ms内，不能再次点击稻壳支付，防止多个支付窗口出现
	if (m_openDocerUnifyPayTimeStamp > QDateTime::currentMSecsSinceEpoch())
		return;
	const QString& strUrl = QString::fromUtf8(args.value("url").toString().toUtf8().data());
	if (strUrl.isEmpty())
		return;
	//启动稻壳支付的500ms内，不能再次点击稻壳支付，防止多个支付窗口出现
	m_openDocerUnifyPayTimeStamp = QDateTime::currentMSecsSinceEpoch() + 500;
	if (args.value("bReport", false).toBool())
	{
		QVariantMap reportInfoMap = args.value("reportInfo").toMap();
		QUrl qUrl(strUrl);
		QHash<QString, QString> content;
		content.insert("mod_name", "newdocs");
		content.insert("vip_type", reportInfoMap.value("vip_type").toString());
		QString userId;

		IKDocerAccount* account = nullptr;
		auto kdocercoreLite = getIKDocerCoreLite();
		if (kdocercoreLite)
			account = kdocercoreLite->getIKDocerAccount();
		if (account)
			userId = account->getUserId();

		content.insert("userid", userId);
		content.insert("resid", reportInfoMap.value("resid").toString());
		content.insert("res_type", reportInfoMap.value("res_type").toString());
		content.insert("respid", reportInfoMap.value("respid").toString());
		content.insert("reason", reportInfoMap.value("reason").toString());
		content.insert("win_type", reportInfoMap.value("win_type").toString());
		content.insert("side", "web");
		QUrlQuery urlQuery(qUrl);
		if (urlQuery.hasQueryItem(PARAM_CSOURCE))
			content.insert(PARAM_CSOURCE, urlQuery.queryItemValue(PARAM_CSOURCE));
		if (urlQuery.hasQueryItem(PARAM_POSITION))
			content.insert(PARAM_POSITION, urlQuery.queryItemValue(PARAM_POSITION));
		KDocerUtils::postGeneralEvent("docer_pay_win_show", content);
	}

	if (auto docerCoreLite = getIKDocerCoreLite())
	{
		docerCoreLite->showDocerPayDlg(strUrl, KPluginName, widgetParent(), nullptr, QVariantMap());
	}
	else
	{
		KxLoggerLite::writeWarning(QString(__FUNCTION__).toStdWString(), L"docercorelite is nullptr");
	}
}

void KNewDocPageJsApiBase::getUserLoginInfo(KxWebViewJSContext& context)
{
	auto ckResult = KNewDocsHelper::getUserLoginInfo();
	context.Result[METHOD_RESULT] = ConvertJsonHelper::variantToJson(ckResult);
}

void KNewDocPageJsApiBase::onBoardcastCustomMessage(const QString& method, const QString& param)
{
	for (int i = 0; i < KNewDocPageJsApiHelper::getInstance()->count(); ++i)
	{
		KNewDocPageJsApiBase* pApi = qobject_cast<KNewDocPageJsApiBase*>(KNewDocPageJsApiHelper::getInstance()->item(i));
		if (pApi)
			pApi->callbackToJS(method, param);
	}
}