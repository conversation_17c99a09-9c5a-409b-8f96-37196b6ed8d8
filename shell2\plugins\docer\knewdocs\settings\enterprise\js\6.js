(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[6],{1694:function(t,e,a){"use strict";a("5ed4")},"2b02":function(t,e,a){},"2f59":function(t,e,a){"use strict";a("4600")},4600:function(t,e,a){},"5ed4":function(t,e,a){},"6a21":function(t,e,a){"use strict";a("2b02")},"9a5e":function(t,e,a){"use strict";a("bd20")},b53e:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"mask"},[e("div",{staticClass:"category-panel"},[e("div",{staticClass:"category-panel-head"},[e("span",{staticClass:"category-panel-head__title"},[t._v("设置我的分类")]),e("span",{directives:[{name:"collect",rawName:"v-collect.click",value:{$m_n:"more_tab_new",$e_n:"close",$e_t:"button",$e_i:10777},expression:"{$m_n: 'more_tab_new',$e_n: 'close',$e_t: 'button',$e_i: 10777}",modifiers:{click:!0}}],staticClass:"category-panel-head__close",on:{click:t.closeModel}},[e("SvgIcon",{attrs:{svgName:"close-16-2"}})],1)]),e("div",{staticClass:"category-panel-content"},[e("DraggableList",{attrs:{myCategoriesData:t.myCategoriesData,defaultCategoriesData:t.defaultCategoriesData},on:{listChange:t.listChange}}),e("SelectableList",{attrs:{statusCode:t.statusCode,allCategoriesData:t.allCategoriesData,myCategoriesIds:t.myCategoriesIds},on:{changeStatus:t.changeStatus,retry:t.initAllCategories}})],1),e("div",{directives:[{name:"collect",rawName:"v-collect.click",value:{$m_n:"more_tab_new",$e_n:"save",$e_t:"button",$e_i:10779},expression:"{$m_n: 'more_tab_new',$e_n: 'save',$e_t: 'button',$e_i: 10779}",modifiers:{click:!0}}],staticClass:"category-panel-save",on:{click:t.saveChange}},[t._v("确定")]),e("div",{staticClass:"category-panel-cancel",on:{click:t.closeModel}},[t._v("取消")])])])},i=[],n=a("2f62"),l=function(){var t=this,e=t._self._c;return e("div",{staticClass:"selectable-list"},[e("div",{staticClass:"selectable-list-title"},[t._v("所有分类")]),t.statusCode?e("DataStatus",{attrs:{statusCode:t.statusCode},on:{retry:t.retry}}):e("ul",{staticClass:"selectable-list-content"},t._l(t.allCategoriesData,(function(a,s){return e("li",{key:s,staticClass:"selectable-list-content__wrap"},[a.category_list&&a.category_list.length?[e("div",{staticClass:"selectable-list-content__wrap__title"},[t._v(t._s(a.name))]),e("ul",{staticClass:"selectable-list-content__list"},t._l(a.category_list,(function(a){return e("li",{directives:[{name:"collect",rawName:"v-collect.display.click",value:{$m_n:"more_tab_new",$e_n:"all_tab_btn",$e_t:"button",$e_i:{click:10769,display:10770},tab_name:a.name,state:t.myCategoriesIds.includes(a.id)?"select":"unselect"},expression:"{\n\t\t\t\t\t\t\t$m_n: 'more_tab_new',\n\t\t\t\t\t\t\t$e_n: 'all_tab_btn',\n\t\t\t\t\t\t\t$e_t: 'button',\n\t\t\t\t\t\t\t$e_i: {\n\t\t\t\t\t\t\t\tclick: 10769,\n\t\t\t\t\t\t\t\tdisplay: 10770\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\ttab_name: item.name,\n\t\t\t\t\t\t\tstate: myCategoriesIds.includes(item.id) ? 'select' : 'unselect'\n\t\t\t\t\t\t}",modifiers:{display:!0,click:!0}}],key:a.id,staticClass:"selectable-list-content__subItem",class:{"selectable-list-content__subItem--selected":t.myCategoriesIds.includes(a.id),"selectable-list-content__subItem--unselectable":!a.is_sort},on:{click:function(e){return t.changeStatus(a)}}},[e("SvgIcon",{directives:[{name:"show",rawName:"v-show",value:!t.myCategoriesIds.includes(a.id)&&a.is_sort,expression:"!myCategoriesIds.includes(item.id) && item.is_sort"}],attrs:{svgName:"add-icon"}}),e("SvgIcon",{directives:[{name:"show",rawName:"v-show",value:t.myCategoriesIds.includes(a.id)&&a.is_sort,expression:"myCategoriesIds.includes(item.id) && item.is_sort"}],attrs:{svgName:"remove-icon"}}),a.icon?e("img",{staticClass:"selectable-list-content__subItem__icon",attrs:{src:a.icon,alt:""}}):t._e(),e("span",{staticClass:"selectable-list-content__subItem__name"},[t._v(t._s(a.name))])],1)})),0)]:t._e()],2)})),0)],1)},r=[],o=a("a4c7"),c={name:"selectable-list",components:{DataStatus:o["a"]},props:{statusCode:{type:String,default:""},allCategoriesData:{type:Array,default:function(){return[]}},myCategoriesIds:{type:Array,default:function(){return[]}}},data(){return{selectedIds:[]}},methods:{changeStatus(t){t.is_sort&&this.$emit("changeStatus",{...t,is_sort:1})},retry(){this.$emit("retry")}}},d=c,g=(a("1694"),a("2877")),m=Object(g["a"])(d,l,r,!1,null,"1ca431ed",null),_=m.exports,u=function(){var t=this,e=t._self._c;return e("div",{staticClass:"draggable-list"},[e("div",{staticClass:"draggable-list-title"},[t._v("我的分类")]),e("div",{staticClass:"draggable-list-wrap"},[e("div",{staticClass:"draggable-list-content"},[e("ul",t._l(t.defaultCategoriesData,(function(a,s){return e("li",{key:a.id+"_"+s,staticClass:"draggable-list-content__item",class:{"draggable-list-content__item--unmovable":!a.is_sort}},[a.icon?e("img",{staticClass:"draggable-list-content__item__icon",attrs:{src:a.icon,alt:""}}):t._e(),e("span",{staticClass:"draggable-list-content__item__name"},[t._v(t._s(a.name))])])})),0),e("DraggableArea",{attrs:{list:t.list,currDragIndex:t.currDragIndex},on:{handleRemove:t.handleRemove,changeListOrder:t.changeListOrder}})],1)])])},h=[],p=function(){var t=this,e=t._self._c;return e("div",{ref:"drag-list",staticClass:"drag-list"},t._l(t.list,(function(a,s){return e("div",{directives:[{name:"collect",rawName:"v-collect.display",value:{$m_n:"more_tab_new",$e_n:"my_tab_btn",$e_t:"button",$e_i:10772,tab_name:a.name},expression:"{\n\t\t\t$m_n: 'more_tab_new',\n\t\t\t$e_n: 'my_tab_btn',\n\t\t\t$e_t: 'button',\n\t\t\t$e_i: 10772,\n\t\t\ttab_name: item.name\n\t\t}",modifiers:{display:!0}}],key:a.id+"_"+s,staticClass:"draggable-list-content__item",on:{mousedown:function(e){return t.mouseDown(e,s)},mouseenter:function(e){return t.mouseEnter(e)}}},[e("SvgIcon",{staticClass:"draggable-list-content__item__remove",class:{"draggable-list-content__item__remove--drag":t.dragElIndex===s},attrs:{svgName:"remove-icon"},nativeOn:{mousedown:function(e){return e.stopPropagation(),t.handleRemove(a,s)}}}),a.icon?e("img",{staticClass:"draggable-list-content__item__icon",attrs:{src:a.icon,alt:""}}):t._e(),e("span",{staticClass:"draggable-list-content__item__name"},[t._v(t._s(a.name))]),e("SvgIcon",{directives:[{name:"show",rawName:"v-show",value:t.dragElIndex===s,expression:"dragElIndex === index"}],staticClass:"draggable-list-content__item__drag",attrs:{svgName:"drag-icon"}})],1)})),0)},b=[],y=a("3387"),v={name:"draggable-area",props:{list:{type:Array,default:function(){return[]}}},data(){return{isDrag:!1,dragElm:null,dragElIndex:-1,placeHolderEl:null,parentEl:null,pointerTop:"",dragElOriginStyle:"",dragElBaseProp:{}}},mounted(){document.addEventListener("mousemove",this.mouseMove),document.addEventListener("mouseup",this.mouseUp)},destroyed(){document.removeEventListener("mousemove",this.mouseMove),document.removeEventListener("mouseup",this.mouseUp)},methods:{mouseDown(t,e){if(this.dragElm=this.parentEl.children[e],this.dragElIndex=e,this.isDrag=!0,this.isDrag&&this.dragElm){this.getDragListBaseProp();const{width:e,height:a,offsetTop:s}=this.dragElBaseProp,i=window.getComputedStyle(this.dragElm,null).getPropertyValue("margin-bottom");this.placeHolderEl=document.createElement("div"),this.placeHolderEl.style.width=e+"px",this.placeHolderEl.style.height=a+"px",this.placeHolderEl.style.marginBottom=i,this.placeHolderEl.style.borderBottom="2px solid var(--kd-color-public-normal)",this.placeHolderEl.id="placeholder",this.parentEl.insertBefore(this.placeHolderEl,this.dragElm),this.pointerTop=t.pageY-s,this.move(this.dragElm,t.pageY-this.pointerTop)}},mouseEnter(t){if(this.isDrag&&this.dragElm){const e=[...this.parentEl.children],a=e.findIndex(e=>t.target===e),s=e.findIndex(t=>this.placeHolderEl===t);s<a?this.parentEl.insertBefore(t.target,this.placeHolderEl):this.parentEl.insertBefore(this.placeHolderEl,t.target)}},mouseMove(t){this.isDrag&&this.dragElm&&this.move(this.dragElm,t.pageY-this.pointerTop)},mouseUp(){if(this.isDrag&&this.dragElm){var t;y["a"].send("click",{$m_n:"more_tab_new",$e_n:"my_tab_btn",$e_t:"button",$e_i:10771,tab_name:null===(t=this.list[this.dragElIndex])||void 0===t?void 0:t.name,operate:"drag"}),this.isDrag=!1,this.dragElOriginStyle?this.dragElm.setAttribute("style",this.dragElOriginStyle):this.dragElm.removeAttribute("style"),this.parentEl.insertBefore(this.dragElm,this.placeHolderEl),document.getElementById("placeholder").remove();let e=[...this.parentEl.children].findIndex(t=>this.dragElm===t);this.$emit("changeListOrder",this.dragElIndex,e),this.dragElIndex=-1}},move(t,e){const{width:a,height:s}=this.dragElBaseProp;t.setAttribute("style",`position: absolute;\n\t\t\twidth: ${a}px;\n\t\t\theight: ${s}px;\n\t\t\ttop: ${e}px;\n\t\t\tpointer-events: none;\n\t\t\tbackground: var(--kd-color-background-top);\n\t\t\topacity: 0.7;\n\t\t\tbox-shadow: 0px 0px 0px 1px rgba(13, 13, 13, 0.1), 0px 1px 0px rgba(13, 13, 13, 0.14);\n\t\t\t${this.dragElOriginStyle}`)},getDragListBaseProp(){const t=this.dragElm,{width:e,height:a}=t.getBoundingClientRect(),{offsetLeft:s,offsetTop:i}=t;this.dragElBaseProp={width:e,height:a,offsetLeft:s,offsetTop:i}},handleRemove(t,e){this.$emit("handleRemove",t,e)}},watch:{list(){this.parentEl=this.$refs["drag-list"]}}},C=v,f=(a("9a5e"),Object(g["a"])(C,p,b,!1,null,"1192a9c0",null)),E=f.exports,w={name:"draggable-list",components:{DraggableArea:E},props:{myCategoriesData:{type:Array,default:function(){return[]}},defaultCategoriesData:{type:Array,default:function(){return[]}}},data(){return{list:[],currDragIndex:-1}},methods:{changeListOrder(t,e){if(t===e)return;const a=this.list[t];t>e?(this.list.splice(e,0,a),this.list.splice(t+1,1)):(this.list.splice(e+1,0,a),this.list.splice(t,1)),this.$emit("listChange",this.list)},handleRemove(t,e){this.list.splice(e,1),y["a"].send("click",{$m_n:"more_tab_new",$e_n:"my_tab_btn",$e_t:"button",$e_i:10771,tab_name:t.name,operate:"delete"}),this.$emit("listChange",this.list)}},watch:{myCategoriesData:{handler(){try{this.list=JSON.parse(JSON.stringify(this.myCategoriesData))}catch(t){console.log(t)}}}}},$=w,D=(a("2f59"),Object(g["a"])($,u,h,!1,null,"6fe74dc2",null)),x=D.exports,I=a("c8fc"),k=a("beeb"),S=a("a6c6"),O={name:"category-model",components:{SelectableList:_,DraggableList:x},data(){return{statusCode:"",allCategoriesData:[],myCategoriesData:[]}},mounted(){this.initAllCategories()},methods:{...Object(n["b"])("common",["setAppComponent"]),...Object(n["b"])("category",["setCategoryData"]),initAllCategories(){this.statusCode="",I["a"].get("personalGateway").getCategoryConfigTree({data:{app:{wps:1,et:2,wpp:3}[k["e"].name],verison_type:1,platform_type:1}}).then(t=>{t&&"ok"==t.result&&t.data&&(this.allCategoriesData=t.data.category_config||[])}).catch(t=>{console.log(t),this.statusCode=k["s"].error}).finally(()=>{this.myCategoriesData=this.categoryData.filter(t=>t.is_sort)})},changeStatus(t){this.myCategoriesIds.includes(t.id)?this.myCategoriesData=this.myCategoriesData.filter(e=>e.id!==t.id):this.myCategoriesData.push(t)},listChange(t){this.myCategoriesData=t},saveChange(){let t=[...this.defaultCategoriesData,...this.myCategoriesData],e=[];for(let a of t)e.push(a.id);e=e.filter(t=>t),I["a"].get("personalGateway").saveCategory({data:{entity_type:{wps:2,et:3,wpp:4}[k["e"].name],entity_ids:e}}).then(e=>{e&&"ok"==e.result?(this.setCategoryData(t),S["a"].$emit(k["p"].saveCategory),this.closeModel()):this.toast({type:"error",content:"保存失败，稍后重试"})}).catch(t=>{console.log(t),this.toast({type:"error",content:"保存失败，稍后重试"})})},closeModel(){this.setAppComponent("")}},computed:{...Object(n["c"])("category",["categoryData"]),myCategoriesIds(){let t=[];return this.myCategoriesData.map(e=>{t.push(e.id)}),t},defaultCategoriesData(){return this.categoryData.filter(t=>!t.is_sort)}}},L=O,A=(a("6a21"),Object(g["a"])(L,s,i,!1,null,"cc069546",null));e["default"]=A.exports},bd20:function(t,e,a){}}]);