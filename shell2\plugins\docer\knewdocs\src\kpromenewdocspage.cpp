﻿#include "stdafx.h"
#include "kpromenewdocspage.h"
#include <kprometheus/kpromepluginwidgetfactory.h>
#include <kprometheus/kpromeapplication.h>
#include <kprometheus/kpromemainwindow.h>
#include <kprometheus/kprometab.h>
#include <kprometheus/kprometabbar.h>
#include <kprometheus/kpromebrowser.h>
#include <kprometheus/kpromecentralarea.h>
#include <kprometheus/kpromeinfocollcethelper.h>
#include <ksolite/kdomainmgr.h>
#include <ksolite/kwebview.h>
#include <ksolite/kdcinfoc.h>
#include <perftool/perftool.h>
#include "kpromenewdocsstartup.h"
#include <kprometheus/kpromecloudsvrproxy.h>
#include "tabbar/kpromenewdocstabbarconfig.h"
#include "tabbar/kpromenewdocstabbar.h"
#include "utility/knewdocshelper.h"
#include "ksolite/kpath.h"
#include "kprometheus/kpromeskin.h"
#include "utilities/path/module/kcurrentmod.h"
#include <QDateTime>
#include "knewdochelper.h"
#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"
#include "kdocertoolkit/helper/kdocercommonhelper.h"

#include "kdocerwebpageperfhelper.h"
#include "ksolite/util/kprivilegeresource.h"
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "ksolite/kplugin.h"
#include <ksolite/kpluginmanager.h>
#include "kpromenewdocswaitwidget.h"
#include <ksolite/ksupportutil.h>
#include "knewdocswebwidget.h"
#include "krt/kconfigmanagercenter.h"

namespace
{
	const char* const g_strClickTabTime = "newdocsClickTime";
	const char* const g_strFromTime = "fromTime";
	const char* const g_strTabType = "tabType";
	const char* const g_strBase64Params = "base64Params";
#if defined(Q_OS_LINUX) && !defined(Q_OS_OHOS) && !defined(Q_OS_ANDROID) && !defined(WEB_OFFICE_ENABLE)
	const char* const g_strPromeNewDocsPageSwitchName = "promeNewDocsPage";
#endif

	KPromeNewDocsPage* findNewDocsPage(KPromeMainWindow* mainWindow, int type)
	{
		if (nullptr == mainWindow || 
			type <= KPromePluginWidget::invalidWidgetType
			|| type >= KPromeNewDocsTabInfoObj::NewdocsExtentEnd)
			return nullptr;

		KPromeTabBar* pPromeTabBar = mainWindow->tabBar();

		if (nullptr == pPromeTabBar)
			return	nullptr;

		int tbabarNumber = mainWindow->tabBar()->count();
		for (int index = 0; index < tbabarNumber; ++index)
		{
			KPromeTab* pPromeTab = mainWindow->tabBar()->tabAt(index);
			if (nullptr == pPromeTab)
				continue;
			KPromeSubPage* pSubPage = pPromeTab->subPage();
			if (nullptr == pSubPage)
				continue;
			KPromePage* pPromePage = pSubPage->page();
			if (nullptr == pPromePage)
				continue;

			KPromeNewDocsPage* pNewdocsPage = qobject_cast<KPromeNewDocsPage*>(pPromePage);
			if (nullptr == pNewdocsPage)
				continue;

			if (type != pNewdocsPage->tabType())
				continue;
			return pNewdocsPage;
		}
		return nullptr;
	}
}
const int KPromeNewDocsPage::s_invalidTabType = -1;
const QString g_strNewDocAppName = "appName";
KPromeNewDocsScrollBar::KPromeNewDocsScrollBar(QWidget* parent)
	: QScrollBar(parent)
{
}

QSize KPromeNewDocsScrollBar::sizeHint() const
{
	return KLiteStyle::dpiScaledSize(8, 200);
}

void KPromeNewDocsScrollBar::paintEvent(QPaintEvent* ev)
{
	//多组件模式下，走的是kwpsstyle2013，需要设置该属性
	QColor bgColor = KDrawHelper::getColorFromTheme("KPromeNewDocsTabBarNew", KDrawHelper::Prop_Background, KDrawHelper::stringToColor("#FFFFFFFF"));
	setProperty("slider-bg-color", bgColor.rgba());
	//整合模式下，走的是KPromestyle
	QScrollBar::paintEvent(ev);
}

KPromeNewDocsPage::KPromeNewDocsPage(QWidget* parent)
	: KPromePage(parent)
	, m_stackedWidget(nullptr)
	, m_tabType(s_invalidTabType)
	, m_expectedTabType(s_invalidTabType)
	, m_loaded(false)
	, m_hasOpenProcessOnFile(false)
	, m_downloadTemplateCount(0)
	, m_entryId("")
	, m_isProcessOnAutoNew(false)
	, m_clickNewTabbarTimeStamp(QDateTime::currentMSecsSinceEpoch())
	, m_tabBar(nullptr)
{
	m_strDefaultTitle = tr("new");
	KBlankTemplateRecentRecordHelper::instance();
	m_entryId = KDocerUtils::generatePayKey();
#if defined(Q_OS_LINUX) && !defined(Q_OS_OHOS) && !defined(Q_OS_ANDROID) && !defined(WEB_OFFICE_ENABLE)
	if (krt::kcmc::support("OnlineTemplateLibrary")
			&& !(promeApp->getDomainMgr()->isIntranet() || krt::kcmc::support("IntranetVersion")))
	{
		if (promeApp && promeApp->getAccountSDK())
		{
			promeApp->getAccountSDK()->connectToLoginInfoChanged(g_strPromeNewDocsPageSwitchName,
				this, SLOT(onLoginStatusChanged()));
		}
	}
#endif
	//perftool内网性能埋点
	PERFLOGBEGIN("PrometheusNewDocsTabLoadV2");
}

KPromeNewDocsPage::~KPromeNewDocsPage()
{

}

KPromePluginWidget::WidgetType KPromeNewDocsPage::type() const
{
	return pageNewDocs;
}

QString KPromeNewDocsPage::getPageTitle() const
{
	return m_strDefaultTitle;
}

bool KPromeNewDocsPage::newDocument(
	const QString& file /*= QString()*/,
	const QVariantList& args /*= QVariantList()*/,
	ProxyStrategy strategy /*= LastOneDirectExeOtherProxy*/,
	KPromeSubPage* parentSubPage/* = nullptr*/,
	const QVariantMap& infoMap /*= QVariantMap()*/)
{
	if (infoMap.contains(g_strFromTime))
	{
		QString panelTime = infoMap.value(g_strFromTime).toString();
		if (m_timeStampMap.contains(g_strFromTime))
			m_timeStampMap[g_strFromTime] = panelTime;
		else
			m_timeStampMap.insert(g_strFromTime, panelTime);
	}

	if (infoMap.contains("expectedUrl"))
	{
		QString url = infoMap.value("expectedUrl").toString();
		setExpectedUrl(url);
	}
	else
	{
		setExpectedUrl("");
	}

	if (infoMap.contains(g_strNewDocAppName))
	{
		QString tabType = infoMap.value(g_strNewDocAppName).toString();
		if (!tabType.isEmpty())
		{
			QString from = infoMap.value(KPromePage::notify_cmd_soucre).toString();
			if (!from.isEmpty())
			{
				m_entrance = from;
				int expectTabType = getExpectTabType(tabType);
				if (expectTabType != KPromePluginWidget::invalidWidgetType)
				{
					setExpectedTabType(expectTabType);
				}
			}
		}
	}
	else if (infoMap.contains(g_strTabType))
	{
		bool bParse = false;
		int expectTabType = infoMap.value(g_strTabType).toInt(&bParse);
		if (bParse && KPromePluginWidget::invalidWidgetType != expectTabType)
			setExpectedTabType(expectTabType);
	}

	KPromePage::newDocument(file, args, strategy, parentSubPage, infoMap);
	// 恢复工作区时恢复标签的图标
	initTopTagButton();
	sendCreateNewPageInfo(infoMap);
	return true;
}

bool KPromeNewDocsPage::openFiles(const QStringList& files,
	const QVariantList& args /*= QVariantList()*/,
	ProxyStrategy strategy /*= LastOneDirectExeOtherProxy*/,
	KPromeSubPage* parentSubPage /*= nullptr*/,
	const QVariantMap& infoMap /*= QVariantMap()*/,
	bool bShowFrontMainWin /*= true*/)
{
	//>命令行 /prometheus plugin://pageNewDocs?from={reportSource}
	QString from;
	int tabType = KPromePluginWidget::invalidWidgetType;
	bool bFindFrom = false;
	bool bFindTabType = false;
	foreach (QString paramEx, files)
	{
		QStringList params = paramEx.split("&");
		foreach(QString param, params)
		{
			QStringList keyValue;
			//处理value为base64字符串的情况，只索引第一个等号
			int nPos = param.indexOf("=");
			if (nPos > -1)
			{
				keyValue.append(param.left(nPos));
				keyValue.append(param.mid(nPos + 1));
			}

			if (keyValue.size() == 2 )
			{
				if (!bFindFrom && keyValue[0].compare("from") == 0)
				{
					from = keyValue[1];
					m_entrance = from;
					bFindFrom = true;
				}
				else if (!bFindTabType && keyValue[0].compare("tabType") == 0)
				{
					bool ok = false;
					tabType = keyValue[1].toInt(&ok);
					if (!ok)
						tabType = KPromeNewDocsTabInfoObj::convertTabIntType(keyValue[1]);

					if (auto pInstance = KPromeNewDocsTabBarConfig::getInstance())
					{
						tabType = (nullptr == pInstance->getInfoObj(tabType) ?
							KPromePluginWidget::pageDocerWps : tabType);
					}
					bFindTabType = true;
				}
				else if (keyValue[0].compare("processonfile") == 0)
				{
					if (keyValue[1].compare("autonew") == 0)
					{
						m_isProcessOnAutoNew = true;
					}
				}
				else if(keyValue[0].compare(g_strFromTime) == 0)
				{
					if (keyValue[1].isEmpty())
						continue;

					if (m_timeStampMap.contains(g_strFromTime))
						m_timeStampMap[g_strFromTime] = keyValue[1];
					else
						m_timeStampMap.insert(g_strFromTime, keyValue[1]);
				}
				else if(keyValue[0].compare(g_strBase64Params) == 0)
					m_cbParams = QByteArray::fromBase64(keyValue[1].toUtf8());
			}
		}
		if (bFindFrom && bFindTabType)
			break;
	}

	QVariantMap infoMapNew = infoMap;
	if (!from.isEmpty())
		infoMapNew["from"] = QVariant(from);
	if (tabType != KPromePluginWidget::invalidWidgetType)
		setExpectedTabType(tabType);

	KPromePage::newDocument(QString(), args, strategy, parentSubPage, infoMapNew);

	sendCreateNewPageInfo(infoMapNew);
	//增值右键新建项信息上报
	if (from.compare("newfilecontextmenu") == 0)
	{
		sendVasShellNewCreatePageInfo(tabType);
	}
	return true;
}

void KPromeNewDocsPage::load()
{
	if (m_loaded)
		return;
	m_loaded = true;

	initUIWidget();

}

void KPromeNewDocsPage::notify(const QString& cmd)
{
	QStringList params = cmd.split(";");
	QMap<QString, QString> mapParam;
	foreach(QString param, params)
	{
		QStringList triple = param.split("=");
		if (triple.size() == 2)
		{
			QString key = triple[0];
			QString value = triple[1];
			mapParam.insert(key, value);
		}
	}
	auto it = mapParam.constEnd();
	if ((it = mapParam.constFind(KPromePage::notify_cmd_expectedtabtype)) != mapParam.constEnd())
	{
		if (krt::kcmc::support("OfficialComponent") && it.value() == QLatin1String("pageDocerOfficial"))
			m_expectedTabType = pageDocerOfficial;
		else
			m_expectedTabType = getExpectTabType(it.value());
	}

	it = mapParam.constEnd();
	if ((it = mapParam.constFind(KPromePage::notify_cmd_soucre)) != mapParam.constEnd())
	{
		m_entrance = it.value();
		if (m_entrance == "templateLib_fixed_tab")
		{
			m_strDefaultTitle = tr("TemplateLib");
		}
	}

	if ((it = mapParam.constFind(KPromePage::notify_cmd_action)) != mapParam.constEnd()
	    && it.value().toStdString() == "reload")
	{
		IKDocerAccount* account = nullptr;
		auto kdocercoreLite = getIKDocerCoreLite();
		if (kdocercoreLite)
			account = kdocercoreLite->getIKDocerAccount();

		if (account && mapParam.contains("source"))
		{
			QString source = mapParam["source"];
			if (source == "privilege"
				&& account->isLogined())
				// 已经登录，且刷新了权限
			{
				onRefreshWebWidget(m_expectedTabType);
			}
			if (source == "loginChanged"
				&& !account->isLogined())
				// 退出登录
			{
				onRefreshWebWidget(m_expectedTabType);
			}
		}
	}
	
	if (mapParam.contains("timeStamp"))
	{
		m_clickNewTabbarTimeStamp = mapParam["timeStamp"].toLongLong();
		KxLoggerLite::writeInfo(L"KPromeNewDocsPage",
			QString("knewdocs tabbar click timeStamp %1").arg(m_clickNewTabbarTimeStamp).toStdWString());
	}

	if (krt::kcmc::support("ChangeTemplateTab"))
		notifyToChangeTemplateTab(mapParam);
}

int KPromeNewDocsPage::getExpectTabType(const QString& appName)
{
	int nType = KPromePluginWidget::invalidWidgetType;
	if (!appName.isEmpty())
	{
		if (appName == "wps")
			nType = KPromePluginWidget::pageDocerWps;
		else if (appName == "et")
			nType = KPromePluginWidget::pageDocerEt;
		else if (appName == "wpp")
			nType = KPromePluginWidget::pageDocerWpp;
		else if (appName == "chuangkit")
			nType = KPromePluginWidget::widgetChuangKit;
		else if (appName == "flow")
			nType = KPromePluginWidget::widgetProcesson_Flow;
		else if (appName == "mind")
			nType = KPromePluginWidget::widgetProcesson_Mind;
		else if (appName == "pdf")
			nType = KPromePluginWidget::pageDocerPdf;
		else if (appName == "form")
			nType = KPromePluginWidget::widgetWpsForm;
		else if (appName == "template")
			nType = KPromePluginWidget::pageMytpl;
		else if (appName == "ksheet")
			nType = KPromeNewDocsTabInfoObj::NewDocTabType::pageKSheet;
		else if (appName == "otl" || appName == "motl")
			nType = KPromeNewDocsTabInfoObj::NewDocTabType::pageKOtl;
		else if (appName == "uof")
			nType = KPromePluginWidget::widgetNewApp;
		else if (appName == "dbt")
			nType = KPromeNewDocsTabInfoObj::NewDocTabType::pageKDbSheet;
		else if (appName == "templateLib")
			nType = KPromeNewDocsTabInfoObj::NewDocTabType::pageTemplateLib;
	}
	return nType;
}

void KPromeNewDocsPage::notifyToChangeTemplateTab(const QMap<QString, QString>& mapParam)
{
	auto it = mapParam.constFind(g_strNewDocAppName);
	if (it != mapParam.constEnd())
	{
		int nType = KPromePluginWidget::invalidWidgetType;
		QString appName = it.value();
		if (appName == "wps")
			nType = KPromePluginWidget::pageDocerWps;
		else if (appName == "et")
			nType = KPromePluginWidget::pageDocerEt;
		else if (appName == "wpp")
			nType = KPromePluginWidget::pageDocerWpp;
		KPromeNewDocsTabBarConfig::getInstance()->updateLastSelectedTab(nType);
	}
}

void KPromeNewDocsPage::doClose()
{
	KPromePage::doClose();
}

void KPromeNewDocsPage::setExpectedTabType(int type)
{
	m_expectedTabType = type;
}

void KPromeNewDocsPage::setExpectedUrl(const QString& url)
{
	m_expectedUrl = url;
}

int KPromeNewDocsPage::expectedTabType()
{
	return m_expectedTabType;
}

void KPromeNewDocsPage::onAddPageComplete()
{
	for(int i = 0; i < subPages()->count(); i++)
	{
		KPromeSubPage *pg = subPages()->subPageAt(i);
		if (pg)
		{
			pg->setCloseMode(KPromeSubPage::CloseMode_ChangePage);
			pg->close();
		}
	}
	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	bool bnotclose = mw && mw->headerbarType() == KPromeHeaderBarBase::Standalone && promeApp->isPromeShelllessMode();
	if (!bnotclose)
	{
		this->doClose();
	}
	if(mw)
	{
		KPromeCentralArea* centralArea = mw->centralArea();
		if(centralArea)
		{
			centralArea->activeCurrentPage();
		}
	}
}

void KPromeNewDocsPage::onShowWidget(const QWidget* widget)
{
	if (nullptr == widget || nullptr == m_stackedWidget)
		return;

	auto it = m_widgetMap.constFind(m_tabType);
	if (it != m_widgetMap.constEnd() &&
		it.value() != nullptr &&
		it.value() == widget)
	{
		emit sigWebLoadfinished();
		m_stackedWidget->setCurrentWidget(it.value());
		//perftool内网性能埋点
		PERFLOGEND("PrometheusNewDocsTabLoadV2");
	}
}

void KPromeNewDocsPage::onUpdateTitleAndIcon(const QString& title, const QIcon& ic)
{
	initialSubPage()->setWindowTitle(title);
	KPromeTab* tab = initialSubPage()->getTab();
	if (tab)
	{
		tab->setIcon(ic);
		tab->update();
	}
}

void KPromeNewDocsPage::onLoadTimeout()
{
	if (!KPromeNewDocsPage::isComponentType(m_tabType))
		return;
	auto widget = m_widgetMap.value(m_tabType);
	if (!widget)
		return;
	auto startupWidget = qobject_cast<KPromeNewDocsStartup*>(widget);
	if (!startupWidget)
		return;

	startupWidget->onOpenDocument("", "", "newBlankDocument", KPromeNewDocsTabInfoObj::convertTabIntType(m_tabType));

	postKNewdocsLoadEvent(KNewDocsWebWidget::KNewdocsloadErrorCode::LoadTimeout);
	KxLoggerLite::writeInfo(KPluginNameW, QString("【description】:Load timeout,【solution】：Create a new document directly!").toStdWString());
}

void KPromeNewDocsPage::onTabClicked(int tabType)
{
	int lastTabType = m_tabType;

	if (lastTabType != tabType && lastTabType != KPromePluginWidget::invalidWidgetType)
	{
		m_clickNewTabbarTimeStamp = QDateTime::currentMSecsSinceEpoch();
		if (m_timeStampMap.contains(g_strClickTabTime))
			m_timeStampMap[g_strClickTabTime] = QString::number(m_clickNewTabbarTimeStamp);
		else
			m_timeStampMap.insert(g_strClickTabTime, QString::number(m_clickNewTabbarTimeStamp));

		KxLoggerLite::writeInfo(L"KPromeNewDocsPage",
			QString("onTabClicked tabType %1, timeStamp is %2").arg(tabType).arg(m_clickNewTabbarTimeStamp).toStdWString());
	}

	m_tabType = tabType;
	QWidget* widget = nullptr;
	bool isV7 = ksolite::isSupported(ksolite::IntranetVersion7);
	if (kcmc::support("NewDocsUsingOldWidget") && !isV7)
	{
		if (widget = m_widgetMap.value(tabType, nullptr))
		{
			m_stackedWidget->setCurrentWidget(widget);
			return;
		}
	}
	bool v7Form = isV7 && isWpsFormType();
	if (isUseStartupWidget(m_tabType) || v7Form)
	{
		widget = new KPromeNewDocsStartup(this);
	}
	else
	{
		widget = KPromePluginWidget::createInstance(
			(KPromePluginWidget::WidgetType)m_tabType, this);
	}

	if (widget == nullptr)
		return;

	bool b = true;
	if (KPromeNewDocsStartup* webWidget = qobject_cast<KPromeNewDocsStartup*>(widget))
	{
		b = connect(widget, SIGNAL(sigInitBrowser()), this, SIGNAL(sigTabInitBrowser()));
		Q_ASSERT(b);

		m_contentStartupPage = webWidget;
		m_contentStartupPage->setFrom(m_entrance);
		static bool bEnable([]() -> bool
			{
#if defined(Q_OS_WIN) || defined(Q_OS_LINUX)
				if (krt::kcmc::support("PDFWebWidgetByBrowser"))
				{	
					return true;	
				}
#endif
				if (ksolite::isSupported(ksolite::IntranetVersion7))
				{
					return false;
				}
				if (kcmc::support("USingOldTemplateNewPage"))
				{
					if (promeApp && promeApp->getDomainMgr())
					{
						if (promeApp->getDomainMgr()->isIntranet() || krt::kcmc::support("IntranetVersion"))
							return true;
					}
				}
				return false;
			}());

		if ((isUseStartupWidget(m_tabType) || v7Form) && !bEnable )
		{
			if (isV7 && m_entrance != "v7jscb-template")
				webWidget->setOnlineWebPage(true);
			webWidget->setExpectedUrl(m_expectedUrl);
			webWidget->initKNewDocsBrowser(m_tabType);
			webWidget->invokeWebPageCallback(m_cbParams);
		}
		else
		{
			webWidget->initBrowser(m_tabType);
			emit sigTabInitBrowser();
		}
		b = connect(widget, SIGNAL(closeTab()), this, SLOT(onOpenDocument()));
		Q_ASSERT(b);
	}

#if !defined(Q_OS_OHOS) && !defined(X_LINUX_DESKTOP)
	if (isProcessOnType())
	{
		//IntranetVersion7 企业私网版本还在使用旧版新建页UI
		b = connect(widget, SIGNAL(processOnFileAddCompleted(bool)), this, SLOT(onOpenProcessOnFile(bool)), Qt::QueuedConnection);
		b = connect(widget, SIGNAL(jumpToNewDocSpecificTab(const QString&, const QString&)),
			this, SLOT(onJumpToSpecificTab(const QString&, const QString&)));
		b = connect(widget, SIGNAL(openProcessOnFile(const QString&)), this, SLOT(onIsOpenProcessonFile(const QString&)), Qt::QueuedConnection);
	}
#endif

	b = connect(widget, SIGNAL(cefKeyEvent(Qt::KeyboardModifiers, int, bool, bool&)),
		this, SLOT(onCefKeyEvent(Qt::KeyboardModifiers, int, bool, bool &)), Qt::DirectConnection);

	if (isChuangKitType())
	{
		KPromeNewDocsTabBarConfig* pConfig = KPromeNewDocsTabBarConfig::getInstance();
		if (nullptr != pConfig)
		{
			if (KPromeNewDocsTabInfoObj* pCfgObj = pConfig->getInfoObj(KPromePluginWidget::widgetChuangKit))
				widget->setProperty("tabbarTitle", pCfgObj->text());
		}
	}

	m_widgetMap.insert(m_tabType, widget);
	m_typeTitleMap[m_tabType] = m_strDefaultTitle;
	if (m_stackedWidget->indexOf(widget) == -1)
		m_stackedWidget->addWidget(widget);

	b = connect(widget, SIGNAL(sigShowWidget(const QWidget*)),
		this, SLOT(onShowWidget(const QWidget*)));

	if (isWpsFormType())
	{
		b = connect(widget, SIGNAL(sigTitleChanged(const QString&)),
			this, SLOT(onTitleChanged(const QString&)), Qt::DirectConnection);
	}

}

void KPromeNewDocsPage::onRefreshWebWidget(int tabType)
{
	if (krt::kcmc::support("TemplateLibFixedTab"))
	{
		m_contentStartupPage->setFrom(m_entrance);
		m_contentStartupPage->refreshUrl(tabType);
		m_contentStartupPage->invokeWebPageCallback(m_cbParams);
		return;
	}
	if (!m_widgetMap.contains(m_tabType))
		return;
	QWidget* widget = m_widgetMap[m_tabType];
	if (!widget)
		return;
	auto webWidget = qobject_cast<KPromeNewDocsStartup*>(widget);
	if (!webWidget)
		return;

	webWidget->refreshUrl(m_tabType, true);
}

void KPromeNewDocsPage::onOpenProcessOnFile(bool)
{
	if (isProcessOnType() && m_hasOpenProcessOnFile)
	{
		onAddPageComplete();
	}
}

void KPromeNewDocsPage::onIsOpenProcessonFile(const QString & fileId)
{
	m_hasOpenProcessOnFile = true;
}

void KPromeNewDocsPage::onTitleChanged(const QString& title)
{
	if (!initialSubPage())
		return;

	if(isProcessOnType() && m_hasOpenProcessOnFile)
	{
		initialSubPage()->setWindowTitle(title);
		KPromeTab* tab = initialSubPage()->getTab();
		if(tab)
		{
			tab->setIcon(KDrawHelper::loadIcon(widgetProcesson_Flow == m_tabType? "processon_icon" : "mindmap_icon"));
			tab->update();
		}
	}
	else if (isWpsFormType())
	{
		upDateTitleAndIcon(title, "wpsform_icon");
	}
}

void KPromeNewDocsPage::upDateTitleAndIcon(const QString& title, const QString& iconName)
{
	initialSubPage()->setWindowTitle(title);
	KPromeTab* tab = initialSubPage()->getTab();
	if (tab)
	{
		tab->setIcon(KDrawHelper::loadIcon(iconName));
		tab->update();
	}
}

void KPromeNewDocsPage::onPluginLoad(bool falgs)
{
	if (true == falgs)
	{
		emit sigPluginLoadSuccess();
		onTabClicked(m_expectedTabType);
	}
	else
	{
		emit sigPluginLoadFailed();
	}
}

void KPromeNewDocsPage::reloadPlugin(KPromeNewDocsTabInfoObj* pConfigObj, KPromeNewDocsTabBarConfig* pConfig)
{
	if (nullptr == pConfigObj || nullptr == pConfig)
		return;
	connect(pConfigObj, &KPromeNewDocsTabInfoObj::relyPluginExist, this, &KPromeNewDocsPage::onPluginLoad, Qt::UniqueConnection);
	pConfig->configuredRelyPlugin(pConfigObj);
}

void KPromeNewDocsPage::initContentWidget(int tabType)
{
	KPromeNewDocsTabBarConfig* pConfig = KPromeNewDocsTabBarConfig::getInstance();
	if (nullptr == pConfig)
		return;
 	
	KPromeNewDocsTabInfoObj* pConfigObj;
	if (entrance() == "templateLib_fixed_tab")
	{
		pConfigObj = pConfig->getInfoObj(KPromeNewDocsTabInfoObj::NewDocTabType::pageTemplateLib);
	}
	else
	{
		pConfigObj = pConfig->getInfoObj(m_expectedTabType);
	}
	if (nullptr == pConfigObj)
		return;

	KPromeSubPage* pSubPage = initialSubPage();
	if (nullptr == pSubPage)
		return;
	pSubPage->setWindowTitle(pConfigObj->text());

	KPromeTab* tab = initialSubPage()->getTab();
	if (nullptr != tab)
	{
		tab->setIcon(pConfigObj->icon());
		tab->update();
	}

	bool bPluginExists = KDocerUtils::isPluginCanUse(pConfigObj->relyPlugin());
	pConfigObj->setRelyPluginExit(bPluginExists);

	KPromeNewdocsWaitWidget* waitLoadWidget = new KPromeNewdocsWaitWidget(KNewDocsHelper::isSupportDefaultPage(tabType), this);
	connect(this, &KPromeNewDocsPage::sigWebLoadfinished, waitLoadWidget, &KPromeNewdocsWaitWidget::onWebLoadfinished, Qt::UniqueConnection);
	connect(this, &KPromeNewDocsPage::sigPluginLoadFailed, waitLoadWidget, &KPromeNewdocsWaitWidget::onPluginLoadFailed, Qt::UniqueConnection);
	connect(waitLoadWidget, &KPromeNewdocsWaitWidget::sigLoadRetry, this, [=]() {
		loadPlugin(pConfigObj, pConfig);
		}, Qt::UniqueConnection);

	if (KNewDocsHelper::isSupportDefaultPage(tabType))
	{
		connect(waitLoadWidget, &KPromeNewdocsWaitWidget::sigLoadTimeout, this, &KPromeNewDocsPage::onLoadTimeout);
		connect(this, &KPromeNewDocsPage::mainResourceVerificationFailed, this, [=]() {
			if (!waitLoadWidget)
				return;
			waitLoadWidget->onWebLoadfinished();
			auto widget = m_widgetMap.value(m_tabType);
			if (!widget)
				return;
			auto startupWidget = qobject_cast<KPromeNewDocsStartup*>(widget);
			if (!startupWidget)
				return;
			startupWidget->loadErrorPage(KNewDocsWebWidget::KNewdocsloadErrorCode::MainResourceVerificationFailed);
			KxLoggerLite::writeInfo(KPluginNameW, QString("【description】:mainResourceVerificationFail,【solution】：Load default errorPage!")
				.toStdWString());
			});
	}

	m_stackedWidget->addWidget(waitLoadWidget);
	m_stackedWidget->setCurrentWidget(waitLoadWidget);

	if (ksolite::isSupported(ksolite::IntranetVersion7)
		&& tabType == KPromePluginWidget::widgetWpsForm)
	{
		bPluginExists = true;
	}

	if (!bPluginExists && !pConfigObj->relyPlugin().isEmpty())
	{
		loadPlugin(pConfigObj, pConfig);
	}
	else
	{
		onTabClicked(tabType);
	}
}

void KPromeNewDocsPage::loadPlugin(KPromeNewDocsTabInfoObj* pConfigObj, KPromeNewDocsTabBarConfig* pConfig)
{
	KPluginManager* pPlgMgr = KPluginManager::getInstance();
	if (nullptr != pPlgMgr)
	{
		if (pPlgMgr->isLoadFinished())
		{
			reloadPlugin(pConfigObj, pConfig);
		}
		else
		{
			connect(pPlgMgr, &KPluginManager::loadFinished,
				this, [=]()
				{
					reloadPlugin(pConfigObj, pConfig);
				},
				Qt::UniqueConnection);
		}
	}
}


void KPromeNewDocsPage::initTopTagButton()
{
	KPromeNewDocsTabBarConfig* pConfig = KPromeNewDocsTabBarConfig::getInstance();
	if (nullptr == pConfig)
		return;

	QPointer<KPromeSubPage> pSubPage = initialSubPage();
	if (nullptr == pSubPage)
		return;

	auto updateIcon = [=]()
	{
		KPromeTab *tab = pSubPage->getTab();
		KPromeNewDocsTabInfoObj* pConfigObj;
		if (tab != nullptr
		    && pSubPage->property("from").isValid()
		    && pSubPage->property("from").toString() == "templateLib_fixed_tab")
		{
			pConfigObj = pConfig->getInfoObj(KPromeNewDocsTabInfoObj::NewDocTabType::pageTemplateLib);
			if (nullptr == pConfigObj)
				return;

			tab->setIcon(pConfigObj->icon());
			pSubPage->setWindowTitle(pConfigObj->text());
			tab->update();
			return;
		}
		//#746703 此处由于在拖拽新建页面所对应的tab生成另外的一个主框页面的时候，
		// 所对应的kpromeTab会先析构，再重新创建的kprometab，导致新创建的kpromeTab未设置Icon
		if (nullptr != tab && false == tab->property("isNeedUpdateIcon").toBool())
		{
			pConfigObj = pConfig->getInfoObj(m_expectedTabType);
			if (nullptr == pConfigObj)
				return;
			tab->setProperty("isNeedUpdateIcon", true);
			tab->setIcon(pConfigObj->icon());
			tab->update();
		}
	};

	updateIcon();

	connect(pSubPage, &KPromeSubPage::subPageActived, this, updateIcon);
}


int KPromeNewDocsPage::getDefaultWidgetType()
{
	
	if (krt::kcmc::support("OnlyFocusOnPDF"))
	{
		return KPromePluginWidget::pageDocerPdf;
	}
	else if (kcmc::support("USingComponentDefaultWidgetType"))
	{
		QString appName = krt::appName();
		if (appName.compare("et", Qt::CaseInsensitive) == 0)
			m_expectedTabType = KPromePluginWidget::pageDocerEt;
		else if (appName.compare("wpp", Qt::CaseInsensitive) == 0)
			m_expectedTabType = KPromePluginWidget::pageDocerWpp;
		else
			m_expectedTabType = KPromePluginWidget::pageDocerWps;
		return m_expectedTabType;
	}
	return KPromePluginWidget::pageDocerWps;
}

void KPromeNewDocsPage::postKNewdocsLoadEvent(int errorCode)
{
	if (!KNewDocsHelper::isSupportDefaultPage(m_tabType))
		return;

	qint64 duration = QDateTime::currentMSecsSinceEpoch() - m_clickNewTabbarTimeStamp;
	QHash<QString, QString> hashMap;
	hashMap.insert("fail_reason", KNewDocsWebWidget::convertLoadErrorCode2String((KNewDocsWebWidget::KNewdocsloadErrorCode)errorCode));
	hashMap.insert("fail_code", QString::number((KNewDocsWebWidget::KNewdocsloadErrorCode)errorCode));
	hashMap.insert("duration", QString::number(duration));
	hashMap.insert("klm", "docer_knewdocx.newfile_fail_page.0.0[page]");
	KDocerUtils::postGeneralEvent("docer_knewdocx_load", hashMap);
}

void KPromeNewDocsPage::initUIWidget()
{
	if (m_expectedTabType == KPromePluginWidget::invalidWidgetType)
	{
		m_expectedTabType = getDefaultWidgetType();
	}

	m_mainLayout = new QVBoxLayout(this);
	m_mainLayout->setMargin(0);
	m_mainLayout->setSpacing(KLiteStyle::dpiScaled(0));
	m_mainLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(0, 0, 0, 0));
	m_stackedWidget = new QStackedWidget(this);
	if (!ksolite::isSupported(ksolite::IntranetVersion7) &&
		(promeApp->getDomainMgr()->isIntranet()))
	{
		m_tabBar = new KPromeNewDocsTabBar(this);
		m_tabBar->initTab();
		m_tabBar->setFixedHeight(KLiteStyle::dpiScaled(60));
		connect(m_tabBar, SIGNAL(tabClicked(int)), this, SLOT(onTabClicked(int)));
		connect(m_tabBar, SIGNAL(refreshWebWidget(int)), this, SLOT(onRefreshWebWidget(int)));
		connect(m_tabBar, &KPromeNewDocsTabBar::tabTitleChanged, this, &KPromeNewDocsPage::onUpdateTitleAndIcon);
		m_mainLayout->addWidget(m_tabBar);
		m_expectedTabType = m_tabBar->getCurrentTabType();
	}
	m_mainLayout->addWidget(m_stackedWidget);

	initContentWidget(m_expectedTabType);
	bool isHandleProcessOnType = false;
	//处理ProcessOn右键新建项调起
	if (m_isProcessOnAutoNew && KPromeNewDocsTabBarConfig::getInstance()->getLastSelectedTab() == m_expectedTabType)
	{
		//处理增值右键新建项调起时导致的初始化两次processon的widget时没有将参数带入想要的时机
		this->setProperty("processonfile", QString("autonew"));
		isHandleProcessOnType = true;
	}

	if (m_isProcessOnAutoNew && !isHandleProcessOnType)
	{
		//如果需要两次重入时，在第二次进入前进行属性的添加
		this->setProperty("processonfile", QString("autonew"));
	}
	//清除状态信息
	m_isProcessOnAutoNew = false;

}

void KPromeNewDocsPage::onCefKeyEvent(Qt::KeyboardModifiers modifyKey, int key, bool keyPress, bool &handle)
{
	if (!promeApp)
		return;

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
#ifdef Q_OS_DARWIN
	if (modifyKey == Qt::MetaModifier)
#else
	if (modifyKey == Qt::ControlModifier)
#endif
	{
		if (mw)
		{
#ifdef Q_OS_WIN
			SendMessage((HWND)mw->winId(), keyPress ? WM_KEYDOWN : WM_KEYUP, key, 0);
#else
			if (key == 'N' && keyPress)
			{
				QKeyEvent *keyPress = new QKeyEvent(QEvent::KeyPress, Qt::Key_N, Qt::ControlModifier);
				bool isSucessed = QCoreApplication::sendEvent(mw, keyPress);
			}
#endif
			handle = true;
		}
	}
	else if (KNewDocsHelper::isSupportCtrlAltAKey() && mw)
	{
#ifdef Q_OS_MACOS
		if (modifyKey == (Qt::MetaModifier | Qt::AltModifier))
#else
		if (modifyKey == (Qt::ControlModifier | Qt::AltModifier))
#endif
		{
#ifdef Q_OS_WIN
			SendMessage((HWND)mw->winId(), keyPress ? WM_KEYDOWN : WM_KEYUP, key, 0);
#else
			if (key == Qt::Key_A && keyPress)
			{
				QKeyEvent* keyPress = new QKeyEvent(QEvent::KeyPress, key, modifyKey);
				bool isSucessed = QCoreApplication::sendEvent(mw, keyPress);
			}
#endif
			handle = true;
		}
	}
}

void KPromeNewDocsPage::sendCreateNewPageInfo(const QVariantMap& infoMap)
{
	QString source = "unknown";
	if(!infoMap["from"].toString().isEmpty())
		source = infoMap["from"].toString();

	QHash<QString,QString> args;
	args.insert("from",source);

	KDocerUtils::postGeneralEvent("kprometheus_newpage_created",args);

	//WPS首页、主框新建文件设置来源
	KPromeOpenFileSourceInfoCollectMgr* pFileSourceInfoCollectMgr = promeApp->getOpenFileSourceInfoCollectMgr();
	if (pFileSourceInfoCollectMgr)
	{
		pFileSourceInfoCollectMgr->setSource(source);
	}
}


void KPromeNewDocsPage::sendVasShellNewCreatePageInfo(int tabType)
{
	QString typeName("VasShellNew_NULL");

	if (tabType == 26)
	{
		typeName = "VasShellNew_PDF";
	}
	else if (tabType == 28)
	{
		typeName = "VasShellNew_FLOW";
	}
	else if (tabType == 29)
	{
		typeName = "VasShellNew_MIND";
	}
	
	QHash<QString, QString> args;
	args.insert("id", typeName);
	QDateTime currentTime = QDateTime::currentDateTime();
	QString time = currentTime.toStringEx("yyyy-MM-dd hh:mm:ss");
	args.insert("time", time);

	KDocerUtils::postGeneralEvent("vas_systemmenu_click", args);
}

bool KPromeNewDocsPage::isKDocsType() const
{
	return KPromeNewDocsTabInfoObj::pageKOtl == m_tabType;
}

bool KPromeNewDocsPage::isProcessOnType() const
{
	if(widgetProcesson_Flow == m_tabType || widgetProcesson_Mind == m_tabType)
		return true;
	
	return false;
}

bool KPromeNewDocsPage::isKSheetType() const
{
	return KPromeNewDocsTabInfoObj::pageKSheet == m_tabType;
}


bool KPromeNewDocsPage::isKDbSheetType() const
{
	return KPromeNewDocsTabInfoObj::pageKDbSheet == m_tabType;
}

bool KPromeNewDocsPage::isWpsFormType() const
{
	return widgetWpsForm == m_tabType;
}

bool KPromeNewDocsPage::isComponentType(int tabType)
{
	return KPromePluginWidget::pageMytpl == tabType
		|| KPromePluginWidget::pageDocerWps == tabType
		|| KPromePluginWidget::pageDocerWpp == tabType
		|| KPromePluginWidget::pageDocerEt == tabType
		|| KPromePluginWidget::pageDocerPdf == tabType
		|| KPromeNewDocsTabInfoObj::NewDocTabType::pageTemplateLib == tabType;

}

bool KPromeNewDocsPage::isChuangKitType() const
{
	return widgetChuangKit == m_tabType;
}

bool KPromeNewDocsPage::isQuickCreateType() const
{
	return KPromeNewDocsTabInfoObj::pageQuickCreate == m_tabType;
}


bool KPromeNewDocsPage::isUseStartupWidget(const int type) const
{
	return isComponentType(type)
		|| isKDbSheetType()
		|| isKDocsType()
		|| isQuickCreateType()
		|| isKSheetType()
		|| isProcessOnType()
		|| isChuangKitType();
}

void KPromeNewDocsPage::decDownloadTemplateCount()
{
	m_downloadTemplateCount--;
}

void KPromeNewDocsPage::incDownloadTemplateCount()
{
	m_downloadTemplateCount++;
}


Q_INVOKABLE void KPromeNewDocsPage::setAppContext(const KAppContext& context)
{
	const QHash<QString, int> g_mapAppId2WidgetType = {
		{ "kpromeprocesson_mind", KPromePluginWidget::widgetProcesson_Mind } ,
		{ "kpromeprocesson_flow", KPromePluginWidget::widgetProcesson_Flow } ,
		{ "kpromechuangkit", KPromePluginWidget::widgetChuangKit }
	};
	if (g_mapAppId2WidgetType.contains(context.appId))
		setExpectedTabType(g_mapAppId2WidgetType[context.appId]);
}

void KPromeNewDocsPage::setCurrentTab(const QString& tabId)
{
	auto tabInfoList = KPromeNewDocsTabBarConfig::getInstance()->getTabInfoList();
	foreach(auto pTabInfo, tabInfoList)
	{
		if (pTabInfo && pTabInfo->id() == tabId)
		{
			setTabType(pTabInfo->getTabType());
			return;
		}
	}
}

int KPromeNewDocsPage::getDownloadTemplateCount()
{
	return m_downloadTemplateCount;
}

bool KPromeNewDocsPage::noClose()
{
	return m_noClose;
}

void KPromeNewDocsPage::setNoClose(bool jsControl)
{
	m_noClose = jsControl;
}

qint64 KPromeNewDocsPage::getKNewDocsTabbarButtonClickedTimeStamp() const
{
	return m_clickNewTabbarTimeStamp;
}

void KPromeNewDocsPage::setTabType(int tabType)
{
	if (tabType == KPromePluginWidget::invalidWidgetType
		|| tabType == m_tabType)
			return;

	if (nullptr == promeApp)
		return;

	KPromeMainWindow* pMainWindows = promeApp->findRelativePromeMainWindow(this);
	if (nullptr == pMainWindows)
		return;

	if (KPromeNewDocsPage* pNewdocsPage = findNewDocsPage(pMainWindows, tabType))
	{
		KPromeSubPage* subPage = pNewdocsPage->initialSubPage();
		if (nullptr == subPage)
			return;

		KPromeTab* pPromeTab = subPage->getTab();
		if (nullptr != pPromeTab)
			pPromeTab->activate();
		return;
	}

	KPromeCentralArea* contentArea = pMainWindows->centralArea();
	if (!contentArea)
		return;

	KPromePage* page = contentArea->addPage(KPromePluginWidget::pageNewDocs);
	if (nullptr == page)
		return;

	QString tabTypeStr = KPromeNewDocsTabInfoObj::convertTabIntType(tabType);
	if (tabTypeStr.isEmpty())
		return;

	QString cmdStr = QString("from=%1&tabType=%2").arg("KPromeNewDocsPage").arg(tabTypeStr);

	page->openFiles(QStringList() << cmdStr,
		QVariantList(), 
		KPromePage::LastOneDirectExeOtherProxy, 
		nullptr,
		QVariantMap(), 
		true);

}

void KPromeNewDocsPage::testNewDocument(int tabType)
{
	auto it = m_widgetMap.find(tabType);
	if (it != m_widgetMap.constEnd())
	{
		KPromeNewDocsStartup* startup = dynamic_cast<KPromeNewDocsStartup*>(it.value());
		if (startup)
			startup->onOpenDocument(QString(),QString(), "testNewDocument", startup->getBrowserType());
	}
}

void KPromeNewDocsPage::paintEvent(QPaintEvent* e)
{
	KNewDocDrawHelper::drawCommonBackground(this);
}


void KPromeNewDocsPage::onOpenDocument()
{
	if (m_tabType == -1)
		return;

	KPromeMainWindow* mw = promeApp->findRelativePromeMainWindow(this);
	if (!mw)
		return;

	KPromeCentralArea* contentArea = mw->centralArea();
	if (!contentArea)
		return;

	onAddPageComplete();
}

QString KPromeNewDocsPage::processonFileId() const
{
	return m_processonFileId;
}

QVariantMap KPromeNewDocsPage::getPageTabFileType() const
{
	QVariantMap mapFileType;
	QString strFileType;
	switch (m_tabType)
	{
	case KPromePluginWidget::pageDocerWps:
		strFileType = "wps";
		break;
	case KPromePluginWidget::pageDocerWpp:
		strFileType = "wpp";
		break;
	case KPromePluginWidget::pageDocerEt:
		strFileType = "et";
		break;
	case KPromePluginWidget::pageDocerPdf:
		strFileType = "pdf";
		break;
	case KPromePluginWidget::pageMytpl:
		strFileType = "tpl";
		break;
	case KPromeNewDocsTabInfoObj::pageKDbSheet:
		strFileType = "dbt";
		break;
	case KPromePluginWidget::widgetWpsForm:
		strFileType = "form";
		break;
	case KPromePluginWidget::widgetChuangKit:
		strFileType = "chuangkit";
		break;
	case KPromeNewDocsTabInfoObj::pageKOtl:
		strFileType = "otl";
		break;
	case KPromeNewDocsTabInfoObj::pageQuickCreate:
		strFileType = "create";
		break;
	case KPromeNewDocsTabInfoObj::pageKSheet:
		strFileType = "ksheet";
		break;
	case KPromePluginWidget::widgetProcesson_Flow:
		strFileType = "flow";
		break;
	case KPromePluginWidget::widgetProcesson_Mind:
		strFileType = "mind";
		break;
	default:
		break;
	}

	mapFileType["tabType"] = m_tabType;
	mapFileType["fileType"] = strFileType;
	return mapFileType;
}

QVariantMap KPromeNewDocsPage::getPageTabIconInfo() const
{
	KPromeNewDocsTabBarConfig* pConfig = KPromeNewDocsTabBarConfig::getInstance();
	if (!pConfig)
		return QVariantMap();

	KPromeNewDocsTabInfoObj* pCfgObj = pConfig->getInfoObj(m_expectedTabType);
	if (!pCfgObj)
		return QVariantMap();

	return pCfgObj->getPageTabIconInfo();
}

KPromeNewDocsTabInfoObj* KPromeNewDocsPage::getDocsTabByType(int tabType)
{
	KPromeNewDocsTabBarConfig* pConfig = KPromeNewDocsTabBarConfig::getInstance();
	if (nullptr == pConfig)
		return nullptr;
	
	KPromeNewDocsTabInfoObj* pCfgObj = pConfig->getInfoObj(tabType);
	return pCfgObj;
}

KPromeNewDocsStartup* KPromeNewDocsPage::getStartupPage() const
{
	return m_contentStartupPage;
}

int KPromeNewDocsPage::getCurrentTabType()
{
	return m_tabType;
}

void KPromeNewDocsPage::openSpecificTab(int tabType, const QString& params)
{
	if (tabType != KPromePluginWidget::invalidWidgetType)
		return;
	setTabType(tabType);
	invokeWebPageCallback(tabType, params);
}

int KPromeNewDocsPage::tabType()
{
	return m_tabType;
}

QString KPromeNewDocsPage::entrance()
{
	return m_entrance;
}

QMap<QString, QString> KPromeNewDocsPage::timeStampMap() const
{
	return m_timeStampMap;
}


QString KPromeNewDocsPage::entryId() const
{
	return m_entryId;
}

void KPromeNewDocsPage::onSubPageActived()
{
	if (m_widgetMap.contains(m_tabType))
	{
		QWidget* widget = m_widgetMap[m_tabType];
		bool b = QMetaObject::invokeMethod(widget, "onSubPageActived", Qt::DirectConnection);
	}
}


void KPromeNewDocsPage::onHideNewDocsPageTabbar()
{
	m_mainLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(0, 0, 0, 0));
}

void KPromeNewDocsPage::invokeWebPageCallback(int type, const QString& param)
{
	KPromeMainWindow* pMainWindows = promeApp->findRelativePromeMainWindow(this);
	if (nullptr == pMainWindows)
		return;

	KPromeNewDocsPage* pNewdocsPage = findNewDocsPage(pMainWindows, type);

	if (nullptr == pNewdocsPage)
		return;
	
	KPromeNewDocsStartup* pStartupPage = pNewdocsPage->getStartupPage();
	if (nullptr == pStartupPage)
		return;
	pStartupPage->invokeWebPageCallback(param);

	KPromeSubPage* subPage = pNewdocsPage->initialSubPage();
	if (nullptr == subPage)
		return;

	KPromeTab* pPromeTab = subPage->getTab();
	if (nullptr != pPromeTab)
		pPromeTab->activate();
}

void KPromeNewDocsPage::onJumpToSpecificTab(const QString& tabId, const QString& params)
{
	auto tabInfoList = KPromeNewDocsTabBarConfig::getInstance()->getTabInfoList();
	foreach(auto pTabInfo, tabInfoList)
	{
		if (pTabInfo && pTabInfo->id() == tabId)
		{
			setTabType(pTabInfo->getTabType());
			invokeWebPageCallback(pTabInfo->getTabType(), params);
			break;
		}
	}
}

KPromeSubPage* KPromeNewDocsPage::createSubPage(KPromeSubPages* subPages) const
{
	KPromeSubPage* subPage = new KPromeNewDocsSubPage(subPages, QPointer<KPromeNewDocsPage>(const_cast<KPromeNewDocsPage*>(this)));
	connect(subPage, SIGNAL(subPageActived()), this, SLOT(onSubPageActived()));
	return subPage;
}

#if defined(Q_OS_LINUX) && !defined(Q_OS_OHOS) && !defined(Q_OS_ANDROID) && !defined(WEB_OFFICE_ENABLE)
void KPromeNewDocsPage::onLoginStatusChanged()
{
	this->close();
}
#endif

KPromeNewDocsSubPage::KPromeNewDocsSubPage(KPromeSubPages* subPages, QPointer<KPromeNewDocsPage> page)
	: KPromeSubPage(subPages)
	, m_page(page)
{

}

KPromeNewDocsSubPage::~KPromeNewDocsSubPage()
{

}

bool KPromeNewDocsSubPage::getWorkAreaRecoverItem(KPromeWorkAreaRecoverItem* pItem)
{
	auto mw = promeApp->findRelativePromeMainWindow(this);
	if (!m_page || !mw || !mw->tabBar())
		return false;

	KPromeSubPage::getWorkAreaRecoverItem(pItem);
	if(!m_page->processonFileId().isEmpty())
	{
		KPromePluginWidget::WidgetType type = (KPromePluginWidget::WidgetType)m_page->tabType();
		if(type == KPromePluginWidget::widgetProcesson_Flow)
			pItem->pageType = KPromePluginWidget::pageProcesson_Flow;
		else if(type == KPromePluginWidget::widgetProcesson_Mind)
			pItem->pageType = KPromePluginWidget::pageProcesson_Mind;
		pItem->extInfo["fileid"] = m_page->processonFileId();
	}
	pItem->extInfo.insert(g_strTabType, m_page->expectedTabType());
	return true;
}
//////////////////////////////////////////////////////////////////////////
void KBlankTemplateRecentRecordHelper::onRecentFileRecordsAdded(const RecordInfoList &recentInfos)
{
	foreach(auto ri, recentInfos)
	{
		QString absPath = ri.m_fileAbsPath;
		QString blankTemplateDirPath = docer::base::KCurrentModule::getModuleResourceDirPath();
		ksolite::toNativeSeparators(blankTemplateDirPath);
		if(absPath.startsWith(blankTemplateDirPath, Qt::CaseInsensitive))
		{
			if(auto proxy = promeApp->recentFileProxy())
				if(proxy->containsRecord(absPath))
					proxy->removeRecentRecord(absPath);
		}
	}
}

KBlankTemplateRecentRecordHelper::KBlankTemplateRecentRecordHelper(QObject* parent /*= nullptr*/)
	: QObject(parent)
{
	bool bOk = connect(promeApp->recentFileProxy(), SIGNAL(sigRecentRecordsAdded(const RecordInfoList &)),
		this, SLOT(onRecentFileRecordsAdded(const RecordInfoList&)));
	ASSERT(bOk);
}

KBlankTemplateRecentRecordHelper::~KBlankTemplateRecentRecordHelper()
{

}

DECLARE_PLUGIN_WIDGET_FACTORY(pageNewDocs, KPromeNewDocsPage);
