﻿#ifndef __KNEWDOCS_HELPER_H__
#define __KNEWDOCS_HELPER_H__

enum ResDir
{
	//res根目录
	HomePath = 0,
	// old
	EnterpriseOldPath,
	// enterprise
	EnterprisePath,
	//res/kotl
	KOtlPath,
	// personal
	PersonalPath,
};

class KxCreateDocInstance : public QObject
{
	Q_OBJECT
public:
	static KxCreateDocInstance& instance();

public:
	void init(const QDir& dir);
	void unInit();
	void setUserPanelJson(const QJsonObject& jsonObj);
	QJsonObject getUserPanelJson();

	void setUserPanelArray(const QJsonArray& jsonArray);
	QJsonArray getUserPanelArray();

	bool downloadFontList(const QVariantList& fontList);

signals:
	void panelJsonUpdated(const QJsonObject& jsonObj);
	void panelArrayUpdated(const QJsonArray& jsonArray);

protected:
	void initRes();
	void unInitRes();

private slots:
	void onCoreInited();

private:
	KxCreateDocInstance();
	virtual ~KxCreateDocInstance();
	QString getCurrentModuleName();
	QString getSubThemeDirPath();

private:
	QDir _dir;
	QJsonObject m_jsonUserPanel;
	QJsonArray m_arrayUserPanel;
};

class KxCreateDocConfig : public QObject
{
	Q_OBJECT
public:
	static KxCreateDocConfig& instance();
	void loadConfig();

public:
	QString getCreateDocUrl(const QString& app) const;
	QString getSkinConfigPath() const;
	QString getSearchResultUrl() const;
	int getDocerType(int ) const;
	int getDocumentType(const QString& ext) const;
	QString buildBeautifyTemplatePassword(const QString& strFileKey);
	int getAppPageType(const QString& appName) const;
	QString getCurrentWorkspaceId() const;
	QString getCurrentGroupId() const;

	void openPromeBrowser(const QString& url, bool bUrlToolbarVisible = false);
	void openDocerPage(const QString& action, const QString& param);

private slots:
	void slot_reloadComponentUrlConfig();

private:
	KxCreateDocConfig();
	~KxCreateDocConfig();

	void addArg(QString& url, const QString& arg) const;
	QByteArray encode32Md5(const QByteArray& key);
	void handleComponentUrlConfig();

#if defined(Q_OS_LINUX) && !defined(Q_OS_OHOS) && !defined(Q_OS_ANDROID) && !defined(WEB_OFFICE_ENABLE)
private slots:
	void onLoginStatusChanged();
#endif
private:
	QMap<QString, QString> m_newDocUrls;
	QMap<int, int> m_mapDocerDocs;
};

class KxPathProvider
{
public:
	struct WebResDirDetail
	{
		bool bUseUpdate = false;
		QString subPath;
	};

	static void init(const QDir& dir);

	static QString getHomePath();
	static QString getWebResDir(const ResDir resDir = HomePath, WebResDirDetail* detail = nullptr);
	static QString getConfigFilePath();

	static QString getLanguageFilePath();

	static QString getOfficeDataPath();
	static const QDir getKsoTemplateDir();
	static QString getTemplateFilePath(
		const QString& templClass,
		const QString& templFilename
		);

	static void setTemplateDownDir(const QString& strPath);
	static QString getTemplateDownDir(const QString& strDefaultPath = "");

	static QString getNetworkRequestFilePath();
	static QString getConfigTransCacheFilePath();
	static QString getOffice6NewDocBasePath();
private:
	static QDir _dir;
};

namespace KNewDocDrawHelper
{
	void drawCommonBackground(QWidget* w);
	void setBackgroundPattle(QWidget* w);
	bool isDarkSkin();
}
#endif // __KNEWDOCS_HELPER_H__
