<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.4 (67378) - http://www.bohemiancoding.com/sketch -->
    <title>PDF转PPt</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="64" height="64" rx="6"></rect>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="#01" transform="translate(-1218.000000, -1130.000000)">
            <g id="1366768" transform="translate(214.000000, 602.000000)">
                <g id="#01">
                    <g id="推荐模版" transform="translate(275.000000, 191.000000)">
                        <g id="分组-7" transform="translate(3.000000, 304.000000)">
                            <g id="PDF转PPt" transform="translate(694.000000, 0.000000)">
                                <g transform="translate(32.000000, 33.000000)">
                                    <mask id="mask-2" fill="white">
                                        <use xlink:href="#path-1"></use>
                                    </mask>
                                    <use id="Mask" fill="#F78232" xlink:href="#path-1"></use>
                                    <g id="分组" mask="url(#mask-2)">
                                        <g transform="translate(8.000000, 8.000000)">
                                            <path d="M44,48 L4,48 C1.792,48 0,46.208 0,44 L0,4 C0,1.792 1.792,0 4,0 L44,0 C46.208,0 48,1.792 48,4 L48,44 C48,46.208 46.208,48 44,48" id="Fill-1" fill="#F78232"></path>
                                            <path d="M36,14 L36,16 C36,17.104 36.896,18 38,18 L40,18" id="Fill-3" fill="#FFFFFF"></path>
                                            <path d="M32,30 L26,30 L26,34 C26,34.552 25.552,35 25,35 C24.448,35 24,34.552 24,34 L24,29.6 C24,28.718 24.718,28 25.6,28 L32,28 C33.102,28 34,27.102 34,26 C34,24.898 33.102,24 32,24 L25,24 C24.448,24 24,23.552 24,23 C24,22.448 24.448,22 25,22 L32,22 C34.206,22 36,23.794 36,26 C36,28.206 34.206,30 32,30 Z M38,20 C35.792,20 34,18.208 34,16 L34,14 L22,14 C20.896,14 20,14.896 20,16 L20,36 C20,37.104 20.896,38 22,38 L38,38 C39.104,38 40,37.104 40,36 L40,20 L38,20 Z" id="Fill-5" fill="#FFFFFF"></path>
                                            <g id="分组" stroke-width="1" fill="none" transform="translate(8.000000, 8.000000)">
                                                <path d="M10,10 L10,8 C10,7.408 10.136,6.852 10.366,6.346 C9.958,6.13 9.496,6 9,6 L8,6 L8,12 L9,12 C9.352,12 9.686,11.928 10,11.816 L10,10 Z" id="Fill-7" fill="#FFFFFF"></path>
                                                <path d="M4,15 C4,15.552 4.448,16 5,16 L6,16 L6,14 L5,14 C4.448,14 4,14.448 4,15" id="Fill-9" fill="#FFFFFF"></path>
                                                <path d="M9,14 L8,14 L8,17.2 C8,17.642 7.642,18 7.2,18 L5,18 C3.342,18 2,16.658 2,15 C2,13.342 3.342,12 5,12 L6,12 L6,4.8 C6,4.358 6.358,4 6.8,4 L9,4 C9.976,4 10.866,4.306 11.634,4.792 C12.298,4.3 13.112,4 14,4 L20,4 L20,2 C20,0.896 19.104,0 18,0 L2,0 C0.896,0 0,0.896 0,2 L0,20 C0,21.104 0.896,22 2,22 L10,22 L10,13.9 C9.676,13.964 9.342,14 9,14" id="Fill-11" fill="#FFFFFF"></path>
                                            </g>
                                            <path d="M18.036,36.9688 C18.042,37.2368 17.938,37.4968 17.748,37.6868 C17.738,37.6968 17.72,37.6928 17.708,37.7028 L14.494,40.8388 C14.104,41.2288 13.472,41.2288 13.082,40.8388 C12.692,40.4488 12.692,39.8168 13.082,39.4268 L14.586,38.0008 L12,38.0008 C9.79,38.0008 8,36.2088 8,34.0008 L8,33.0008 C8,32.4488 8.448,32.0008 9,32.0008 C9.552,32.0008 10,32.4488 10,33.0008 L10,34.0008 C10,35.1048 10.896,36.0008 12,36.0008 L14.65,36.0008 L13.066,34.5408 C12.676,34.1528 12.674,33.5188 13.064,33.1288 C13.452,32.7388 14.084,32.7368 14.476,33.1268 L14.48,33.1288 L17.708,36.2348 C17.72,36.2448 17.738,36.2388 17.748,36.2508 C17.938,36.4408 18.042,36.7008 18.036,36.9688" id="Fill-13" fill="#FFFFFF"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>