﻿#include "stdafx.h"
#include "kpdfpluginloaddialog.h"
#include <public_header/kliteui/kdrawhelper.h>
#include "kpromeapplication.h"
#include <kprometheus/kpromecloudsvrproxy.h>
#include <auth/productinfo.h>
#include <ksolite/kdcinfoc.h>
#include "kprometheus/kpromeapplication.h"
#include "kprometheus/kpromeinfocollcethelper.h"
#include "kprometheus/kpromemainwindow.h"
#include <ksolite/kfeedbackdlgmgr.h>
#include <public_header/kliteui/kliteshadowborder.h>
#include "kliteui/klitestyle.h"
#include <krt/kconfigmanagercenter.h>
//////////////////////////////////////////////////////////////////////////
QString PluginWidgetColorHelper::getNameColorFromTheme(const QString& className, const QString& attributeName, const QColor& defaultColor)
{
	QColor color = KDrawHelper::getColorFromTheme(className, attributeName, defaultColor);
	return QString::fromUtf8("rgba(%1,%2,%3,%4)").arg(color.red()).arg(color.green()).arg(color.blue()).arg(color.alpha());
}

QString PluginWidgetColorHelper::getCancelQPushButtonCss()
{
	QString btnBgColor = getNameColorFromTheme("PluginWidgetColorHelper_CancleButton", "background-color-nomal", QColor(0xFFFFFF));
	QString btnBorderColor = getNameColorFromTheme("PluginWidgetColorHelper_CancleButton", "boder-color-nomal", QColor(0xD7D9DE));
	QString btnTextColor = getNameColorFromTheme("PluginWidgetColorHelper_CancleButton", "font-color-nomal", QColor(0x36425A));
	QString btnHoverBgColor = getNameColorFromTheme("PluginWidgetColorHelper_CancleButton", "background-color-hover", QColor(0xEDF0F2));
	QString btnHoverBoderColor = getNameColorFromTheme("PluginWidgetColorHelper_CancleButton", "boder-color-hover", QColor(0xB9BDC6));
	QString btnPressedBgColor = getNameColorFromTheme("PluginWidgetColorHelper_CancleButton", "background-color-select", QColor(0xE8EBEF));
	QString btnPressedBoderColor = getNameColorFromTheme("PluginWidgetColorHelper_CancleButton", "boder-color-select", QColor(0xAFB3BD));

	const QString s_cancelQPushButtonCss = QString(
		"QPushButton{background-color:%1;border-radius: %2px;font-family: Microsoft YaHei;font-size: %3px;color: %4;letter-spacing: 0;border:%5px solid %6;padding-right:%11px;padding-left:%11px;}"
		"QPushButton:hover{background: %7;border:%5px solid %8}"
		"QPushButton:pressed{background: %9;border:%5px solid %10}");
	return toHdpiStyle(s_cancelQPushButtonCss.arg(btnBgColor).arg(2).arg(14).arg(btnTextColor).arg(1).arg(btnBorderColor).arg(btnHoverBgColor).arg(btnHoverBoderColor)
		.arg(btnPressedBgColor).arg(btnPressedBoderColor).arg(12));
}

QString PluginWidgetColorHelper::getOkPushButtonCss()
{
	QString btnBgColor = getNameColorFromTheme("KWebTeamTopConfirmButton", "background", QColor(0x417FF9));
	QString btnTextColor = getNameColorFromTheme("KWebTeamTopConfirmButton", "blue-btn-font-color", QColor(0xFFFFFF));
	QString btnHoverBgColor = getNameColorFromTheme("KWebTeamTopConfirmButton", "background-hover", QColor(0x608DFA));
	QString btnPressedBgColor = getNameColorFromTheme("KWebTeamTopConfirmButton", "background-down", QColor(0x1F72F1));

	const QString s_okQPushButtonCss = QString(
		"QPushButton{background-color:%1;border-radius: %2px;font-family: Microsoft YaHei;font-size: %3px;color: %4;letter-spacing: 0;border:none;padding-right:%7px;padding-left:%7px;}"
		"QPushButton:hover{background: %5;}"
		"QPushButton:pressed{background: %6;}");
	return toHdpiStyle(s_okQPushButtonCss.arg(btnBgColor).arg(2).arg(14).arg(btnTextColor).arg(btnHoverBgColor).arg(btnPressedBgColor).arg(12));
}

QString PluginWidgetColorHelper::getQlabelStyleCss(int fontSize, const QString& colorName, int lineHeight, const QString& fontFamily/* = "Microsoft YaHei"*/)
{
	static QString QlabelStyleCss(
		"QLabel{font-family: %1;font-size: %2px;color: %3;line-height: %4px;}"
		);
	return toHdpiStyle(QlabelStyleCss.arg(fontFamily).arg(fontSize).arg(colorName).arg((lineHeight)));
}

QString PluginWidgetColorHelper::toHdpiStyle(const QString& style)
{
	if (style.isEmpty())
		return style;

	QStringList pxList = style.split("px", QString::KeepEmptyParts, Qt::CaseInsensitive);

	int stringSize = pxList.size();
	if (stringSize <= 1)
		return style;

	QString styleHdpi;

	for (int i = 0; i < stringSize - 1; i++)
	{
		QString stringTemp = pxList.at(i).toLocal8Bit().constData();
		int numIndex = stringTemp.lastIndexOf(":") + 1;
		QString numString = stringTemp.mid(numIndex);

		bool isNum;
		qreal pxData = numString.toInt(&isNum);

		if (!isNum)
			continue;

		qreal dpiPx = KLiteStyle::dpiScaled(pxData);
		if (stringTemp.contains("font-size", Qt::CaseSensitive) || stringTemp.contains("font", Qt::CaseSensitive))
			dpiPx = floor(dpiPx);
		QString dpiPxString = QString::number(dpiPx);
		QString stringHdpi = stringTemp.replace(numIndex, numString.length(), dpiPxString);

		styleHdpi.append(stringHdpi);
		styleHdpi.append("px");
	}

	QString stringTemp = pxList.at(stringSize - 1).toLocal8Bit().constData();
	styleHdpi.append(stringTemp);

	return styleHdpi;
}

/////////////////////////////////////////////////////////////
KPluginLoadFailedWidget::KPluginLoadFailedWidget(QWidget* parent,KAppObj* appObj)
	: QWidget(parent)
	, m_appObj(appObj)
{
	setWindowFlags(Qt::Window | Qt::FramelessWindowHint | Qt::Tool);
	setFixedSize(KLiteStyle::dpiScaledSize(420, 180));
	initUI();
	initConnect();
}

KPluginLoadFailedWidget::~KPluginLoadFailedWidget()
{

}
void KPluginLoadFailedWidget::initConnect()
{
	bool bConnected = QObject::connect(m_closeBtn, SIGNAL(clicked()),
		this, SLOT(onCloseBtnClicked()));
	Q_ASSERT(bConnected);

	QObject::connect(m_oKBtn, SIGNAL(clicked()),
		this, SLOT(onBottomBtnClicked()));
	QObject::connect(m_cancelBtn, SIGNAL(clicked()),
		this, SLOT(onCloseBtnClicked()));
}

void KPluginLoadFailedWidget::setContentText(const QString& content)
{
	if (m_contentLbl)
		m_contentLbl->setText(content);
}

void KPluginLoadFailedWidget::initUI()
{
	setSizePolicy(QSizePolicy::Maximum, QSizePolicy::Maximum);
	QString bgColor = PluginWidgetColorHelper::getNameColorFromTheme("KPromeNewDocsTabBarNew", KDrawHelper::Prop_Background, KDrawHelper::stringToColor("#FFFFFFFF"));
	setStyleSheet(QString(
		"QWidget{background:%1;border:none;padding:0px;margin:0px;spacing:0px;}"
		).arg(bgColor));
	KLiteShadowBorder* shadowBorder = new KLiteShadowBorder(this, this);
 	shadowBorder->setRadius(KLiteStyle::dpiScaled(4));

	QVBoxLayout* mainLayout = new QVBoxLayout(this);
	mainLayout->setSpacing(KLiteStyle::dpiScaled(12));
	mainLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(24, 18, 24, 24));
	
	QHBoxLayout* topLayout = new QHBoxLayout(this);
	{
		topLayout->setMargin(0);
		m_titleLbl = new QLabel(this);
		m_titleLbl->setFixedSize(KLiteStyle::dpiScaledSize(356, 28));
		QString titleColor = PluginWidgetColorHelper::getNameColorFromTheme("KShareFolderGuideDlg", "title", QColor(0x0C0F17));
		m_titleLbl->setStyleSheet(PluginWidgetColorHelper::getQlabelStyleCss(20,titleColor));
		m_titleLbl->setText(tr("Download exception"));
		m_closeBtn = new QPushButton(this);
		m_closeBtn->setFixedSize(KLiteStyle::dpiScaledSize(16, 16));
		static QString CloseQPushButtonSkinStyleCss = QString(
			"QPushButton{border:none;image: url(:/shared/icons/dialogbox_close_m_16.svg); background:rgba(0,0,0,0);}"
			"QPushButton:hover{border:none;image: url(:/shared/icons/dialogbox_close_m_16_hover.svg); background:rgba(0,0,0,0);}"
			"QPushButton:pressed{border:none;image: url(:/shared/icons/dialogbox_close_m_16.svg);background:rgba(0,0,0,0);}"
			);
		m_closeBtn->setStyleSheet(CloseQPushButtonSkinStyleCss);
		topLayout->setSpacing(0);
		topLayout->addWidget(m_titleLbl);
		topLayout->addSpacing(0);
		topLayout->addWidget(m_closeBtn);
	}
	
	{
		m_contentLbl = new QLabel(this);
		m_contentLbl->setWordWrap(true);
		m_contentLbl->setFixedSize(KLiteStyle::dpiScaledSize(372, 44));
		QString contentColor = PluginWidgetColorHelper::getNameColorFromTheme("KShareFolderGuideDlg", "content", QColor(0x444E63));
		m_contentLbl->setStyleSheet(PluginWidgetColorHelper::getQlabelStyleCss(14, contentColor));
	}

	QHBoxLayout* bottomLayout = new QHBoxLayout(this);
	bottomLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(200, 0, 0, 0));
	{
		m_oKBtn = new QPushButton(this);
		QFontMetrics fm(m_oKBtn->font());
		m_oKBtn->setFixedWidth(fm.width(m_oKBtn->text()) + KLiteStyle::dpiScaled(15));
		m_oKBtn->setFixedSize(KLiteStyle::dpiScaledSize(80, 30));
		m_oKBtn->setStyleSheet(PluginWidgetColorHelper::getOkPushButtonCss());
		m_oKBtn->setText(tr("toWeb"));
		m_oKBtn->setVisible(false);

		m_cancelBtn = new QPushButton(this);
		m_cancelBtn->setFixedSize(KLiteStyle::dpiScaledSize(80, 30));
		m_cancelBtn->setStyleSheet(PluginWidgetColorHelper::getCancelQPushButtonCss());
		m_cancelBtn->setText(tr("cancle"));

		bottomLayout->setSpacing(0);
		bottomLayout->addWidget(m_oKBtn);
		bottomLayout->addSpacing(KLiteStyle::dpiScaled(12));
		bottomLayout->addWidget(m_cancelBtn);
	}
	mainLayout->setSpacing(0);
	mainLayout->addLayout(topLayout);
	mainLayout->addSpacing(KLiteStyle::dpiScaled(11));
	mainLayout->addWidget(m_contentLbl);
	mainLayout->addSpacing(KLiteStyle::dpiScaled(25));
	mainLayout->addLayout(bottomLayout);
	setLayout(mainLayout);
}
void KPluginLoadFailedWidget::setShowIsWebBtn(bool isShow)
{
	m_oKBtn->setVisible(isShow);
}

void KPluginLoadFailedWidget::onCloseBtnClicked()
{
	close();
}

void KPluginLoadFailedWidget::onBottomBtnClicked()
{
	emit openUrl();
}

void KPluginLoadFailedWidget::adjustPos()
{
	QRect deskRect = QApplication::desktop()->availableGeometry();
	QPoint centerPoint = QPoint((deskRect.width() - width()) / 2,
		(deskRect.height() - height()) / 2);

	int intervalNum = m_posIndex * 20;
	QPoint pos = QPoint(centerPoint.x() + intervalNum,
		centerPoint.y() + intervalNum);
	move(pos);
}

void KPluginLoadFailedWidget::mousePressEvent(QMouseEvent* e)
{
	if (rect().contains(e->pos()))
	{
		m_bDraging = true;
		m_dragStartPos = e->pos();
	}

	QWidget::mousePressEvent(e);
}

void KPluginLoadFailedWidget::mouseReleaseEvent(QMouseEvent* e)
{
	m_bDraging = false;
	m_dragStartPos = e->pos();

	QWidget::mouseReleaseEvent(e);
}

void KPluginLoadFailedWidget::mouseMoveEvent(QMouseEvent* e)
{
	if (m_bDraging)
	{
		int deltaX = x() + (e->pos() - m_dragStartPos).x();
		int deltaY = y() + (e->pos() - m_dragStartPos).y();
		move(deltaX, deltaY);
	}

	QWidget::mouseMoveEvent(e);
}

/////////////////////////////////////////////////////////
KPluginLoadingProgressBar::KPluginLoadingProgressBar(QWidget* parent)
	: QProgressBar(parent)
	, m_xRadius(0)
	, m_yRadius(0)
{
	m_bgColor = KDrawHelper::getColorFromTheme("KNewDocs-ProgressBar", "background-color", QColor(38, 46, 62, 0));
	m_chunkColor = KDrawHelper::getColorFromTheme("KNewDocs-ProgressBar", "chunk-color", QColor(0x417FF9));
}

KPluginLoadingProgressBar::~KPluginLoadingProgressBar()
{

}

void KPluginLoadingProgressBar::setbgColor(const QColor& color)
{
	m_bgColor = color;
}

void KPluginLoadingProgressBar::setChunkColor(const QColor& color)
{
	m_chunkColor = color;
}

void KPluginLoadingProgressBar::setRadius(int x, int y)
{
	m_xRadius = x;
	m_yRadius = y;
}

void KPluginLoadingProgressBar::paintEvent(QPaintEvent* e)
{
	QPainter painter(this);

	painter.save();
	painter.setPen(Qt::transparent);
	painter.setBrush(m_bgColor);
	if (m_xRadius > 0 || m_yRadius > 0)
	{
		QRectF bgRect = rect();
		painter.setRenderHint(QPainter::Antialiasing);
		painter.drawRoundedRect(bgRect, m_xRadius, m_yRadius);

		QRectF progressRect = bgRect;
		progressRect.setWidth(bgRect.width() * value() / 100);
		painter.setBrush(m_chunkColor);
		painter.setPen(QPen(Qt::transparent));
		painter.drawRoundedRect(progressRect, m_xRadius, m_yRadius);
	}
	else
	{
		painter.drawRect(rect());

		QRect progressRect = rect();
		progressRect.setWidth(rect().width() * value() / 100);
		painter.setPen(QPen(Qt::transparent));
		painter.setBrush(m_chunkColor);
		painter.drawRect(progressRect);
	}

	painter.restore();
}

////////////////////////////////////
KPluginLoadWidget::KPluginLoadWidget(QWidget* parent)
	: QWidget(parent)
	, m_mainLayout(nullptr)
	, m_titleLayout(nullptr)
	, m_titleLbl(nullptr)
	, m_contentLbl(nullptr)
	, m_closeBtn(nullptr)
	, m_bottomBtn(nullptr)
	, m_progressBar(nullptr)
	, m_progressWidget(nullptr)
	, m_needShadowBorder(true)
{
	setFixedSize(KLiteStyle::dpiScaledSize(420, 180));
}

KPluginLoadWidget::~KPluginLoadWidget()
{

}

void KPluginLoadWidget::initUI()
{
	if (m_needShadowBorder)
	{
		setWindowFlags(Qt::Tool | Qt::FramelessWindowHint);
		KLiteShadowBorder* shadowBorder = new KLiteShadowBorder(this, this);
		shadowBorder->setRadius(KLiteStyle::dpiScaled(4));
	}
	m_mainLayout = new QVBoxLayout(this);
	m_mainLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(24, 18, 24, 24));
	m_mainLayout->setSpacing(0);

	{
		m_titleLayout = new QHBoxLayout(this);
		m_titleLayout->setMargin(0);
		m_titleLayout->setSpacing(0);
		{
			m_titleLbl = new QLabel(this);
			m_titleLbl->setFixedSize(KLiteStyle::dpiScaledSize(356, 28));
			m_closeBtn = new QPushButton(this);
			m_closeBtn->setFixedSize(KLiteStyle::dpiScaledSize(16, 16));
			m_titleLayout->addWidget(m_titleLbl);
			m_titleLayout->addSpacing(0);
			m_titleLayout->addWidget(m_closeBtn);
		}

		m_progressWidget = new QWidget(this);
		m_progressWidget->setFixedSize(KLiteStyle::dpiScaledSize(372, 80));
		QVBoxLayout* progressLayout = new QVBoxLayout(this);
		progressLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(0, 22, 0, 20));
		progressLayout->setSpacing(0);
		{
			m_progressBar = new KPluginLoadingProgressBar(this);
			m_contentLbl = new QLabel(this);
			m_contentLbl->setFixedHeight(KLiteStyle::dpiScaled(22));
			m_progressBar->setFixedHeight(KLiteStyle::dpiScaled(8));
			m_progressBar->setRadius(KLiteStyle::dpiScaled(4), KLiteStyle::dpiScaled(4));

			progressLayout->addWidget(m_progressBar);
			progressLayout->addSpacing(KLiteStyle::dpiScaled(8));
			progressLayout->addWidget(m_contentLbl);
			m_progressWidget->setLayout(progressLayout);
		}

		QHBoxLayout* bottomLayout = new QHBoxLayout(this);
		bottomLayout->setContentsMargins(KLiteStyle::dpiScaledMargins(292, 0, 0, 0));
		{
			m_bottomBtn = new QPushButton(this);
			m_bottomBtn->setFixedSize(KLiteStyle::dpiScaledSize(80, 30));
			bottomLayout->addWidget(m_bottomBtn);
		}

		m_mainLayout->addLayout(m_titleLayout);
		m_mainLayout->addSpacing(0);
		m_mainLayout->addWidget(m_progressWidget);
		m_mainLayout->addSpacing(0);
		m_mainLayout->addLayout(bottomLayout);
	}
	setLayout(m_mainLayout);
	initStyleSheet();
}

void KPluginLoadWidget::initStyleSheet()
{
	QString titleColor = PluginWidgetColorHelper::getNameColorFromTheme("KShareFolderGuideDlg", "title", QColor(0x0C0F17));
	QString numbelTipColor = PluginWidgetColorHelper::getNameColorFromTheme("KWebCompanyPkgDialogDetails", "text-02", QColor(0x727E96));
	m_titleLbl->setStyleSheet(PluginWidgetColorHelper::getQlabelStyleCss(20,titleColor));
	m_bottomBtn->setStyleSheet(PluginWidgetColorHelper::getOkPushButtonCss());
	static QString CloseQPushButtonSkinStyleCss = QString(
		"QPushButton{border:none;image: url(:/shared/icons/dialogbox_close_m_16.svg); background:rgba(0,0,0,0);}"
		"QPushButton:hover{border:none;image: url(:/shared/icons/dialogbox_close_m_16_hover.svg); background:rgba(0,0,0,0);}"
		"QPushButton:pressed{border:none;image: url(:/shared/icons/dialogbox_close_m_16.svg);background:rgba(0,0,0,0);}"
		);
	m_closeBtn->setStyleSheet(CloseQPushButtonSkinStyleCss);
	m_contentLbl->setStyleSheet(PluginWidgetColorHelper::getQlabelStyleCss(12, numbelTipColor));
	QString bgColor = PluginWidgetColorHelper::getNameColorFromTheme("KPromeNewDocsTabBarNew", KDrawHelper::Prop_Background, KDrawHelper::stringToColor("#FFFFFFFF"));
	setStyleSheet(QString(
		"QWidget{background:%1;border:none;padding:0px;margin:0px;spacing:0px;}"
		).arg(bgColor));
}

void KPluginLoadWidget::updateToFailedMode()
{
	m_progressWidget->hide();
	m_titleLbl->setStyleSheet("color: yellow");
	update();
}

void KPluginLoadWidget::setTitleText(const QString& title)
{
	if (m_titleLbl)
		m_titleLbl->setText(title);
}

QString KPluginLoadWidget::contentText() const
{
	QString str;
	if (m_contentLbl)
		str = m_contentLbl->text();
	return str;
}

void KPluginLoadWidget::setContentText(const QString& content)
{
	if (m_contentLbl)
		m_contentLbl->setText(content);
}

void KPluginLoadWidget::setBtnText(const QString& btnText)
{
	if (m_bottomBtn)
	{
		m_bottomBtn->setText(btnText);
		if (krt::kcmc::support("PDFNewPageBackgroundButtonCustom"))
			m_bottomBtn->setFixedWidth(m_bottomBtn->fontMetrics().width(btnText) + KLiteStyle::dpiScaled(20));
	}
}

void KPluginLoadWidget::setProgressValue(double v)
{
	if (m_progressBar)
		m_progressBar->setValue(v);
}

static QMap<QString, QPointer<KPluginLoadWindow>> s_loadingWindowMap;

KPluginLoadWindow::KPluginLoadWindow(QWidget* parent, KAppObj* appObj, const QString& src)
	: KPluginLoadWidget(parent)
	, m_appObj(appObj)
	, m_src(src)
	, m_bLoadFailedMode(false)
	, m_bLoadingCloseBtnClick(false)
	, m_bAutoShowFailedMode(true)
	, m_bDraging(false)
	, m_posIndex(0)
{
	setAttribute(Qt::WA_DeleteOnClose);
	initUI();
	init();
}

KPluginLoadWindow::~KPluginLoadWindow()
{

}

void KPluginLoadWindow::init()
{
	setBtnText(tr("Background"));
	connectSignals();
}

void KPluginLoadWindow::setLoadTitleText(const QString& titleTr)
{
	if (m_appObj)
		setTitleText(titleTr);
}

void KPluginLoadWindow::setAutoShowFailedMode(bool bSwitch)
{
	m_bAutoShowFailedMode = bSwitch;
}

bool KPluginLoadWindow::isCloseBtnClicked() const
{
	return m_bLoadingCloseBtnClick;
}

void KPluginLoadWindow::showLoadFailed()
{
	hide();
	if (isHidden())
	{
		if (!m_bLoadingCloseBtnClick)
			emit showPopupFailedWindow();
	}
	else
	{
		m_bLoadFailedMode = true;
		if (!m_bLoadingCloseBtnClick)
			emit showPopupFailedWindow();
	}
}

void KPluginLoadWindow::connectSignals()
{
	bool bConnected = QObject::connect(m_closeBtn, SIGNAL(clicked()),
		this, SLOT(onCloseBtnClicked()));
	Q_ASSERT(bConnected);

	bConnected = QObject::connect(m_bottomBtn, SIGNAL(clicked()),
		this, SLOT(onBottomBtnClicked()));
	Q_ASSERT(bConnected);

	if (m_appObj.isNull())
		return;
	bConnected = QObject::connect(m_appObj, SIGNAL(loadSuccess()),
		this, SLOT(onLoadSuccess()));
	Q_ASSERT(bConnected);

	bConnected = QObject::connect(m_appObj, SIGNAL(loadFailed()),
		this, SLOT(onLoadFailed()));
	Q_ASSERT(bConnected);

	bConnected = QObject::connect(m_appObj, SIGNAL(loadCanceled()),
		this, SLOT(onLoadCanceled()));
	Q_ASSERT(bConnected);

	bConnected = QObject::connect(m_appObj, SIGNAL(loadProgress(double)),
		this, SLOT(onProgressUpdate(double)));
	Q_ASSERT(bConnected);
}

void KPluginLoadWindow::adjustPos()
{
	QRect deskRect = QApplication::desktop()->availableGeometry();
	QPoint centerPoint = QPoint((deskRect.width() - width()) / 2,
		(deskRect.height() - height()) / 2);

	int intervalNum = m_posIndex * 20;
	QPoint pos = QPoint(centerPoint.x() + intervalNum,
		centerPoint.y() + intervalNum);
	move(pos);
}

void KPluginLoadWindow::mousePressEvent(QMouseEvent* e)
{
	if (rect().contains(e->pos()))
	{
		m_bDraging = true;
		m_dragStartPos = e->pos();
	}

	QWidget::mousePressEvent(e);
}

void KPluginLoadWindow::mouseReleaseEvent(QMouseEvent* e)
{
	m_bDraging = false;
	m_dragStartPos = e->pos();

	QWidget::mouseReleaseEvent(e);
}

void KPluginLoadWindow::mouseMoveEvent(QMouseEvent* e)
{
	if (m_bDraging)
	{
		int deltaX = x() + (e->pos() - m_dragStartPos).x();
		int deltaY = y() + (e->pos() - m_dragStartPos).y();
		move(deltaX, deltaY);
	}

	QWidget::mouseMoveEvent(e);
}

void KPluginLoadWindow::showEvent(QShowEvent* e)
{
	if (m_appObj)
	{
		auto itWindow = s_loadingWindowMap.find(m_appObj->id());
		if (itWindow != s_loadingWindowMap.end() && (*itWindow))
			(*itWindow)->deleteLater();
		s_loadingWindowMap[m_appObj->id()] = this;
	}

	adjustPos();
	activateWindow();
	KPluginLoadWidget::showEvent(e);
}

void KPluginLoadWindow::onProgressUpdate(double progress)
{
	if (m_progressBar)
		m_progressBar->setValue(progress * 100);

	if (m_contentLbl)
		m_contentLbl->setText(QString("%1%").arg(QString::number(uint(progress * 100))));
	emit loadProgressUpdate(progress);
}

void KPluginLoadWindow::onLoadSuccess()
{
	emit loadSuccess();
	hide();
	QTimer::singleShot(0, this, SLOT(close()));
}

void KPluginLoadWindow::onLoadFailed()
{
	emit loadProgressUpdate(0);
	emit loadFailed();
	if (m_bAutoShowFailedMode)
	{
		showLoadFailed();
	}
	else
	{
		hide();
		QTimer::singleShot(0, this, SLOT(close()));
	}
}

void KPluginLoadWindow::onLoadCanceled()
{
	emit loadProgressUpdate(0);
	emit loadCanceled();
	hide();
	QTimer::singleShot(0, this, SLOT(close()));
}

void KPluginLoadWindow::onCloseBtnClicked()
{
	if (m_bLoadFailedMode)
	{
		close();
	}
	else
	{
		m_bLoadingCloseBtnClick = true;
		hide();
		emit cancelBtnClicked();
	}
}

void KPluginLoadWindow::onBottomBtnClicked()
{
	if (m_bLoadFailedMode)
	{
		close();
	}
	else
	{
		hide();
		emit backwardBtnClicked();
	}
}

