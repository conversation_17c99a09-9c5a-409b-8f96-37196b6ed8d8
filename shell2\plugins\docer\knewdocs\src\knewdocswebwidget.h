﻿#ifndef __KNEWDOCS_WEBWIDGET_H__
#define __KNEWDOCS_WEBWIDGET_H__
#include "jsapi/widget/kdocercommonwebwidget.h"
#include "legacy/async/kpromise.h"

class KPromeNewDocsStartup;

class KNewDocsWebWidget
	: public KDocerCommonWebWidget
	, public IWebViewContainerFactory
{
	Q_OBJECT
public:
	enum KNewdocsloadErrorCode
	{
		NoError = 0,
		MainResourceMiss = 1,
		MainResourceVerificationFailed = 2,
		CefRenderAbnormal = 3,
		LoadTimeout = 4
	};
	Q_ENUM(KNewdocsloadErrorCode)
	explicit KNewDocsWebWidget(int tabType, KPromeNewDocsStartup* startup);
	virtual ~KNewDocsWebWidget();
	virtual KxWebViewImplementationType webviewType() override;
	virtual ksolite::KxCommonJsApi* initJsObject() override;
	virtual QWidget* createWaitingWidget() override;
	void invokeWebPageCallback(const QString& param);
	void setStartTimeStamp(qint64 timeStamp);
	void setErrorPage(KNewdocsloadErrorCode errorCode);
	static QString convertLoadErrorCode2String(KNewdocsloadErrorCode errorCode);
	virtual KxWebViewContainer* createWebViewContainer() override;
	void resetTemplateLibTabTitle();

	//重写加载接口，根据url的scheme决定是否启用跨域
	virtual void init(const QString& initUrl, const QString& appID,
		bool bInitWaitingWidget = true, KxWebViewCefJsApiFlag jsapiFlag = KXWEBVIEW_CEF_CEFQUERY) override;
	void reload(const QString& reloadUrl, const QString& appID);

public slots:
	void onBrowserCreated();
	void onKeyBoardEvent(int modifierkey, int key, int eventType);
	void onJsScriptReady();
	void showWebWidget();

protected slots:
	virtual void onLoadedFinished(bool bOk) override;
	void onScriptReady();
	virtual void updateCookies() override;
	void onNewCloudDocSwitchStatus(const QVariantMap& paramsMap);
	void onRenderProcAbnormalTerminate(int status);
	void onInjectInfo();
	void onWebViewPopupWindow(const KxWebViewClickedContext& context);
	void onBrowserException(qint32);
protected:
	virtual void initWebView() override;
	void prepareComponent();
	
	virtual QUrl getErrorPage() override;
	bool openPromeBrowser(const QString& url);
signals:
	void cefKeyEvent(Qt::KeyboardModifiers modifyKey, int key, bool keyPress, bool& handle);
	void mainResourceVerificationFailed(const QString& url);

private:
	void addSpecialCookies(const QString& domain, const QVariantMap& varintMap);
	void sendDocCreateCloudSwitchInfo();
	void errorPageCallback();
	void postLoadEvent();
	void reportSRELoadPageFailed();

	void updateAccessControlAllowAllOrigin(const QString strUrl);

private:
	KPromeNewDocsStartup* m_startup;
	int m_tabType = KPromePluginWidget::invalidWidgetType;
	QList<docer::base::closure>		m_onScriptReadyTasks;
	bool						m_bScriptReady = false;
	qint64 m_startTimeStamp = 0;
	KNewdocsloadErrorCode m_loadErrorCode = KNewdocsloadErrorCode::NoError;
};

#endif //__KNEWDOCS_WEBWIDGET_H__