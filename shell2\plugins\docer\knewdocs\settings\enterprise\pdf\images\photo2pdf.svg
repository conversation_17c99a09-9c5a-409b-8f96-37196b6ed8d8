<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.4 (67378) - http://www.bohemiancoding.com/sketch -->
    <title>图片转PDF</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="64" height="64" rx="6"></rect>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="#01" transform="translate(-524.000000, -1454.000000)">
            <g id="1366768" transform="translate(214.000000, 602.000000)">
                <g id="#01">
                    <g id="推荐模版" transform="translate(275.000000, 191.000000)">
                        <g id="分组-7" transform="translate(3.000000, 304.000000)">
                            <g id="图片转PDF" transform="translate(0.000000, 324.000000)">
                                <g transform="translate(32.000000, 33.000000)">
                                    <mask id="mask-2" fill="white">
                                        <use xlink:href="#path-1"></use>
                                    </mask>
                                    <use id="Mask" fill="#FF5F5F" xlink:href="#path-1"></use>
                                    <g id="分组" mask="url(#mask-2)">
                                        <g transform="translate(8.000000, 8.000000)">
                                            <path d="M44,48 L4,48 C1.792,48 0,46.208 0,44 L0,4 C0,1.792 1.792,0 4,0 L44,0 C46.208,0 48,1.792 48,4 L48,44 C48,46.208 46.208,48 44,48" id="Fill-1" fill="#FF5F5F"></path>
                                            <path d="M15.7998,19.4004 C15.7998,19.6004 15.5998,19.8004 15.5998,19.8004 L11.7998,23.6004 C11.3998,24.0004 10.7998,24.0004 10.3998,23.6004 C9.9998,23.2004 9.9998,22.6004 10.3998,22.2004 L12.5998,20.0004 L6.9998,20.0004 C6.3998,20.0004 5.9998,19.6004 5.9998,19.0004 C5.9998,18.4004 6.3998,18.0004 6.9998,18.0004 L12.3998,18.0004 L10.1998,15.8004 C9.7998,15.4004 9.7998,14.8004 10.1998,14.4004 C10.7998,14.0004 11.3998,14.0004 11.7998,14.4004 L15.5998,18.2004 C15.7998,18.4004 15.9998,18.6004 15.9998,19.0004 C15.9998,19.2004 15.9998,19.4004 15.7998,19.4004" id="Fill-3" fill="#FFFFFF"></path>
                                            <path d="M38,14 L38,12.634 L31.322,6 L14,6 C11.792,6 10,7.792 10,10 L10,11 C10,11.552 10.448,12 11,12 C11.552,12 12,11.552 12,11 L12,10 C12,8.898 12.898,8 14,8 L30,8 L30,10.8 C30,12.568 31.432,14 33.2,14 L36,14 L36,38 C36,39.102 35.102,40 34,40 L14,40 C12.898,40 12,39.102 12,38 L12,27 C12,26.448 11.552,26 11,26 C10.448,26 10,26.448 10,27 L10,38 C10,40.208 11.792,42 14,42 L34,42 C36.208,42 38,40.208 38,38 L38,14 Z" id="Fill-5" fill="#FFFFFF"></path>
                                            <path d="M40,38 L25.986,38 C24.882,38 23.986,37.104 23.986,36 L23.986,22 C23.986,20.896 24.882,20 25.986,20 L40,20 C41.104,20 42,20.896 42,22 L42,36 C42,37.104 41.104,38 40,38" id="Fill-7" fill="#FFFFFF"></path>
                                            <path d="M33,30 L32,30 L32,24 L33,24 C34.658,24 36,25.342 36,27 C36,28.656 34.658,30 33,30 Z M30,34 L29,34 C28.448,34 28,33.552 28,33 C28,32.448 28.448,32 29,32 L30,32 L30,34 Z M33,22 L30.8,22 C30.358,22 30,22.358 30,22.8 L30,30 L29,30 C27.342,30 26,31.342 26,33 C26,34.658 27.342,36 29,36 L31.2,36 C31.642,36 32,35.642 32,35.2 L32,32 L33,32 C35.762,32 38,29.762 38,27 C38,24.238 35.762,22 33,22 Z" id="Fill-9" fill="#FF5F5F"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>