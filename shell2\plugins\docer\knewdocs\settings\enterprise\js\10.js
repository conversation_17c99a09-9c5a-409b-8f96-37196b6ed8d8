(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[10],{"29c1":function(t,e,a){"use strict";a("cd30")},bc79:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"stick",rawName:"v-stick"}],ref:"rMainContentCon",staticClass:"screen g-component-bg",on:{scroll:t.scrollHome}},[e("div",{staticClass:"screen__container"},[e("FilterBar",{ref:"filterBar",attrs:{pageType:t.pageType,pageId:t.pageId,cateList:t.cateList,filterParams:t.filterParams,isFixed:t.isFixed,limit:t.limit},on:{change:t.handleChange}}),e("div",{staticClass:"screen__list",class:{screen__scroll:t.isFixed}},[e("List",{attrs:{list:t.list,limit:t.limit,maxCount:t.maxCount,pageName:"topic",pageSize:t.pageSize,commonCollectParams:t.commonCollectParams},on:{loadMore:t.handleLoadData}}),t.statusCode?e("DataStatus",{attrs:{statusCode:t.statusCode},on:{retry:function(e){return t.initList(!1)}}}):t._e()],1)],1)])},s=[],r=a("beeb"),l=a("2f62"),o=a("f348"),n=a("f905"),c=a("c8fc"),d=a("eff4"),h=a("a4c7"),p=a("a6c6"),u=a("e400"),m=a("3387"),f={mixins:[u["a"]],data(){return{list:[],maxCount:0,pageId:0,pageType:"",filterBarData:{},filterParams:{activeCateId:"",activeTagId:"",activeColorId:"",activeBgId:"",activePayId:""},statusCode:"",isInterfaceLoading:!1,isFixed:!1,limit:0,pageSize:0,isScroll:!1,policy:"",request_id:""}},beforeRouteEnter(t,e,a){a(e=>{e.$nextTick(()=>{let a=t&&t.query||{};e.routeInit(a),e.ajustTplLayout(),e.init(!0)})})},beforeRouteUpdate(t,e,a){let i=t&&t.query||{};this.routeInit(i),this.ajustTplLayout(),this.init(!0),a()},mounted(){p["a"].$on(r["p"].scrollTop,()=>{this.isScroll=!0,this.isFixed=!1;let t=this.$refs.filterBar.$el;t&&(t.style.top=0,t.style.padding=0)}),p["a"].$on(r["p"].resizeWindow,()=>{this.ajustTplLayout(),this.$nextTick(()=>{this.scrollHome()})})},beforeDestroy(){p["a"].$off(r["p"].scrollTop),p["a"].$off(r["p"].resizeWindow)},computed:{...Object(l["c"])("common",["appName"]),...Object(l["c"])("category",["currentCategory"]),isWpp(){return"wpp"==this.appName},listReqestParams(){return{app:o["a"].convertAppName(r["e"].name),verison_type:1,platform_type:1,id:this.pageId||0,limit:this.pageSize,offset:this.pageOffset,page_type:this.pageType,request_id:this.request_id,bj_params:this.bj_params,resource_params:this.resource_params}},classReqestParams(){return{app:o["a"].convertAppName(r["e"].name),verison_type:1,platform_type:1,id:this.pageId,page_type:this.pageType}},cateList(){var t,e;let a=(null===(t=this.filterBarData)||void 0===t||null===(e=t.node_info)||void 0===e?void 0:e.children)||[];return a.map((t,e)=>{var a;t.label=null===(a=t.props)||void 0===a?void 0:a.title,t.value=e}),a},activeCateItem(){return this.cateList.find(t=>t.value==this.filterParams["activeCateId"])},pageOffset(){return this.list.filter(t=>!!t.id).length},commonCollectParams(){var t,e;return{$m_n:"resource_list",$e_i:{display:10613,click:10614},$track:"",first_cat_id:null===(t=this.activeCateItem)||void 0===t?void 0:t.id,first_cat_name:null===(e=this.activeCateItem)||void 0===e?void 0:e.label,color:this.getSelectedLabel("filter_color"),style:this.getSelectedLabel("filter_style"),payment_type:this.getSelectedLabel("filter_mb_type"),recommend_id:this.request_id,policy:this.policy,category_name:this.currentCategory.name}},bj_params(){var t,e;return"data_resource"!==(null===(t=this.activeCateItem)||void 0===t||null===(e=t.props)||void 0===e?void 0:e.resource_type)?{hdid:r["e"].hdid,bj_position:"pc_new_homepage",bj_type:"mb",useBehavior:!0,freeRecall:String(this.filterParams["activePayId"]||0)}:{}},resource_params(){var t,e,a,i;return"data_resource"==(null===(t=this.activeCateItem)||void 0===t||null===(e=t.props)||void 0===e?void 0:e.resource_type)?[{datasource_id:+(null===(a=this.activeCateItem.props)||void 0===a||null===(i=a.data_resource_config)||void 0===i?void 0:i.datasource_id),new_22_tag_ids:[this.filterParams["activeTagId"],this.filterParams["activeColorId"],this.filterParams["activeBgId"]].filter(t=>!!t),moban_types:[this.filterParams["activePayId"]].filter(t=>!!t)}]:[]}},methods:{...Object(l["b"])("common",["setAppName"]),init(t=!1){this.initList(t),this.initFilter()},initList(t){this.statusCode="",this.maxCount=0,this.list=[{isNewDoc:!0}].concat(Array(this.pageSize-1).fill({})),this.request_id=o["a"].getRandomKey(),this.fetchListData(this.listReqestParams).then(e=>{this.renderListData(e,t)}).catch(t=>{this.renderListData(null)})},initFilter(){this.fetchClassData(this.classReqestParams).then(t=>{this.renderClassData(t)}).catch(t=>{this.renderClassData(null)})},checkListData(t){var e,a,i;return t&&"ok"===t.result&&(null===(e=t.data)||void 0===e?void 0:e.data)&&Array.isArray(null===(a=t.data)||void 0===a?void 0:a.data)&&(null===(i=t.data)||void 0===i?void 0:i.data.length)},checkClassData(t){var e;return t&&"ok"===t.result&&t.data&&(null===(e=t.data)||void 0===e?void 0:e.node_info)},renderListData(t,e){var a;if(this.checkListData(t)){var i,s;let e=(null===t||void 0===t||null===(i=t.data)||void 0===i?void 0:i.data)||[];this.list=[{isNewDoc:!0}].concat(e),this.maxCount=null===(s=t.data)||void 0===s?void 0:s.count,this.statusCode="",console.warn("renderListData: "+JSON.stringify(t))}else this.maxCount=0,this.list=[{isNewDoc:!0}],this.statusCode=t?r["s"].empty:r["s"].error;this.policy=(null===t||void 0===t||null===(a=t.data)||void 0===a?void 0:a.strategy)||"";let l=this.statusCode==r["s"].error?"fail":"success";e&&this.$_collectLoadTime(l),m["a"].send("display",{$e_t:"module",resource_count:this.maxCount,...this.commonCollectParams})},renderClassData(t){console.warn("renderClassData: "+JSON.stringify(t)),this.checkClassData(t)?this.filterBarData=t.data:this.filterBarData={}},handleLoadData(){this.isInterfaceLoading||(this.isInterfaceLoading=!0,this.fetchListData(this.listReqestParams).then(t=>{this.checkListData(t)?this.list=this.list.concat(t.data.data):this.maxCount=0}).catch(t=>{this.maxCount=0}).finally(()=>{}).finally(()=>{this.isInterfaceLoading=!1}))},fetchListData(t){return console.log("--列表请求参数--",this.listReqestParams),c["a"].get("personalGateway")["pageList"]({data:t})},fetchClassData(t){return console.log("--筛选器请求参数--",this.classReqestParams),c["a"].get("personalGateway")["classList"]({data:t})},handleChange(t,e){var a;this.resetScroll(),"activeCateId"==t&&Object.keys(this.filterParams).forEach(t=>{"activeCateId"!==t&&(this.filterParams[t]="")}),this.filterParams[t]=e,this.pageId=null===(a=this.filterBarData)||void 0===a?void 0:a.page_id,this.initList()},routeInit(t){this.resetPageStatus(),this.resetScroll(),this.pageId=+(null===t||void 0===t?void 0:t.id)||0,this.pageType=t.pageType||""},resetPageStatus(){this.statusCode="",this.maxCount=0,this.isInterfaceLoading=!1,Object.keys(this.filterParams).forEach(t=>{this.filterParams[t]=""}),this.pageSize=0,this.list=[],this.filterBarData={},this.policy="",this.request_id=""},scrollHome(){var t;let e=null===(t=this.$refs.rMainContentCon)||void 0===t?void 0:t.scrollTop;if(0==e&&(this.isScroll=!1),this.isScroll||!this.cateList.length)return;let a=this.$refs.filterBar.$el,i=document.querySelector(".screen__container");e>0?(this.isFixed=!0,a.style.top=e+"px",a.style.padding=`0.5rem calc(50% - ${i.offsetWidth/2}px) 0.3rem`):(this.isFixed=!1,a.style.top=0,a.style.padding=0)},resetScroll(){let t=this.$refs&&this.$refs.rMainContentCon;t&&(t.scrollTop=0),this.isFixed=!1;let e=this.$refs.filterBar.$el;e&&(e.style.top=0,e.style.padding=0)},ajustTplLayout(){let[t,e]=o["a"].getScreenDisplay(this.$refs.rMainContentCon);this.limit!=e&&(this.limit=e),this.pageSize||(this.pageSize=t*e||30)},getSelectedLabel(t){var e,a,i;let s=null===(e=this.activeCateItem)||void 0===e||null===(a=e.props)||void 0===a?void 0:a[t];if(null!==s&&void 0!==s&&null!==(i=s.options)&&void 0!==i&&i.length&&null!==s&&void 0!==s&&s.state){var r;let e={filter_color:"activeColorId",filter_style:"activeTagId",filter_mb_type:"activePayId"}[t];return null===(r=s.options.find(t=>t.value==this.filterParams[e]))||void 0===r?void 0:r.label}return""}},components:{FilterBar:n["a"],List:d["a"],DataStatus:h["a"]}},v=f,g=(a("29c1"),a("2877")),y=Object(g["a"])(v,i,s,!1,null,"9c6a9faa",null);e["default"]=y.exports},cd30:function(t,e,a){}}]);