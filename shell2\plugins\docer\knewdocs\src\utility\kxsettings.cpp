﻿#include "stdafx.h"
#include "kxsettings.h"
#include <kprometheus/kpromeapplication.h>

#define KNEWDOCS_COMMON			"common"
#define KNEWDOCS_PLUGINS		"plugins"

KxCommonSettings::KxCommonSettings()
	: QSettings(QSettings::NativeFormat, QSettings::UserScope,
		promeApp->organizationName(), promeApp->productName())
{
	beginGroup(promeApp->productVersion());
	beginGroup(KNEWDOCS_COMMON);
}

KxPluginsSettings::KxPluginsSettings()
	: QSettings(QSettings::NativeFormat, QSettings::UserScope,
		promeApp->organizationName(), promeApp->productName())
{
	beginGroup(promeApp->productVersion());
	beginGroup(KNEWDOCS_PLUGINS);
}

KxNewDocsSettings::KxNewDocsSettings()
{
	beginGroup(KPluginName);
}
