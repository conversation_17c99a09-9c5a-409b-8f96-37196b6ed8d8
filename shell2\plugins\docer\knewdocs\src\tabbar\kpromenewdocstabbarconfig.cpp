﻿#include "stdafx.h"
#include "kpromenewdocstabbarconfig.h"
#include "ksolite/kcoreapplication.h"
#include "ksolite/kcachefile.h"
#include "utility/kxsettings.h"
#include "utility/knewdocshelper.h"
#include "ksolite/kappmgr.h"
#include "kprometheus/kpromeapplication.h"
#include "ksolite/kdomainmgr.h"
#include "ksolite/kdcinfoc.h"
#include <kprometheus/kpromeuifilter.h>
#include <kprometheus/kpromecloudsvrproxy.h>
#include <auth/productinfo.h>
#include <kdocertoolkit/helper/kdocercommonhelper.h>
#include "knewdochelper.h"
#include "kdocercorelitehelper.h"
#include "kdoceraccount.h"
#include <kdocertoolkit/helper/kdocercommonhelper.h>
#include <ksolite/kdocercoreitef.h>
#include <ksolite/kpluginmanager.h>
#include "kdocertoolkitlite/kdocerutilslite.h"
#include "include/kxonlinereshelper.h"
#include "ksolite/kplugin.h"
#include <ksolite/ksupportutil.h>
#include <kliteui/klitestyle.h>
#include "kdocercorelitehelper.h"
#include "krt/kconfigmanagercenter.h"

namespace
{
#define LAST_SELECTED_TAB			"lastSelectedTab"
#define CONFIG_INSTANCE_NAME		"__knewdocs_tabbar_config__"
#define LastOnlineConfigMd5		"lastOnlineConfigMd5"

#define StrTimeStamp "timestamp"
#define TabTextTrans "translation"
#define TabType "tabtype"
#define	WebTitle "webTitle"
#define NetWorkReply "reply"
#define NewDocerEntry "entry"

const char* const g_strRecommendPosition = "client_new_nav_tag";
const char* const g_strTabIconName = "icon_name";
const char* const g_strTabIconDefaultName = "icon_default_name";
const char* const g_strTabIconSize = "icon_size";

const qint64 g_enterpriseId = 27049;
const qint64 g_personalId = 27403;

bool setContent(const QString& filePath, QJsonObject& jsonObject)
{

	if (auto fileMgr = kcoreApp->getCacheFileManager())
	{
		QJsonParseError error;
		QByteArray content = fileMgr->getFileContent(filePath);
		jsonObject = QJsonDocument::fromJson(content, &error).object();
		if (jsonObject.isEmpty())
			return false;
	}
	return true;
}

QJsonArray getDefaultConfigData()
{
	auto enableEnterpriseFunc = []()
	{
		if (promeApp->getDomainMgr()->isIntranet() || !krt::kcmc::support("OfficeNav365"))
			return true;

		return false;
	};
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	QJsonObject jsonObject;
	if (!setContent(KNewDocsHelper::getTabbarConfigFilePath(), jsonObject))
		return QJsonArray();
	if (account && account->isCompanyAccount() || enableEnterpriseFunc())
		return jsonObject.value("enterprise").toObject().value(NewDocerEntry).toArray();
	return jsonObject.value("personal").toObject().value(NewDocerEntry).toArray();
}

}

QMetaEnum& KPromeNewDocsTabInfoObj::getTabTypeEnumrator()
{
	static QMetaEnum metaEnum;
	static bool bInited = false;
	if (!bInited)
	{
		bInited = true;
		const QMetaObject& mo = KPromeNewDocsTabInfoObj::staticMetaObject;
		int index = mo.indexOfEnumerator("NewDocTabType");
		Q_ASSERT(index != -1);
		metaEnum = mo.enumerator(index);
	}

	return metaEnum;
}

KPromeNewDocsTabInfoObj::NewDocTabType KPromeNewDocsTabInfoObj::convertTabType(const QString& strTabType)
{
	const QMetaEnum& metaEnum = getTabTypeEnumrator();
	return (KPromeNewDocsTabInfoObj::NewDocTabType)metaEnum.keyToValue(
		strTabType.toLatin1().data());
}

QString KPromeNewDocsTabInfoObj::convertTabType(NewDocTabType type)
{
	const QMetaEnum& metaEnum = getTabTypeEnumrator();
	return metaEnum.valueToKey(type);
}

int KPromeNewDocsTabInfoObj::convertTabIntType(const QString& strTabType)
{
	int intType = KPromePluginWidget::convertWidgetType(strTabType);
	if (intType == INVALID_NEWDOCS_TAB)
		intType = convertTabType(strTabType);
	return intType;
}

QString KPromeNewDocsTabInfoObj::convertTabIntType(int type)
{
	if (type < newdocExtendWidgetTypeBase)
		return KPromePluginWidget::convertWidgetType((KPromePluginWidget::WidgetType)type);
	return convertTabType((NewDocTabType)type);
}

KPromeNewDocsTabInfoObj::KPromeNewDocsTabInfoObj(QObject* parent)
	: QObject(parent)
	, m_tabType(INVALID_NEWDOCS_TAB)
	, m_strTabType(KPromePluginWidget::convertWidgetType(
		(KPromePluginWidget::WidgetType)INVALID_NEWDOCS_TAB))
	, m_relyPluginExit(false)
	, m_bOpenUrl(false)
	, m_bNeedSeparator(false)
{

}

KPromeNewDocsTabInfoObj::~KPromeNewDocsTabInfoObj()
{

}

void KPromeNewDocsTabInfoObj::setUpConfigs(const QJsonObject& jsonObject)
{
	const QMetaObject* mo = this->metaObject();
	const QStringList& keysList = jsonObject.keys();
	for (auto iter = keysList.constBegin(); iter != keysList.constEnd(); ++iter)
	{
		if ((*iter) == "icon_url" || (*iter) == "link" || (*iter) == "iconWps")
			continue;
		QString value = jsonObject.value(*iter).toString();
		if ((*iter) == "text")
			value = tr(value.toUtf8());
		int propIdx = mo->indexOfProperty((*iter).toLatin1());
		if (-1 == propIdx)
			continue;
		QMetaProperty mp = mo->property(propIdx);
		if (!mp.isWritable())
			continue;
		int userType = mp.userType();
		QVariant val;
		switch (userType)
		{
		case QVariant::String:
			val = value;
			break;
		case QVariant::Bool:
			val = parseBool(value);
			break;
		case QVariant::Int:
			val = parseInt(value);
			break;
		case QVariant::Icon:
			val = initIcon(jsonObject, value, val);
			break;
		}
		mp.write(this, val);
	}
}

QVariant& KPromeNewDocsTabInfoObj::initIcon(const QJsonObject& jsonObject, const QString& value, QVariant& varint)
{
	if (value.isEmpty() || jsonObject.isEmpty())
		return varint;

	if (auto appMgr = kcoreApp->getAppMgr())
	{
		auto icon_url = jsonObject.value("icon_url").toString();
		auto icon_name = KxOnlineResHelper::getFileNameFromUrl(icon_url);

		m_defaultIconName = value;
		if (icon_name.isEmpty() || !icon_name.endsWith(".svg"))
		{
#ifndef Q_OS_MACOS
			bool bIsIntranet = false;
			if (kcoreApp && kcoreApp->getDomainMgr())
				bIsIntranet = kcoreApp->getDomainMgr()->isIntranet();
			if (bIsIntranet)
				varint = KDrawHelper::loadIcon(KLiteStyle::dpiScaledSize(QSize(24, 24)), value);
			else
#endif
				varint = KDrawHelper::loadIcon(KLiteStyle::dpiScaledSize(QSize(16, 16)), value);
			return varint;
		}

		m_iconName = icon_name;
		connect(appMgr, &KAppMgr::loadIconFinished, this, &KPromeNewDocsTabInfoObj::loadIcon);
		appMgr->addIconUrl(icon_name, icon_url);
		varint = appMgr->getIcon(icon_name, false);

		if (varint.isNull())
			varint = KDrawHelper::loadIcon(KLiteStyle::dpiScaledSize(QSize(16, 16)), value);
	}
	return varint;
}

void KPromeNewDocsTabInfoObj::loadIcon(const QString& iconName)
{
	if (iconName.isEmpty() || iconName != m_iconName)
		return;

	if (auto appMgr = kcoreApp->getAppMgr())
	{
		m_icon = appMgr->getIcon(iconName, false);
		if (!m_icon.isNull())
			emit updateIconSuccess();
	}
}

QString KPromeNewDocsTabInfoObj::relyPlugin()
{
	return m_relyPlugin;
}

void KPromeNewDocsTabInfoObj::setRelyPluginExit(bool b)
{
	m_relyPluginExit = b;
}

bool KPromeNewDocsTabInfoObj::isRelyPluginExit()
{
	return m_relyPluginExit;
}

void KPromeNewDocsTabInfoObj::onRelyPluginLoaded()
{
	m_relyPluginExit = true;
	KxLoggerLite::writeInfo(QString(KPluginName).toStdWString(),
		QString("%1  KPlugin load plugin: %2").arg(__FUNCTION__).arg(m_relyPlugin).toStdWString());
	emit relyPluginExist(true);
}

void KPromeNewDocsTabInfoObj::onReplyPluginLoadfailed()
{
	m_relyPluginExit = false;
	KxLoggerLite::writeInfo(QString(KPluginName).toStdWString(),
		QString("%1  KPlugin load plugin: %2").arg(__FUNCTION__).arg(m_relyPlugin).toStdWString());
	emit relyPluginExist(false);
}

void KPromeNewDocsTabInfoObj::setRelyPlugin(const QString& relyPlugin)
{
	m_relyPlugin = relyPlugin;
}

bool KPromeNewDocsTabInfoObj::isValid() const
{
	return m_tabType != INVALID_NEWDOCS_TAB;
}

QString KPromeNewDocsTabInfoObj::id() const
{
	return m_id;
}

void KPromeNewDocsTabInfoObj::setId(const QString& id)
{
	m_id = id;
}

QString KPromeNewDocsTabInfoObj::webTitleText() const
{
	return m_webTitleText;
}

void KPromeNewDocsTabInfoObj::setWebTitleText(const QString& webTitleText)
{
	m_webTitleText = webTitleText;
}

QString KPromeNewDocsTabInfoObj::text() const
{
	return m_text;
}

void KPromeNewDocsTabInfoObj::setText(const QString& text)
{
	m_text = text;
}

QString KPromeNewDocsTabInfoObj::styleName() const
{
	return m_styleName;
}

void KPromeNewDocsTabInfoObj::setStyleName(const QString& styleName)
{
	if (styleName.isEmpty())
		return;

	m_styleName = styleName;
}

QIcon KPromeNewDocsTabInfoObj::icon() const
{
	return m_icon;
}

void KPromeNewDocsTabInfoObj::setIcon(const QIcon& icon)
{
	m_icon = icon;
}

QString KPromeNewDocsTabInfoObj::collect() const
{
	return m_collect;
}

void KPromeNewDocsTabInfoObj::setCollect(const QString& collect)
{
	m_collect = collect;
}

bool KPromeNewDocsTabInfoObj::isOpenUrl() const
{
	return m_bOpenUrl;
}

void KPromeNewDocsTabInfoObj::setIsOpenUrl(bool b)
{
	m_bOpenUrl = b;
}

bool KPromeNewDocsTabInfoObj::isNeedSeparator() const
{
	return m_bNeedSeparator;
}

void KPromeNewDocsTabInfoObj::setIsNeedSeparator(bool b)
{
	m_bNeedSeparator = b;
}

bool KPromeNewDocsTabInfoObj::waitJs() const
{
	return m_waitJs;
}

void KPromeNewDocsTabInfoObj::setWaitJs(bool b)
{
	m_waitJs = b;
}

QString KPromeNewDocsTabInfoObj::openUrl() const
{
	return m_openUrl;
}

void KPromeNewDocsTabInfoObj::setOpenUrl(const QString& s)
{
	m_openUrl = s;
}

QString KPromeNewDocsTabInfoObj::tabType()
{
	return m_strTabType;
}

void KPromeNewDocsTabInfoObj::setTabType(const QString& tabType)
{
	m_strTabType = tabType;
	m_tabType = convertTabIntType(tabType);
}

int KPromeNewDocsTabInfoObj::getTabType()
{
	return m_tabType;
}

QVariantMap KPromeNewDocsTabInfoObj::getPageTabIconInfo() const
{
	bool bIsIntranet = false;
	QVariantMap mapIconInfo;
	mapIconInfo[g_strTabIconName] = m_iconName;
	mapIconInfo[g_strTabIconDefaultName] = m_defaultIconName;

	if (kcoreApp && kcoreApp->getDomainMgr())
		bIsIntranet = kcoreApp->getDomainMgr()->isIntranet();
	if (bIsIntranet)
		mapIconInfo[g_strTabIconSize] = QSize(24, 24);
	else
		mapIconInfo[g_strTabIconSize] = QSize(16, 16);

	return mapIconInfo;
}

QIcon KPromeNewDocsTabInfoObj::parseIcon(const QString& val)
{
	return KDrawHelper::loadIcon(val);
}

bool KPromeNewDocsTabInfoObj::parseBool(const QString& val)
{
	if (val.isEmpty())
		return false;
	bool bInt = false;
	int x = val.toInt(&bInt);
	if (bInt && x == 0)
		return false;
	if (val.trimmed().toLower() == "false")
		return false;
	return true;
}

int KPromeNewDocsTabInfoObj::parseInt(
	const QString& val, bool * isSucceed /*= NULL*/)
{
	bool bInt = false;
	int x = val.toInt(&bInt);
	if (!bInt)
	{
		if (isSucceed)
			*isSucceed = false;
		return -1;
	}
	if (isSucceed)
		*isSucceed = true;
	return x;
}

//////////////////////////////////////////////////////////////////////////

KPromeNewDocsTabBarConfig* KPromeNewDocsTabBarConfig::getInstance()
{
	QVariant v = qApp->property(CONFIG_INSTANCE_NAME);
	if (v.value<QObject*>() == nullptr)
	{
		KPromeNewDocsTabBarConfig* pInstance = new KPromeNewDocsTabBarConfig(qApp);
		qApp->setProperty(CONFIG_INSTANCE_NAME,
			QVariant::fromValue(static_cast<QObject*>(pInstance)));
		pInstance->init();
		connect(qApp, SIGNAL(aboutToQuit()), pInstance, SLOT(onAppAboutToQuit()));
		KCloudSvrProxy* pCloudSvrProxy = kcoreApp->cloudService();
		if (pCloudSvrProxy)
			QObject::connect(pCloudSvrProxy, &KCloudSvrProxy::loginStatusChanged, pInstance, &KPromeNewDocsTabBarConfig::onLoginChangedUpDateData);

		return pInstance;
	}
	return qobject_cast<KPromeNewDocsTabBarConfig*>(v.value<QObject*>());
}

QList<KPromeNewDocsTabInfoObj*> KPromeNewDocsTabBarConfig::getTabInfoList()
{
	return m_tabInfoList;
}

KPromeNewDocsTabBarConfig::KPromeNewDocsTabData KPromeNewDocsTabBarConfig::getTabInfo() const
{
	return m_tabGroupInfoList;
}

KPromeNewDocsTabInfoObj* KPromeNewDocsTabBarConfig::getInfoObj(int tabType)
{
	auto lstInfoObjs = getTabInfoList();
	foreach(auto infoObj, lstInfoObjs)
	{
		if (infoObj->getTabType() == tabType)
			return infoObj;
	}
	return nullptr;
}

KPromeNewDocsTabInfoObj* KPromeNewDocsTabBarConfig::getInfoObjById(const QString& idTab)
{
	auto lstInfoObjs = getTabInfoList();
	foreach(auto infoObj, lstInfoObjs)
	{
		if (infoObj->id() == idTab)
			return infoObj;
	}
	return nullptr;
}

int KPromeNewDocsTabBarConfig::getLastSelectedTab()
{
	return m_lastSelectedTab;
}

void KPromeNewDocsTabBarConfig::updateLastSelectedTab(int tabType)
{
	if (tabType != KPromePluginWidget::pageMytpl)
		m_lastSelectedTab = tabType;
}

void KPromeNewDocsTabBarConfig::sendDisplayDcInfo(KPromeNewDocsTabInfoObj* obj)
{
	if(obj && !obj->text().isEmpty())
	{
		QHash<QString, QString> content;
		content["workboard_id"] = obj->id();
		content["workboard_name"] = obj->text();
		KDocerUtils::postGeneralEvent("docer_newtabs_display", content);
	}
}

void KPromeNewDocsTabBarConfig::sendClickedDcInfo(KPromeNewDocsTabInfoObj* obj)
{
	if(obj && !obj->text().isEmpty())
	{
		QHash<QString, QString> content;
		content["workboard_id"] = obj->id();
		content["workboard_name"] = obj->text();
		KDocerUtils::postGeneralEvent("docer_newtabs_click", content);
	}
}

void KPromeNewDocsTabBarConfig::onAppAboutToQuit()
{
	saveLastSelectedTab();
}

void KPromeNewDocsTabBarConfig::onAppMgrLoadFinished()
{
	foreach(KPromeNewDocsTabInfoObj* infoObj, m_tabInfoList)
	{
		if (!infoObj->relyPlugin().isEmpty() && !infoObj->isRelyPluginExit())
		{
			KAppObj* appObj = KAppMgr::getInstance()->getApp(infoObj->relyPlugin());
			if(!appObj || !appObj->checkIsCanLoadInLocal())
			{
				infoObj->setRelyPluginExit(false);
			}
			else
			{
				infoObj->setRelyPluginExit(true);
			}
			if (appObj && !infoObj->isRelyPluginExit())
			{
				appObj->load(KAppObj::Load_Manual);
				connect(appObj, SIGNAL(loadSuccess()), infoObj, SLOT(onRelyPluginLoaded()));
			}
		}
	}
}

void KPromeNewDocsTabBarConfig::init()
{
	if (m_bInited)
		return;

	m_bInited = true;
	auto pDocerdocer = getIKDocerCoreLite();
	QJsonArray config;
	if (pDocerdocer && pDocerdocer->isFpcInitFinished())
	{
		config = getFpcCombData().value(NewDocerEntry).toArray();
	}
	if (!config.isEmpty())
	{
		KxLoggerLite::writeInfo(QString(KPluginName).toStdWString(),
			QString("[%1] : knewdocstab information is initialized by FpccomData").arg(__FUNCTION__).toStdWString());
		setUpConfigs(config);
	}
	else
	{
		KxLoggerLite::writeInfo(QString(KPluginName).toStdWString(),
			QString("[%1] : knewdocstab information is initialized by default data").arg(__FUNCTION__).toStdWString());
		setUpConfigs(getDefaultConfigData());
	}

	initLastSelectedTab();
}

void KPromeNewDocsTabBarConfig::onLoginChangedUpDateData()
{
	IKDocerAccount* account = nullptr;
	auto kdocercoreLite = getIKDocerCoreLite();
	if (kdocercoreLite)
		account = kdocercoreLite->getIKDocerAccount();
	bool isLogined = false;
	if (account)
		isLogined = account->isLogined();
	if (isLogined)
	{
		if (account && !account->isCompanyAccount())
		{
			if (getLastSelectedTab() == KPromeNewDocsTabInfoObj::pageQuickCreate)
				m_lastSelectedTab = KPromePluginWidget::pageMytpl;
			else if (getLastSelectedTab() == KPromeNewDocsTabInfoObj::pageKSheet)
				m_lastSelectedTab = KPromeNewDocsTabInfoObj::pageKOtl;
		}
		updateNewEntryData();
	}
	else if (account && !account->isSwitching())
	{
		if (getLastSelectedTab() == KPromeNewDocsTabInfoObj::pageQuickCreate)
		{
			m_lastSelectedTab = KPromePluginWidget::pageMytpl;
		}
		updateNewEntryData();
	}
}

void KPromeNewDocsTabBarConfig::updateNewEntryData()
{
	QJsonArray config = getFpcCombData().value(NewDocerEntry).toArray();
	if (config.isEmpty())
	{
		config = getDefaultConfigData();
	}
	setUpConfigs(config);
}

void KPromeNewDocsTabBarConfig::onAppManagerLoadFinished()
{
	if(KPluginManager::getInstance()->isLoadFinished())
	{
		foreach(auto infoObj, m_tabInfoList)
		{
			if(!infoObj->relyPlugin().isEmpty())
			{
				configuredRelyPlugin(infoObj);
			}
		}
	}
}

void KPromeNewDocsTabBarConfig::initLastSelectedTab()
{
	KxNewDocsSettings newfileSettings;
	QString type = newfileSettings.value(
		LAST_SELECTED_TAB, "").toString();

	m_lastSelectedTab = KPromeNewDocsTabInfoObj::INVALID_NEWDOCS_TAB;
	if (!type.isEmpty())
	{
		int lastTabType = KPromeNewDocsTabInfoObj::convertTabIntType(type);
		foreach(auto infoObj, m_tabInfoList)
		{
			if (infoObj->getTabType() == lastTabType)
			{
				if (!infoObj->relyPlugin().isEmpty())
				{
					if (infoObj->isRelyPluginExit())
						m_lastSelectedTab = lastTabType;
				}
				else
				{
					m_lastSelectedTab = lastTabType;
				}
				break;
			}
		}
	}
}

void KPromeNewDocsTabBarConfig::saveLastSelectedTab()
{
	int type = m_lastSelectedTab;

	if (type != KPromePluginWidget::pageMytpl)
	{
		KxNewDocsSettings newfileSettings;
		newfileSettings.setValue(LAST_SELECTED_TAB, KPromeNewDocsTabInfoObj::convertTabIntType(type));
	}
}

KPromeNewDocsTabBarConfig::KPromeNewDocsTabBarConfig(QObject* parent)
	: QObject(parent)
	, m_bInited(false)
	, m_lastSelectedTab(KPromeNewDocsTabInfoObj::INVALID_NEWDOCS_TAB)
{
}

KPromeNewDocsTabBarConfig::~KPromeNewDocsTabBarConfig()
{
}

void KPromeNewDocsTabBarConfig::setUpConfigs(const QJsonArray& jsonArray)
{
	if (jsonArray.isEmpty())
		return;
	m_tabGroupInfoList.clear();
	for (auto iter = jsonArray.constBegin(); iter != jsonArray.constEnd(); ++iter)
		parseEntryGroup(iter->toObject());
}

QJsonObject KPromeNewDocsTabBarConfig::getFpcCombData()
{
	auto pDocerdocer = getIKDocerCoreLite();
	if (pDocerdocer)
	{
		auto account = pDocerdocer->getIKDocerAccount();
		if (account && account->isCompanyAccount())
			return pDocerdocer->getFpcCombJsonObject(g_enterpriseId);
		else
			return pDocerdocer->getFpcCombJsonObject(g_personalId);;
	}
	return QJsonObject();
}

void KPromeNewDocsTabBarConfig::parseEntryGroup(const QJsonObject& jsonObject)
{
	if (jsonObject.isEmpty())
		return;
	QString groupName = jsonObject.value("groupName").toString();
	if (groupName.isEmpty())
		return;
	QJsonArray entryArray = jsonObject.value("entry").toArray();
	if (entryArray.isEmpty())
		return;
	KPromeUiFilter uiFilter;
	QList<KPromeNewDocsTabInfoObj*> groupEntryList;

	for (auto entry = entryArray.constBegin(); entry != entryArray.constEnd(); ++entry)
	{
		QJsonObject entryObject = entry->toObject();
		if (!uiFilter.isThroughFilter(entryObject.value("filter").toString()))
		{
#ifdef X_LINUX_DESKTOP
			QString tabType = entryObject.value("tabType").toString();
			if (tabType.isEmpty() || KPromePluginWidget::widgetProcesson_Mind != KPromeNewDocsTabInfoObj::convertTabIntType(tabType))
				continue;
#else
			continue;
#endif
		}
		QString id = entryObject.value("id").toString();
		if (id.isEmpty())
			continue;

		QString tabType = entryObject.value("tabType").toString();
		if(tabType.isEmpty() || KPromePluginWidget::invalidWidgetType == KPromeNewDocsTabInfoObj::convertTabIntType(tabType))
			continue;
	
		KPromeNewDocsTabInfoObj* infoObj = nullptr;
		if (m_tabIdMap.contains(id))
			infoObj = m_tabIdMap.value(id);
		else
			infoObj = new KPromeNewDocsTabInfoObj(this);
		infoObj->setUpConfigs(entryObject);
		if (!infoObj->isValid())
		{
			delete infoObj;
			continue;
		}

		m_tabIdMap.insert(id, infoObj);
		groupEntryList.append(infoObj);
		m_tabInfoList.append(infoObj);
	}
	m_tabGroupInfoList.append(qMakePair(groupName, groupEntryList));
}

void KPromeNewDocsTabBarConfig::configuredRelyPlugin(KPromeNewDocsTabInfoObj* infoObj)
{
	if (nullptr == infoObj)
		return;

	bool bPluginExist = KDocerUtils::isPluginCanUse(infoObj->relyPlugin());
	infoObj->setRelyPluginExit(bPluginExist);
	if (bPluginExist)
		return;

	KPlugin* pPlugin = nullptr;
	if(auto pPlgMgr = KPluginManager::getInstance())
		pPlugin = pPlgMgr->getPlugin(infoObj->relyPlugin());
		
	if (nullptr != pPlugin)
	{
		KxLoggerLite::writeInfo(QString(KPluginName).toStdWString(),
			QString("%1  KPlugin start to load plugin: %2").arg(__FUNCTION__).arg(infoObj->relyPlugin()).toStdWString());
		pPlugin->load(KPlugin::LoadOption::Load_Normal, KPlugin::LoadMode::Load_Manual);
		connect(pPlugin, &KPlugin::loadSuccess, infoObj, &KPromeNewDocsTabInfoObj::onRelyPluginLoaded);
		connect(pPlugin, &KPlugin::loadFailed, infoObj, &KPromeNewDocsTabInfoObj::onReplyPluginLoadfailed);
	}
	else
	{
		KxLoggerLite::writeInfo(QString(KPluginName).toStdWString(),
			QString("%1 KPlugin load plugin: %2 failed").arg(__FUNCTION__).arg(infoObj->relyPlugin()).toStdWString());
		infoObj->onReplyPluginLoadfailed();
	}
}
