const getUrlParam = function(name) {
	var reg = new RegExp('(^|\\?|&)' + name + '=([^&]*)(\\s|&|$)', 'i')
	if (reg.test(location.href)) {
		return decodeURIComponent(RegExp.$2.replace(/\+/g, ' '))
	}
	return ''
}

window.WebVitalsWithClient && new window.WebVitalsWithClient({
	bussiness: 'docer_knewdocx',
	probability: 1000,
	pluginName: 'knewdocx',
	extra_attr_1: getUrlParam('app') || 'wps',
	complete: (perfResult) => {
		window.perfLcp = perfResult.lcp
	}
})