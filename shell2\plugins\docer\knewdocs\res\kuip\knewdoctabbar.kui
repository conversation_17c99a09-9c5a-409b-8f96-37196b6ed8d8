<?xml version="1.0" encoding="UTF-8"?>
<KNewdocsTabbar tc="KPromeNewDocsTabInfoObj" component="knewdocs">
	<KNewDocsTab id="tabMytpl" text="@MyTemplate" tabType="pageMytpl" collect="wps"
		icon="newdoc_mytemplate" styleName="KNewDocs-Tab-Mytpl" isNeedSeparator="true" />

	<KNewDocsTab id="tabWps" text="@Document" tabType="pageDocerWps" collect="wps"
		icon="newdoc_wps" styleName="KNewDocs-Tab-Document" />

	<KNewDocsTab id="tabEt" text="@Excel" tabType="pageDocerEt" collect="et"
		icon="newdoc_et"  styleName="KNewDocs-Tab-Excel" />

	<KNewDocsTab id="tabWpp" text="@Presentation" tabType="pageDocerWpp" collect="wpp"
		icon="newdoc_wpp" styleName="KNewDocs-Tab-Presentation" />

	<KNewDocsTab id="tabPdf" text="@PDF" tabType="pageDocerPdf" collect="pdf"
		icon="newdoc_pdf"  styleName="KNewDocs-Tab-PDF" />

	<KNewDocsTab id="tabKDocs" text="@KDocs" tabType="newdocWidgetDocerKDocs" collect="kdocs"
		icon="newdoc_kdocs"  styleName="KNewDocs-Tab-KDocs" isNeedSeparator="true" waitJs="false"/>

	<KNewDocsTab id="tabChuangkit" text="@WPS ChuangKit" tabType="widgetChuangKit" collect="chuangkit"
		icon="newdoc_chuangkit"  styleName="KNewDocs-Tab-ChuangKit" />

	<KNewDocsTab id="tabFlowChart" text="@WPS Flow Chart" tabType="widgetProcesson_Flow" relyPlugin="kpromeprocesson" collect="flowchart"
		icon="newdoc_processon"  styleName="KNewDocs-Tab-ProcessOnFlow" />

	<KNewDocsTab id="tabMindMap" text="@WPS Mind Map" tabType="widgetProcesson_Mind" relyPlugin="kpromeprocesson" collect="mindmap" idKey="CommonTabMindMap"
		icon="newdoc_mindmap" styleName="KNewDocs-Tab-ProcessOnMind" />

	<KNewDocsTab id="tabFlowChartLocal" text="@WPS Flow Chart" icon="newdoc_processon" tabType="widgetProcessonlocal_Flow" styleName="Flow" collect="flowchart" filter="support:FlowMind=true"/>
	<KNewDocsTab id="tabMindMapLocal" text="@WPS Mind Map" icon="newdoc_mindmap" tabType="widgetProcessonlocal_Mind" styleName="Mind" collect="mindmap" filter="support:FlowMind=true"/>

	<KNewDocsTab id="tabForm" text="@WPS Form" tabType="widgetWpsForm" relyPlugin="kpromeform" collect="wpsform"
		icon="newdoc_wpsform" styleName="KNewDocs-Tab-WPSForm" isNeedSeparator="true"/>

	<KNewDocsTab id="tabCloudFolderShare" text="@WPS Folder Share" tabType="widgetCloudFolderShare" collect="cloudfoldershare"
		icon="newdoc_cloudfoldershare" styleName="KNewDocs-Tab-CloudShareFolder" />

	<KNewDocsTab id="tabApp" text="@New App" relyPlugin="kwpsnewapp" tabType="widgetNewApp" collect="wpsnewapp"
		icon="newdoc_wpsnewapp" styleName="KNewDocs-Tab-NewApp" />
</KNewdocsTabbar>
