(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[3],{"0252":function(t,e,i){"use strict";i("e528")},"0fda":function(t,e,i){},"19d7":function(t,e,i){"use strict";i("cbb7")},"267c":function(t,e,i){t.exports=i.p+"images/wps.png"},"2a4c":function(t,e,i){"use strict";i("da2b")},3119:function(t,e,i){"use strict";i("f016")},"3a9b":function(t,e,i){t.exports=i.p+"images/wpp.png"},"40cb":function(t,e,i){},"455d":function(t,e,i){"use strict";i("9c76")},"4a78":function(t,e,i){"use strict";var a=i("13bc"),s=i("c8fc"),l=i("2f62"),r=i("beeb");const c=i("267c"),n=i("3a9b"),o=i("db6c"),h=function(t){if(t){var e=r["u"][t];if(e){"?"==e.slice(-1)?r["u"].appUrl=e.slice(0,-1):r["u"].appUrl=e;var i=e.indexOf("v3.php/");r["u"].privateUrl=-1!=i?e.slice(0,i+7):r["u"].appUrl.slice(0,-4),r["u"].isSupportIntranetTpl=!0}}},p=function(){a["a"].IsSupportIntranetTemplate().then(t=>{t&&a["a"].getCreateDocUrl().then(t=>{t&&(r["u"].wps=t.url_wps,r["u"].et=t.url_et,r["u"].wpp=t.url_wpp,h(r["e"].name))}).catch(t=>{console.log(t)})}).catch(t=>{console.warn(t)})};p(),e["a"]={computed:{...Object(l["c"])("common",["isEnterprise"])},data:()=>({localTplData:[],customTplInfo:[],customTplData:[],recentlyData:[]}),methods:{_getDefaultPreviewImgSrc(){switch(r["e"].name){case"wps":return c;case"wpp":return n;case"et":return o;default:return n}},_loadMoreData(t,e,i){if(r["u"].isSupportIntranetTpl)return this._getPrivateTplByTag(t,e,i)},_getRecentlyOpenFile(){this._getFile().then(t=>{this.recentlyData=t},()=>{console.error("获取最近打开文档失败")})},_getTplData(){return r["u"].isSupportIntranetTpl?this._getPrivateTpl():this._getLocalTpl()},_getFile(){return new Promise((t,e)=>{a["a"].getRecentFiles({maxCount:-1}).then(e=>{e&&t(e),t([])}).catch(t=>{e(t)})})},_getLocalRecentTpl(t){return a["a"].getRecentUseTemplate({appName:t})},_removeRecentTemplate(t){return a["a"].removeRecentTemplate({templatePath:t})},_getLocalTpl(){return a["a"].getLocalTemplateFileInfo({appName:r["e"].name}).then(t=>(Array.isArray(t)&&t.forEach(t=>{Array.isArray(t.fileList)?t.total_num=t.fileList.length:t&&(t.total_num=0)}),t))},_getCustomTpl(){return a["a"].getCustomTemplateFileInfo({appName:r["e"].name})},_getPrivateTags(){return new Promise(t=>{s["a"].get("pritag").getPrivateTags({data:{mb_app:r["e"].mark},privateUrl:{url:r["u"].privateUrl}}).then(e=>{if("ok"===e.result){let a=0;var i=[];e.data.forEach(t=>{t[0].item.forEach(t=>{if(a++,a>3)return!1;i.push(t)})}),t(i)}})})},_getPrivateTplByTag(t,e=1,i=10){return new Promise(a=>{s["a"].get("pritpl").getPriTplByTags({data:{term:t.k,sort_by:"mix",moban_type:"",sort_way:"down",limit:i,page:e,vip:1},privateUrl:{url:r["u"].appUrl}}).then(e=>{var i={tagName:t.v};"ok"===e.result&&e.data&&0!==e.data.total_num?(i.fileList=e.data.data,i.total_num=e.data.total_num):(i.fileList=[],i.total_num=0),a(i)})})},_getPrivateTpl(){this.localTplData=[];let t=[];return this._getPrivateTags().then(e=>(e.forEach(i=>{t.push(this._getPrivateTplByTag(i).then(t=>{e.forEach(e=>{e.v==t.tagName&&(e.tagName=t.tagName,e.fileList=t.fileList)})}))}),Promise.all(t).then(()=>(this.localTplData=e,e))))},_getCustomOfficialTemplate(){this.customData=[],a["a"]._getCustomOfficialTemplate().then(t=>{t&&t.length>0&&(console.log("自定义模板：",t),this.customData=t)})},_searchPrivateTplData(t,e,i){return s["a"].get("pritpl").searchPriTpl({data:{term:t,page:e,is_push:1,limit:i,vip:1},privateUrl:{url:r["u"].appUrl}}).then(t=>("ok"===t.result&&t.data&&Array.isArray(t.data.moban)&&(t.data.fileList=t.data.moban),t.data))},_searchlocalTplData(t){return a["a"].localTemplateSearch({keyWord:t,appName:r["e"].name}).then(t=>(Array.isArray(t.fileList)?t.total_num=t.fileList.length:t&&(t.total_num=0),t))},_searchTplDataFn(t,e,i){return r["u"].isSupportIntranetTpl?this._searchPrivateTplData(t,e,i):this._searchlocalTplData(t)},_localSearch(t,e,i){return this._searchTplDataFn(t,e,i).then(t=>(console.log("_localSearch: "+JSON.stringify(t)),t))},_formartPrevieImage(t){const e=t=>{t.thumb_img="data:image/png;base64,"+t.preview};return Array.isArray(t)?t.forEach(t=>{t.preview&&e(t)}):t&&t.preview&&e(t),t}}}},"4da1":function(t,e,i){},"5a29":function(t,e,i){"use strict";i("ae7f")},"63f5":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"search search__container"},[e("header",{staticClass:"search__top l-d-flex l-align-items-center l-justify-content-between"},[e("div",{staticClass:"l-d-flex l-align-items-center search__top--box",style:t.bodyStyle},[e("Crumbs",{attrs:{router:t.homeRouter},on:{collectHomeClick:t.$_search_collect_homeClick}}),e("span",{staticClass:"search__result"},[t._v("找到"+t._s(t.totalNum)+'份 "'+t._s(t.word)+'" '),e("em",[t._v(t._s("wps"==t.appName?"文字":"et"==t.appName?"表格":"演示")+"模板")])])],1)]),e("div",{directives:[{name:"stick",rawName:"v-stick"}],ref:"nSearchList",staticClass:"search__content"},[e("div",{staticClass:"search__body",style:t.bodyStyle},[e("SearchFilter",{ref:"sfComp",attrs:{sortFilterKey:t.orderbyKey,sort:t.filterSortList,switchList:t.filterSwitchList,tagList:t.filterTagList.slice(0,3),tagTreeList:t.filterTagTreeList,cateList:t.filterCateList,appName:t.appName,isSkeleton:!t.filterCateList.length&&t.statusCode==t.netStatus.loading&&!t.isFilter},on:{report:t.$_search_collect_ftReport,catChange:t.filterCatChange,change:t.filterChange}}),e("main",{staticClass:"search__list"},[t.statusCode?e("DataStatus",{attrs:{statusCode:t.statusCode,hideapp:t.appName,word:t.word,isScreen:!0},on:{retry:t.onRetry}}):t._e(),e("List",{attrs:{list:t.data,limit:t.limit,maxCount:t.totalNum,pageSize:t.pageSize,commonCollectParams:t.$_search_commonCollectParams},on:{loadMore:t.loadMore}})],1)],1)])])},s=[],l=function(){var t=this,e=t._self._c;return t.totalPage&&t.totalPage>1?e("div",[e("div",{staticClass:"m-pagination"},[e("a",{staticClass:"m-pagination_btn",class:{disabled:t.hasPrevPage},attrs:{href:"javascript:void(0) "},on:{click:function(e){return t.prevPage()}}},[e("SvgIcon",{attrs:{svgName:"arrow-left-15",className:"g-font_size-16"}})],1),e("input",{directives:[{name:"model",rawName:"v-model",value:t.inputPage,expression:"inputPage"}],ref:"pageInput",staticClass:"m-pagination_input m-input",attrs:{type:"text"},domProps:{value:t.inputPage},on:{keyup:t.gotoPageByEnter,blur:t.checkInputPage,input:function(e){e.target.composing||(t.inputPage=e.target.value)}}}),e("span",{staticClass:"m-pagination_total"},[t._v("/ "+t._s(t.totalPage))]),e("a",{staticClass:"m-pagination_btn",attrs:{href:"javascript:void(0) "},on:{click:function(e){return t.gotoPage("nav_go")}}},[t._v("GO")]),e("a",{staticClass:"m-pagination_btn",class:{disabled:t.hasNextPage},attrs:{href:"javascript:void(0) "},on:{click:function(e){return t.nextPage()}}},[e("SvgIcon",{attrs:{svgName:"arrow-right-15",className:"g-font_size-16"}})],1)])]):t._e()},r=[],c={data(){return{inputPage:1}},props:["totalPage","page"],created(){this.inputPage=this.page},methods:{gotoPage(t){this.checkInputPage(),this.$emit("gotoPage",this.inputPage,t)},prevPage(){this.page>1&&(this.inputPage=this.page,this.inputPage--,this.gotoPage("nav_prev"))},nextPage(){this.page<this.totalPage&&(this.inputPage=this.page,this.inputPage++,this.gotoPage("nav_next"))},gotoPageByEnter(t){13==t.keyCode&&(this.$refs.pageInput.blur(),this.gotoPage("nav_go"))},checkInputPage(){let t=1;try{t=parseInt(this.inputPage,10)}catch(e){console.log(e)}t=isNaN(t)||t<1||t>this.totalPage?isNaN(this.page)?1:this.page:t,this.inputPage=t}},computed:{hasPrevPage(){return!(this.page>1&&this.page<=this.totalPage)},hasNextPage(){return!(this.page>0&&this.page<this.totalPage)}},watch:{page(){this.inputPage=this.page}}},n=c,o=(i("0252"),i("2877")),h=Object(o["a"])(n,l,r,!1,null,"642529d9",null),p=h.exports,d=i("eff4"),u=i("a4c7"),g=i("2f62"),m=i("beeb"),f=i("f348"),y=i("c8fc"),_=i("a6c6"),v=i("3813"),C=function(){var t=this,e=t._self._c;return e("div",{staticClass:"crumbs"},[e("div",{staticClass:"crumbs-home",on:{click:function(e){return t.goHome()}}},[e("div",{staticClass:"m-radio-icon"},[e("SvgIcon",{staticClass:"crumbs-home_icon",attrs:{svgName:"icon-left"}})],1),e("span",{staticClass:"crumbs-home_label g-pointer"},[t._v("返回")])]),t._l(t.list,(function(i,a){return e("div",{key:a,staticClass:"crumbs-cat"},[t._v(" \\ "),e("a",{on:{click:function(e){return t.handleClick(i)}}},[t._v(t._s(i.title))])])}))],2)},b=[],T={name:"crumbs",props:["from","originFromPage","router","list"],data(){return{}},methods:{goHome(){this.$emit("collectHomeClick"),this.$router.history.go(-1)},handleClick(t){t.emit&&this.$emit(t.emit,t.emitParams)}}},w=T,S=(i("19d7"),Object(o["a"])(w,C,b,!1,null,"f99e72cc",null)),I=S.exports,P=function(){var t=this,e=t._self._c;return e("div",{class:["sf","theme-"+t.appName]},[t.cateList.length?e("div",{staticClass:"sf-body"},[t._l(t.cateList,(function(i,a){return e("CateTree",{key:i.title+a,attrs:{title:i.title,list:i.list||[],filterKey:i.filterKey,elementType:i.reportElementType||i.type,activeId:t.catParams[i.filterKey]||"",index:a},on:{collect:t.report,change:t.cateTreeChange}})})),t._l(t.tagTreeList,(function(i,a){return e("CateTree",{key:i.title+a,attrs:{title:i.title,list:i.list||[],filterKey:i.type||"",elementType:i.reportElementType||i.type,activeId:t.tagActiveMap[i.type]||""},on:{collect:t.report,change:t.tagChange}})})),t.tagList.length?e("div",{ref:"tagGroup",staticClass:"sf-body-bottom"},[1==t.tagList.length&&"color"!=t.firstTagGroup.type?[e("CateTree",{key:t.firstTagGroup.title,attrs:{title:t.firstTagGroup.title,list:t.firstTagGroup.list||[],filterKey:t.firstTagGroup.type||"",elementType:t.firstTagGroup.reportElementType||t.firstTagGroup.type,activeId:t.tagActiveMap[t.firstTagGroup.type]||"",tip:t.firstTagGroup.tip},on:{collect:t.report,change:t.tagChange}})]:t._l(t.tagList,(function(i,a){return e("div",{key:i.type+a,staticClass:"sf-body-tag",class:{"split-lint":a>0&&t.sliceNum>0}},["color"==i.type?e("ColorFilte",{attrs:{index:a,val:i,activeId:t.tagActiveMap[i.type]||"",firstStyle:0==a,sliceNum:t.sliceNum},on:{collect:t.report,change:t.tagChange}}):e("TagFilter",{attrs:{val:i,index:a,activeId:t.tagActiveMap[i.type]||"",firstStyle:0==a,sliceNum:t.sliceNum},on:{collect:t.report,change:t.tagChange}},[i.tip?e("IconTips",{attrs:{tips:i.tip}}):t._e()],1)],1)}))],2):t._e()],2):t._e(),e("div",{staticClass:"sf-bottom"},[e("div",{staticClass:"sf-bottom__set"},[t.sort.length?e("ul",{staticClass:"sf-bottom-ul"},t._l(t.sort,(function(i,a){return e("li",{key:a,staticClass:"sf-bottom-li",class:{"sort-active":t.sortActive.val==i.val},on:{click:function(e){return t.sortChange(a)}}},[t._v(" "+t._s(i.name)+" ")])})),0):t._e(),t._l(t.switchList,(function(i,a){return e("SfSwitch",{key:a,class:{"split-lint":0==a},attrs:{title:i.title,filterKey:i.key,isOpen:t.switchOpenList.includes(i.key)||!1},on:{change:t.switchChange}})}))],2),t.isFilter?e("div",{staticClass:"sf-bottom__reset",on:{click:t.resetAll}},[e("SvgIcon",{attrs:{svgName:"icon-retry"}}),t._v(" 重置筛选项 ")],1):t._e()])])},k=[],L=function(){var t=this,e=t._self._c;return t.list.length&&t.filterKey?e("div",{staticClass:"cate"},[e("div",{staticClass:"cate__title"},[t._v(" "+t._s(t.title)+"： ")]),e("div",{staticClass:"cate__content"},[e("ul",{ref:"fiBox-"+t.filterKey,staticClass:"cate-ul",class:{"height-auto":t.expand}},[e("li",{staticClass:"cate-li",class:{"is-active":!t.activeId},on:{click:function(e){return t.handleClick("")}}},[t._v("全部")]),t._l(t.list,(function(i,a){return e("li",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:t.handleDisplay(i)},expression:"{displayHandler: handleDisplay(item)}",modifiers:{display:!0}}],key:i.id||i,ref:"fiItem",refInFor:!0,staticClass:"cate-li",class:{"is-active":t.activeId==(i.id||i)},attrs:{title:i.name||i},on:{click:function(e){return t.handleClick(i)}}},[e("span",[t._v(t._s(i.name||i))]),a==t.list.length-1?t._t("default"):t._e()],2)}))],2),t.sliceIndex-1<t.list.length?e("div",{staticClass:"cate__more",on:{click:t.expandChange}},[t._v(" "+t._s(t.expand?"收起":"更多")+" "),e("SvgIcon",{class:{"transform-180":t.expand},attrs:{svgName:"arrow-bottom-16"}})],1):t._e()])]):t._e()},N=[],x=i("10de"),$={name:"category-tree",props:{title:{type:String,required:!0},list:{type:Array,default:()=>[]},filterKey:{type:[String,Number],default:""},elementType:{type:[String,Number],default:""},activeId:{type:[String,Number],default:""},index:{type:Number,default:0}},data(){return{expand:!1,sliceIndex:0,calculating:!1,debounce:f["a"].debounce(200)}},mounted(){x["a"].$on(m["p"].resizeWindow,this.ajustTplLayout),this.countSliceIndex()},beforeDestroy(){x["a"].$off(m["p"].resizeWindow,this.ajustTplLayout)},methods:{ajustTplLayout(){this.debounce(()=>{this.countSliceIndex()})},handleClick(t){this.activeId!=(t.id||t)&&this.$emit("change",{key:this.filterKey,val:t.id||t||"",name:t.name||t||"",index:this.index})},handleDisplay(t){return()=>{this.$emit("collect",Object.assign({},{element_name:this.elementType},this.elementType.includes("_cat")?{[this.elementType+"_name"]:t.name,[this.elementType+"_id"]:t.id}:{tag_text:t.name||t}))}},expandChange(){this.expand=!this.expand,this.$emit("collect",{event:"click",element_name:"more",type:this.expand?"more":"retract",content_type:this.elementType})},countSliceIndex(){try{let t=this.$refs&&this.$refs["fiBox-"+this.filterKey]||{},e=t&&t.getBoundingClientRect();if(!t.children||!t.children.length)return void(this.sliceIndex=this.list.length);let i=0,a=0;for(let s of t.children){let t=s.getBoundingClientRect();if(i+=t.width?t.width+8:0,i>e.width)break;a++}this.sliceIndex=a>0?a:this.list.length}catch(e){console.log(e),this.sliceIndex=this.list.length}if(!this.activeId||this.sliceIndex>=this.list.length||this.expand)return;let t=this.list.findIndex(t=>t.id==this.activeId);this.expand=t+1>=this.sliceIndex}},watch:{list:{handler(t,e){this.$nextTick(()=>{JSON.stringify(t)!=JSON.stringify(e)&&this.countSliceIndex()})},immediate:!1}}},D=$,A=(i("9e514"),Object(o["a"])(D,L,N,!1,null,"1442ab3e",null)),O=A.exports,M=function(){var t=this,e=t._self._c;return t.colorList.length?e("div",{staticClass:"cf"},[t.sliceNum>0?[e("div",{staticClass:"cf-title",class:{"max-title":t.firstStyle}},[t._v(t._s(t.val.title)+":")]),e("ul",{staticClass:"cf-ul"},[e("li",{staticClass:"cf-li cf-all sf-tag-li",class:{"is-active":!t.activeId},attrs:{title:"全部"},on:{click:function(e){return t.change("")}}},[e("div",{staticClass:"cf-li-boder"})]),t._l(t.colorList.slice(0,t.sliceNum),(function(i){return e("li",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:t.handleDisplay(i)},expression:"{displayHandler: handleDisplay(item)}",modifiers:{display:!0}}],key:i,staticClass:"cf-li sf-tag-li",class:{"is-active":t.activeId==i},style:{background:t.colorMap[i]||""},attrs:{title:i},on:{click:function(e){return t.change(i)}}},[e("div",{staticClass:"cf-li-boder",class:{"cf-special-back":"白色"==i}})])}))],2),t.colorList.length>t.sliceNum?e("Dropdown",{attrs:{list:t.colorList.slice(t.sliceNum),nomalTitle:"",activeId:t.activeId,isSlot:""},on:{change:t.change}},[e("div",{attrs:{slot:"dw-title"},slot:"dw-title"},[t.activeId&&t.colorList.slice(t.sliceNum).includes(t.activeId)?e("li",{staticClass:"cf-li sf-tag-li cf-more-title is-active",style:{background:t.colorMap[t.activeId]||""},attrs:{title:t.activeId||"全部"}},[e("div",{staticClass:"cf-li-boder",class:{"cf-special-back":"白色"==t.item}})]):e("span",[t._v("更多")])]),e("ul",{staticClass:"cf-dw",attrs:{slot:"dw-content"},slot:"dw-content"},t._l(t.colorList.slice(t.sliceNum),(function(i){return e("li",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:t.handleDisplay(i)},expression:"{displayHandler: handleDisplay(item)}",modifiers:{display:!0}}],key:i,staticClass:"cf-li",class:{"is-active":t.activeId==i},style:{background:t.colorMap[i]||""},attrs:{title:i},on:{click:function(e){return t.change(i)}}},[e("div",{staticClass:"cf-li-boder",class:{"cf-special-back":"白色"==i}})])})),0)]):t._e()]:e("div",{staticClass:"min-screen"},[t.colorList.length?e("Dropdown",{attrs:{nomalTitle:"",list:t.colorList,activeId:t.activeId,isSlot:""},on:{change:t.change}},[e("ul",{attrs:{slot:"dw-title"},slot:"dw-title"},[e("li",{staticClass:"cf-li cf-more-title",class:{"cf-all":!t.activeId},style:{background:t.colorMap[t.activeId]||""},attrs:{title:t.activeId||"全部"}})]),e("div",{attrs:{slot:"dw-title"},slot:"dw-title"}),e("ul",{staticClass:"cf-dw",attrs:{slot:"dw-content"},slot:"dw-content"},[e("li",{staticClass:"cf-li cf-all sf-tag-li",class:{"is-active":!t.activeId},attrs:{title:"全部"},on:{click:function(e){return t.change("")}}},[e("div",{staticClass:"cf-li-boder"})]),t._l(t.colorList,(function(i){return e("li",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:t.handleDisplay(i)},expression:"{displayHandler: handleDisplay(item)}",modifiers:{display:!0}}],key:i,staticClass:"cf-li",class:{"is-active":t.activeId==i},style:{background:t.colorMap[i]||""},attrs:{title:i},on:{click:function(e){return t.change(i)}}},[e("div",{staticClass:"cf-li-boder",class:{"cf-special-back":"白色"==i}})])}))],2)]):t._e()],1)],2):t._e()},j=[],F=function(){var t=this,e=t._self._c;return e("div",{staticClass:"dw"},[e("div",{staticClass:"dw-btn",class:{"dw-is-active":t.list.includes(this.activeId)&&!t.nomalTitle},on:{mouseenter:t.mouseenter,mouseleave:t.mouseleave}},[t._t("dw-title",(function(){return[t.moreTip?e("span",[t._v(t._s(t.moreTip.length>6?t.moreTip.slice(0,6)+"...":t.moreTip))]):t._e()]})),e("SvgIcon",{staticClass:"item-svg",attrs:{svgName:"arrow-bottom-18"}}),t.isShow?e("div",{staticClass:"dw-body",class:{"absolute-right":t.bodyRight}},[e("div",{staticClass:"dw-body-back"},[t._t("dw-content",(function(){return[e("ul",{staticClass:"dw-body-ul"},t._l(t.list,(function(i,a){return e("li",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:t.handleDisplay(i)},expression:"{displayHandler: handleDisplay(item)}",modifiers:{display:!0}}],key:i+a,staticClass:"dw-body-li",class:{"is-active":t.activeId==i||t.includeAll&&!t.activeId&&"全部"==i},attrs:{title:i},on:{click:function(e){return t.change(i)}}},[e("span",[t._v(t._s(i))])])})),0)]}))],2)]):t._e()],2)])},R=[],K={name:"dropdown",props:{title:{type:String,default:""},list:{type:Array,default:()=>[]},isSlot:{type:Boolean,default:!1},activeId:{type:String,default:""},bodyRight:{type:Boolean,default:!1},includeAll:{type:Boolean,default:!1},nomalTitle:{type:Boolean,default:!1}},data(){return{isShow:!1,timeout:null}},methods:{change(t){this.$emit("change",this.includeAll&&"全部"==t?"":t)},handleDisplay(t){return()=>{this.$emit("collect",t)}},mouseenter(){this.timeout=setTimeout(()=>{this.isShow=!0},200)},mouseleave(){clearTimeout(this.timeout),this.timeout=null,this.isShow=!1}},computed:{moreTip(){return!this.title&&this.list.length&&this.activeId?this.list.includes(this.activeId)?this.activeId:"更多":this.title||"更多"}}},E=K,B=(i("3119"),Object(o["a"])(E,F,R,!1,null,"6c2b19a2",null)),q=B.exports,z={name:"color-filter",props:{val:{type:Object,required:!0},activeId:{type:String,default:""},firstStyle:{type:Boolean,default:!1},sliceNum:{type:Number,default:2}},components:{Dropdown:q},data(){return{colorMap:{"黑色":"#000000","灰色":"#D8D8D8","棕色":"#683B3B","粉色":"#FF2E78","紫色":"#8979F0","黄色":"#FFC500","绿色":"#1FBB7D","红色":"#F14441","蓝色":"#4991F2","橙色":"#F46D43","白色":"#ffffff"},colorList:[]}},methods:{change(t){this.$emit("change",{key:this.val.type,val:t})},handleDisplay(t){return()=>{this.$emit("collect",{element_name:"color",tag_text:t})}}},watch:{val:{handler(t){if(t.list&&t.list.length){let e=Object.keys(this.colorMap);this.colorList=t.list.filter(t=>e.includes(t))||[]}},immediate:!0,deep:!0}}},H=z,G=(i("a215"),Object(o["a"])(H,M,j,!1,null,"598f9138",null)),U=G.exports,J=function(){var t=this,e=t._self._c;return t.val.list.length?e("div",{staticClass:"sf-tag"},[t.sliceNum>0?[e("div",{staticClass:"sf-tag-title",class:{"max-title":t.firstStyle}},[t._v(t._s(t.val.title)+":")]),e("ul",{staticClass:"sf-tag-ul"},[e("li",{staticClass:"sf-tag-li",class:{"is-active":!t.activeId},on:{click:function(e){return t.change("")}}},[t._v("全部")]),t._l(t.val.list.slice(0,t.sliceNum),(function(i,a){return e("li",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:t.handleDisplay(i)},expression:"{displayHandler: handleDisplay(item)}",modifiers:{display:!0}}],key:i,staticClass:"sf-tag-li",class:{"is-active":t.activeId==i},attrs:{title:a==t.sliceNum-1&&i.tip?"":i},on:{click:function(e){return t.change(i)}}},[e("span",[t._v(t._s(`${i.slice(0,6)}${i.length>6?"...":""}`))]),a==t.sliceNum-1?t._t("default"):t._e()],2)})),t.val.list.length>t.sliceNum?e("Dropdown",{attrs:{list:t.val.list.slice(t.sliceNum),activeId:t.activeId},on:{collect:t.dropDownReport,change:t.change}}):t._e()],2)]:e("Dropdown",{staticClass:"margin-right-12",attrs:{nomalTitle:"",includeAll:"",list:t.includeAllList,title:t.activeId||t.val.title,activeId:t.activeId},on:{collect:t.dropDownReport,change:t.change}})],2):t._e()},W=[],Y={name:"sf-tag",props:{val:{type:Object,required:!0},activeId:{type:String,default:""},index:{type:Number,default:0},firstStyle:{type:Boolean,default:!1},sliceNum:{type:Number,default:2}},components:{Dropdown:q},methods:{change(t){this.$emit("change",{key:this.val.type,val:t})},handleDisplay(t){return()=>{this.$emit("collect",{element_name:this.val.type,tag_text:t})}},dropDownReport(t){this.$emit("collect",{element_name:this.val.type,tag_text:t})}},computed:{includeAllList(){return["全部",...this.val.list]}}},Q=Y,V=(i("9694"),Object(o["a"])(Q,J,W,!1,null,"54f0db2b",null)),X=V.exports,Z=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"switch",on:{click:t.handleClick}},[e("div",{staticClass:"switch-box"},[t.isOpen?e("SvgIcon",{attrs:{svgName:"rec-switch-all"}}):e("SvgIcon",{attrs:{svgName:"rec-switch-unselect"}})],1),t._v(" "+t._s(t.title)+" ")])])},tt=[],et={name:"sf-switch",props:{title:{type:String,required:!0},filterKey:{type:String,required:!0},isOpen:{type:Boolean,default:!1}},methods:{handleClick(){this.$emit("change",this.filterKey)}}},it=et,at=(i("455d"),Object(o["a"])(it,Z,tt,!1,null,"72b36388",null)),st=at.exports,lt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"msg"},[e("SvgIcon",{attrs:{svgName:"preview-info-16"}}),e("div",{staticClass:"msg__tip"},[e("div",{staticClass:"msg__tip__icon"}),e("div",[t._v(t._s(t.tips))])])],1)},rt=[],ct={name:"icon-tip",props:{tips:{type:String,default:""}}},nt=ct,ot=(i("5a29"),Object(o["a"])(nt,lt,rt,!1,null,"7dc27e88",null)),ht=ot.exports;let pt=f["a"].debounce(200);var dt={name:"search-filter",components:{CateTree:O,ColorFilte:U,TagFilter:X,SfSwitch:st,IconTips:ht},props:{sortFilterKey:{type:String,default:"order_by"},tagFilterKey:{type:String,default:"tags"},sort:{type:Array,default:()=>[]},cateList:{type:Array,default:()=>[]},tagTreeList:{type:Array,default:()=>[]},tagList:{type:Array,default:()=>[]},switchList:{type:Array,default:()=>[]},appName:{type:String,default:"wps"},isFilter:{type:Boolean,default:!1}},data(){return{catParams:{},tagActiveMap:{},sortSelectIndex:0,switchOpenList:[],sliceNum:2,tagGroupMaxWidth:0}},mounted(){x["a"].$on(m["p"].resizeWindow,this.ajustTplLayout)},beforeDestroy(){x["a"].$off(m["p"].resizeWindow,this.ajustTplLayout)},methods:{reset(t=!1){t&&(this.catParams={}),this.tagActiveMap={},this.sortSelectIndex=0,this.switchOpenList=[]},resetAll(){this.$emit("reset"),this.report({event:"click",element_name:"delete"})},initData(t={}){t.cat&&(this.catParams=t.cat),t.tag&&(this.tagActiveMap=t.tag)},cateTreeChange(t={}){if(t.key){if(t.val?this.$set(this.catParams,t.key,t.val):this.$delete(this.catParams,t.key),this.tagActiveMap={},t.index<this.cateList.length-1){let e=this.cateList[t.index+1]&&this.cateList[t.index+1].filterKey;this.catParams[e]=""}this.$emit("catChange",{name:t.name||"",key:t.key,index:t.index}),this.normalizeAndEmit(),this.report({event:"click",element_name:t.index?"second_cat":"first_cat",[(t.index?"second":"first")+"_cat_name"]:t.name,[(t.index?"second":"first")+"_cat_id"]:t.val})}},tagChange(t={}){let{key:e,val:i}=t;e&&this.$set(this.tagActiveMap,e,i||""),this.normalizeAndEmit(),this.report({event:"click",element_name:e,tag_text:i||""})},sortChange(t){this.sortSelectIndex=t,this.normalizeAndEmit(),this.report({event:"click",element_name:"sort",sort_name:this.sortActive.val||"cool"})},switchChange(t){let e=this.switchOpenList.indexOf(t);e>-1?this.switchOpenList.splice(e,1):this.switchOpenList.push(t),this.normalizeAndEmit(),this.report({event:"click",element_name:"quality"!=t?"mb_type"==t?"is_free":t:"is_quality"})},normalizeAndEmit(){const t={};this.sortActive&&this.sortActive.val&&(t[this.sortFilterKey]=this.sortActive.val);let e=Object.values(this.tagActiveMap).filter(t=>t).join("@");e&&(t[this.tagFilterKey]=e),Object.assign(t,this.catParams),this.switchOpenList.map(e=>(t[e]=1,e)),this.$emit("change",t,this.tagActiveMap)},report(t){let e=t.event||"display";delete t.event,this.$emit("report",{event:e,params:t})},ajustTplLayout(){pt(()=>{this.countTagSliceIndex()})},countTagSliceIndex(){try{this.$nextTick(()=>{let t=this.$refs&&this.$refs.tagGroup;if(!t||!t.children||!t.children.length)return;let e=t&&t.getBoundingClientRect(),i=e.width;if(1==this.tagList.length)this.tagList[0]&&"color"==this.tagList[0].type&&(this.sliceNum=this.tagList[0].list.length);else{let e=0;for(let i of t.children){let t=i.getBoundingClientRect();e+=t.width?t.width+8:0}e>this.tagGroupMaxWidth&&(this.tagGroupMaxWidth=e),this.sliceNum=this.tagGroupMaxWidth>=i?0:2}})}catch(t){console.log(t),this.sliceNum=2}}},computed:{...Object(g["c"])("common",["isDarkSkin"]),sortActive(){return this.sort[this.sortSelectIndex]||{}},firstTagGroup(){return this.tagList[0]||{}}},watch:{tagList:{handler(t,e){t.length&&JSON.stringify(t)!=JSON.stringify(e)&&(this.tagGroupMaxWidth=0,this.sliceNum=2,this.countTagSliceIndex())},immediate:!0}}},ut=dt,gt=(i("68ee"),Object(o["a"])(ut,P,k,!1,null,"628b3469",null)),mt=gt.exports,ft=i("e400"),yt=i("4a78"),_t=i("3387"),vt={methods:{$_search_collect_initPageDisplay(){_t["a"].send("display",{page_name:"search_page",$e_t:"page",search_type:this.reportSearchType,$track:""})},$_search_collect_initPageLeave(){_t["a"].setCollectInfo("searchId"),_t["a"].setCollectInfo("subSearchId")},$_search_collect_renderSearch(){_t["a"].send("display",{page_name:"search_page",$m_n:"template_list",$e_t:"module",search_type:this.reportSearchType,keyword:this.word,policy:this.policy,resource_cout:this.totalNum,page_num:this.page,sort_name:this.filterParams[this.orderbyKey]||"cool",...this.$_search_collect_ftCommonReport()})},$_search_collect_filterChange(){_t["a"].setCollectInfo("subSearchId",f["a"].getRandomKey())},$_search_collect_homeClick(){_t["a"].send("click",{page_name:"search_page",$m_n:"nav_bar",$e_n:"back",$e_t:"botton",policy:this.policy,...this.$_search_collect_ftCommonReport()})},$_search_collect_ftCommonReport(){return{sort_name:this.filterParams[this.orderbyKey]||"cool",first_cat_id:this.filterParams["category_id"],first_cat_name:this.filterCategoryName,is_quality:!!this.filterParams["is_quality"],...this.reportTagMap}},$_search_collect_ftReport({event:t,params:e}){_t["a"].send(t,Object.assign({page_name:"search_page",$m_n:"screen_classify",$e_t:"button",policy:this.policy,keyword:this.word,search_type:this.reportSearchType,$track:"",...this.$_search_collect_ftCommonReport()},e))}},computed:{$_search_commonCollectParams(){return{$m_n:"template_list",$e_i:{display:363,click:362},resource_count:this.maxCount,first_cat_name:this.filterCategoryName,sort_name:"",$track:"",policy:this.policy,keyword:this.word,search_type:this.reportSearchType,...this.$_search_collect_ftCommonReport()}}}};let Ct=setTimeout("1"),bt=setTimeout("1"),Tt=f["a"].debounce(50);var wt={mixins:[ft["a"],yt["a"],vt],data(){return{oldword:"",word:"",totalPage:0,totalNum:0,page:1,statusCode:"",pageSize:50,query:{},data:[],policy:"",topMbId:"",eventTime:"",requestId:"",homeRouter:"/screen",searchType:"",reportSearchType:"",searchMarkType:{1:131073,2:131074,3:131075},filterParams:{},orderbyKey:"order_by",filterSortList:[{name:"综合",val:""},{name:"最新",val:"publish_time"},{name:"最热",val:"down_value"}],filterSwitchList:[{title:"只看精品",key:"is_quality"}],filterTagList:[],filterTagTreeList:[],filterCateList:[],sessionId:"",filterCategoryName:"",reportTagMap:{},netStatus:m["s"],limit:0,bodyStyle:{}}},beforeRouteEnter(t,e,i){i(e=>{e.oldword="",_["a"].$on(m["p"].resizeWindow,e.ajustTplLayout),v["a"].setOriginInfo({secondPage:"ssjgy",sort:"",policy:""}),e.init(t)})},beforeRouteUpdate(t,e,i){this.init(t),i()},beforeRouteLeave(t,e,i){this.data=[],_["a"].$emit(m["p"].keywordChange,""),_["a"].$off(m["p"].resizeWindow,this.ajustTplLayout),this.$_search_collect_initPageLeave(),i()},methods:{loadMore(){if(console.log("搜索页的loadMore"),this.statusCode==m["s"].loading)return void console.log("正在请求中","屏蔽loadMore");let t=this.page+1;t>0&&t<=this.totalPage&&(this.page!=t||1==t)&&(this.page=t,this.getData())},init(t){this.initQuery(t.query),_["a"].$emit(m["p"].keywordChange,this.word||""),this.resetPageData(),this.ajustTplLayout(),this.$_search_collect_initPageDisplay(),this.$nextTick(()=>{this.getData()})},resetPageData(t=!0){console.log("resetPageData"),this.filter={},this.eventTime="",this.policy="",this.requestId="",this.page=1,this.statusCode="",this.data=[],t&&(this.$refs&&this.$refs.sfComp&&this.$refs.sfComp.reset(!0),this.filterCateList=[],this.filterParams={},this.filterTagList=[],this.filterTagTreeList=[]),this.$refs.nSearchList&&(this.$refs.nSearchList.scrollTop=0)},initQuery(t){t=t||{},this.word=t.word||"",this.topMbId=t.topMbId||"",this.query=t,this.searchType=t.searchType,this.reportSearchType=t.collect&&t.collect.searchType},getData(){if(this.sessionId=f["a"].getRandomKey(),"word"==this.query.searchType)this.getSearch(this.sessionId);else{let t=decodeURIComponent(this.query.content);this.getSearchByNavTag(this.query.classify,t,this.sessionId)}},getSearch(t){this.isEnterprise?Tt(()=>{this.statusCode=m["s"].loading,this.oldword=this.word,this._localSearch(this.word,this.page,this.pageSize).then(t=>{if(1==this.page)if(!t||!Array.isArray(t.fileList)||t.fileList.length<1)this.totalNum=0,this.totalPage=0,this.statusCode="SEARTCH_EMPTY";else{this._formartPrevieImage(t.fileList),this.data=t.fileList;const e=t.total_num||0;this.totalPage=Math.ceil(e/this.pageSize),this.totalNum=e>500?500:e,this.statusCode=this.data.length?"":"SEARTCH_EMPTY"}else Array.isArray(t.fileList)&&(this._formartPrevieImage(t.fileList),this.data=this.data.concat(t.fileList))}).catch(t=>{this.totalNum=0,this.totalPage=0,this.policy="",this.requestId="",this.eventTime="",this.statusCode=m["s"].error}).finally(()=>{this.filterTagTreeList=[],this.filterTagList=[]})}):Tt(()=>{let e,i;this.statusCode=m["s"].loading,i={mb_app:m["e"].mark,keyword:this.word,per_page:this.pageSize,pos:"newdoc",page:this.page,mb_platform:8,add_preview:1,add_tag:1,bj_search:1,frontend_invoke_position:"pc_new_hybrid_input",guid:m["e"].guid,hdid:m["e"].hdid,version:m["e"].version,dist:m["e"].dist,lib_retail:1},this.topMbId&&(i.top_mb_id=this.topMbId),1==this.page&&(i.is_req_filter=1),Object.assign(i,this.filterParams),this.oldword=this.word,e=y["a"].get("search").searchFilter({data:i,serialNumber:Ct}),console.log("发出请求getSearch",i),e.then(e=>{this.renderSearch(e,t)}).catch(()=>{this.totalNum=0,this.totalPage=0,this.policy="",this.requestId="",this.eventTime="",this.statusCode=m["s"].error})})},getSearchByNavTag(t,e,i){this.statusCode=m["s"].loading;let a={mb_app:m["e"].mark,type:t,limit:this.pageSize,content:e,pos:"newdoc",offset:(this.page-1)*this.pageSize,guid:m["e"].guid,hdid:m["e"].hdid,version:m["e"].version,dist:m["e"].dist};return console.log("发出请求getSearchByNavTag",a),y["a"].get("search").searchByNavTag({data:a,serialNumber:bt}).then(t=>{this.renderSearch(t,i)}).catch(()=>{this.policy="",this.requestId="",this.eventTime="",this.totalNum=0,this.totalPage=0,this.statusCode=m["s"].error})},renderSearch(t,e){if(this.sessionId==e){if(this.policy=t.data&&t.data.policy||"",this.eventTime=t.data&&t.data.event_time||"",this.requestId=t["wps-docer-request-id"]||"","ok"==t.result){if(t.data&&(t.data.data||t.data.tmpls)){let e=t.data.data||t.data.tmpls||[];if(this.data=this.data.concat(e),1==this.page){this.totalPage=Math.ceil((t.data.total_num||t.data.count||0)/this.pageSize);let e=t.data.total_num||t.data.count||0;this.totalNum=e>5e3?5e3:e,this.initFilterData(t.data||{})}}else this.totalPage=0,this.totalNum=0;this.statusCode=this.data.length?"":"SEARTCH_EMPTY"}else this.totalNum=0,this.totalPage=0,this.statusCode=m["s"].error;this.$_search_collect_renderSearch()}},initFilterData(t){if(!this.filterCateList.length&&t.categories&&t.categories.length&&this.filterCateList.push({title:"分类",list:t.categories,filterKey:"category_id",reportElementType:"first_cat"}),this.filterParams.tags)return;let e=[],i=[];t.tag_groups&&t.tag_groups.map(t=>{if(t&&t.tag_group_type&&t.tags&&t.tags.length){let e=[];["type","content"].includes(t.tag_group_type)?e=[...e,...t.tags||[]]:i.push({title:t.tag_group_name,type:t.tag_group_type,list:t.tags}),e.length&&i.push({title:"猜你想找",type:"guess",list:e})}return t}),this.filterTagTreeList=e||[],this.filterTagList=i||[]},filterCatChange(t={}){this.filterCategoryName=t.name},filterChange(t={},e={}){try{if(JSON.stringify(this.filterParams)==JSON.stringify(t))return;this.$_search_collect_filterChange(),this.reportTagMap=JSON.parse(JSON.stringify(e)),this.filterParams[this.orderbyKey]!=t[this.orderbyKey]&&v["a"].setOriginInfo({sort:t[this.orderbyKey]}),v["a"].setOriginInfo({secondPage:f["a"].isEmptyObject(t)?"ssjgy":"jgysx"}),this.filterParams=JSON.parse(JSON.stringify(t)),this.resetPageData(!1),this.getData()}catch(i){console.log(i)}},onRetry(){this.resetPageData(),this.getData()},ajustTplLayout(){this.$nextTick(()=>{let[t,e,i]=f["a"].getScreenDisplay(this.$refs.nSearchList);console.log(t,e),this.limit!=e&&(this.limit=e),this.bodyStyle={width:i+"px"},this.pageSize||(this.pageSize=t*e||30)})}},computed:{...Object(g["c"])("common",["appName"]),...Object(g["c"])("user",["userInfo"]),isFilter(){return!!(this.filterParams[this.orderbyKey]||this.filterParams["category_id"]||this.filterParams["is_quality"])}},components:{Pagination:p,List:d["a"],DataStatus:u["a"],Crumbs:I,SearchFilter:mt},created(){this.isEnterprise&&(this.filterSortList=[],this.filterSwitchList=[])}},St=wt,It=(i("2a4c"),Object(o["a"])(St,a,s,!1,null,"6de7a11c",null));e["default"]=It.exports},"68ee":function(t,e,i){"use strict";i("4da1")},9694:function(t,e,i){"use strict";i("40cb")},"9c76":function(t,e,i){},"9e514":function(t,e,i){"use strict";i("f58f")},a215:function(t,e,i){"use strict";i("0fda")},ae7f:function(t,e,i){},cbb7:function(t,e,i){},da2b:function(t,e,i){},db6c:function(t,e,i){t.exports=i.p+"images/et.png"},e528:function(t,e,i){},f016:function(t,e,i){},f58f:function(t,e,i){}}]);