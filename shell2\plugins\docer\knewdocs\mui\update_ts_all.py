import os
import sys
cwd = os.path.dirname(__file__)
sys.path.append(os.path.join(cwd, '../../../../../../resource_bundle/shell2/mui/bin'))
from update_plugin_ts import update_plugin_ts

lang_list = ['zh_CN', 'en_US', 'ja_JP', 'zh_TW', 'fr_FR', 'de_DE', 'es_ES', 'ru_RU', 
    'pt_PT', 'pt_BR', 'pl_PL', 'id_ID', 'zh_HK']
update_plugin_ts(cwd + os.sep + '../src', cwd + os.sep + '../mui', 'knewdocs.ts', lang_list, True)
