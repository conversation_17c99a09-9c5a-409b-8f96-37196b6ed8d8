﻿#include "stdafx.h"

#include "knewdocjsapihelper.h"
#include "knewdocjsapi.h"
#include "pdf/kpromepdfwindow.h"
#include "kprometheus/kprometab.h"
#include "kprometheus/kpromemainwindow.h"
#include "kprometheus/kprometabbar.h"
#include "kprometheus/kpromeapplication.h"

KxNewDocJsApiHelper::KxNewDocJsApiHelper()
{

}

KxNewDocJsApiHelper::~KxNewDocJsApiHelper()
{
}

QStringList KxNewDocJsApiHelper::openFileTabText(KxNewDocJsApi* jsApi, KPromePluginWidget::WidgetType wt, int maxCount /*= 20*/)
{
	QStringList openTabList;
	if (!(wt == KPromePluginWidget::pageWps ||
		wt == KPromePluginWidget::pageWpp ||
		wt == KPromePluginWidget::pageEt))
		return openTabList;
	auto currentmw = promeApp->findRelativePromeMainWindow(jsApi);
	if (currentmw)
		 openFileTabText(currentmw, wt, maxCount, openTabList);
	auto promemws = promeApp->mainWindows();
	foreach (auto promemw, promemws)
	{
		if (promemw != currentmw)
			openFileTabText(promemw, wt, maxCount, openTabList);
		if (openTabList.length() > maxCount)
			break;
	}
	return openTabList;
}

void KxNewDocJsApiHelper::openFileTabText(KPromeMainWindow* mw, KPromePluginWidget::WidgetType wt, int maxCount, QStringList& openTabList)
{
	if (openTabList.length() > maxCount)
		return;
	if (mw)
	{
		if (mw->tabBar())
		{
			int tabCount = mw->tabBar()->count();
			for (int index = 0 ; index < tabCount; index++)
			{
				auto promeTab = mw->tabBar()->tabAt(index);
				if (promeTab && promeTab->subPage())
				{
					if (wt == promeTab->subPage()->pageType())
					{
						openTabList << promeTab->text();
						if (openTabList.length() > maxCount)
							break;
					}
				}
			}
		}
	}
}

QString KxNewDocJsApiHelper::getDownloadID(const QString& templateID)
{
	if (downloadIDs.contains(templateID))
		return downloadIDs[templateID];
	else
		return QString();
}

void KxNewDocJsApiHelper::addDownloadTask(const QString& downloadId, const QString& templateId)
{
	downloadIDs[templateId] = downloadId;
}

bool KxNewDocJsApiHelper::removeByResId(const QString& id)
{
	if (downloadIDs.contains(id))
	{
		downloadIDs.remove(id);
		return true;
	}
	return false;
}

bool KxNewDocJsApiHelper::removeByTaskId(const QString& id)
{
	if (downloadIDs.values().contains(id))
	{
		for (const auto& item : downloadIDs.keys())
		{
			if (downloadIDs[item] == id)
			{
				downloadIDs.remove(item);
				return true;
			}
		}
	}
	return false;
}

int KxNewDocJsApiHelper::downloadCount() const
{
	return downloadIDs.count();
}

void KxNewDocJsApiHelper::notifyCancelDownload(KxNewDocJsApi* notifier, const QString& templateId)
{
	for (int i = 0; i < count(); ++i)
	{
		KxNewDocJsApi* api = qobject_cast<KxNewDocJsApi*>(item(i));
		if (!api || api == notifier)
			continue;

		api->notifyCancelDownload(templateId);
	}
}


void KxNewDocJsApiHelper::notifyCancelDownload(KxPromePdfWindowJsApi* notifier, const QString& templateId)
{
	for (int i = 0; i < count(); ++i)
	{
		KxPromePdfWindowJsApi* api = qobject_cast<KxPromePdfWindowJsApi*>(item(i));
		if (!api || api == notifier)
			continue;

		api->notifyCancelDownload(templateId);
	}
}

void KxNewDocJsApiHelper::notifyDownloadProgress(KxNewDocJsApi* notifier, const QString& templateId, int nPercent)
{
	for (int i = 0; i < count(); ++i)
	{
		KxNewDocJsApi* api = qobject_cast<KxNewDocJsApi*>(item(i));
		if (!api || api == notifier)
			continue;

		api->notifyDownloadProgress(templateId, nPercent);
	}
}


void KxNewDocJsApiHelper::notifyDownloadProgress(KxPromePdfWindowJsApi* notifier, const QString& templateId, int nPercent)
{
	for (int i = 0; i < count(); ++i)
	{
		KxPromePdfWindowJsApi* api = qobject_cast<KxPromePdfWindowJsApi*>(item(i));
		if (!api || api == notifier)
			continue;

		api->notifyDownloadProgress(templateId, nPercent);
	}
}

void KxNewDocJsApiHelper::notifyDownloadSuccess(KxNewDocJsApi* notifier, const QString& templateId, const QString& fileName)
{
	for (int i = 0; i < count(); ++i)
	{
		KxNewDocJsApi* api = qobject_cast<KxNewDocJsApi*>(item(i));
		if (!api || api == notifier)
			continue;

		api->notifyDownloadSuccess(templateId, fileName);
	}
}

void KxNewDocJsApiHelper::notifyDownloadSuccess(KxPromePdfWindowJsApi* notifier, const QString& templateId, const QString& fileName)
{
	for (int i = 0; i < count(); ++i)
	{
		KxPromePdfWindowJsApi* api = qobject_cast<KxPromePdfWindowJsApi*>(item(i));
		if (!api || api == notifier)
			continue;

		api->notifyDownloadSuccess(templateId, fileName);
	}
}

void KxNewDocJsApiHelper::notifyDownloadError(KxNewDocJsApi* notifier, const QString& templateId, kdocerresnetwork::DownloadFailInfo info)
{
	for (int i = 0; i < count(); ++i)
	{
		KxNewDocJsApi* api = qobject_cast<KxNewDocJsApi*>(item(i));
		if (!api || api == notifier)
			continue;

		api->notifyDownloadError(templateId, info);
	}
}

void KxNewDocJsApiHelper::notifyDownloadError(KxPromePdfWindowJsApi* notifier, const QString& templateId, kdocerresnetwork::DownloadFailInfo info)
{
	for (int i = 0; i < count(); ++i)
	{
		KxPromePdfWindowJsApi* api = qobject_cast<KxPromePdfWindowJsApi*>(item(i));
		if (!api || api == notifier)
			continue;

		api->notifyDownloadError(templateId, info);
	}
}
