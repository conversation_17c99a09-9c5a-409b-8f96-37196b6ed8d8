﻿#include "stdafx.h"
#include "kmobilescanjshandler.h"
#include "kmobilescanmanager.h"
#include "ksolite/jsapiutilities/util/kjsapiutil.h"

KMobileScanJSHandler* KMobileScanJSHandler::getInstance()
{
	static KMobileScanJSHandler handle;
	return &handle;
}

KMobileScanJSHandler::KMobileScanJSHandler(QObject* parent)
	: QObject(parent)
{
	connect(this, &KMobileScanJSHandler::webDialogClose, KMobileScanManager::getMobileScanManager(), &KMobileScanManager::webDialogClose);
}

KMobileScanJSHandler::~KMobileScanJSHandler()
{
}

void KMobileScanJSHandler::initJsApiEvent(IKJSExtentionApiEvent* pJsApiEvent)
{
	m_jsAPiObjApiEvent = pJsApiEvent;
}

void KMobileScanJSHandler::tempSpaceDownloadCallback(const QVariantMap& result)
{
	KJSVariantList retList = KJSApiUtil::getAsynRetValList(VARIANT_TRUE, "", result);
	if (ksolite::GetKSOJSCore() && ksolite::GetKSOJSCore()->GetFunctionService())
	{
		ksolite::GetKSOJSCore()->GetFunctionService()->ExecuteFunction(
			m_funcDownloadJsCb.first, m_funcDownloadJsCb.second, retList);
	}
}

void KMobileScanJSHandler::nearFieldFileNoticeCallback(const QVariantMap& result)
{
	KJSVariantList retList = KJSApiUtil::getAsynRetValList(VARIANT_TRUE, "", result);
	if (ksolite::GetKSOJSCore() && ksolite::GetKSOJSCore()->GetFunctionService())
	{
		ksolite::GetKSOJSCore()->GetFunctionService()->ExecuteFunction(
			m_funcNearFieldNoticeJsCb.first, m_funcNearFieldNoticeJsCb.second, retList);
	}
}

HRESULT KMobileScanJSHandler::onCloseWebWidget(
	KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments argList, KJSVariant& retVal, KJSVariant& strException)
{
	emit webDialogClose();
	return S_OK;
}

HRESULT KMobileScanJSHandler::sendGeneralEvent(
	KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments argList, KJSVariant& retVal, KJSVariant& strException)
{
	ks_stdptr<IJSONValue> nameJson = argList[0].GetInterface();
	if (nameJson.IsEqualObject(nullptr))
		return S_OK;
	QString strName = nameJson.get()->GetString("eventName");
	ks_stdptr<IJSONValue> argsJson = argList[1].GetInterface();
	QVariantMap params = KJSApiUtil::v8JsonValueToQVariantMap(argsJson);
	KMobileScanManager::getMobileScanManager()->sendGeneralEvent(strName, params);
	return S_OK;
}

HRESULT KMobileScanJSHandler::onMobileScanFinish(KMobileScanJSObject* pThis,
	const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
	KJSVariant& strException)
{
	ks_stdptr<IJSONValue> argsJson = argList[0].GetInterface();
	if (argsJson.IsEqualObject(nullptr))
		return S_OK;
	QVariantMap params = KJSApiUtil::v8JsonValueToQVariantMap(argsJson);
	KMobileScanManager::getMobileScanManager()->onMobileScanFinish(params);
	return S_OK;
}

HRESULT KMobileScanJSHandler::onTempSpaceDownload(KMobileScanJSObject* pThis,
	const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
	KJSVariant& strException)
{
	ks_stdptr<IJSONValue> argsJson = argList[0].GetInterface();
	if (argsJson.IsEqualObject(nullptr))
		return S_OK;
	QVariantMap params = KJSApiUtil::v8JsonValueToQVariantMap(argsJson);
	KMobileScanManager::getMobileScanManager()->asynTempSpaceDownload(params);
	return S_OK;
}

HRESULT KMobileScanJSHandler::cancelAllTempSpaceDownload(KMobileScanJSObject* pThis,
	const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
	KJSVariant& strException)
{
	KMobileScanManager::getMobileScanManager()->cancelAllTempSpaceDownload();
	return S_OK;
}

void KMobileScanJSHandler::registTempSpaceDownloadCallback(const JsCallBackFunc& callbackFunc)
{
	m_funcDownloadJsCb = callbackFunc;
}


void KMobileScanJSHandler::registNearFieldFileNoticeCallback(const JsCallBackFunc& callbackFunc)
{
	m_funcNearFieldNoticeJsCb = callbackFunc;
}

HRESULT KMobileScanJSHandler::onSizeChange(KMobileScanJSObject* pThis,
	const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
	KJSVariant& strException)
{
	ks_stdptr<IJSONValue> argsJson = argList[0].GetInterface();
	if (argsJson.IsEqualObject(nullptr))
		return S_OK;
	QVariantMap params = KJSApiUtil::v8JsonValueToQVariantMap(argsJson);
	KMobileScanManager::getMobileScanManager()->onSizeChange(params);
	return S_OK;
}

void KMobileScanJSHandler::registNetWorkCallback(const JsCallBackFunc& callbackFunc)
{
	m_funcNetWorkJsCb = callbackFunc;
}

HRESULT KMobileScanJSHandler::onNetWorkRequest(KMobileScanJSObject* pThis,
	const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
	KJSVariant& strException)
{
	ks_stdptr<IJSONValue> argsJson = argList[0].GetInterface();
	if (argsJson.IsEqualObject(nullptr))
		return S_OK;
	QVariantMap params = KJSApiUtil::v8JsonValueToQVariantMap(argsJson);
	KMobileScanManager::getMobileScanManager()->onNetWorkRequest(params);
	return S_OK;
}

void KMobileScanJSHandler::netWorkRequestCallback(const QVariantMap& result)
{
	KJSVariantList retList = KJSApiUtil::getAsynRetValList(VARIANT_TRUE, "", result);
	if (ksolite::GetKSOJSCore() && ksolite::GetKSOJSCore()->GetFunctionService())
	{
		ksolite::GetKSOJSCore()->GetFunctionService()->ExecuteFunction(
			m_funcNetWorkJsCb.first, m_funcNetWorkJsCb.second, retList);
	}
}

void KMobileScanJSHandler::registCreatePdfCallback(const JsCallBackFunc& callbackFunc)
{
	m_funcCreatePdfJsCb = callbackFunc;
}

void KMobileScanJSHandler::createScanPdfCallback(bool bAllSuccess)
{
	QVariantMap result;
	result["status"] = bAllSuccess;
	KJSVariantList retList = KJSApiUtil::getAsynRetValList(VARIANT_TRUE, "", result);
	if (ksolite::GetKSOJSCore() && ksolite::GetKSOJSCore()->GetFunctionService())
	{
		ksolite::GetKSOJSCore()->GetFunctionService()->ExecuteFunction(
			m_funcCreatePdfJsCb.first, m_funcCreatePdfJsCb.second, retList);
	}
}
