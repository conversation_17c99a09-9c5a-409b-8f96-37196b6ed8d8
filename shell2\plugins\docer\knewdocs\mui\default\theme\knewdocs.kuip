<?xml version="1.0" encoding="utf-8"?>
<shell xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<resource relativePath="res">
		<icon type="svg">
		</icon>
	</resource>
	<theme>
		<style class="KNewDocs-WaitWidgetIcon">
			<color name="colorGrayFill" value="CommonThemePalette::kd-color-fill-regular"/>
		</style>
		
		<style class="KNewDocs-WaitWidget">
			<color name="background" value="CommonThemePalette::kd-color-background-base"/>
		</style>

		<style class="KNewDocs-ErrorText">
			<font name="text" family="Microsoft YaHei" pixelSize="CommonHint::kd-font-size-base" />
			<color name="text" value="CommonThemePalette::kd-color-text-secondary"/>
		</style>

		<style class="KNewDocs-WaitText">
			<font name="text" family="Microsoft YaHei" pixelSize="CommonHint::kd-font-size-middle" />
			<color name="text" value="CommonThemePalette::kd-color-text-secondary"/>
		</style>
	</theme>
	<theme relatedName="2018white_dark">
		<style class="KNewDocsWebWidget">
			<color name="background" value="CommonThemePalette::kd-color-background-plate"/>
		</style>
	</theme>
	<theme relatedName="*dark" baseRelated="2018white_dark"></theme>
</shell> 