﻿#include "stdafx.h"

#include "ktransfertask.h"
#include <communication/file/kfilestatus.h>
#include <softbus/business/kcommunicatorproxy.h>
#include <softbus/business/cobwrapper.h>
#include "gen-cpp/kqingservices_types.h"

using namespace SoftBus;
using namespace SoftBus::Business;

namespace
{
const int k_checkDownStateInterval = 1000;
const unsigned long k_threadWaitMsec = 3000;
const int k_tempSpaceTaskCreateSuccess = 1;

template <class T>
struct Initializer
{
	Initializer(T& obj) : obj(obj)
	{
		obj.init();
	}
	~Initializer()
	{
		obj.uninit();
	}
	T& obj;
};
}

KTransferTask& getTransferTask()
{
	static KTransferTask obj;
	static Initializer<KTransferTask> holder(obj);
	return obj;
}

KTransferTask::KTransferTask()
{
}

KTransferTask::~KTransferTask()
{

}

void KTransferTask::asynTempSpaceDownload(const QString& fileId,
	const QString& fileSize, const QString& path)
{
	QMetaObject::invokeMethod(this, "tempSpaceDownload",
		Qt::QueuedConnection,
		Q_ARG(QString, fileId),
		Q_ARG(QString, fileSize),
		Q_ARG(QString, path));
}

void KTransferTask::nearFieldFileTaskNotice(const QVariantMap& argMap)
{
	QVariantMap dataMap = argMap["data"].toMap();
	QJsonObject obj = QJsonObject::fromVariantMap(dataMap);
	Protocol::KFileTaskInfo info;
	info.read(obj);
	onNearFieldFileTaskNotice(info);
}

void KTransferTask::asynCancelAllTransfer()
{
	QMetaObject::invokeMethod(this, "cancelAllTransfer", Qt::QueuedConnection);
}

void KTransferTask::tempSpaceDownload(const QString& fileId,
	const QString& fileSize, const QString& path)
{
	KCloudSvrProxy* service = kcoreApp->cloudService();
	if (!service)
		return;

	QFileInfo fileInfo(path);
	QString fileName = fileInfo.fileName();
	for (int count = 1; QFileInfo(fileInfo.dir().filePath(fileName)).exists(); count++)
	{
		fileName = fileInfo.completeBaseName() + QString("(%1).").arg(count) + fileInfo.suffix();
	}

	QString filePath = fileInfo.dir().filePath(fileName);
	QFile file(filePath);
	if (!file.exists())
	{
		file.open(QIODevice::ReadWrite);
		file.close();
	}

	buildTask(KSpaceType::TempSpace, fileId, filePath, fileSize.toLongLong());

	QingIpcArg arg;
	std::map<std::string, std::string> argMap;
	arg.method = "downloadFileFromTempStore";
	argMap["fileId"] = fileId.toUtf8().data();
	argMap["downloadFilePath"] = filePath.toUtf8().data();
	argMap["fileSize"] = fileSize.toUtf8().data();
	argMap["businessSource"] = "SoftBusFarField";
	arg.argMap = argMap;
	QString requestId = service->asynCallEx(
		"qingIpcCall",
		Q_ARG(QingIpcArg, arg),
		this, "onDownTempSpaceCallBack");

	if (requestId.isEmpty())
		return;
	QString info = QString("downloadFileFromTempStore fileId:%1 filePath:%2")
		.arg(fileId, filePath);
	m_transferRequestId[requestId] = fileId;
}

void KTransferTask::cancelAllTransfer()
{
	foreach(auto & taskItem, m_taskMrg)
	{
		deleteTransferTask(taskItem.spaceType, taskItem.fileId, taskItem.taskId);
	}
}

void KTransferTask::deleteTransferTask(const KSpaceType& spaceType,
	const QString& fileId, const QString& taskId)
{
	KCloudSvrProxy* service = kcoreApp->cloudService();
	if (!service)
		return;

	if (spaceType == KSpaceType::TempSpace)
	{
		QingIpcArg arg;
		arg.method = "doTransTempStoreFileAction";
		arg.argMap["taskIds"] = taskId.toUtf8().data();
		arg.argMap["fileIds"] = fileId.toUtf8().data();
		arg.argMap["action"] = QString::number(static_cast<int>(TransTempSpaceAction::DeleteTask)).toUtf8().data();;
		service->asynCallEx("qingIpcCall", Q_ARG(QingIpcArg, arg));

	}
	else if (spaceType == KSpaceType::NearField)
	{
		KCommunicatorProxy::instance().cancelTaskByOwnerId(taskId.toStdString());
	}
}

void KTransferTask::init()
{
	connect(&m_workerThread, SIGNAL(started()), this, SLOT(onThreadStarted()));
	moveToThread(&m_workerThread);
	m_workerThread.start();
}

void KTransferTask::uninit()
{
	if (m_workerThread.isRunning())
	{
		m_workerThread.quit();
		m_workerThread.wait(k_threadWaitMsec);

		if (m_workerThread.isRunning())
		{
			m_workerThread.terminate();
		}
	}
}

void KTransferTask::listen(const QString& action)
{
	if (m_listenedActions.contains(action))
		return;
	auto errorCode = KCommunicatorProxy::instance().listenFileTaskInfo(action.toStdString(),
		callbackToThreadOf(this,
			[this, action](Error::Code errorCode, const Protocol::KFileTaskInfo& info)
			{
				if (!m_listenedActions.contains(action))
					return CobLifeState::Dead;

				if (errorCode == Error::Success)
					onNearFieldFileTaskNotice(info);
				return CobLifeState::Alive;
			}));

	if (errorCode == Error::Success)
		m_listenedActions.insert(action);
}

void KTransferTask::onGetDownStateTimerTimeout()
{
	KCloudSvrProxy* service = kcoreApp->cloudService();
	if (!service)
	{
		if (m_downCheckTimer)
			m_downCheckTimer->stop();
		return;
	}

	if (m_downingTaskId.empty())
	{
		if (m_downCheckTimer)
			m_downCheckTimer->stop();
		return;
	}

	QList<QString> downTaskList = m_downingTaskId.values();
	foreach(const QString & fileId, downTaskList)
	{
		auto taskItem = m_taskMrg.constFind(fileId);
		if (taskItem != m_taskMrg.constEnd())
		{
			std::map<std::string, std::string> argMap;
			argMap["taskId"] = taskItem.value().taskId.toUtf8().data();

			QingIpcArg arg;
			arg.method = "getTempStoreTaskStatus";
			arg.argMap = argMap;
			QString requestId = service->asynCallEx("qingIpcCall",
				Q_ARG(QingIpcArg, arg), this, "onGetDownTempTaskStateCallBack");

			if (!requestId.isEmpty())
				m_stateRequestId[requestId] = fileId;
		}
	}
}

void KTransferTask::onThreadStarted()
{
	m_downCheckTimer = new QTimer(this);

	connect(m_downCheckTimer, SIGNAL(timeout()), this, SLOT(onGetDownStateTimerTimeout()));
}

void KTransferTask::onDownTempSpaceCallBack(bool success,
	const QString& requestId, const QingIpcResult& result)
{
	auto requestItem = m_transferRequestId.find(requestId);
	if (requestItem == m_transferRequestId.constEnd())
		return;

	QString fileId = requestItem.value();
	m_transferRequestId.erase(requestItem);

	auto taskItem = m_taskMrg.constFind(fileId);
	if (taskItem == m_taskMrg.constEnd())
	{
		notifyDownloadError((int)KSpaceType::TempSpace, fileId);
		return;
	}

	if (!success || result.state != ResultState::Succeeded)
	{
		notifyDownloadError((int)KSpaceType::TempSpace, fileId);
		return;
	}

	auto resultMap = result.resultMap;
	if (QString::fromStdString(resultMap["result"]).toInt() != k_tempSpaceTaskCreateSuccess)
	{
		notifyDownloadError((int)KSpaceType::TempSpace, fileId);
		return;
	}

	QString taskId = QString::fromStdString(resultMap["taskId"]);
	if (taskId.isEmpty())
	{
		notifyDownloadError((int)KSpaceType::TempSpace, fileId);
		return;
	}

	m_taskMrg[fileId].taskId = taskId;
	m_downingTaskId.insert(fileId);

	if (m_downCheckTimer && !m_downCheckTimer->isActive())
	{
		m_downCheckTimer->start(k_checkDownStateInterval);
	}
}

void KTransferTask::onGetDownTempTaskStateCallBack(bool success,
	const QString& requestId, const QingIpcResult& result)
{
	auto requestItem = m_stateRequestId.find(requestId);
	if (requestItem == m_stateRequestId.constEnd())
		return;

	QString fileId = requestItem.value();
	m_stateRequestId.erase(requestItem);
	auto resultMap = result.resultMap;
	auto taskItem = m_taskMrg.constFind(fileId);

	bool initSuccess = false;
	do
	{
		if (!success || result.state != ResultState::Succeeded)
			break;
		if (resultMap.empty())
			break;
		if (taskItem == m_taskMrg.constEnd())
			break;

		initSuccess = true;
	} while (false);
	if (!initSuccess)
	{
		notifyDownloadError((int)KSpaceType::TempSpace, fileId);
		deleteTask(fileId);
		return;
	}

	const TaskInfo& taskInfo = taskItem.value();

	TempSpaceTaskStatus status = static_cast<TempSpaceTaskStatus>(std::stoi(resultMap["status"].c_str()));
	if (status == TempSpaceTaskStatus::Finish)
	{
		QVariantMap argMap;
		argMap["status"] = static_cast<int>(TaskStatus::Finish);
		argMap["path"] = taskInfo.path;
		argMap["type"] = static_cast<int>(taskInfo.spaceType);
		argMap["fileId"] = taskInfo.fileId;
		argMap["progress"] = QVariant(QString::fromStdString(resultMap["progress"])).toInt();
		emit sigDownloadStateChange(argMap);

		deleteTask(fileId);
	}
	else if (status == TempSpaceTaskStatus::Transfer)
	{
		QVariantMap argMap;
		argMap["status"] = static_cast<int>(TaskStatus::Transferring);
		argMap["path"] = taskInfo.path;
		argMap["type"] = static_cast<int>(taskInfo.spaceType);
		argMap["fileId"] = taskInfo.fileId;
		argMap["progress"] = QVariant(QString::fromStdString(resultMap["progress"])).toInt();
		emit sigDownloadStateChange(argMap);
	}
	else if (status == TempSpaceTaskStatus::OtherError ||
		status == TempSpaceTaskStatus::Error)
	{
		notifyDownloadError((int)KSpaceType::TempSpace, fileId);

		deleteTask(fileId);
	}
	else if (status == TempSpaceTaskStatus::Cancel ||
		status == TempSpaceTaskStatus::Pause)
	{
		notifyDownloadError((int)KSpaceType::TempSpace, fileId);
		deleteTask(fileId);
	}
}

void KTransferTask::onNearFieldFileTaskNotice(const SoftBus::Protocol::KFileTaskInfo& taskInfo)
{
	KCloudSvrProxy* service = kcoreApp->cloudService();
	if (!service)
		return;
	if (!service->isLogined())
		return;

	int progress = taskInfo.fileSize == 0 ? 0 : taskInfo.currentOffet * 100 / taskInfo.fileSize;

	if (SoftBus::KTransferFileStatus::Start == taskInfo.fileStatus ||
		SoftBus::KTransferFileStatus::Recovery == taskInfo.fileStatus ||
		SoftBus::KTransferFileStatus::Resume == taskInfo.fileStatus)
	{
		emit sigNearFieldMsgContent(taskInfo.msg.body.content, taskInfo.ownerId);
		deleteTask(taskInfo.ownerId);
		buildTask(KSpaceType::NearField, taskInfo.ownerId, taskInfo.filePath, taskInfo.fileSize);

		QVariantMap argMap;
		argMap["status"] = static_cast<int>(TaskStatus::Transferring);
		argMap["path"] = taskInfo.filePath;
		argMap["type"] = static_cast<int>(KSpaceType::NearField);
		argMap["fileId"] = taskInfo.ownerId;
		argMap["progress"] = progress;
		argMap["content"] = taskInfo.msg.body.content;
		emit sigDownloadStateChange(argMap);
	}
	else if (SoftBus::KTransferFileStatus::Transfering == taskInfo.fileStatus)
	{
		QVariantMap argMap;
		argMap["status"] = static_cast<int>(TaskStatus::Transferring);
		argMap["path"] = taskInfo.filePath;
		argMap["type"] = static_cast<int>(KSpaceType::NearField);
		argMap["fileId"] = taskInfo.ownerId;
		argMap["progress"] = progress;
		argMap["content"] = taskInfo.msg.body.content;
		emit sigDownloadStateChange(argMap);
	}
	else if (SoftBus::KTransferFileStatus::Finish == taskInfo.fileStatus)
	{
		QVariantMap argMap;
		argMap["status"] = static_cast<int>(TaskStatus::Finish);
		argMap["path"] = taskInfo.filePath;
		argMap["type"] = static_cast<int>(KSpaceType::NearField);
		argMap["fileId"] = taskInfo.ownerId;
		argMap["progress"] = progress;
		argMap["content"] = taskInfo.msg.body.content;
		emit sigDownloadStateChange(argMap);

		deleteTask(taskInfo.ownerId);
	}
	else if (SoftBus::KTransferFileStatus::Cancel == taskInfo.fileStatus ||
		SoftBus::KTransferFileStatus::Pause == taskInfo.fileStatus)
	{
		notifyDownloadError((int)KSpaceType::NearField, taskInfo.ownerId);
		deleteTask(taskInfo.ownerId);
	}
	else if (SoftBus::KTransferFileStatus::Error == taskInfo.fileStatus)
	{
		notifyDownloadError((int)KSpaceType::NearField, taskInfo.ownerId);
		deleteTask(taskInfo.ownerId);
	}
}

void KTransferTask::buildTask(const KSpaceType& spaceType,
	const QString& fileId, const QString& path, qint64 size)
{
	TaskInfo taskInfo;
	taskInfo.spaceType = spaceType;
	taskInfo.fileId = fileId;
	taskInfo.path = path;
	taskInfo.fileSize = size;
	m_taskMrg[fileId] = taskInfo;
}

void KTransferTask::deleteTask(const QString& fileId)
{
	m_downingTaskId.remove(fileId);
	m_taskMrg.remove(fileId);
}

void KTransferTask::notifyDownloadError(int nType, const QString& fileId)
{
	QVariantMap argMap;
	argMap["status"] = static_cast<int>(TaskStatus::Error);
	argMap["fileId"] = fileId;
	argMap["type"] = nType;
	emit sigDownloadStateChange(argMap);
}