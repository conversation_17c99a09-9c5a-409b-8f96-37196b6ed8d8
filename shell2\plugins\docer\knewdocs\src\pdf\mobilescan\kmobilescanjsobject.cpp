﻿#include "stdafx.h"
#include "kmobilescanjsobject.h"
#include "kmobilescanjshandler.h"
#include "ksolite/jsapiutilities/util/kjsapiutil.h"

void KMobileScanJSObject::OnObjectCreate()
{
	static bool hasInit = [&]()->bool
	{
		if (KMobileScanJSHandler* apiHanle = KMobileScanJSHandler::getInstance())
			apiHanle->initJsApiEvent(this->GetApiEvent());
		hasInit = true;
		return hasInit;
	}();
}

HRESULT KMobileScanJSObject::onCloseWebWidget(KMobileScanJSObject* pThis,
	const JSContextEnv& jsEnv, JSApiArguments& argList, KJSVariant& retVal,
	KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->onCloseWebWidget(pThis, jsEnv, argList, retVal, strException);
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}

HRESULT KMobileScanJSObject::sendGeneralEvent(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->sendGeneralEvent(pThis, jsEnv, argList, retVal, strException);
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}

HRESULT KMobileScanJSObject::onMobileScanFinish(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->onMobileScanFinish(pThis, jsEnv, argList, retVal, strException);
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}

HRESULT KMobileScanJSObject::onTempSpaceDownload(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->onTempSpaceDownload(pThis, jsEnv, argList, retVal, strException);
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}

HRESULT KMobileScanJSObject::cancelAllTempSpaceDownload(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->cancelAllTempSpaceDownload(pThis, jsEnv, argList, retVal, strException);
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}

HRESULT KMobileScanJSObject::registTempSpaceDownloadCallback(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->registTempSpaceDownloadCallback(KMobileScanJSHandler::JsCallBackFunc(jsEnv, argList[0]));
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}

HRESULT KMobileScanJSObject::onSizeChange(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->onSizeChange(pThis, jsEnv, argList, retVal, strException);
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}

HRESULT KMobileScanJSObject::registNearFieldFileNoticeCallback(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->registNearFieldFileNoticeCallback(KMobileScanJSHandler::JsCallBackFunc(jsEnv, argList[0]));
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}
HRESULT KMobileScanJSObject::registNetWorkCallback(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->registNetWorkCallback(KMobileScanJSHandler::JsCallBackFunc(jsEnv, argList[0]));
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}

HRESULT KMobileScanJSObject::onNetWorkRequest(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->onNetWorkRequest(pThis, jsEnv, argList, retVal, strException);
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}

HRESULT KMobileScanJSObject::registCreatePdfCallback(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
	JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException)
{
	KMobileScanJSHandler* apiHandle = KMobileScanJSHandler::getInstance();
	if (!apiHandle)
		return E_FAIL;
	apiHandle->registCreatePdfCallback(KMobileScanJSHandler::JsCallBackFunc(jsEnv, argList[0]));
	KJSApiUtil::setRetVal(retVal, VARIANT_TRUE, "", nullptr);
	return S_OK;
}