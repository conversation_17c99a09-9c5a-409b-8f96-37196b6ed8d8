(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[5],{"2f0c":function(e,t,i){"use strict";i("9ed3")},6942:function(e,t,i){},7573:function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"free-traial-index"},[t("transition",{attrs:{name:"transition-fade"}},[e.introductionModalVisible?t("IntroductionModal",{attrs:{data:e.freeTrialOpData,trialTime:e.trialTime,QRImg:e.QRImg,freeTrialCollectInfo:e.freeTrialCollectInfo},on:{onTrialModalClose:e.onTrialModalClose}}):e._e()],1),t("transition",{attrs:{name:"transition-fade"}},[e.successModalVisible?t("SuccessModal",{attrs:{trialDays:e.trialDays,trialMemberType:e.trialMemberType,freeTrialCollectInfo:e.freeTrialCollectInfo},on:{onTrialModalClose:e.onTrialModalClose}}):e._e()],1),t("transition",{attrs:{name:"transition-fade"}},[e.errorModalVisible?t("ErrorModal",{attrs:{errorKey:e.errorKey},on:{onTrialModalClose:e.onTrialModalClose}}):e._e()],1)],1)},r=[];const s={INTRODUCTION:"INTRODUCTION",SUCCESS:"SUCCESS",NEXTWORKD_ERROR:"NEXTWORKD_ERROR",REPECT_RECEIVE_ERROR:"REPECT_RECEIVE_ERROR"},l={[s.INTRODUCTION]:{closeIconMode:"light",BG:"logo-modal-bg",key:s.INTRODUCTION},[s.SUCCESS]:{closeIconMode:"light",BG:"logo-modal-bg",key:s.SUCCESS},[s.NEXTWORKD_ERROR]:{closeIconMode:"dark",BG:"error-modal-bg",key:s.NEXTWORKD_ERROR},[s.REPECT_RECEIVE_ERROR]:{closeIconMode:"dark",BG:"error-modal-bg",key:s.REPECT_RECEIVE_ERROR}};var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"introduction-modal"},[t("TrialModal",{attrs:{bg:e.MODAL_MAP.SUCCESS.BG,closeIconMode:e.MODAL_MAP.SUCCESS.closeIconMode},on:{onClose:e.handleClose}},[t("div",{staticClass:"trial-info"},[t("p",[e._v(e._s(e.data.title))]),t("div",{staticClass:"trial-desc"},[t("span",[e._v("试用时间：")]),t("span",[e._v(e._s(e.trialTime))])])]),t("div",{staticClass:"main"},[t("div",{staticClass:"left"},[e._l(e.data.introductions,(function(i,a){return[i.hide?e._e():t("div",{key:a,staticClass:"left-item"},[t("div",{staticClass:"key"},[e._v(e._s(i.field)+" ：")]),[i.is_html?t("div",{staticClass:"value values",domProps:{innerHTML:e._s(i.val)}},[e._v(" "+e._s(i.val)+" ")]):t("div",{staticClass:"value"},[e._v(" "+e._s(i.val)+" ")])]],2)]}))],2),t("div",{staticClass:"right"},[t("div",{staticClass:"img-container"},[e.QRImg?t("img",{staticClass:"scan-img",attrs:{src:e.QRImg,alt:""},on:{load:e.onImgload}}):e._e(),e.imgLoaded?e._e():t("div",{staticClass:"download-loading"},[t("SvgIcon",{attrs:{svgName:"loading-white"}})],1)]),t("p",{staticClass:"scan-desc"},[e._v("微信/支付宝扫一扫领免费会员")])])])])],1)},n=[],c=function(){var e=this,t=e._self._c;return t("div",{staticClass:"trial-modal-container"},[t("div",{staticClass:"trial-modal"},[t("div",{class:e.bg}),t("div",{staticClass:"close-icon",on:{click:e.handleClose}},[t("SvgIcon",{class:e.closeIconMode,attrs:{svgName:"close-16-3"}})],1),e._t("default")],2)])},d=[],u={name:"trial-modal",props:{bg:{type:String,default:l.NEXTWORKD_ERROR.BG},closeIconMode:{type:String,default:"dark"}},methods:{handleClose(){this.$emit("onClose")}}},_=u,p=(i("815e"),i("2877")),h=Object(p["a"])(_,c,d,!1,null,"266da5b2",null),f=h.exports,m=i("3387"),C={name:"introduction-modal",components:{TrialModal:f},props:{data:{type:Object,default:()=>({})},trialDays:{type:Number,default:0},trialTime:{type:String},QRImg:{type:String},freeTrialCollectInfo:{type:Object,default:()=>({})}},data(){return{MODAL_MAP:l,imgLoaded:!1}},methods:{handleClose(){this.$emit("onTrialModalClose",s.INTRODUCTION),m["a"].send("click",{...this.freeTrialCollectInfo,page_name:"previewpage",module_name:"tryvip_explain",element_name:"close",element_type:"button"})},onImgload(){this.imgLoaded=!0,m["a"].send("display",{...this.freeTrialCollectInfo,page_name:"previewpage",module_name:"tryvip_explain",element_name:"qr_code",element_type:"button"})}},mounted(){m["a"].send("display",{...this.freeTrialCollectInfo,page_name:"previewpage",module_name:"tryvip_explain",element_type:"module"})}},T=C,v=(i("2f0c"),Object(p["a"])(T,o,n,!1,null,"11a5d7d4",null)),g=v.exports,R=function(){var e=this,t=e._self._c;return t("div",{staticClass:"success-modal"},[t("TrialModal",{attrs:{bg:e.MODAL_MAP.SUCCESS.BG,closeIconMode:e.MODAL_MAP.SUCCESS.closeIconMode},on:{onClose:e.handleClose}},[t("div",{staticClass:"trial-info"},[t("p",[e._v("恭喜你")]),t("div",{staticClass:"trial-desc"},[t("span",[e._v("您已成功领取")]),t("span",{staticClass:"days"},[e._v(e._s(e.trialDays)+"天")]),t("span",[e._v(e._s(e.trialMemberType)+"试用")])])]),t("div",{staticClass:"main"},[t("div",{staticClass:"title-container"},[t("SvgIcon",{attrs:{svgName:"trial-privilege-left"}}),t("span",{staticClass:"title"},[e._v(e._s(e.trialMemberType)+"专享权益")]),t("SvgIcon",{attrs:{svgName:"trial-privilege-right"}})],1),t("div",{staticClass:"items"},e._l(e.privilegeList,(function(i,a){return t("div",{key:a,staticClass:"item"},[t("SvgIcon",{attrs:{svgName:i.svg}}),t("p",[e._v(e._s(i.text))])],1)})),0)]),t("div",{staticClass:"action",on:{click:e.handleClose}},[t("a",{staticClass:"btn",attrs:{href:"javascript:void(0)"}},[e._v("我知道了")])])])],1)},E=[],y={name:"success-modal",components:{TrialModal:f},props:{trialDays:{type:Number,default:0},trialMemberType:{type:String,default:"稻壳会员"},freeTrialCollectInfo:{type:Object,default:{}}},data(){return{MODAL_MAP:l,privilegeList:[{svg:"trial-privilege-1",text:"5大模板库"},{svg:"trial-privilege-2",text:"10大素材库"},{svg:"trial-privilege-3",text:"千万级文库资源"},{svg:"trial-privilege-4",text:"60+项AI应用"}]}},methods:{handleClose(){this.$emit("onTrialModalClose",s.SUCCESS),m["a"].send("click",{...this.freeTrialCollectInfo,page_name:"previewpage",module_name:"tryvip_success",element_name:"close",element_type:"button"})}},mounted(){m["a"].send("click",{...this.freeTrialCollectInfo,page_name:"previewpage",module_name:"tryvip_success",element_type:"module"})}},M=y,O=(i("d436"),Object(p["a"])(M,R,E,!1,null,"75d662f4",null)),b=O.exports,I=function(){var e=this,t=e._self._c;return t("section",{staticClass:"error-modal"},[t("TrialModal",{on:{onClose:function(t){return e.handleClose(e.errorKey)}}},[t("div",{staticClass:"error-icon"},[t("SvgIcon",{attrs:{svgName:"trial-error"}})],1),t("div",{staticClass:"error-info"},[e._v(" 领取失败 ")]),e.errorKey===e.MODALE_KEY.REPECT_RECEIVE_ERROR?t("div",{staticClass:"error-desc"},[e._v(" 抱歉，您目前未符合试用条件，不能领取试用会员 ")]):e._e(),e.errorKey===e.MODALE_KEY.NEXTWORKD_ERROR?t("div",{staticClass:"error-desc"},[e._v(" 当前网络异常，领取失败 ")]):e._e(),t("div",{staticClass:"error-action",on:{click:function(t){return e.handleClose(e.errorKey,e.errorKey===e.MODALE_KEY.NEXTWORKD_ERROR)}}},[t("a",{staticClass:"btn",attrs:{href:"javascript:void(0)"}},[e._v("我知道了")])])])],1)},S=[],D={name:"error-modal",components:{TrialModal:f},data(){return{MODALE_KEY:s}},props:{errorKey:{type:String}},methods:{handleClose(e,t=!1){this.$emit("onTrialModalClose",e,t)}}},k=D,V=(i("ea68"),Object(p["a"])(k,I,S,!1,null,"c6946e06",null)),N=V.exports,K=i("f348"),w=i("10de"),P=i("beeb"),U=i("c8fc"),x=i("3813"),A=i("3aea"),$={name:"free-trial",components:{IntroductionModal:g,SuccessModal:b,ErrorModal:N},data(){return{introductionModalVisible:!1,successModalVisible:!1,errorModalVisible:!1,errorKey:s.NEXTWORKD_ERROR,timer:null,QRImg:"",contractCode:"",signSuccessed:!1}},props:{freeTrialOpData:{type:Object},freeTrialCollectInfo:{type:Object,default:()=>({})},serverTime:{type:Number}},mounted(){w["a"].$on(P["p"].showFreeTrialPop,()=>{this.handleOpenPop()}),m["a"].send("display",{...this.freeTrialCollectInfo,$e_n:"tryvip_download",$e_t:"button",$m_n:`rightside_pay[${this.freeTrialCollectInfo.resource_type}]`})},methods:{onTrialModalClose(e){switch(e){case s.INTRODUCTION:this.introductionModalVisible=!1;break;case s.SUCCESS:this.successModalVisible=!1,this.introductionModalVisible=!1,this.regainUserInfo();break;case s.NEXTWORKD_ERROR:this.errorModalVisible=!1;break;case s.REPECT_RECEIVE_ERROR:this.errorModalVisible=!1,this.introductionModalVisible=!1;break;default:break}},handleOpenPop(){if(!(this.introductionModalVisible||this.successModalVisible||this.errorModalVisible)){if(this.signSuccessed)return this.errorModalVisible=!0,this.errorKey=s.REPECT_RECEIVE_ERROR,void w["a"].$emit(P["p"].checkFreeTrialRight);this.introductionModalVisible=!0,this.handleSignContract(),m["a"].send("click",{...this.freeTrialCollectInfo,$e_n:"tryvip_download",$e_t:"button",$m_n:`rightside_pay[${this.freeTrialCollectInfo.resource_type}]`})}},clearTimer(){this.timer&&clearInterval(this.timer)},handleSignContract(){this.clearTimer();const{freeTrialPosition:e,pay_key:t}=this.freeTrialCollectInfo,i={hdid:P["e"].hdid,pay_origin:P["e"].name+"_newfile_preview_sign",position:"function"==typeof e?e():x["a"].getPosition(),pay_key:t,pos:P["e"].isPrivilegeSwitchOn?"pc_free_trial_new":"pc_free_trial"};this.QRImg="",U["a"].get("freeTrial").signContract({data:i}).then(e=>{if(e&&"ok"===e.result&&e.data&&e.data.contract_code&&e.data.url){const{url:t,contract_code:i}=e.data;return this.QRImg=t,this.contractCode=i,void this.checkSignResult()}this.clearTimer(),this.introductionModalVisible=!1,this.errorModalVisible=!0,this.errorKey=s.REPECT_RECEIVE_ERROR}).catch(e=>{console.log("err",e),this.clearTimer(),this.introductionModalVisible=!1,this.errorModalVisible=!0,this.errorKey=s.NEXTWORKD_ERROR})},checkSignResult(){this.clearTimer();const e={contract_code:this.contractCode};this.timer=setInterval(()=>{console.log("轮询签约状态"),U["a"].get("freeTrial").checkIsSigned({data:e}).then(e=>{if(e&&"ok"===e.result&&e.data){console.log("成功了，将会显示成功弹框"),this.clearTimer(),this.successModalVisible=!0,this.introductionModalVisible=!1,this.signSuccessed=!0;const e=P["g"].freeTrialedCache+this.freeTrialCollectInfo.userid;A["a"].set(e,!0)}}).catch(e=>{console.log(e),this.clearTimer(),this.introductionModalVisible=!1,this.errorModalVisible=!0,this.errorKey=s.NEXTWORKD_ERROR})},5e3)},regainUserInfo(){this.$nextTick(()=>{w["a"].$emit(P["p"].checkFreeTrialRight),window.rootApp.$store.dispatch("user/setUserInfo")})}},beforeDestroy(){this.clearTimer(),w["a"].$off(P["p"].showFreeTrialPop)},computed:{trialDays(){return this.freeTrialOpData&&this.freeTrialOpData.introductions&&this.freeTrialOpData.introductions.length&&this.freeTrialOpData.introductions[0].trial_days?this.freeTrialOpData.introductions[0].trial_days:0},trialMemberType(){return this.freeTrialOpData&&this.freeTrialOpData.introductions&&this.freeTrialOpData.introductions.length&&this.freeTrialOpData.introductions[0].val?this.freeTrialOpData.introductions[0].val:""},trialTime(){const e=(this.serverTime||(new Date).valueOf())/1e3,t=e+24*this.trialDays*60*60;return[K["a"].dateFormat(e,"yyyy.MM.dd",!0),K["a"].dateFormat(t,"yyyy.MM.dd",!0)].join("-")},TrialBtnText(){return this.freeTrialOpData&&this.freeTrialOpData.new_btn_text||""},freeTrialBadgeText(){return this.freeTrialOpData&&this.freeTrialOpData.new_btn_subscript||""}}},L=$,j=(i("ba58"),Object(p["a"])(L,a,r,!1,null,"29a2299a",null));t["default"]=j.exports},"815e":function(e,t,i){"use strict";i("aec4")},"9ed3":function(e,t,i){},aec4:function(e,t,i){},b620:function(e,t,i){},ba58:function(e,t,i){"use strict";i("b620")},d436:function(e,t,i){"use strict";i("6942")},ea68:function(e,t,i){"use strict";i("f380")},f380:function(e,t,i){}}]);