﻿#ifndef __KMOBILESCAN_VIEW_KJSOBJECT_H__
#define __KMOBILESCAN_VIEW_KJSOBJECT_H__

#include <api/jsapi/ksojscore/ksojscore_h.h>

class KMobileScanJSObject : public KJCExtendJsEventObj<KMobileScanJSObject>
{
	BeginWPSJSObjPropMap_SINGLETONE(KMobileScanJSObject)
		WPSJSObjFunctor("onCloseWebWidget", onCloseWebWidget,
			JSScope_WpsApp | JSScope_DefaultCanuse,
			JArg<PERSON>(json), 0, J<PERSON><PERSON><PERSON>(json, NULL))
		WPSJSObjFunctor("onMobileScanFinish", onMobileScanFinish,
			JSScope_WpsApp | JSScope_DefaultCanuse,
			JArgR(json), 1, J<PERSON><PERSON><PERSON>(json, NULL))
		WPSJSObjFunctor("onTempSpaceDownload", onTempSpaceDownload,
			JSScope_WpsApp | J<PERSON>cope_DefaultCanuse,
			<PERSON><PERSON><PERSON><PERSON>(json), 1, <PERSON><PERSON><PERSON><PERSON>(json, NULL))
		WPSJSObjFunctor("cancelAllTempSpaceDownload", cancelAllTempSpaceDownload,
			JSScope_WpsApp | JSScope_DefaultCanuse,
			JArgR(json), 0, JArgC(json, NULL))
		WPSJSObjFunctor("sendGeneralEvent", sendGeneralEvent,
			JSScope_WpsApp | JSScope_DefaultCanuse,
			JArgR(json), 2, JArgC(json, NULL), JArgC(json, NULL))
		WPSJSObjFunctor("registTempSpaceDownloadCallback", registTempSpaceDownloadCallback,
			JSScope_WpsApp | JSScope_DefaultCanuse, JArgR(json), 1,
			WPSJSArgument((VARENUM)VT_VARIANT, EJSParamNormal, NULL))
		WPSJSObjFunctor("onSizeChange", onSizeChange,
			JSScope_WpsApp | JSScope_DefaultCanuse,
			JArgR(json), 1, JArgC(json, NULL))
		WPSJSObjFunctor("registNearFieldFileNoticeCallback", registNearFieldFileNoticeCallback,
			JSScope_WpsApp | JSScope_DefaultCanuse, JArgR(json), 1,
			WPSJSArgument((VARENUM)VT_VARIANT, EJSParamNormal, NULL))
		WPSJSObjFunctor("registNetWorkCallback", registNetWorkCallback,
			JSScope_WpsApp | JSScope_DefaultCanuse, JArgR(json), 1,
			WPSJSArgument((VARENUM)VT_VARIANT, EJSParamNormal, NULL))
		WPSJSObjFunctor("onNetWorkRequest", onNetWorkRequest,
			JSScope_WpsApp | JSScope_DefaultCanuse,
			JArgR(json), 1, JArgC(json, NULL))
		WPSJSObjFunctor("registCreatePdfCallback", registCreatePdfCallback,
			JSScope_WpsApp | JSScope_DefaultCanuse, JArgR(json), 1,
			WPSJSArgument((VARENUM)VT_VARIANT, EJSParamNormal, NULL))
	EndWPSJSObjPropMap()

public:
	virtual void OnObjectCreate() override;
	using JsCallBackFunc = QPair<JSContextEnv, KJSVariant>;
	static HRESULT onCloseWebWidget(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT sendGeneralEvent(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT onMobileScanFinish(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT onTempSpaceDownload(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT cancelAllTempSpaceDownload(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT registTempSpaceDownloadCallback(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT onSizeChange(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT registNearFieldFileNoticeCallback(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT registNetWorkCallback(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT onNetWorkRequest(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
	static HRESULT registCreatePdfCallback(KMobileScanJSObject* pThis, const JSContextEnv& jsEnv,
		JSApiArguments& argList, KJSVariant& retVal, KJSVariant& strException);
};

#endif // __KMOBILESCAN_VIEW_KJSOBJECT_H__