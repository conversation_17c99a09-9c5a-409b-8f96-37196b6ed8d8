﻿#ifndef __MOBILESCAN_VIEW_KJSHANDLER_H__
#define __MOBILESCAN_VIEW_KJSHANDLER_H__

#include "api/jsapi/ksojscore/ksojscore_intf.h"
#include "kmobilescanjsobject.h"

class KMobileScanJSHandler : public QObject
{
	Q_OBJECT
public:
	using JsCallBackFunc = QPair<JSContextEnv, KJSVariant>;
	static KMobileScanJSHandler* getInstance();
	void initJsApiEvent(IKJSExtentionApiEvent* pJsApiEvent);
	HRESULT onCloseWebWidget(KMobileScanJSObject* pThis,
		const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
		KJSVariant& strException);
	HRESULT sendGeneralEvent(KMobileScanJSObject* pThis,
		const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
		KJSVariant& strException);
	HRESULT onMobileScanFinish(KMobileScanJSObject* pThis,
		const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
		KJSVariant& strException);
	HRESULT onTempSpaceDownload(KMobileScanJSObject* pThis,
		const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
		KJSVariant& strException);
	HRESULT cancelAllTempSpaceDownload(KMobileScanJSObject* pThis,
		const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
		KJSVariant& strException);
	HRESULT onSizeChange(KMobileScanJSObject* pThis,
		const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
		KJSVariant& strException);
	HRESULT onNetWorkRequest(KMobileScanJSObject* pThis,
		const JSContextEnv& jsEnv, JSApiArguments argList, KJSVariant& retVal,
		KJSVariant& strException);
	void registTempSpaceDownloadCallback(const JsCallBackFunc& callbackFunc);
	void tempSpaceDownloadCallback(const QVariantMap& result);
	void registNearFieldFileNoticeCallback(const JsCallBackFunc& callbackFunc);
	void nearFieldFileNoticeCallback(const QVariantMap& result);
	void registNetWorkCallback(const JsCallBackFunc& callbackFunc);
	void netWorkRequestCallback(const QVariantMap& result);
	void registCreatePdfCallback(const JsCallBackFunc& callbackFunc);
	void createScanPdfCallback(bool bAllSuccess);

signals:
	void webDialogClose();

private:
	explicit KMobileScanJSHandler(QObject* parent = nullptr);
	~KMobileScanJSHandler();

private:
	IKJSExtentionApiEvent* m_jsAPiObjApiEvent{ nullptr };
	JsCallBackFunc m_funcDownloadJsCb;
	JsCallBackFunc m_funcNearFieldNoticeJsCb;
	JsCallBackFunc m_funcNetWorkJsCb;
	JsCallBackFunc m_funcCreatePdfJsCb;
};

#endif // __MOBILESCAN_VIEW_KJSHANDLER_H__