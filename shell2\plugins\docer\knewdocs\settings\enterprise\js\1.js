webpackJsonp([1],{"+6Bu":function(t,e,n){"use strict";e.__esModule=!0,e.default=function(t,e){var n={};for(var i in t)e.indexOf(i)>=0||Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i]);return n}},"4jC7":function(t,e){},"5+YK":function(t,e){},"5n6T":function(t,e){},"8cyh":function(t,e){},"9HN5":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i={name:"preview-footer",props:{appName:{type:String,default:function(){return"wps"}},showTip:{type:Boolean,default:function(){return!1}}},methods:{downloadTpl:function(){return this.$emit("downloadTpl")},nextStep:function(){return this.$emit("nextStep")}}},r={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"footer",class:t.appName},[n("div",{staticClass:"button-group"},[n("div",{staticClass:"g-price-button button",on:{click:t.nextStep}},[t._v(t._s(t.$t("m.downloadTpl")))]),t._v(" "),t.showTip?n("span",{staticClass:"tip"},[t._v("该模板支持智能公文生成")]):t._e()])])},staticRenderFns:[]};var s=n("VU/8")(i,r,!1,function(t){n("rROH")},"data-v-755c2568",null).exports,o={name:"preview",props:{previewThumbList:{type:Array},appName:{type:String,default:function(){return"wps"}}},computed:{size:function(){return{wps:"/480X960.webp",et:"/552X900.WEBP",wpp:"/640X1280.webp"}[this.appName]},getStyle:function(){return{wps:{width:"650px"},et:{width:"552px"},wpp:{width:"640px"}}[this.appName]}}},a={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"img-list",class:t.appName},t._l(t.previewThumbList,function(e){return n("div",{key:e,staticClass:"img-body g-flex-box-center"},[n("img",{style:t.getStyle,attrs:{src:e}})])}))},staticRenderFns:[]};var u=n("VU/8")(o,a,!1,function(t){n("dWkZ")},"data-v-a2d2eef6",null).exports,c=n("//Fk"),h=n.n(c),l=n("Gu7T"),f=n.n(l),p=n("woOf"),d=n.n(p),v=n("Dd8w"),_=n.n(v),y=n("iWRZ"),g=n("n1nN"),m=n("NYxO"),w=n("wYMm"),O=n("v8ob"),b=n("59SO"),C=n("s0MJ"),I=n("Dzwp"),D=n("w7XY"),x={wps:1,et:2,wpp:3}[w.b.name],S={name:"preview",mixins:[y.a],props:{previewThumbList:{type:Object,default:function(){return{}}}},data:function(){return{status:w.h.LOADING,item:{},listData:{},uMayLikeList:[],appName:w.b.name,previewStyle:{transform:"translateY(100%)",opacity:0},isPressional:!0}},computed:_()({},Object(m.c)(["getPreviewData","getPreviewIndex","getIsDocerVip","getPreviewInfo","checkHasTplBought"]),{currentItem:function(){var t=this.getPreviewData[this.getPreviewIndex]||{};return w.i.enable&&(t=d()(t,{c_avatar:n("qhKz"),author:"WPS",publish_time:"2020-02-20"})),t},shouldShowGotoNext:function(){return this.getPreviewIndex<this.getPreviewData.length-1},shouldShowGotoLast:function(){return!!this.getPreviewIndex},shouldShowView:function(){return!w.i.enable},isEasy:function(){return"wpp"===w.b.name}}),mounted:function(){this.getPreviewDetails(),window._ESC_HOOKS.push({method:this.close,key:"preview"}),O.a.$on(w.g.boardcastPurchaseStatus,this.downloadCallback),this.show(),this.consolidateInfo("preview_show"),I.a.setIsPreview(!0)},beforeDestroy:function(){O.a.$off(w.g.boardcastPurchaseStatus,this.downloadCallback),Object(C.f)("preview"),I.a.setIsPreview(!1)},methods:_()({},Object(m.b)(["setAppComponent","setPreviewIndex","addIdtoPurchasesList"]),{consolidateInfo:function(t){b.a.send.apply(b.a,f()([this.getPreviewInfo.from,t,this.getPreviewInfo.module].filter(function(t){return t})))},show:function(){var t=this;window.setTimeout(function(){t.previewStyle={transform:"translateY(0)",opacity:1}},100)},hide:function(){return this.previewStyle={transform:"translateY(100%)",opacity:0},new h.a(function(t){window.setTimeout(function(){t()},200)})},gotoNext:function(){this.consolidateInfo("preview_right_click"),this.getPreviewIndex>=this.getPreviewData.length-1||this.setPreviewIndex(this.getPreviewIndex+1)},gotoLast:function(){this.consolidateInfo("preview_left_click"),this.getPreviewIndex&&this.setPreviewIndex(this.getPreviewIndex-1)},getPreviewDetails:function(){this.status=w.h.LOADING,w.i.enable&&this.currentItem.filePath?this.setListData():this.isPressional?this.getDocerData():this.isEasy?this.getEasyData():(this.getDocerData(),this.getUMaylikeDocer())},setListData:function(){var t=Object(C.d)(this.currentItem.filePath);this.listData.urls=[n("SkEa")("./"+t+".svg")]},getDocerData:function(){var t=this;g.a.get("preview").fetch({data:{id:this.currentItem.id}}).then(function(e){"ok"===e.result?(t.item=e.data.basic_data,t.listData=e.data.page_data,t.status=w.h.LOADED):t.status=w.h.ERROR}).catch(function(){t.status=w.h.ERROR})},getEasyData:function(){var t=this;g.a.get("easy").fetchDetail({data:{tid:+this.currentItem.id}}).then(function(e){"ok"===e.result?(t.uMayLikeList=e.data.detail.similars,t.item={c_avatar:e.data.detail.authorAvatar},t.listData={urls:e.data.detail.preUrls},t.status=w.h.LOADED):t.status=w.h.ERROR}).catch(function(){t.status=w.h.ERROR})},getUMaylikeDocer:function(){var t=this;return g.a.get("uMayLike").query({data:{id:this.currentItem.id,mb_app:x}}).then(function(e){"ok"===e.result&&e.data?t.uMayLikeList=e.data.data:t.uMayLikeList=[]}).catch(function(){t.uMayLikeList=[]})},buyTpl:function(){this.buyItem(this.currentItem),this.consolidateInfo("preview_retail_click"),I.a.setBtnPos("")},downloadTpl:function(){w.i.enable&&this.currentItem.filePath?D.a.openLocalFile({filePath:this.currentItem.filePath,mod:1}).then(function(t){}).catch(function(t){}):(this.consolidateInfo("preview_use_click"),this.download(this.currentItem))},downloadCallback:function(t){var e=t.mbid;"finish"===t.paystatus&&(this.download(this.currentItem),this.addIdtoPurchasesList(e))},close:function(t){var e=this;t&&b.a.send(this.getPreviewInfo.from,"preview_close_click",t),this.hide().then(function(){e.setAppComponent("")})},resetTpl:function(){this.uMayLikeList=[],this.listData={}}}),watch:{"currentItem.id":function(t){t&&(this.resetTpl(),this.getPreviewDetails())},"currentItem.filePath":function(){this.setListData()}},components:{PreviewFooter:s,ImgList:u}},k={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"preview",style:t.previewStyle,on:{click:function(e){t.close("1")}}},[n("div",{staticClass:"min-width"},[n("div",{staticClass:"preview-body",class:t.appName,on:{click:function(t){t.stopPropagation()}}},[n("span",{staticClass:"icon icon-close",on:{click:function(e){t.close("0")}}}),t._v(" "),n("div",{staticClass:"preview-left"},[n("div",{staticClass:"title-line"},[n("div",{staticClass:"wrapper"},[n("div",{staticClass:"title g-txt-ellipsis wrapper"},[t._v(t._s(t.currentItem.name))]),t._v(" "),n("div",{staticClass:"tooltip"},[n("div",{attrs:{selectable:"tooltip"}},[t._v(t._s(t.currentItem.name))]),t._v(" "),t.shouldShowView?n("div",{attrs:{selectable:"tooltip"}},[t._v("编号："+t._s(t.currentItem.id))]):t._e()])]),t._v(" "),n("div",{staticClass:"desc"},[n("div",{staticClass:"author"},[n("img",{staticClass:"head-icon",attrs:{src:t.currentItem.c_avatar}}),t._v(" "),n("span",{staticClass:"author-name"},[t._v(t._s(t.currentItem.author))]),t._v(" "),n("span",{staticClass:"data"},[t._v(t._s(t.currentItem.publish_time||t.currentItem.pubTime.slice(0,10)))])]),t._v(" "),t.shouldShowView?n("div",{staticClass:"view"},[n("span",{staticClass:"icon icon-view"}),t._v(" "),n("span",[t._v(t._s(t.currentItem.preview||t.currentItem.down_num))])]):t._e()])]),t._v(" "),n("div",{staticClass:"preview-row"},[n("div",{staticClass:"op-body"},[n("img-list",{attrs:{previewThumbList:t.listData.urls,appName:t.appName}})],1)]),t._v(" "),n("preview-footer",{attrs:{appName:t.appName},on:{nextStep:t.downloadTpl}})],1),t._v(" "),t.shouldShowGotoLast?n("div",{staticClass:"goto-last",on:{click:function(e){e.stopPropagation(),t.gotoLast()}}},[n("span",{staticClass:"icon icon-pre"})]):t._e(),t._v(" "),t.shouldShowGotoNext?n("div",{staticClass:"goto-next",on:{click:function(e){e.stopPropagation(),t.gotoNext()}}},[n("span",{staticClass:"icon icon-next"})]):t._e()])])])},staticRenderFns:[]};var E=n("VU/8")(S,k,!1,function(t){n("S1Rw")},"data-v-5a827a97",null).exports,T=n("bqkH");n.d(e,"PreviewFooter",function(){return s}),n.d(e,"ImgList",function(){return u}),n.d(e,"default",function(){return E}),n.d(e,"OfficialPreview",function(){return T.default})},NKwL:function(t,e){t.exports={1:{"类别":"命令","要素":"5,8,13,14,17,18"},2:{"类别":"信函","要素":"1,2,3,4,5,7,10,11,12,13,14,15,16,18,19,23,24,25"},3:{"类别":"会议纪要","要素":"1,2,3,4,6,8,10,11,13,14,16,18,20,21,22,24,25,26,27"},4:{"类别":"报告","要素":"1,2,3,4,5,7,9,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},5:{"类别":"公告","要素":"1,2,3,4,5,7,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},6:{"类别":"公报","要素":"1,2,3,4,5,7,9,10,11,13,14,15,16,18,19,23,24,25,26,27"},7:{"类别":"函","要素":"1,2,3,4,5,7,9,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},8:{"类别":"决定","要素":"1,2,3,4,5,7,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},9:{"类别":"决议","要素":"1,2,3,4,5,7,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},10:{"类别":"批复","要素":"1,2,3,4,5,7,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},11:{"类别":"请示","要素":"1,2,3,4,5,7,9,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},12:{"类别":"通报","要素":"1,2,3,4,5,7,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},13:{"类别":"通告","要素":"1,2,3,4,5,7,9,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},14:{"类别":"通知","要素":"1,2,3,4,5,7,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},15:{"类别":"议案","要素":"1,2,3,4,5,7,9,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},16:{"类别":"意见","要素":"1,2,3,4,5,7,9,10,11,12,13,14,15,16,18,19,23,24,25,26,27"},17:{"类别":"讲话稿","要素":"11,28,29,30"}}},QC5y:function(t,e){},R3w5:function(t,e){},S1Rw:function(t,e){},"Tkt/":function(t,e){},VvEv:function(t,e){},bqkH:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=n("woOf"),r=n.n(i),s=n("//Fk"),o=n.n(s),a=n("Gu7T"),u=n.n(a),c=n("Dd8w"),h=n.n(c),l=n("9HN5"),f=n("iWRZ"),p=n("n1nN"),d=n("NYxO"),v=n("wYMm"),_=n("v8ob"),y=n("59SO"),g=n("s0MJ"),m=n("Dzwp"),w=n("Yvq4"),O=n.n(w),b={data:function(){return{create_date:""}},props:{type:String},methods:h()({},Object(d.b)(["setOfficialDocElement"]),{setDate:function(t,e){var n={name:this.title(t),content:e};this.setOfficialDocElement({key:t,value:n})},getDate:function(t){return this.create_date=this.getOfficialDocElements[t].content,this.getOfficialDocElements[t].content},title:function(t){return O.a[t]["名字"]}}),computed:h()({},Object(d.c)(["getOfficialDocElements","getOfficialDocToday"])),created:function(){this.setDate(this.type,this.getOfficialDocToday),this.getDate(this.type)}},C={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"da-table"},[n("el-date-picker",{attrs:{type:"date",format:"yyyy-MM-dd",placeholder:"请选择日期"},on:{change:function(e){t.setDate(t.type,t.create_date)}},model:{value:t.create_date,callback:function(e){t.create_date=e},expression:"create_date"}})],1)])},staticRenderFns:[]};var I=n("VU/8")(b,C,!1,function(t){n("5+YK")},null,null).exports,D={data:function(){return{indexCl:"",indexEm:"",indexTi:""}},props:{},methods:h()({},Object(d.b)(["setOfficialDocElement"]),{title:function(t){return O.a[t]["名字"]},clickOption:function(t,e){var n="";if(t==this.idCl)this.indexCl==e?this.indexCl="":this.indexCl=e,n=this.indexCl;else if(t==this.idEm)this.indexEm==e?this.indexEm="":this.indexEm=e,n=this.indexEm;else{if(t!=this.idTi)return;this.indexTi==e?this.indexTi="":this.indexTi=e,n=this.indexTi}var i={name:this.title(t),content:n};this.setOfficialDocElement({key:t,value:i})},clickEm:function(t){this.indexEm==t?this.indexEm=-1:this.indexEm=t},clickTi:function(t){this.indexTi==t?this.indexTi=-1:this.indexTi=t},getOption:function(t){return t in this.getOfficialDocElements?this.getOfficialDocElements[t].content:""},classOption:function(t,e){return{buttonSelected:t==e}}}),computed:h()({},Object(d.c)(["getOfficialDocElements"]),{titleCl:function(){return O.a[2]["名字"]+"："},titleEm:function(){return O.a[4]["名字"]+"："},titleTi:function(){return O.a[3]["名字"]+"："},dataCl:function(){return O.a[2]["选项"]},dataEm:function(){return O.a[4]["选项"]},dataTi:function(){return O.a[3]["选项"]},timeList:function(){for(var t=this.dataTi,e=[],n=[],i=0;i<t.length;i++)n.push(t[i]),3==n.length&&(e.push(n),n=[]);return e.push(n),e},bgcCl:function(){return this.indexCl},bgcEm:function(){return this.indexEm},bgcTi:function(){return this.indexTi},idCl:function(){return"2"},idEm:function(){return"4"},idTi:function(){return"3"}})},x={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("table",{staticClass:"pr-table"},[n("tr",[n("th",{staticClass:"pr-table-head"},[t._v(t._s(t.titleCl))]),t._v(" "),t._l(t.dataCl,function(e){return n("td",{key:e},[n("button",{staticClass:"pr-div",class:t.classOption(t.indexCl,e),on:{click:function(n){t.clickOption(t.idCl,e)}}},[t._v(t._s(e))])])})],2),t._v(" "),n("tr",[n("th",{staticClass:"pr-table-head"},[t._v(t._s(t.titleEm))]),t._v(" "),t._l(t.dataEm,function(e){return n("td",{key:e},[n("button",{staticClass:"pr-div",class:t.classOption(t.indexEm,e),on:{click:function(n){t.clickOption(t.idEm,e)}}},[t._v(t._s(e))])])})],2),t._v(" "),n("tr",[n("th",{staticClass:"pr-table-head pr-height",staticStyle:{"vertical-align":"top"}},[t._v(t._s(t.titleTi))]),t._v(" "),n("td",{attrs:{colspan:"3"}},[n("table",{staticClass:"pr-table"},t._l(t.timeList,function(e,i){return n("tr",{key:i},t._l(e,function(e){return n("td",{key:e},[n("button",{staticClass:"pr-div",class:t.classOption(t.indexTi,e),on:{click:function(n){t.clickOption(t.idTi,e)}}},[t._v(t._s(e))])])}))}))])])])])},staticRenderFns:[]};var S=n("VU/8")(D,x,!1,function(t){n("QC5y")},null,null).exports,k={data:function(){return{stateData:{},maxShow:3}},props:{type:String},methods:h()({},Object(d.b)(["setOfficialDocElement","setOdCustomListData","setOdCustomListChecked"]),{title:function(t){return O.a[t]["名字"]}}),computed:h()({},Object(d.c)(["getOfficialDocElements","getOdCustomListData","getOdCustomListChecked"]),{rawList:function(){return this.getOdCustomListData[this.type]||[]},radioList:function(){for(var t=this.rawList,e=Math.min(t.length,this.maxShow),n=[],i=0;i<e;i++)n.push(t[i]);return n},hasMore:function(){return this.rawList.length>3},checkedNames:{get:function(){return this.getOdCustomListChecked[this.type]||[]},set:function(t){var e=this.type,n={key:e,value:t};this.setOdCustomListChecked(n);var i=t.join(","),r={name:this.title(e),content:i};this.setOfficialDocElement({key:e,value:r})}},ctrlData:function(){var t=v.l.options||{};return this.type in t?v.l.options[this.type]["选项"]:O.a[this.type]["选项"]}}),created:function(){if(!(this.type in this.getOdCustomListData)){var t={key:this.type,value:this.ctrlData||[]};this.setOdCustomListData(t)}}},E={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("table",{staticClass:"ra-table"},[n("tbody",[t._l(t.radioList,function(e){return n("div",{key:e,staticStyle:{height:"32px"}},[n("tr",[n("td",{staticClass:"ra-table-radio"},[n("label",{staticClass:"ra-click"},[n("input",{directives:[{name:"model",rawName:"v-model",value:t.checkedNames,expression:"checkedNames"}],staticClass:"ra-input",attrs:{type:"checkbox"},domProps:{value:e,checked:Array.isArray(t.checkedNames)?t._i(t.checkedNames,e)>-1:t.checkedNames},on:{change:function(n){var i=t.checkedNames,r=n.target,s=!!r.checked;if(Array.isArray(i)){var o=e,a=t._i(i,o);r.checked?a<0&&(t.checkedNames=i.concat([o])):a>-1&&(t.checkedNames=i.slice(0,a).concat(i.slice(a+1)))}else t.checkedNames=s}}})]),t._v(" "),n("div",{staticClass:"ra-content",attrs:{title:e}},[t._v(t._s(e))])])])])}),t._v(" "),t.hasMore?n("div",{staticClass:"ra-table-more"},[t._v("......")]):t._e()],2)]),t._v(" "),0==t.radioList.length?n("div",{staticClass:"ra-empty"},[t._v("暂无数据，请点击更多新增数据")]):t._e()])},staticRenderFns:[]};var T=n("VU/8")(k,E,!1,function(t){n("Tkt/")},null,null).exports,L={data:function(){return{radioList:[],stateData:{},hasMore:!1}},props:{type:String,colCount:Number},methods:h()({},Object(d.b)(["setOfficialDocElement","setOdCustomListData","setOdCustomListChecked"]),{setData:function(t,e){this.setItem(t,e);var n="";for(var i in this.stateData)n=n+this.stateData[i]+",";var r=this.type,s={name:this.title(r),content:n};this.setOfficialDocElement({key:r,value:s})},setItem:function(t,e){this.stateData[t]==e?this.stateData[t]="":this.stateData[t]=e},title:function(t){return O.a[t]["名字"]},topNItems:function(t,e){for(var n=[],i=t.length>e?e:t.length,r=0;r<i;r++)n.push(t[r]);return n}}),computed:h()({},Object(d.c)(["getOfficialDocElements","getOdCustomListData","getOdCustomListChecked"]),{rowList:function(){var t=this.getOdCustomListData[this.type];this.hasMore=t.length>3*this.colCount;for(var e=[],n=[],i=0;i<t.length;i++)n.push(t[i]),n.length==this.colCount&&(e.push(n),n=[]);return 0!=n.length&&e.push(n),e},checkedNames:{get:function(){return this.getOdCustomListChecked[this.type]||[]},set:function(t){var e=this.type,n={key:e,value:t};this.setOdCustomListChecked(n);var i=t.join(","),r={name:this.title(e),content:i};this.setOfficialDocElement({key:e,value:r})}},ctrlData:function(){var t=v.l.options||{};return this.type in t?v.l.options[this.type]["选项"]:O.a[this.type]["选项"]}}),created:function(){if(!(this.type in this.getOdCustomListData)){var t={key:this.type,value:this.ctrlData||[]};this.setOdCustomListData(t)}}},z={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("table",{staticClass:"rax-table"},[n("tbody",[t._l(t.topNItems(t.rowList,3),function(e,i){return n("tr",{key:i},t._l(e,function(e){return n("td",{key:e,staticClass:"rax-table-radio"},[n("span",[n("label",{staticClass:"rax-click"},[n("input",{directives:[{name:"model",rawName:"v-model",value:t.checkedNames,expression:"checkedNames"}],staticClass:"rax-input",attrs:{type:"checkbox"},domProps:{value:e,checked:Array.isArray(t.checkedNames)?t._i(t.checkedNames,e)>-1:t.checkedNames},on:{change:function(n){var i=t.checkedNames,r=n.target,s=!!r.checked;if(Array.isArray(i)){var o=e,a=t._i(i,o);r.checked?a<0&&(t.checkedNames=i.concat([o])):a>-1&&(t.checkedNames=i.slice(0,a).concat(i.slice(a+1)))}else t.checkedNames=s}}})]),t._v(" "),n("div",{staticClass:"rax-content",attrs:{title:e}},[t._v(t._s(e))])])])}))}),t._v(" "),t.hasMore?n("tr",[n("td",{attrs:{colspan:"3"}},[t._v("......")])]):t._e()],2)]),t._v(" "),0==t.rowList.length?n("div",{staticClass:"rax-empty"},[t._v("暂无数据，请点击更多新增数据")]):t._e()])},staticRenderFns:[]};var A=n("VU/8")(L,z,!1,function(t){n("jHgH")},null,null).exports,M={data:function(){return{number:{},year:"",num:""}},props:{type:String},methods:h()({},Object(d.b)(["setOfficialDocElement"]),{setParam:function(t,e){2===t&&(this.year=e.replace(/[^0-9]/g,"")),3===t&&(this.num=e.replace(/[^0-9]/g,"")),this.number[t]=e;var n=(this.number[1]||"")+"〔"+(this.year.replace(/\b(0+)/gi,"")||"")+"〕"+(this.num.replace(/\b(0+)/gi,"")||"")+"号",i=this.type,r={name:this.title(i),content:n};this.setOfficialDocElement({key:i,value:r})},title:function(t){return O.a[t]["名字"]}}),computed:h()({},Object(d.c)(["getOfficialDocElements"]))},j={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"nu-table"},[n("input",{staticClass:"nu-i1",attrs:{maxlength:"25"},on:{input:function(e){t.setParam(1,e.target.value)}}}),t._v(" "),n("a",[t._v("〔")]),t._v(" "),n("input",{directives:[{name:"model",rawName:"v-model",value:t.year,expression:"year"}],staticClass:"nu-i2",attrs:{maxlength:"4"},domProps:{value:t.year},on:{input:[function(e){e.target.composing||(t.year=e.target.value)},function(e){t.setParam(2,t.year)}]}}),t._v(" "),n("a",[t._v("〕")]),t._v(" "),n("input",{directives:[{name:"model",rawName:"v-model",value:t.num,expression:"num"}],staticClass:"nu-i2",domProps:{value:t.num},on:{input:[function(e){e.target.composing||(t.num=e.target.value)},function(e){t.setParam(3,t.num)}]}}),t._v(" "),n("a",[t._v("号")])])])},staticRenderFns:[]};var N=n("VU/8")(M,j,!1,function(t){n("racU")},null,null).exports,P={data:function(){return{}},props:{type:String},methods:h()({},Object(d.b)(["setOfficialDocElement"]),{setData:function(t){var e=this.type,n={name:this.title(e),content:t};this.setOfficialDocElement({key:e,value:n})},title:function(t){return O.a[t]["名字"]}}),computed:h()({},Object(d.c)(["getOfficialDocElements"]))},R={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"te-body"},[n("input",{staticClass:"te-input",attrs:{maxlength:"25"},on:{input:function(e){t.setData(e.target.value)}}})])])},staticRenderFns:[]};var q=n("VU/8")(P,R,!1,function(t){n("cXZo")},null,null).exports,U={data:function(){return{}},props:{type:String},methods:h()({},Object(d.b)(["setShowOdCustomList","setOdCustomListType"]),{clickMore:function(){this.setOdCustomListType(this.type),this.setShowOdCustomList(!0)}}),computed:h()({},Object(d.c)(["showOdCustomList","getOdCustomListData"]),{name:function(){switch(this.type){case"2":case"3":case"4":return"保密与紧急程度";default:return O.a[this.type]["名字"]}},tableCtrl:function(){switch(this.type){case"18":case"27":case"30":return"DateCtrl";case"2":case"3":case"4":return"PrivacyCtrl";case"7":return"NumCtrl";case"5":case"12":case"25":case"26":return"RadioCtrl";case"9":case"29":return"RadioCtrlEx";case"11":case"16":case"28":default:return"TextCtrl"}},hasMore:function(){switch(this.type){case"5":case"9":case"12":case"25":case"26":case"29":return!0;default:return!1}}}),components:{DateCtrl:I,PrivacyCtrl:S,RadioCtrl:T,RadioCtrlEx:A,NumCtrl:N,TextCtrl:q}},B={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"setting-element"},[n("div",{staticClass:"setting-title"},[n("span",[t._v(t._s(t.name))]),t._v(" "),t.hasMore?n("span",{staticClass:"setting-more",on:{click:t.clickMore}},[t._v("更多")]):t._e()]),t._v(" "),n(t.tableCtrl,{tag:"component",staticClass:"setting-body",attrs:{type:t.type,colCount:3}})],1)},staticRenderFns:[]};var V=n("VU/8")(U,B,!1,function(t){n("5n6T")},null,null).exports,K=n("NKwL"),$=n.n(K),H={data:function(){return{insertList:{}}},methods:{tryInsert:function(t){if(t in this.insertList)return!1;switch(t){case"2":case"3":case"4":this.insertList[2]=!1,this.insertList[3]=!1,this.insertList[4]=!1;break;case"1":case"6":case"8":case"10":case"13":case"14":case"15":case"19":case"20":case"21":case"22":case"23":case"24":return!1;default:this.insertList[t]=!1}return!0}},computed:h()({},Object(d.c)(["getOfficialDocType"]),{officialList:function(){var t=this,e=$.a[this.getOfficialDocType]["要素"].split(",");this.insertList={};var n=[];return e.forEach(function(e){t.tryInsert(e)&&n.push(e)}),n}}),components:{OfficialSettingItem:V}},W={render:function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"of-Body"},this._l(this.officialList,function(t){return e("div",{key:t},[e("OfficialSettingItem",{attrs:{type:t}})],1)}))},staticRenderFns:[]};var F=n("VU/8")(H,W,!1,function(t){n("lGK7")},null,null).exports,G=n("mvHQ"),Y=n.n(G),J=n("+6Bu"),X=n.n(J),Z=n("CXu2"),Q=5,tt=1<<Q,et=tt-1,nt={};function it(t){t&&(t.value=!0)}function rt(){}function st(t){return void 0===t.size&&(t.size=t.__iterate(at)),t.size}function ot(t,e){if("number"!=typeof e){var n=e>>>0;if(""+n!==e||4294967295===n)return NaN;e=n}return e<0?st(t)+e:e}function at(){return!0}function ut(t,e,n){return(0===t&&!ft(t)||void 0!==n&&t<=-n)&&(void 0===e||void 0!==n&&e>=n)}function ct(t,e){return lt(t,e,0)}function ht(t,e){return lt(t,e,e)}function lt(t,e,n){return void 0===t?n:ft(t)?e===1/0?e:0|Math.max(0,e+t):void 0===e||e===t?t:0|Math.min(e,t)}function ft(t){return t<0||0===t&&1/t==-1/0}var pt="@@__IMMUTABLE_ITERABLE__@@";function dt(t){return Boolean(t&&t[pt])}var vt="@@__IMMUTABLE_KEYED__@@";function _t(t){return Boolean(t&&t[vt])}var yt="@@__IMMUTABLE_INDEXED__@@";function gt(t){return Boolean(t&&t[yt])}function mt(t){return _t(t)||gt(t)}var wt=function(t){return dt(t)?t:Wt(t)},Ot=function(t){function e(t){return _t(t)?t:Ft(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(wt),bt=function(t){function e(t){return gt(t)?t:Gt(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(wt),Ct=function(t){function e(t){return dt(t)&&!mt(t)?t:Yt(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e}(wt);wt.Keyed=Ot,wt.Indexed=bt,wt.Set=Ct;var It="@@__IMMUTABLE_SEQ__@@";function Dt(t){return Boolean(t&&t[It])}var xt="@@__IMMUTABLE_RECORD__@@";function St(t){return Boolean(t&&t[xt])}function kt(t){return dt(t)||St(t)}var Et="@@__IMMUTABLE_ORDERED__@@";function Tt(t){return Boolean(t&&t[Et])}var Lt=0,zt=1,At=2,Mt="function"==typeof Symbol&&Symbol.iterator,jt="@@iterator",Nt=Mt||jt,Pt=function(t){this.next=t};function Rt(t,e,n,i){var r=0===t?e:1===t?n:[e,n];return i?i.value=r:i={value:r,done:!1},i}function qt(){return{value:void 0,done:!0}}function Ut(t){return!!Kt(t)}function Bt(t){return t&&"function"==typeof t.next}function Vt(t){var e=Kt(t);return e&&e.call(t)}function Kt(t){var e=t&&(Mt&&t[Mt]||t[jt]);if("function"==typeof e)return e}Pt.prototype.toString=function(){return"[Iterator]"},Pt.KEYS=Lt,Pt.VALUES=zt,Pt.ENTRIES=At,Pt.prototype.inspect=Pt.prototype.toSource=function(){return this.toString()},Pt.prototype[Nt]=function(){return this};var $t=Object.prototype.hasOwnProperty;function Ht(t){return!(!Array.isArray(t)&&"string"!=typeof t)||t&&"object"==typeof t&&Number.isInteger(t.length)&&t.length>=0&&(0===t.length?1===Object.keys(t).length:t.hasOwnProperty(t.length-1))}var Wt=function(t){function e(t){return null===t||void 0===t?te():kt(t)?t.toSeq():function(t){var e=ie(t);if(e)return e;if("object"==typeof t)return new Xt(t);throw new TypeError("Expected Array or collection object of values, or keyed object: "+t)}(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toSeq=function(){return this},e.prototype.toString=function(){return this.__toString("Seq {","}")},e.prototype.cacheResult=function(){return!this._cache&&this.__iterateUncached&&(this._cache=this.entrySeq().toArray(),this.size=this._cache.length),this},e.prototype.__iterate=function(t,e){var n=this._cache;if(n){for(var i=n.length,r=0;r!==i;){var s=n[e?i-++r:r++];if(!1===t(s[1],s[0],this))break}return r}return this.__iterateUncached(t,e)},e.prototype.__iterator=function(t,e){var n=this._cache;if(n){var i=n.length,r=0;return new Pt(function(){if(r===i)return{value:void 0,done:!0};var s=n[e?i-++r:r++];return Rt(t,s[0],s[1])})}return this.__iteratorUncached(t,e)},e}(wt),Ft=function(t){function e(t){return null===t||void 0===t?te().toKeyedSeq():dt(t)?_t(t)?t.toSeq():t.fromEntrySeq():St(t)?t.toSeq():ee(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toKeyedSeq=function(){return this},e}(Wt),Gt=function(t){function e(t){return null===t||void 0===t?te():dt(t)?_t(t)?t.entrySeq():t.toIndexedSeq():St(t)?t.toSeq().entrySeq():ne(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return e(arguments)},e.prototype.toIndexedSeq=function(){return this},e.prototype.toString=function(){return this.__toString("Seq [","]")},e}(Wt),Yt=function(t){function e(t){return(dt(t)&&!mt(t)?t:Gt(t)).toSetSeq()}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return e(arguments)},e.prototype.toSetSeq=function(){return this},e}(Wt);Wt.isSeq=Dt,Wt.Keyed=Ft,Wt.Set=Yt,Wt.Indexed=Gt,Wt.prototype[It]=!0;var Jt=function(t){function e(t){this._array=t,this.size=t.length}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t,e){return this.has(t)?this._array[ot(this,t)]:e},e.prototype.__iterate=function(t,e){for(var n=this._array,i=n.length,r=0;r!==i;){var s=e?i-++r:r++;if(!1===t(n[s],s,this))break}return r},e.prototype.__iterator=function(t,e){var n=this._array,i=n.length,r=0;return new Pt(function(){if(r===i)return{value:void 0,done:!0};var s=e?i-++r:r++;return Rt(t,s,n[s])})},e}(Gt),Xt=function(t){function e(t){var e=Object.keys(t);this._object=t,this._keys=e,this.size=e.length}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t,e){return void 0===e||this.has(t)?this._object[t]:e},e.prototype.has=function(t){return $t.call(this._object,t)},e.prototype.__iterate=function(t,e){for(var n=this._object,i=this._keys,r=i.length,s=0;s!==r;){var o=i[e?r-++s:s++];if(!1===t(n[o],o,this))break}return s},e.prototype.__iterator=function(t,e){var n=this._object,i=this._keys,r=i.length,s=0;return new Pt(function(){if(s===r)return{value:void 0,done:!0};var o=i[e?r-++s:s++];return Rt(t,o,n[o])})},e}(Ft);Xt.prototype[Et]=!0;var Zt,Qt=function(t){function e(t){this._collection=t,this.size=t.length||t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.__iterateUncached=function(t,e){if(e)return this.cacheResult().__iterate(t,e);var n=Vt(this._collection),i=0;if(Bt(n))for(var r;!(r=n.next()).done&&!1!==t(r.value,i++,this););return i},e.prototype.__iteratorUncached=function(t,e){if(e)return this.cacheResult().__iterator(t,e);var n=Vt(this._collection);if(!Bt(n))return new Pt(qt);var i=0;return new Pt(function(){var e=n.next();return e.done?e:Rt(t,i++,e.value)})},e}(Gt);function te(){return Zt||(Zt=new Jt([]))}function ee(t){var e=Array.isArray(t)?new Jt(t):Ut(t)?new Qt(t):void 0;if(e)return e.fromEntrySeq();if("object"==typeof t)return new Xt(t);throw new TypeError("Expected Array or collection object of [k, v] entries, or keyed object: "+t)}function ne(t){var e=ie(t);if(e)return e;throw new TypeError("Expected Array or collection object of values: "+t)}function ie(t){return Ht(t)?new Jt(t):Ut(t)?new Qt(t):void 0}var re="@@__IMMUTABLE_MAP__@@";function se(t){return Boolean(t&&t[re])}function oe(t){return se(t)&&Tt(t)}function ae(t){return Boolean(t&&"function"==typeof t.equals&&"function"==typeof t.hashCode)}function ue(t,e){if(t===e||t!=t&&e!=e)return!0;if(!t||!e)return!1;if("function"==typeof t.valueOf&&"function"==typeof e.valueOf){if((t=t.valueOf())===(e=e.valueOf())||t!=t&&e!=e)return!0;if(!t||!e)return!1}return!!(ae(t)&&ae(e)&&t.equals(e))}var ce="function"==typeof Math.imul&&-2===Math.imul(4294967295,2)?Math.imul:function(t,e){var n=65535&(t|=0),i=65535&(e|=0);return n*i+((t>>>16)*i+n*(e>>>16)<<16>>>0)|0};function he(t){return t>>>1&1073741824|3221225471&t}var le=Object.prototype.valueOf;function fe(t){switch(typeof t){case"boolean":return t?1108378657:1108378656;case"number":return function(t){if(t!=t||t===1/0)return 0;var e=0|t;e!==t&&(e^=4294967295*t);for(;t>4294967295;)e^=t/=4294967295;return he(e)}(t);case"string":return t.length>we?function(t){var e=Ce[t];void 0===e&&(e=pe(t),be===Oe&&(be=0,Ce={}),be++,Ce[t]=e);return e}(t):pe(t);case"object":case"function":return null===t?1108378658:"function"==typeof t.hashCode?he(t.hashCode(t)):(t.valueOf!==le&&"function"==typeof t.valueOf&&(t=t.valueOf(t)),function(t){var e;if(ye&&void 0!==(e=_e.get(t)))return e;if(void 0!==(e=t[me]))return e;if(!ve){if(void 0!==(e=t.propertyIsEnumerable&&t.propertyIsEnumerable[me]))return e;if(void 0!==(e=function(t){if(t&&t.nodeType>0)switch(t.nodeType){case 1:return t.uniqueID;case 9:return t.documentElement&&t.documentElement.uniqueID}}(t)))return e}e=++ge,1073741824&ge&&(ge=0);if(ye)_e.set(t,e);else{if(void 0!==de&&!1===de(t))throw new Error("Non-extensible objects are not allowed as keys.");if(ve)Object.defineProperty(t,me,{enumerable:!1,configurable:!1,writable:!1,value:e});else if(void 0!==t.propertyIsEnumerable&&t.propertyIsEnumerable===t.constructor.prototype.propertyIsEnumerable)t.propertyIsEnumerable=function(){return this.constructor.prototype.propertyIsEnumerable.apply(this,arguments)},t.propertyIsEnumerable[me]=e;else{if(void 0===t.nodeType)throw new Error("Unable to set a non-enumerable property on object.");t[me]=e}}return e}(t));case"undefined":return 1108378659;default:if("function"==typeof t.toString)return pe(t.toString());throw new Error("Value type "+typeof t+" cannot be hashed.")}}function pe(t){for(var e=0,n=0;n<t.length;n++)e=31*e+t.charCodeAt(n)|0;return he(e)}var de=Object.isExtensible,ve=function(){try{return Object.defineProperty({},"@",{}),!0}catch(t){return!1}}();var _e,ye="function"==typeof WeakMap;ye&&(_e=new WeakMap);var ge=0,me="__immutablehash__";"function"==typeof Symbol&&(me=Symbol(me));var we=16,Oe=255,be=0,Ce={},Ie=function(t){function e(t,e){this._iter=t,this._useKeys=e,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.get=function(t,e){return this._iter.get(t,e)},e.prototype.has=function(t){return this._iter.has(t)},e.prototype.valueSeq=function(){return this._iter.valueSeq()},e.prototype.reverse=function(){var t=this,e=Te(this,!0);return this._useKeys||(e.valueSeq=function(){return t._iter.toSeq().reverse()}),e},e.prototype.map=function(t,e){var n=this,i=Ee(this,t,e);return this._useKeys||(i.valueSeq=function(){return n._iter.toSeq().map(t,e)}),i},e.prototype.__iterate=function(t,e){var n=this;return this._iter.__iterate(function(e,i){return t(e,i,n)},e)},e.prototype.__iterator=function(t,e){return this._iter.__iterator(t,e)},e}(Ft);Ie.prototype[Et]=!0;var De=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.includes=function(t){return this._iter.includes(t)},e.prototype.__iterate=function(t,e){var n=this,i=0;return e&&st(this),this._iter.__iterate(function(r){return t(r,e?n.size-++i:i++,n)},e)},e.prototype.__iterator=function(t,e){var n=this,i=this._iter.__iterator(zt,e),r=0;return e&&st(this),new Pt(function(){var s=i.next();return s.done?s:Rt(t,e?n.size-++r:r++,s.value,s)})},e}(Gt),xe=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.has=function(t){return this._iter.includes(t)},e.prototype.__iterate=function(t,e){var n=this;return this._iter.__iterate(function(e){return t(e,e,n)},e)},e.prototype.__iterator=function(t,e){var n=this._iter.__iterator(zt,e);return new Pt(function(){var e=n.next();return e.done?e:Rt(t,e.value,e.value,e)})},e}(Yt),Se=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.entrySeq=function(){return this._iter.toSeq()},e.prototype.__iterate=function(t,e){var n=this;return this._iter.__iterate(function(e){if(e){Ue(e);var i=dt(e);return t(i?e.get(1):e[1],i?e.get(0):e[0],n)}},e)},e.prototype.__iterator=function(t,e){var n=this._iter.__iterator(zt,e);return new Pt(function(){for(;;){var e=n.next();if(e.done)return e;var i=e.value;if(i){Ue(i);var r=dt(i);return Rt(t,r?i.get(0):i[0],r?i.get(1):i[1],e)}}})},e}(Ft);function ke(t){var e=Ve(t);return e._iter=t,e.size=t.size,e.flip=function(){return t},e.reverse=function(){var e=t.reverse.apply(this);return e.flip=function(){return t.reverse()},e},e.has=function(e){return t.includes(e)},e.includes=function(e){return t.has(e)},e.cacheResult=Ke,e.__iterateUncached=function(e,n){var i=this;return t.__iterate(function(t,n){return!1!==e(n,t,i)},n)},e.__iteratorUncached=function(e,n){if(e===At){var i=t.__iterator(e,n);return new Pt(function(){var t=i.next();if(!t.done){var e=t.value[0];t.value[0]=t.value[1],t.value[1]=e}return t})}return t.__iterator(e===zt?Lt:zt,n)},e}function Ee(t,e,n){var i=Ve(t);return i.size=t.size,i.has=function(e){return t.has(e)},i.get=function(i,r){var s=t.get(i,nt);return s===nt?r:e.call(n,s,i,t)},i.__iterateUncached=function(i,r){var s=this;return t.__iterate(function(t,r,o){return!1!==i(e.call(n,t,r,o),r,s)},r)},i.__iteratorUncached=function(i,r){var s=t.__iterator(At,r);return new Pt(function(){var r=s.next();if(r.done)return r;var o=r.value,a=o[0];return Rt(i,a,e.call(n,o[1],a,t),r)})},i}function Te(t,e){var n=this,i=Ve(t);return i._iter=t,i.size=t.size,i.reverse=function(){return t},t.flip&&(i.flip=function(){var e=ke(t);return e.reverse=function(){return t.flip()},e}),i.get=function(n,i){return t.get(e?n:-1-n,i)},i.has=function(n){return t.has(e?n:-1-n)},i.includes=function(e){return t.includes(e)},i.cacheResult=Ke,i.__iterate=function(n,i){var r=this,s=0;return i&&st(t),t.__iterate(function(t,o){return n(t,e?o:i?r.size-++s:s++,r)},!i)},i.__iterator=function(i,r){var s=0;r&&st(t);var o=t.__iterator(At,!r);return new Pt(function(){var t=o.next();if(t.done)return t;var a=t.value;return Rt(i,e?a[0]:r?n.size-++s:s++,a[1],t)})},i}function Le(t,e,n,i){var r=Ve(t);return i&&(r.has=function(i){var r=t.get(i,nt);return r!==nt&&!!e.call(n,r,i,t)},r.get=function(i,r){var s=t.get(i,nt);return s!==nt&&e.call(n,s,i,t)?s:r}),r.__iterateUncached=function(r,s){var o=this,a=0;return t.__iterate(function(t,s,u){if(e.call(n,t,s,u))return a++,r(t,i?s:a-1,o)},s),a},r.__iteratorUncached=function(r,s){var o=t.__iterator(At,s),a=0;return new Pt(function(){for(;;){var s=o.next();if(s.done)return s;var u=s.value,c=u[0],h=u[1];if(e.call(n,h,c,t))return Rt(r,i?c:a++,h,s)}})},r}function ze(t,e,n,i){var r=t.size;if(ut(e,n,r))return t;var s=ct(e,r),o=ht(n,r);if(s!=s||o!=o)return ze(t.toSeq().cacheResult(),e,n,i);var a,u=o-s;u==u&&(a=u<0?0:u);var c=Ve(t);return c.size=0===a?a:t.size&&a||void 0,!i&&Dt(t)&&a>=0&&(c.get=function(e,n){return(e=ot(this,e))>=0&&e<a?t.get(e+s,n):n}),c.__iterateUncached=function(e,n){var r=this;if(0===a)return 0;if(n)return this.cacheResult().__iterate(e,n);var o=0,u=!0,c=0;return t.__iterate(function(t,n){if(!u||!(u=o++<s))return c++,!1!==e(t,i?n:c-1,r)&&c!==a}),c},c.__iteratorUncached=function(e,n){if(0!==a&&n)return this.cacheResult().__iterator(e,n);if(0===a)return new Pt(qt);var r=t.__iterator(e,n),o=0,u=0;return new Pt(function(){for(;o++<s;)r.next();if(++u>a)return{value:void 0,done:!0};var t=r.next();return i||e===zt||t.done?t:Rt(e,u-1,e===Lt?void 0:t.value[1],t)})},c}function Ae(t,e,n,i){var r=Ve(t);return r.__iterateUncached=function(r,s){var o=this;if(s)return this.cacheResult().__iterate(r,s);var a=!0,u=0;return t.__iterate(function(t,s,c){if(!a||!(a=e.call(n,t,s,c)))return u++,r(t,i?s:u-1,o)}),u},r.__iteratorUncached=function(r,s){var o=this;if(s)return this.cacheResult().__iterator(r,s);var a=t.__iterator(At,s),u=!0,c=0;return new Pt(function(){var t,s,h;do{if((t=a.next()).done)return i||r===zt?t:Rt(r,c++,r===Lt?void 0:t.value[1],t);var l=t.value;s=l[0],h=l[1],u&&(u=e.call(n,h,s,o))}while(u);return r===At?t:Rt(r,s,h,t)})},r}function Me(t,e,n){var i=Ve(t);return i.__iterateUncached=function(r,s){if(s)return this.cacheResult().__iterate(r,s);var o=0,a=!1;return function t(u,c){u.__iterate(function(s,u){return(!e||c<e)&&dt(s)?t(s,c+1):(o++,!1===r(s,n?u:o-1,i)&&(a=!0)),!a},s)}(t,0),o},i.__iteratorUncached=function(i,r){if(r)return this.cacheResult().__iterator(i,r);var s=t.__iterator(i,r),o=[],a=0;return new Pt(function(){for(;s;){var t=s.next();if(!1===t.done){var u=t.value;if(i===At&&(u=u[1]),e&&!(o.length<e)||!dt(u))return n?t:Rt(i,a++,u,t);o.push(s),s=u.__iterator(i,r)}else s=o.pop()}return{value:void 0,done:!0}})},i}function je(t,e,n){e||(e=$e);var i=_t(t),r=0,s=t.toSeq().map(function(e,i){return[i,e,r++,n?n(e,i,t):e]}).valueSeq().toArray();return s.sort(function(t,n){return e(t[3],n[3])||t[2]-n[2]}).forEach(i?function(t,e){s[e].length=2}:function(t,e){s[e]=t[1]}),i?Ft(s):gt(t)?Gt(s):Yt(s)}function Ne(t,e,n){if(e||(e=$e),n){var i=t.toSeq().map(function(e,i){return[e,n(e,i,t)]}).reduce(function(t,n){return Pe(e,t[1],n[1])?n:t});return i&&i[0]}return t.reduce(function(t,n){return Pe(e,t,n)?n:t})}function Pe(t,e,n){var i=t(n,e);return 0===i&&n!==e&&(void 0===n||null===n||n!=n)||i>0}function Re(t,e,n,i){var r=Ve(t),s=new Jt(n).map(function(t){return t.size});return r.size=i?s.max():s.min(),r.__iterate=function(t,e){for(var n,i=this.__iterator(zt,e),r=0;!(n=i.next()).done&&!1!==t(n.value,r++,this););return r},r.__iteratorUncached=function(t,r){var s=n.map(function(t){return t=wt(t),Vt(r?t.reverse():t)}),o=0,a=!1;return new Pt(function(){var n;return a||(n=s.map(function(t){return t.next()}),a=i?n.every(function(t){return t.done}):n.some(function(t){return t.done})),a?{value:void 0,done:!0}:Rt(t,o++,e.apply(null,n.map(function(t){return t.value})))})},r}function qe(t,e){return t===e?t:Dt(t)?e:t.constructor(e)}function Ue(t){if(t!==Object(t))throw new TypeError("Expected [K, V] tuple: "+t)}function Be(t){return _t(t)?Ot:gt(t)?bt:Ct}function Ve(t){return Object.create((_t(t)?Ft:gt(t)?Gt:Yt).prototype)}function Ke(){return this._iter.cacheResult?(this._iter.cacheResult(),this.size=this._iter.size,this):Wt.prototype.cacheResult.call(this)}function $e(t,e){return void 0===t&&void 0===e?0:void 0===t?1:void 0===e?-1:t>e?1:t<e?-1:0}function He(t,e){e=e||0;for(var n=Math.max(0,t.length-e),i=new Array(n),r=0;r<n;r++)i[r]=t[r+e];return i}function We(t,e){if(!t)throw new Error(e)}function Fe(t){We(t!==1/0,"Cannot perform this action with an infinite size.")}function Ge(t){if(Ht(t)&&"string"!=typeof t)return t;if(Tt(t))return t.toArray();throw new TypeError("Invalid keyPath: expected Ordered Collection or Array: "+t)}function Ye(t){return t&&("function"!=typeof t.constructor||"Object"===t.constructor.name)}function Je(t){return"object"==typeof t&&(kt(t)||Array.isArray(t)||Ye(t))}function Xe(t){try{return"string"==typeof t?JSON.stringify(t):String(t)}catch(e){return JSON.stringify(t)}}function Ze(t,e){return kt(t)?t.has(e):Je(t)&&$t.call(t,e)}function Qe(t,e,n){return kt(t)?t.get(e,n):Ze(t,e)?"function"==typeof t.get?t.get(e):t[e]:n}function tn(t){if(Array.isArray(t))return He(t);var e={};for(var n in t)$t.call(t,n)&&(e[n]=t[n]);return e}function en(t,e){if(!Je(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(kt(t)){if(!t.remove)throw new TypeError("Cannot update immutable value without .remove() method: "+t);return t.remove(e)}if(!$t.call(t,e))return t;var n=tn(t);return Array.isArray(n)?n.splice(e,1):delete n[e],n}function nn(t,e,n){if(!Je(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(kt(t)){if(!t.set)throw new TypeError("Cannot update immutable value without .set() method: "+t);return t.set(e,n)}if($t.call(t,e)&&n===t[e])return t;var i=tn(t);return i[e]=n,i}function rn(t,e,n,i){i||(i=n,n=void 0);var r=function t(e,n,i,r,s,o){var a=n===nt;if(r===i.length){var u=a?s:n,c=o(u);return c===u?n:c}if(!a&&!Je(n))throw new TypeError("Cannot update within non-data-structure value in path ["+i.slice(0,r).map(Xe)+"]: "+n);var h=i[r];var l=a?nt:Qe(n,h,nt);var f=t(l===nt?e:kt(l),l,i,r+1,s,o);return f===l?n:f===nt?en(n,h):nn(a?e?Pn():{}:n,h,f)}(kt(t),t,Ge(e),0,n,i);return r===nt?n:r}function sn(t,e,n){return rn(t,e,nt,function(){return n})}function on(t,e){return sn(this,t,e)}function an(t,e){return rn(t,e,function(){return nt})}function un(t){return an(this,t)}function cn(t,e,n,i){return rn(t,[e],n,i)}function hn(t,e,n){return 1===arguments.length?t(this):cn(this,t,e,n)}function ln(t,e,n){return rn(this,t,e,n)}function fn(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return dn(this,t)}function pn(t){for(var e=[],n=arguments.length-1;n-- >0;)e[n]=arguments[n+1];if("function"!=typeof t)throw new TypeError("Invalid merger function: "+t);return dn(this,e,t)}function dn(t,e,n){for(var i=[],r=0;r<e.length;r++){var s=Ot(e[r]);0!==s.size&&i.push(s)}return 0===i.length?t:0!==t.toSeq().size||t.__ownerID||1!==i.length?t.withMutations(function(t){for(var e=n?function(e,i){cn(t,i,nt,function(t){return t===nt?e:n(t,e,i)})}:function(e,n){t.set(n,e)},r=0;r<i.length;r++)i[r].forEach(e)}):t.constructor(i[0])}function vn(t,e,n){return _n(t,e,function(t){return function e(n,i,r){return Je(n)&&Je(i)?_n(n,[i],e):t?t(n,i,r):i}}(n))}function _n(t,e,n){if(!Je(t))throw new TypeError("Cannot merge into non-data-structure value: "+t);if(kt(t))return"function"==typeof n&&t.mergeWith?t.mergeWith.apply(t,[n].concat(e)):t.merge?t.merge.apply(t,e):t.concat.apply(t,e);for(var i=Array.isArray(t),r=t,s=i?bt:Ot,o=i?function(e){r===t&&(r=tn(r)),r.push(e)}:function(e,i){var s=$t.call(r,i),o=s&&n?n(r[i],e,i):e;s&&o===r[i]||(r===t&&(r=tn(r)),r[i]=o)},a=0;a<e.length;a++)s(e[a]).forEach(o);return r}function yn(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return vn(this,t)}function gn(t){for(var e=[],n=arguments.length-1;n-- >0;)e[n]=arguments[n+1];return vn(this,e,t)}function mn(t){for(var e=[],n=arguments.length-1;n-- >0;)e[n]=arguments[n+1];return rn(this,t,Pn(),function(t){return _n(t,e)})}function wn(t){for(var e=[],n=arguments.length-1;n-- >0;)e[n]=arguments[n+1];return rn(this,t,Pn(),function(t){return vn(t,e)})}function On(t){var e=this.asMutable();return t(e),e.wasAltered()?e.__ensureOwner(this.__ownerID):this}function bn(){return this.__ownerID?this:this.__ensureOwner(new rt)}function Cn(){return this.__ensureOwner()}function In(){return this.__altered}De.prototype.cacheResult=Ie.prototype.cacheResult=xe.prototype.cacheResult=Se.prototype.cacheResult=Ke;var Dn=function(t){function e(e){return null===e||void 0===e?Pn():se(e)&&!Tt(e)?e:Pn().withMutations(function(n){var i=t(e);Fe(i.size),i.forEach(function(t,e){return n.set(e,t)})})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return Pn().withMutations(function(e){for(var n=0;n<t.length;n+=2){if(n+1>=t.length)throw new Error("Missing value for key: "+t[n]);e.set(t[n],t[n+1])}})},e.prototype.toString=function(){return this.__toString("Map {","}")},e.prototype.get=function(t,e){return this._root?this._root.get(0,void 0,t,e):e},e.prototype.set=function(t,e){return Rn(this,t,e)},e.prototype.remove=function(t){return Rn(this,t,nt)},e.prototype.deleteAll=function(t){var e=wt(t);return 0===e.size?this:this.withMutations(function(t){e.forEach(function(e){return t.remove(e)})})},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._root=null,this.__hash=void 0,this.__altered=!0,this):Pn()},e.prototype.sort=function(t){return ci(je(this,t))},e.prototype.sortBy=function(t,e){return ci(je(this,e,t))},e.prototype.map=function(t,e){return this.withMutations(function(n){n.forEach(function(i,r){n.set(r,t.call(e,i,r,n))})})},e.prototype.__iterator=function(t,e){return new An(this,t,e)},e.prototype.__iterate=function(t,e){var n=this,i=0;return this._root&&this._root.iterate(function(e){return i++,t(e[1],e[0],n)},e),i},e.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Nn(this.size,this._root,t,this.__hash):0===this.size?Pn():(this.__ownerID=t,this.__altered=!1,this)},e}(Ot);Dn.isMap=se;var xn=Dn.prototype;xn[re]=!0,xn.delete=xn.remove,xn.removeAll=xn.deleteAll,xn.setIn=on,xn.removeIn=xn.deleteIn=un,xn.update=hn,xn.updateIn=ln,xn.merge=xn.concat=fn,xn.mergeWith=pn,xn.mergeDeep=yn,xn.mergeDeepWith=gn,xn.mergeIn=mn,xn.mergeDeepIn=wn,xn.withMutations=On,xn.wasAltered=In,xn.asImmutable=Cn,xn["@@transducer/init"]=xn.asMutable=bn,xn["@@transducer/step"]=function(t,e){return t.set(e[0],e[1])},xn["@@transducer/result"]=function(t){return t.asImmutable()};var Sn=function(t,e){this.ownerID=t,this.entries=e};Sn.prototype.get=function(t,e,n,i){for(var r=this.entries,s=0,o=r.length;s<o;s++)if(ue(n,r[s][0]))return r[s][1];return i},Sn.prototype.update=function(t,e,n,i,r,s,o){for(var a=r===nt,u=this.entries,c=0,h=u.length;c<h&&!ue(i,u[c][0]);c++);var l=c<h;if(l?u[c][1]===r:a)return this;if(it(o),(a||!l)&&it(s),!a||1!==u.length){if(!l&&!a&&u.length>=$n)return function(t,e,n,i){t||(t=new rt);for(var r=new Ln(t,fe(n),[n,i]),s=0;s<e.length;s++){var o=e[s];r=r.update(t,0,void 0,o[0],o[1])}return r}(t,u,i,r);var f=t&&t===this.ownerID,p=f?u:He(u);return l?a?c===h-1?p.pop():p[c]=p.pop():p[c]=[i,r]:p.push([i,r]),f?(this.entries=p,this):new Sn(t,p)}};var kn=function(t,e,n){this.ownerID=t,this.bitmap=e,this.nodes=n};kn.prototype.get=function(t,e,n,i){void 0===e&&(e=fe(n));var r=1<<((0===t?e:e>>>t)&et),s=this.bitmap;return 0==(s&r)?i:this.nodes[Vn(s&r-1)].get(t+Q,e,n,i)},kn.prototype.update=function(t,e,n,i,r,s,o){void 0===n&&(n=fe(i));var a=(0===e?n:n>>>e)&et,u=1<<a,c=this.bitmap,h=0!=(c&u);if(!h&&r===nt)return this;var l=Vn(c&u-1),f=this.nodes,p=h?f[l]:void 0,d=qn(p,t,e+Q,n,i,r,s,o);if(d===p)return this;if(!h&&d&&f.length>=Hn)return function(t,e,n,i,r){for(var s=0,o=new Array(tt),a=0;0!==n;a++,n>>>=1)o[a]=1&n?e[s++]:void 0;return o[i]=r,new En(t,s+1,o)}(t,f,c,a,d);if(h&&!d&&2===f.length&&Un(f[1^l]))return f[1^l];if(h&&d&&1===f.length&&Un(d))return d;var v=t&&t===this.ownerID,_=h?d?c:c^u:c|u,y=h?d?Kn(f,l,d,v):function(t,e,n){var i=t.length-1;if(n&&e===i)return t.pop(),t;for(var r=new Array(i),s=0,o=0;o<i;o++)o===e&&(s=1),r[o]=t[o+s];return r}(f,l,v):function(t,e,n,i){var r=t.length+1;if(i&&e+1===r)return t[e]=n,t;for(var s=new Array(r),o=0,a=0;a<r;a++)a===e?(s[a]=n,o=-1):s[a]=t[a+o];return s}(f,l,d,v);return v?(this.bitmap=_,this.nodes=y,this):new kn(t,_,y)};var En=function(t,e,n){this.ownerID=t,this.count=e,this.nodes=n};En.prototype.get=function(t,e,n,i){void 0===e&&(e=fe(n));var r=(0===t?e:e>>>t)&et,s=this.nodes[r];return s?s.get(t+Q,e,n,i):i},En.prototype.update=function(t,e,n,i,r,s,o){void 0===n&&(n=fe(i));var a=(0===e?n:n>>>e)&et,u=r===nt,c=this.nodes,h=c[a];if(u&&!h)return this;var l=qn(h,t,e+Q,n,i,r,s,o);if(l===h)return this;var f=this.count;if(h){if(!l&&--f<Wn)return function(t,e,n,i){for(var r=0,s=0,o=new Array(n),a=0,u=1,c=e.length;a<c;a++,u<<=1){var h=e[a];void 0!==h&&a!==i&&(r|=u,o[s++]=h)}return new kn(t,r,o)}(t,c,f,a)}else f++;var p=t&&t===this.ownerID,d=Kn(c,a,l,p);return p?(this.count=f,this.nodes=d,this):new En(t,f,d)};var Tn=function(t,e,n){this.ownerID=t,this.keyHash=e,this.entries=n};Tn.prototype.get=function(t,e,n,i){for(var r=this.entries,s=0,o=r.length;s<o;s++)if(ue(n,r[s][0]))return r[s][1];return i},Tn.prototype.update=function(t,e,n,i,r,s,o){void 0===n&&(n=fe(i));var a=r===nt;if(n!==this.keyHash)return a?this:(it(o),it(s),Bn(this,t,e,n,[i,r]));for(var u=this.entries,c=0,h=u.length;c<h&&!ue(i,u[c][0]);c++);var l=c<h;if(l?u[c][1]===r:a)return this;if(it(o),(a||!l)&&it(s),a&&2===h)return new Ln(t,this.keyHash,u[1^c]);var f=t&&t===this.ownerID,p=f?u:He(u);return l?a?c===h-1?p.pop():p[c]=p.pop():p[c]=[i,r]:p.push([i,r]),f?(this.entries=p,this):new Tn(t,this.keyHash,p)};var Ln=function(t,e,n){this.ownerID=t,this.keyHash=e,this.entry=n};Ln.prototype.get=function(t,e,n,i){return ue(n,this.entry[0])?this.entry[1]:i},Ln.prototype.update=function(t,e,n,i,r,s,o){var a=r===nt,u=ue(i,this.entry[0]);return(u?r===this.entry[1]:a)?this:(it(o),a?void it(s):u?t&&t===this.ownerID?(this.entry[1]=r,this):new Ln(t,this.keyHash,[i,r]):(it(s),Bn(this,t,e,fe(i),[i,r])))},Sn.prototype.iterate=Tn.prototype.iterate=function(t,e){for(var n=this.entries,i=0,r=n.length-1;i<=r;i++)if(!1===t(n[e?r-i:i]))return!1},kn.prototype.iterate=En.prototype.iterate=function(t,e){for(var n=this.nodes,i=0,r=n.length-1;i<=r;i++){var s=n[e?r-i:i];if(s&&!1===s.iterate(t,e))return!1}},Ln.prototype.iterate=function(t,e){return t(this.entry)};var zn,An=function(t){function e(t,e,n){this._type=e,this._reverse=n,this._stack=t._root&&jn(t._root)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.next=function(){for(var t=this._type,e=this._stack;e;){var n=e.node,i=e.index++,r=void 0;if(n.entry){if(0===i)return Mn(t,n.entry)}else if(n.entries){if(i<=(r=n.entries.length-1))return Mn(t,n.entries[this._reverse?r-i:i])}else if(i<=(r=n.nodes.length-1)){var s=n.nodes[this._reverse?r-i:i];if(s){if(s.entry)return Mn(t,s.entry);e=this._stack=jn(s,e)}continue}e=this._stack=this._stack.__prev}return{value:void 0,done:!0}},e}(Pt);function Mn(t,e){return Rt(t,e[0],e[1])}function jn(t,e){return{node:t,index:0,__prev:e}}function Nn(t,e,n,i){var r=Object.create(xn);return r.size=t,r._root=e,r.__ownerID=n,r.__hash=i,r.__altered=!1,r}function Pn(){return zn||(zn=Nn(0))}function Rn(t,e,n){var i,r;if(t._root){var s={value:!1},o={value:!1};if(i=qn(t._root,t.__ownerID,0,void 0,e,n,s,o),!o.value)return t;r=t.size+(s.value?n===nt?-1:1:0)}else{if(n===nt)return t;r=1,i=new Sn(t.__ownerID,[[e,n]])}return t.__ownerID?(t.size=r,t._root=i,t.__hash=void 0,t.__altered=!0,t):i?Nn(r,i):Pn()}function qn(t,e,n,i,r,s,o,a){return t?t.update(e,n,i,r,s,o,a):s===nt?t:(it(a),it(o),new Ln(e,i,[r,s]))}function Un(t){return t.constructor===Ln||t.constructor===Tn}function Bn(t,e,n,i,r){if(t.keyHash===i)return new Tn(e,i,[t.entry,r]);var s,o=(0===n?t.keyHash:t.keyHash>>>n)&et,a=(0===n?i:i>>>n)&et,u=o===a?[Bn(t,e,n+Q,i,r)]:(s=new Ln(e,i,r),o<a?[t,s]:[s,t]);return new kn(e,1<<o|1<<a,u)}function Vn(t){return t=(t=(858993459&(t-=t>>1&1431655765))+(t>>2&858993459))+(t>>4)&252645135,t+=t>>8,127&(t+=t>>16)}function Kn(t,e,n,i){var r=i?t:He(t);return r[e]=n,r}var $n=tt/4,Hn=tt/2,Wn=tt/4,Fn="@@__IMMUTABLE_LIST__@@";function Gn(t){return Boolean(t&&t[Fn])}var Yn=function(t){function e(e){var n=ni();if(null===e||void 0===e)return n;if(Gn(e))return e;var i=t(e),r=i.size;return 0===r?n:(Fe(r),r>0&&r<tt?ei(0,r,Q,null,new Xn(i.toArray())):n.withMutations(function(t){t.setSize(r),i.forEach(function(e,n){return t.set(n,e)})}))}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.prototype.toString=function(){return this.__toString("List [","]")},e.prototype.get=function(t,e){if((t=ot(this,t))>=0&&t<this.size){var n=si(this,t+=this._origin);return n&&n.array[t&et]}return e},e.prototype.set=function(t,e){return function(t,e,n){if((e=ot(t,e))!=e)return t;if(e>=t.size||e<0)return t.withMutations(function(t){e<0?oi(t,e).set(0,n):oi(t,0,e+1).set(e,n)});e+=t._origin;var i=t._tail,r=t._root,s={value:!1};e>=ai(t._capacity)?i=ii(i,t.__ownerID,0,e,n,s):r=ii(r,t.__ownerID,t._level,e,n,s);if(!s.value)return t;if(t.__ownerID)return t._root=r,t._tail=i,t.__hash=void 0,t.__altered=!0,t;return ei(t._origin,t._capacity,t._level,r,i)}(this,t,e)},e.prototype.remove=function(t){return this.has(t)?0===t?this.shift():t===this.size-1?this.pop():this.splice(t,1):this},e.prototype.insert=function(t,e){return this.splice(t,0,e)},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=this._origin=this._capacity=0,this._level=Q,this._root=this._tail=null,this.__hash=void 0,this.__altered=!0,this):ni()},e.prototype.push=function(){var t=arguments,e=this.size;return this.withMutations(function(n){oi(n,0,e+t.length);for(var i=0;i<t.length;i++)n.set(e+i,t[i])})},e.prototype.pop=function(){return oi(this,0,-1)},e.prototype.unshift=function(){var t=arguments;return this.withMutations(function(e){oi(e,-t.length);for(var n=0;n<t.length;n++)e.set(n,t[n])})},e.prototype.shift=function(){return oi(this,1)},e.prototype.concat=function(){for(var e=arguments,n=[],i=0;i<arguments.length;i++){var r=e[i],s=t("string"!=typeof r&&Ut(r)?r:[r]);0!==s.size&&n.push(s)}return 0===n.length?this:0!==this.size||this.__ownerID||1!==n.length?this.withMutations(function(t){n.forEach(function(e){return e.forEach(function(e){return t.push(e)})})}):this.constructor(n[0])},e.prototype.setSize=function(t){return oi(this,0,t)},e.prototype.map=function(t,e){var n=this;return this.withMutations(function(i){for(var r=0;r<n.size;r++)i.set(r,t.call(e,i.get(r),r,i))})},e.prototype.slice=function(t,e){var n=this.size;return ut(t,e,n)?this:oi(this,ct(t,n),ht(e,n))},e.prototype.__iterator=function(t,e){var n=e?this.size:0,i=ti(this,e);return new Pt(function(){var r=i();return r===Qn?{value:void 0,done:!0}:Rt(t,e?--n:n++,r)})},e.prototype.__iterate=function(t,e){for(var n,i=e?this.size:0,r=ti(this,e);(n=r())!==Qn&&!1!==t(n,e?--i:i++,this););return i},e.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?ei(this._origin,this._capacity,this._level,this._root,this._tail,t,this.__hash):0===this.size?ni():(this.__ownerID=t,this.__altered=!1,this)},e}(bt);Yn.isList=Gn;var Jn=Yn.prototype;Jn[Fn]=!0,Jn.delete=Jn.remove,Jn.merge=Jn.concat,Jn.setIn=on,Jn.deleteIn=Jn.removeIn=un,Jn.update=hn,Jn.updateIn=ln,Jn.mergeIn=mn,Jn.mergeDeepIn=wn,Jn.withMutations=On,Jn.wasAltered=In,Jn.asImmutable=Cn,Jn["@@transducer/init"]=Jn.asMutable=bn,Jn["@@transducer/step"]=function(t,e){return t.push(e)},Jn["@@transducer/result"]=function(t){return t.asImmutable()};var Xn=function(t,e){this.array=t,this.ownerID=e};Xn.prototype.removeBefore=function(t,e,n){if(n===e?1<<e:0===this.array.length)return this;var i=n>>>e&et;if(i>=this.array.length)return new Xn([],t);var r,s=0===i;if(e>0){var o=this.array[i];if((r=o&&o.removeBefore(t,e-Q,n))===o&&s)return this}if(s&&!r)return this;var a=ri(this,t);if(!s)for(var u=0;u<i;u++)a.array[u]=void 0;return r&&(a.array[i]=r),a},Xn.prototype.removeAfter=function(t,e,n){if(n===(e?1<<e:0)||0===this.array.length)return this;var i,r=n-1>>>e&et;if(r>=this.array.length)return this;if(e>0){var s=this.array[r];if((i=s&&s.removeAfter(t,e-Q,n))===s&&r===this.array.length-1)return this}var o=ri(this,t);return o.array.splice(r+1),i&&(o.array[r]=i),o};var Zn,Qn={};function ti(t,e){var n=t._origin,i=t._capacity,r=ai(i),s=t._tail;return o(t._root,t._level,0);function o(t,a,u){return 0===a?function(t,o){var a=o===r?s&&s.array:t&&t.array,u=o>n?0:n-o,c=i-o;c>tt&&(c=tt);return function(){if(u===c)return Qn;var t=e?--c:u++;return a&&a[t]}}(t,u):function(t,r,s){var a,u=t&&t.array,c=s>n?0:n-s>>r,h=1+(i-s>>r);h>tt&&(h=tt);return function(){for(;;){if(a){var t=a();if(t!==Qn)return t;a=null}if(c===h)return Qn;var n=e?--h:c++;a=o(u&&u[n],r-Q,s+(n<<r))}}}(t,a,u)}}function ei(t,e,n,i,r,s,o){var a=Object.create(Jn);return a.size=e-t,a._origin=t,a._capacity=e,a._level=n,a._root=i,a._tail=r,a.__ownerID=s,a.__hash=o,a.__altered=!1,a}function ni(){return Zn||(Zn=ei(0,0,Q))}function ii(t,e,n,i,r,s){var o,a=i>>>n&et,u=t&&a<t.array.length;if(!u&&void 0===r)return t;if(n>0){var c=t&&t.array[a],h=ii(c,e,n-Q,i,r,s);return h===c?t:((o=ri(t,e)).array[a]=h,o)}return u&&t.array[a]===r?t:(s&&it(s),o=ri(t,e),void 0===r&&a===o.array.length-1?o.array.pop():o.array[a]=r,o)}function ri(t,e){return e&&t&&e===t.ownerID?t:new Xn(t?t.array.slice():[],e)}function si(t,e){if(e>=ai(t._capacity))return t._tail;if(e<1<<t._level+Q){for(var n=t._root,i=t._level;n&&i>0;)n=n.array[e>>>i&et],i-=Q;return n}}function oi(t,e,n){void 0!==e&&(e|=0),void 0!==n&&(n|=0);var i=t.__ownerID||new rt,r=t._origin,s=t._capacity,o=r+e,a=void 0===n?s:n<0?s+n:r+n;if(o===r&&a===s)return t;if(o>=a)return t.clear();for(var u=t._level,c=t._root,h=0;o+h<0;)c=new Xn(c&&c.array.length?[void 0,c]:[],i),h+=1<<(u+=Q);h&&(o+=h,r+=h,a+=h,s+=h);for(var l=ai(s),f=ai(a);f>=1<<u+Q;)c=new Xn(c&&c.array.length?[c]:[],i),u+=Q;var p=t._tail,d=f<l?si(t,a-1):f>l?new Xn([],i):p;if(p&&f>l&&o<s&&p.array.length){for(var v=c=ri(c,i),_=u;_>Q;_-=Q){var y=l>>>_&et;v=v.array[y]=ri(v.array[y],i)}v.array[l>>>Q&et]=p}if(a<s&&(d=d&&d.removeAfter(i,0,a)),o>=f)o-=f,a-=f,u=Q,c=null,d=d&&d.removeBefore(i,0,o);else if(o>r||f<l){for(h=0;c;){var g=o>>>u&et;if(g!==f>>>u&et)break;g&&(h+=(1<<u)*g),u-=Q,c=c.array[g]}c&&o>r&&(c=c.removeBefore(i,u,o-h)),c&&f<l&&(c=c.removeAfter(i,u,f-h)),h&&(o-=h,a-=h)}return t.__ownerID?(t.size=a-o,t._origin=o,t._capacity=a,t._level=u,t._root=c,t._tail=d,t.__hash=void 0,t.__altered=!0,t):ei(o,a,u,c,d)}function ai(t){return t<tt?0:t-1>>>Q<<Q}var ui,ci=function(t){function e(t){return null===t||void 0===t?li():oe(t)?t:li().withMutations(function(e){var n=Ot(t);Fe(n.size),n.forEach(function(t,n){return e.set(n,t)})})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.prototype.toString=function(){return this.__toString("OrderedMap {","}")},e.prototype.get=function(t,e){var n=this._map.get(t);return void 0!==n?this._list.get(n)[1]:e},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._map.clear(),this._list.clear(),this):li()},e.prototype.set=function(t,e){return fi(this,t,e)},e.prototype.remove=function(t){return fi(this,t,nt)},e.prototype.wasAltered=function(){return this._map.wasAltered()||this._list.wasAltered()},e.prototype.__iterate=function(t,e){var n=this;return this._list.__iterate(function(e){return e&&t(e[1],e[0],n)},e)},e.prototype.__iterator=function(t,e){return this._list.fromEntrySeq().__iterator(t,e)},e.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t),n=this._list.__ensureOwner(t);return t?hi(e,n,t,this.__hash):0===this.size?li():(this.__ownerID=t,this._map=e,this._list=n,this)},e}(Dn);function hi(t,e,n,i){var r=Object.create(ci.prototype);return r.size=t?t.size:0,r._map=t,r._list=e,r.__ownerID=n,r.__hash=i,r}function li(){return ui||(ui=hi(Pn(),ni()))}function fi(t,e,n){var i,r,s=t._map,o=t._list,a=s.get(e),u=void 0!==a;if(n===nt){if(!u)return t;o.size>=tt&&o.size>=2*s.size?(i=(r=o.filter(function(t,e){return void 0!==t&&a!==e})).toKeyedSeq().map(function(t){return t[0]}).flip().toMap(),t.__ownerID&&(i.__ownerID=r.__ownerID=t.__ownerID)):(i=s.remove(e),r=a===o.size-1?o.pop():o.set(a,void 0))}else if(u){if(n===o.get(a)[1])return t;i=s,r=o.set(a,[e,n])}else i=s.set(e,o.size),r=o.set(o.size,[e,n]);return t.__ownerID?(t.size=i.size,t._map=i,t._list=r,t.__hash=void 0,t):hi(i,r)}ci.isOrderedMap=oe,ci.prototype[Et]=!0,ci.prototype.delete=ci.prototype.remove;var pi="@@__IMMUTABLE_STACK__@@";function di(t){return Boolean(t&&t[pi])}var vi=function(t){function e(t){return null===t||void 0===t?mi():di(t)?t:mi().pushAll(t)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.prototype.toString=function(){return this.__toString("Stack [","]")},e.prototype.get=function(t,e){var n=this._head;for(t=ot(this,t);n&&t--;)n=n.next;return n?n.value:e},e.prototype.peek=function(){return this._head&&this._head.value},e.prototype.push=function(){var t=arguments;if(0===arguments.length)return this;for(var e=this.size+arguments.length,n=this._head,i=arguments.length-1;i>=0;i--)n={value:t[i],next:n};return this.__ownerID?(this.size=e,this._head=n,this.__hash=void 0,this.__altered=!0,this):gi(e,n)},e.prototype.pushAll=function(e){if(0===(e=t(e)).size)return this;if(0===this.size&&di(e))return e;Fe(e.size);var n=this.size,i=this._head;return e.__iterate(function(t){n++,i={value:t,next:i}},!0),this.__ownerID?(this.size=n,this._head=i,this.__hash=void 0,this.__altered=!0,this):gi(n,i)},e.prototype.pop=function(){return this.slice(1)},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._head=void 0,this.__hash=void 0,this.__altered=!0,this):mi()},e.prototype.slice=function(e,n){if(ut(e,n,this.size))return this;var i=ct(e,this.size);if(ht(n,this.size)!==this.size)return t.prototype.slice.call(this,e,n);for(var r=this.size-i,s=this._head;i--;)s=s.next;return this.__ownerID?(this.size=r,this._head=s,this.__hash=void 0,this.__altered=!0,this):gi(r,s)},e.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?gi(this.size,this._head,t,this.__hash):0===this.size?mi():(this.__ownerID=t,this.__altered=!1,this)},e.prototype.__iterate=function(t,e){var n=this;if(e)return new Jt(this.toArray()).__iterate(function(e,i){return t(e,i,n)},e);for(var i=0,r=this._head;r&&!1!==t(r.value,i++,this);)r=r.next;return i},e.prototype.__iterator=function(t,e){if(e)return new Jt(this.toArray()).__iterator(t,e);var n=0,i=this._head;return new Pt(function(){if(i){var e=i.value;return i=i.next,Rt(t,n++,e)}return{value:void 0,done:!0}})},e}(bt);vi.isStack=di;var _i,yi=vi.prototype;function gi(t,e,n,i){var r=Object.create(yi);return r.size=t,r._head=e,r.__ownerID=n,r.__hash=i,r.__altered=!1,r}function mi(){return _i||(_i=gi(0))}yi[pi]=!0,yi.shift=yi.pop,yi.unshift=yi.push,yi.unshiftAll=yi.pushAll,yi.withMutations=On,yi.wasAltered=In,yi.asImmutable=Cn,yi["@@transducer/init"]=yi.asMutable=bn,yi["@@transducer/step"]=function(t,e){return t.unshift(e)},yi["@@transducer/result"]=function(t){return t.asImmutable()};var wi="@@__IMMUTABLE_SET__@@";function Oi(t){return Boolean(t&&t[wi])}function bi(t){return Oi(t)&&Tt(t)}function Ci(t,e){if(t===e)return!0;if(!dt(e)||void 0!==t.size&&void 0!==e.size&&t.size!==e.size||void 0!==t.__hash&&void 0!==e.__hash&&t.__hash!==e.__hash||_t(t)!==_t(e)||gt(t)!==gt(e)||Tt(t)!==Tt(e))return!1;if(0===t.size&&0===e.size)return!0;var n=!mt(t);if(Tt(t)){var i=t.entries();return e.every(function(t,e){var r=i.next().value;return r&&ue(r[1],t)&&(n||ue(r[0],e))})&&i.next().done}var r=!1;if(void 0===t.size)if(void 0===e.size)"function"==typeof t.cacheResult&&t.cacheResult();else{r=!0;var s=t;t=e,e=s}var o=!0,a=e.__iterate(function(e,i){if(n?!t.has(e):r?!ue(e,t.get(i,nt)):!ue(t.get(i,nt),e))return o=!1,!1});return o&&t.size===a}function Ii(t,e){var n=function(n){t.prototype[n]=e[n]};return Object.keys(e).forEach(n),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach(n),t}function Di(t){if(!t||"object"!=typeof t)return t;if(!dt(t)){if(!Je(t))return t;t=Wt(t)}if(_t(t)){var e={};return t.__iterate(function(t,n){e[n]=Di(t)}),e}var n=[];return t.__iterate(function(t){n.push(Di(t))}),n}var xi=function(t){function e(e){return null===e||void 0===e?Li():Oi(e)&&!Tt(e)?e:Li().withMutations(function(n){var i=t(e);Fe(i.size),i.forEach(function(t){return n.add(t)})})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.fromKeys=function(t){return this(Ot(t).keySeq())},e.intersect=function(t){return(t=wt(t).toArray()).length?ki.intersect.apply(e(t.pop()),t):Li()},e.union=function(t){return(t=wt(t).toArray()).length?ki.union.apply(e(t.pop()),t):Li()},e.prototype.toString=function(){return this.__toString("Set {","}")},e.prototype.has=function(t){return this._map.has(t)},e.prototype.add=function(t){return Ei(this,this._map.set(t,t))},e.prototype.remove=function(t){return Ei(this,this._map.remove(t))},e.prototype.clear=function(){return Ei(this,this._map.clear())},e.prototype.map=function(t,e){var n=this,i=[],r=[];return this.forEach(function(s){var o=t.call(e,s,s,n);o!==s&&(i.push(s),r.push(o))}),this.withMutations(function(t){i.forEach(function(e){return t.remove(e)}),r.forEach(function(e){return t.add(e)})})},e.prototype.union=function(){for(var e=[],n=arguments.length;n--;)e[n]=arguments[n];return 0===(e=e.filter(function(t){return 0!==t.size})).length?this:0!==this.size||this.__ownerID||1!==e.length?this.withMutations(function(n){for(var i=0;i<e.length;i++)t(e[i]).forEach(function(t){return n.add(t)})}):this.constructor(e[0])},e.prototype.intersect=function(){for(var e=[],n=arguments.length;n--;)e[n]=arguments[n];if(0===e.length)return this;e=e.map(function(e){return t(e)});var i=[];return this.forEach(function(t){e.every(function(e){return e.includes(t)})||i.push(t)}),this.withMutations(function(t){i.forEach(function(e){t.remove(e)})})},e.prototype.subtract=function(){for(var e=[],n=arguments.length;n--;)e[n]=arguments[n];if(0===e.length)return this;e=e.map(function(e){return t(e)});var i=[];return this.forEach(function(t){e.some(function(e){return e.includes(t)})&&i.push(t)}),this.withMutations(function(t){i.forEach(function(e){t.remove(e)})})},e.prototype.sort=function(t){return Yi(je(this,t))},e.prototype.sortBy=function(t,e){return Yi(je(this,e,t))},e.prototype.wasAltered=function(){return this._map.wasAltered()},e.prototype.__iterate=function(t,e){var n=this;return this._map.__iterate(function(e){return t(e,e,n)},e)},e.prototype.__iterator=function(t,e){return this._map.__iterator(t,e)},e.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t);return t?this.__make(e,t):0===this.size?this.__empty():(this.__ownerID=t,this._map=e,this)},e}(Ct);xi.isSet=Oi;var Si,ki=xi.prototype;function Ei(t,e){return t.__ownerID?(t.size=e.size,t._map=e,t):e===t._map?t:0===e.size?t.__empty():t.__make(e)}function Ti(t,e){var n=Object.create(ki);return n.size=t?t.size:0,n._map=t,n.__ownerID=e,n}function Li(){return Si||(Si=Ti(Pn()))}ki[wi]=!0,ki.delete=ki.remove,ki.merge=ki.concat=ki.union,ki.withMutations=On,ki.asImmutable=Cn,ki["@@transducer/init"]=ki.asMutable=bn,ki["@@transducer/step"]=function(t,e){return t.add(e)},ki["@@transducer/result"]=function(t){return t.asImmutable()},ki.__empty=Li,ki.__make=Ti;var zi,Ai=function(t){function e(t,n,i){if(!(this instanceof e))return new e(t,n,i);if(We(0!==i,"Cannot step a Range by 0"),t=t||0,void 0===n&&(n=1/0),i=void 0===i?1:Math.abs(i),n<t&&(i=-i),this._start=t,this._end=n,this._step=i,this.size=Math.max(0,Math.ceil((n-t)/i-1)+1),0===this.size){if(zi)return zi;zi=this}}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.toString=function(){return 0===this.size?"Range []":"Range [ "+this._start+"..."+this._end+(1!==this._step?" by "+this._step:"")+" ]"},e.prototype.get=function(t,e){return this.has(t)?this._start+ot(this,t)*this._step:e},e.prototype.includes=function(t){var e=(t-this._start)/this._step;return e>=0&&e<this.size&&e===Math.floor(e)},e.prototype.slice=function(t,n){return ut(t,n,this.size)?this:(t=ct(t,this.size),(n=ht(n,this.size))<=t?new e(0,0):new e(this.get(t,this._end),this.get(n,this._end),this._step))},e.prototype.indexOf=function(t){var e=t-this._start;if(e%this._step==0){var n=e/this._step;if(n>=0&&n<this.size)return n}return-1},e.prototype.lastIndexOf=function(t){return this.indexOf(t)},e.prototype.__iterate=function(t,e){for(var n=this.size,i=this._step,r=e?this._start+(n-1)*i:this._start,s=0;s!==n&&!1!==t(r,e?n-++s:s++,this);)r+=e?-i:i;return s},e.prototype.__iterator=function(t,e){var n=this.size,i=this._step,r=e?this._start+(n-1)*i:this._start,s=0;return new Pt(function(){if(s===n)return{value:void 0,done:!0};var o=r;return r+=e?-i:i,Rt(t,e?n-++s:s++,o)})},e.prototype.equals=function(t){return t instanceof e?this._start===t._start&&this._end===t._end&&this._step===t._step:Ci(this,t)},e}(Gt);function Mi(t,e,n){for(var i=Ge(e),r=0;r!==i.length;)if((t=Qe(t,i[r++],nt))===nt)return n;return t}function ji(t,e){return Mi(this,t,e)}function Ni(t,e){return Mi(t,e,nt)!==nt}function Pi(){Fe(this.size);var t={};return this.__iterate(function(e,n){t[n]=e}),t}wt.isIterable=dt,wt.isKeyed=_t,wt.isIndexed=gt,wt.isAssociative=mt,wt.isOrdered=Tt,wt.Iterator=Pt,Ii(wt,{toArray:function(){Fe(this.size);var t=new Array(this.size||0),e=_t(this),n=0;return this.__iterate(function(i,r){t[n++]=e?[r,i]:i}),t},toIndexedSeq:function(){return new De(this)},toJS:function(){return Di(this)},toKeyedSeq:function(){return new Ie(this,!0)},toMap:function(){return Dn(this.toKeyedSeq())},toObject:Pi,toOrderedMap:function(){return ci(this.toKeyedSeq())},toOrderedSet:function(){return Yi(_t(this)?this.valueSeq():this)},toSet:function(){return xi(_t(this)?this.valueSeq():this)},toSetSeq:function(){return new xe(this)},toSeq:function(){return gt(this)?this.toIndexedSeq():_t(this)?this.toKeyedSeq():this.toSetSeq()},toStack:function(){return vi(_t(this)?this.valueSeq():this)},toList:function(){return Yn(_t(this)?this.valueSeq():this)},toString:function(){return"[Collection]"},__toString:function(t,e){return 0===this.size?t+e:t+" "+this.toSeq().map(this.__toStringMapper).join(", ")+" "+e},concat:function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return qe(this,function(t,e){var n=_t(t),i=[t].concat(e).map(function(t){return dt(t)?n&&(t=Ot(t)):t=n?ee(t):ne(Array.isArray(t)?t:[t]),t}).filter(function(t){return 0!==t.size});if(0===i.length)return t;if(1===i.length){var r=i[0];if(r===t||n&&_t(r)||gt(t)&&gt(r))return r}var s=new Jt(i);return n?s=s.toKeyedSeq():gt(t)||(s=s.toSetSeq()),(s=s.flatten(!0)).size=i.reduce(function(t,e){if(void 0!==t){var n=e.size;if(void 0!==n)return t+n}},0),s}(this,t))},includes:function(t){return this.some(function(e){return ue(e,t)})},entries:function(){return this.__iterator(At)},every:function(t,e){Fe(this.size);var n=!0;return this.__iterate(function(i,r,s){if(!t.call(e,i,r,s))return n=!1,!1}),n},filter:function(t,e){return qe(this,Le(this,t,e,!0))},find:function(t,e,n){var i=this.findEntry(t,e);return i?i[1]:n},forEach:function(t,e){return Fe(this.size),this.__iterate(e?t.bind(e):t)},join:function(t){Fe(this.size),t=void 0!==t?""+t:",";var e="",n=!0;return this.__iterate(function(i){n?n=!1:e+=t,e+=null!==i&&void 0!==i?i.toString():""}),e},keys:function(){return this.__iterator(Lt)},map:function(t,e){return qe(this,Ee(this,t,e))},reduce:function(t,e,n){return Bi(this,t,e,n,arguments.length<2,!1)},reduceRight:function(t,e,n){return Bi(this,t,e,n,arguments.length<2,!0)},reverse:function(){return qe(this,Te(this,!0))},slice:function(t,e){return qe(this,ze(this,t,e,!0))},some:function(t,e){return!this.every($i(t),e)},sort:function(t){return qe(this,je(this,t))},values:function(){return this.__iterator(zt)},butLast:function(){return this.slice(0,-1)},isEmpty:function(){return void 0!==this.size?0===this.size:!this.some(function(){return!0})},count:function(t,e){return st(t?this.toSeq().filter(t,e):this)},countBy:function(t,e){return function(t,e,n){var i=Dn().asMutable();return t.__iterate(function(r,s){i.update(e.call(n,r,s,t),0,function(t){return t+1})}),i.asImmutable()}(this,t,e)},equals:function(t){return Ci(this,t)},entrySeq:function(){var t=this;if(t._cache)return new Jt(t._cache);var e=t.toSeq().map(Ki).toIndexedSeq();return e.fromEntrySeq=function(){return t.toSeq()},e},filterNot:function(t,e){return this.filter($i(t),e)},findEntry:function(t,e,n){var i=n;return this.__iterate(function(n,r,s){if(t.call(e,n,r,s))return i=[r,n],!1}),i},findKey:function(t,e){var n=this.findEntry(t,e);return n&&n[0]},findLast:function(t,e,n){return this.toKeyedSeq().reverse().find(t,e,n)},findLastEntry:function(t,e,n){return this.toKeyedSeq().reverse().findEntry(t,e,n)},findLastKey:function(t,e){return this.toKeyedSeq().reverse().findKey(t,e)},first:function(t){return this.find(at,null,t)},flatMap:function(t,e){return qe(this,function(t,e,n){var i=Be(t);return t.toSeq().map(function(r,s){return i(e.call(n,r,s,t))}).flatten(!0)}(this,t,e))},flatten:function(t){return qe(this,Me(this,t,!0))},fromEntrySeq:function(){return new Se(this)},get:function(t,e){return this.find(function(e,n){return ue(n,t)},void 0,e)},getIn:ji,groupBy:function(t,e){return function(t,e,n){var i=_t(t),r=(Tt(t)?ci():Dn()).asMutable();t.__iterate(function(s,o){r.update(e.call(n,s,o,t),function(t){return(t=t||[]).push(i?[o,s]:s),t})});var s=Be(t);return r.map(function(e){return qe(t,s(e))}).asImmutable()}(this,t,e)},has:function(t){return this.get(t,nt)!==nt},hasIn:function(t){return Ni(this,t)},isSubset:function(t){return t="function"==typeof t.includes?t:wt(t),this.every(function(e){return t.includes(e)})},isSuperset:function(t){return(t="function"==typeof t.isSubset?t:wt(t)).isSubset(this)},keyOf:function(t){return this.findKey(function(e){return ue(e,t)})},keySeq:function(){return this.toSeq().map(Vi).toIndexedSeq()},last:function(t){return this.toSeq().reverse().first(t)},lastKeyOf:function(t){return this.toKeyedSeq().reverse().keyOf(t)},max:function(t){return Ne(this,t)},maxBy:function(t,e){return Ne(this,e,t)},min:function(t){return Ne(this,t?Hi(t):Fi)},minBy:function(t,e){return Ne(this,e?Hi(e):Fi,t)},rest:function(){return this.slice(1)},skip:function(t){return 0===t?this:this.slice(Math.max(0,t))},skipLast:function(t){return 0===t?this:this.slice(0,-Math.max(0,t))},skipWhile:function(t,e){return qe(this,Ae(this,t,e,!0))},skipUntil:function(t,e){return this.skipWhile($i(t),e)},sortBy:function(t,e){return qe(this,je(this,e,t))},take:function(t){return this.slice(0,Math.max(0,t))},takeLast:function(t){return this.slice(-Math.max(0,t))},takeWhile:function(t,e){return qe(this,function(t,e,n){var i=Ve(t);return i.__iterateUncached=function(i,r){var s=this;if(r)return this.cacheResult().__iterate(i,r);var o=0;return t.__iterate(function(t,r,a){return e.call(n,t,r,a)&&++o&&i(t,r,s)}),o},i.__iteratorUncached=function(i,r){var s=this;if(r)return this.cacheResult().__iterator(i,r);var o=t.__iterator(At,r),a=!0;return new Pt(function(){if(!a)return{value:void 0,done:!0};var t=o.next();if(t.done)return t;var r=t.value,u=r[0],c=r[1];return e.call(n,c,u,s)?i===At?t:Rt(i,u,c,t):(a=!1,{value:void 0,done:!0})})},i}(this,t,e))},takeUntil:function(t,e){return this.takeWhile($i(t),e)},update:function(t){return t(this)},valueSeq:function(){return this.toIndexedSeq()},hashCode:function(){return this.__hash||(this.__hash=function(t){if(t.size===1/0)return 0;var e=Tt(t),n=_t(t),i=e?1:0;return function(t,e){return e=ce(e,3432918353),e=ce(e<<15|e>>>-15,461845907),e=ce(e<<13|e>>>-13,5),e=ce((e=(e+3864292196|0)^t)^e>>>16,2246822507),e=he((e=ce(e^e>>>13,3266489909))^e>>>16)}(t.__iterate(n?e?function(t,e){i=31*i+Gi(fe(t),fe(e))|0}:function(t,e){i=i+Gi(fe(t),fe(e))|0}:e?function(t){i=31*i+fe(t)|0}:function(t){i=i+fe(t)|0}),i)}(this))}});var Ri=wt.prototype;Ri[pt]=!0,Ri[Nt]=Ri.values,Ri.toJSON=Ri.toArray,Ri.__toStringMapper=Xe,Ri.inspect=Ri.toSource=function(){return this.toString()},Ri.chain=Ri.flatMap,Ri.contains=Ri.includes,Ii(Ot,{flip:function(){return qe(this,ke(this))},mapEntries:function(t,e){var n=this,i=0;return qe(this,this.toSeq().map(function(r,s){return t.call(e,[s,r],i++,n)}).fromEntrySeq())},mapKeys:function(t,e){var n=this;return qe(this,this.toSeq().flip().map(function(i,r){return t.call(e,i,r,n)}).flip())}});var qi=Ot.prototype;qi[vt]=!0,qi[Nt]=Ri.entries,qi.toJSON=Pi,qi.__toStringMapper=function(t,e){return Xe(e)+": "+Xe(t)},Ii(bt,{toKeyedSeq:function(){return new Ie(this,!1)},filter:function(t,e){return qe(this,Le(this,t,e,!1))},findIndex:function(t,e){var n=this.findEntry(t,e);return n?n[0]:-1},indexOf:function(t){var e=this.keyOf(t);return void 0===e?-1:e},lastIndexOf:function(t){var e=this.lastKeyOf(t);return void 0===e?-1:e},reverse:function(){return qe(this,Te(this,!1))},slice:function(t,e){return qe(this,ze(this,t,e,!1))},splice:function(t,e){var n=arguments.length;if(e=Math.max(e||0,0),0===n||2===n&&!e)return this;t=ct(t,t<0?this.count():this.size);var i=this.slice(0,t);return qe(this,1===n?i:i.concat(He(arguments,2),this.slice(t+e)))},findLastIndex:function(t,e){var n=this.findLastEntry(t,e);return n?n[0]:-1},first:function(t){return this.get(0,t)},flatten:function(t){return qe(this,Me(this,t,!1))},get:function(t,e){return(t=ot(this,t))<0||this.size===1/0||void 0!==this.size&&t>this.size?e:this.find(function(e,n){return n===t},void 0,e)},has:function(t){return(t=ot(this,t))>=0&&(void 0!==this.size?this.size===1/0||t<this.size:-1!==this.indexOf(t))},interpose:function(t){return qe(this,function(t,e){var n=Ve(t);return n.size=t.size&&2*t.size-1,n.__iterateUncached=function(n,i){var r=this,s=0;return t.__iterate(function(t){return(!s||!1!==n(e,s++,r))&&!1!==n(t,s++,r)},i),s},n.__iteratorUncached=function(n,i){var r,s=t.__iterator(zt,i),o=0;return new Pt(function(){return(!r||o%2)&&(r=s.next()).done?r:o%2?Rt(n,o++,e):Rt(n,o++,r.value,r)})},n}(this,t))},interleave:function(){var t=[this].concat(He(arguments)),e=Re(this.toSeq(),Gt.of,t),n=e.flatten(!0);return e.size&&(n.size=e.size*t.length),qe(this,n)},keySeq:function(){return Ai(0,this.size)},last:function(t){return this.get(-1,t)},skipWhile:function(t,e){return qe(this,Ae(this,t,e,!1))},zip:function(){return qe(this,Re(this,Wi,[this].concat(He(arguments))))},zipAll:function(){return qe(this,Re(this,Wi,[this].concat(He(arguments)),!0))},zipWith:function(t){var e=He(arguments);return e[0]=this,qe(this,Re(this,t,e))}});var Ui=bt.prototype;function Bi(t,e,n,i,r,s){return Fe(t.size),t.__iterate(function(t,s,o){r?(r=!1,n=t):n=e.call(i,n,t,s,o)},s),n}function Vi(t,e){return e}function Ki(t,e){return[e,t]}function $i(t){return function(){return!t.apply(this,arguments)}}function Hi(t){return function(){return-t.apply(this,arguments)}}function Wi(){return He(arguments)}function Fi(t,e){return t<e?1:t>e?-1:0}function Gi(t,e){return t^e+2654435769+(t<<6)+(t>>2)|0}Ui[yt]=!0,Ui[Et]=!0,Ii(Ct,{get:function(t,e){return this.has(t)?t:e},includes:function(t){return this.has(t)},keySeq:function(){return this.valueSeq()}}),Ct.prototype.has=Ri.includes,Ct.prototype.contains=Ct.prototype.includes,Ii(Ft,Ot.prototype),Ii(Gt,bt.prototype),Ii(Yt,Ct.prototype);var Yi=function(t){function e(t){return null===t||void 0===t?Qi():bi(t)?t:Qi().withMutations(function(e){var n=Ct(t);Fe(n.size),n.forEach(function(t){return e.add(t)})})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.of=function(){return this(arguments)},e.fromKeys=function(t){return this(Ot(t).keySeq())},e.prototype.toString=function(){return this.__toString("OrderedSet {","}")},e}(xi);Yi.isOrderedSet=bi;var Ji,Xi=Yi.prototype;function Zi(t,e){var n=Object.create(Xi);return n.size=t?t.size:0,n._map=t,n.__ownerID=e,n}function Qi(){return Ji||(Ji=Zi(li()))}Xi[Et]=!0,Xi.zip=Ui.zip,Xi.zipWith=Ui.zipWith,Xi.__empty=Qi,Xi.__make=Zi;var tr=function(t,e){var n,i=function(s){var o=this;if(s instanceof i)return s;if(!(this instanceof i))return new i(s);if(!n){n=!0;var a=Object.keys(t),u=r._indices={};r._name=e,r._keys=a,r._defaultValues=t;for(var c=0;c<a.length;c++){var h=a[c];u[h]=c,r[h]?"object"==typeof console&&console.warn:sr(r,h)}}this.__ownerID=void 0,this._values=Yn().withMutations(function(t){t.setSize(o._keys.length),Ot(s).forEach(function(e,n){t.set(o._indices[n],e===o._defaultValues[n]?void 0:e)})})},r=i.prototype=Object.create(er);return r.constructor=i,e&&(i.displayName=e),i};tr.prototype.toString=function(){for(var t,e=ir(this)+" { ",n=this._keys,i=0,r=n.length;i!==r;i++)t=n[i],e+=(i?", ":"")+t+": "+Xe(this.get(t));return e+" }"},tr.prototype.equals=function(t){return this===t||t&&this._keys===t._keys&&rr(this).equals(rr(t))},tr.prototype.hashCode=function(){return rr(this).hashCode()},tr.prototype.has=function(t){return this._indices.hasOwnProperty(t)},tr.prototype.get=function(t,e){if(!this.has(t))return e;var n=this._indices[t],i=this._values.get(n);return void 0===i?this._defaultValues[t]:i},tr.prototype.set=function(t,e){if(this.has(t)){var n=this._values.set(this._indices[t],e===this._defaultValues[t]?void 0:e);if(n!==this._values&&!this.__ownerID)return nr(this,n)}return this},tr.prototype.remove=function(t){return this.set(t)},tr.prototype.clear=function(){var t=this._values.clear().setSize(this._keys.length);return this.__ownerID?this:nr(this,t)},tr.prototype.wasAltered=function(){return this._values.wasAltered()},tr.prototype.toSeq=function(){return rr(this)},tr.prototype.toJS=function(){return Di(this)},tr.prototype.entries=function(){return this.__iterator(At)},tr.prototype.__iterator=function(t,e){return rr(this).__iterator(t,e)},tr.prototype.__iterate=function(t,e){return rr(this).__iterate(t,e)},tr.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._values.__ensureOwner(t);return t?nr(this,e,t):(this.__ownerID=t,this._values=e,this)},tr.isRecord=St,tr.getDescriptiveName=ir;var er=tr.prototype;function nr(t,e,n){var i=Object.create(Object.getPrototypeOf(t));return i._values=e,i.__ownerID=n,i}function ir(t){return t.constructor.displayName||t.constructor.name||"Record"}function rr(t){return ee(t._keys.map(function(e){return[e,t.get(e)]}))}function sr(t,e){try{Object.defineProperty(t,e,{get:function(){return this.get(e)},set:function(t){We(this.__ownerID,"Cannot set on an immutable record."),this.set(e,t)}})}catch(t){}}function or(t,e){return function t(e,n,i,r,s,o){var a=Array.isArray(i)?Gt:Ye(i)?Ft:null;if(a){if(~e.indexOf(i))throw new TypeError("Cannot convert circular structure to Immutable");e.push(i),s&&""!==r&&s.push(r);var u=n.call(o,r,a(i).map(function(r,o){return t(e,n,r,o,s,i)}),s&&s.slice());return e.pop(),s&&s.pop(),u}return i}([],e||ar,t,"",e&&e.length>2?[]:void 0,{"":t})}function ar(t,e){return _t(e)?e.toMap():e.toList()}er[xt]=!0,er.delete=er.remove,er.deleteIn=er.removeIn=un,er.getIn=ji,er.hasIn=Ri.hasIn,er.merge=fn,er.mergeWith=pn,er.mergeIn=mn,er.mergeDeep=yn,er.mergeDeepWith=gn,er.mergeDeepIn=wn,er.setIn=on,er.update=hn,er.updateIn=ln,er.withMutations=On,er.asMutable=bn,er.asImmutable=Cn,er[Nt]=er.entries,er.toJSON=er.toObject=Ri.toObject,er.inspect=er.toSource=function(){return this.toString()};var ur={name:"outline",props:{visible:{type:Boolean},onClose:{type:Function},selectedTags:{type:Array,default:function(){return[]}}},data:function(){return{newTag:"",tagList:[],selectedTagsTemp:or(this.selectedTags),error:""}},mounted:function(){this.getTagList(),window.addEventListener("storage",this.getTagList,!1)},methods:{getTagList:function(){var t=Z.a.getLocalCache(v.c.oaTagList);this.tagList=t&&t.length>0?t:[]},onSave:function(){this.newTag&&this.addNewTag(),this.$emit("onSave",this.selectedTagsTemp.toJS())},addNewTag:function(){if(this.newTag&&!this.error){var t=this.tagList;t&&t.length>49?this.$message({message:"标签已达上限",type:"warning",center:!0,customClass:"alert-message",duration:1e3}):t.indexOf(this.newTag)>-1?this.$message({message:"标签新增失败",type:"error",center:!0,customClass:"alert-message",duration:1e3}):(t.splice(0,0,this.newTag),this.onTagToggleClick(this.newTag,!0),Z.a.setLocalCache(v.c.oaTagList,t),this.newTag=null,this.getTagList(),this.$message({message:"标签新增成功",type:"success",center:!0,customClass:"alert-message",duration:1e3}))}},onDeleteTag:function(t){var e=this.tagList,n=e[t];this.selectedTagsTemp.indexOf(n)>-1&&(this.selectedTagsTemp=this.selectedTagsTemp.delete(this.selectedTagsTemp.indexOf(n))),e.splice(t,1),Z.a.setLocalCache(v.c.oaTagList,e),this.getTagList()},onTagToggleClick:function(t,e){var n=this.selectedTagsTemp.indexOf(t);n>-1?this.selectedTagsTemp=this.selectedTagsTemp.delete(n):this.selectedTagsTemp&&this.selectedTagsTemp.size>9&&!e?this.$message({message:"只可选择10个标签",type:"warning",center:!0,customClass:"alert-message",duration:1e3}):this.selectedTagsTemp.size>9?this.selectedTagsTemp=this.selectedTagsTemp.set(0,t):this.selectedTagsTemp=this.selectedTagsTemp.push(t)},onClickClose:function(){this.onClose(),this.selectedTagsTemp=or(this.selectedTags)},onInputChange:function(t){var e=t.replace(/[^a-zA-Z0-9\u4E00-\u9FA5]/g,"");this.newTag=e,this.$refs.input.setCurrentValue(e),this.newTag.replace(/[^\x00-\xff]/g,"01").length>20?this.error="标签不可大于10个字":this.error=""}}},cr={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-dialog",{attrs:{visible:t.visible,"append-to-body":"","close-on-click-modal":"","before-close":t.onClickClose,width:"472px"}},[n("div",{staticClass:"dialog-title",attrs:{slot:"title"},slot:"title"},[t._v("文章标签")]),t._v(" "),n("div",{staticClass:"dialog-header"},[n("el-input",{ref:"input",attrs:{size:"small",placeholder:"请输入标签"},on:{input:t.onInputChange},model:{value:t.newTag,callback:function(e){t.newTag=e},expression:"newTag"}},[n("a",{class:{addnewtag:!0,disable:!t.newTag||t.error},attrs:{slot:"append"},on:{click:t.addNewTag},slot:"append"},[t._v("新增")])]),t._v(" "),n("div",{staticClass:"error"},[t._v(t._s(t.error))])],1),t._v(" "),n("div",{ref:"body",staticClass:"dialog-body"},[n("div",{staticClass:"tag-title"},[t._v("标签："+t._s(t.tagList&&t.tagList.length>0?"":"暂无"))]),t._v(" "),n("div",{staticClass:"tag-list"},t._l(t.tagList,function(e,i){return n("div",{key:e,class:{"tag-item":!0,selected:t.selectedTagsTemp.indexOf(e)>-1}},[n("span",{on:{click:function(n){t.onTagToggleClick(e,!1)}}},[t._v(t._s(e))]),t._v(" "),n("i",{staticClass:"el-icon-close close",on:{click:function(e){t.onDeleteTag(i)}}})])}))]),t._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{size:"small"},on:{click:t.onClickClose}},[t._v("取 消")]),t._v(" "),n("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){t.onSave()}}},[t._v("确 定")])],1)])},staticRenderFns:[]};var hr=n("VU/8")(ur,cr,!1,function(t){n("4jC7")},"data-v-70f13685",null).exports,lr={name:"loadingOutline",props:{},data:function(){return{loading1:!0,loading2:!0,loading3:!0}},computed:{},mounted:function(){this.startLoading()},methods:{startLoading:function(){var t=this;this.loading1=!0,this.loading2=!0,this.loading3=!0,window.setTimeout(function(){t.loading1=!1,t.loading2=!0,t.loading3=!0},1e3),window.setTimeout(function(){t.loading1=!1,t.loading2=!1,t.loading3=!0},2e3)}}},fr={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"body nodata-body"},[n("div",{staticClass:"loading-img"}),t._v(" "),n("div",{class:{"loading-wrapper":!0,done:!t.loading1}},[n("i",{class:{"el-icon-refresh":t.loading1,"el-icon-check":!t.loading1}}),t._v(" "),n("span",[t._v("识别标题和标签")])]),t._v(" "),n("div",{class:{"loading-wrapper":!0,done:!t.loading2}},[n("i",{class:{"el-icon-refresh":t.loading2,"el-icon-check":!t.loading2}}),t._v(" "),n("span",[t._v("正在生成提纲")])]),t._v(" "),n("div",{class:{"loading-wrapper":!0,done:!t.loading3}},[n("i",{class:{"el-icon-refresh":t.loading3,"el-icon-check":!t.loading3}}),t._v(" "),n("span",[t._v("正在生成公文")])])])},staticRenderFns:[]};var pr=n("VU/8")(lr,fr,!1,function(t){n("8cyh")},"data-v-02cc6304",null).exports,dr={name:"emptyOutline",props:{fullScreen:{type:Boolean},statusCode:{type:String},onToggleOLs:{type:Function}},data:function(){return{}}},vr={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"body nodata-body"},[n("div",{directives:[{name:"show",rawName:"v-show",value:"INIT"==t.statusCode,expression:"statusCode=='INIT'"}]},[n("div",{staticClass:"init-img"}),t._v(" "),n("div",{staticClass:"empty-txt"},[t._v("WPS AI 助手智能公文生成")])]),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:"EMPTY"==t.statusCode,expression:"statusCode=='EMPTY'"}]},[n("div",{staticClass:"empty-img"}),t._v(" "),n("div",{staticClass:"empty-txt"},[t._v("暂无符合素材，请调整标题后重试")])]),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:"DELETE_ALL"==t.statusCode,expression:"statusCode=='DELETE_ALL'"}]},[n("div",{staticClass:"empty-img"}),t._v(" "),n("div",{staticClass:"empty-txt"},[t._v("内容已清空，请重新生成")])]),t._v(" "),n("div",{staticClass:"tools"},[t.fullScreen?t._e():n("a",{staticClass:"icon-fullscreen",on:{click:function(e){t.onToggleOLs(!0)}}}),t._v(" "),t.fullScreen?n("a",{staticClass:"icon-fullscreen-exit",on:{click:function(e){t.onToggleOLs(!1)}}}):t._e()])])},staticRenderFns:[]};var _r=n("VU/8")(dr,vr,!1,function(t){n("VvEv")},"data-v-eb794524",null).exports,yr={VIEW:1,ADD:2,EDIT:3},gr="INIT",mr="EMPTY",wr="DELETE_ALL",Or={name:"outline",props:{currentItem:{type:Object},item:{type:Object},onSkip:{type:Function},downloadOADoc:{type:Function}},data:function(){return{STATUS:yr,MAX_LENGTH:50,showLoading:!1,dialogVisible:!1,title:"",docs:[],indexObject:{},originDocs:[],selectedTags:[],newOutline:"",updatingOutline:"",pageStatus:gr,error:"",genOABtnText:"生成公文",isEditorAdd:!1,isOutlineEmpty:!1,fullScreen:!1}},mounted:function(){var t=this.getOfficialDocSetting;"11"in t.elements&&(this.title=t.elements[11].content,this.onGenOADoc())},computed:h()({},Object(d.c)(["getOfficialDocSetting"]),{transferDocs:function(){return this.flattenData(this.docs)},isOverMaxLength:function(){return this.newOutline&&this.newOutline.replace(/[^\x00-\xff]/g,"01").length>100},hasEditItem:function(){var t=!1;return this.transferDocs.map(function(e){return e.status!==yr.VIEW&&(t=!0),e}),t},hasDocs:function(){return this.docs&&this.docs.length>0&&!this.showLoading},getRenderNum:function(){var t=0,e=0;return this.selectedTags.map(function(n,i){return 100<(t+=n.replace(/[^\x00-\xff]/g,"01").length)&&t<=110?e=i+1:100>=t&&(e=i+1),e}),e},insertOutlines:function(){return this.transferDocs.map(function(t){var e=t.text,n=t.outline,i=t.path;return r()({},{outline:""+n,content:e.replace(new RegExp('<br/><div style="width: 24px;display: inline-block;"></div>',"gm"),"\n"),path:i})}).filter(function(t){return t.outline&&t.content})}}),methods:{onCloseModal:function(){this.dialogVisible=!1},onSaveSelectedTags:function(t){this.selectedTags=t,this.dialogVisible=!1},onInputChange:function(t){this.title=t.replace(/\s/g,""),this.$refs.titleInput.setCurrentValue(this.title),this.title.replace(/[^\x00-\xff]/g,"01").length>100?this.error="标题不可大于50个字":this.error=""},setOutlines:function(t){var e=this;this.docs=[],1==t.code&&null!==t.data&&null!==t.data.docs?(t.data.docs.forEach(function(t,n){e.indexObject[""+(n+1)]=0,e.docs.push(r()({childDocs:[]},t))}),this.pageStatus=""):this.pageStatus=mr,this.showLoading=!1,this.updatingOutline="",this.genOABtnText="重新生成",this.$nextTick(function(){document.getElementsByClassName("body-content-right")[0].scrollTop=0})},onGenOADoc:function(){var t=this;!this.title||this.showLoading||this.error||(this.showLoading=!0,document.body.clientHeight<=640&&(this.fullScreen=!0),this.indexObject[0]=0,p.a.get("docGenerate").getDocs({data:{title:this.title,clientId:"123456789",keywords:this.selectedTags,index:this.indexObject[0]}}).then(function(e){window.setTimeout(function(){t.setOutlines(e)},3e3)}).catch(function(e){window.setTimeout(function(){t.docs=[],t.showLoading=!1,t.updatingOutline="",t.genOABtnText="重新生成"},3e3)}))},toChinesNum:function(t){var e=["零","一","二","三","四","五","六","七","八","九"],n=["","十","百","千","万"];t=parseInt(t,10);var i=function(t){for(var i=t.toString().split("").reverse(),r="",s=0;s<i.length;s++)r=(0==s&&0==i[s]?"":s>0&&0==i[s]&&0==i[s-1]?"":e[i[s]]+(0==i[s]?n[0]:n[s]))+r;return t>9&&t<20&&(r=r.slice(1)),r},r=Math.floor(t/1e4),s=t%1e4;return s.toString().length<4&&(s="0"+s),r?i(r)+"万"+i(s):i(t)},flattenData:function(t){var e=[];return function t(n,i){n.forEach(function(n,r){var s=n.childDocs,o=n.contents,a=n.text,u=n.status,c=X()(n,["childDocs","contents","text","status"]),l=i?i+"."+(r+1):""+(r+1),f=u||yr.VIEW,p=o?o[0].text.replace(new RegExp("\\n","gm"),'<br/><div style="width: 24px;display: inline-block;"></div>'):a;e.push(h()({},c,{path:l,status:f,text:p})),s&&t(s,""+l)})}(t),e},getIndex:function(t){switch(t.split(".").length){case 1:return this.toChinesNum(t)+"、";case 2:return"（"+this.toChinesNum(t.split(".")[1])+"）";default:return null}},genEditingTip:function(t){return t===yr.ADD?"正在修改提纲":null},isEleInView:function(t,e,n){return!(t.offsetTop<e.scrollTop||n>0)},setEleInView:function(){var t=document.getElementById("Edit");if(t&&t.focus(),t=t&&t.parentElement.parentElement.parentElement){var e=document.getElementsByClassName("body-content-left")[0],n=t.offsetTop+t.offsetHeight+20-e.scrollTop-e.clientHeight;this.isEleInView(t,e,n)||(e.scrollTop+=n)}},onChangeNextDoc:function(){var t=this;this.isEditorAdd=!1,!this.title||this.showLoading||this.error||(this.updatingOutline="0",p.a.get("docGenerate").getDocs({data:{title:this.title,clientId:"123456789",keywords:this.selectedTags,index:++this.indexObject[0]}}).then(function(e){t.setOutlines(e)}).catch(function(e){t.docs=[],t.showLoading=!1,t.updatingOutline="",t.genOABtnText="重新生成"}))},onChangeNextOutline:function(t){this.newOutline=t.outline,this.isEditorAdd=!1,this.onOutlineConfirm(t)},addOutline:function(t){if(!this.isEditorAdd){this.isEditorAdd=!0,this.newOutline="";var e=t.split(".");switch(this.originDocs=JSON.parse(Y()(this.docs)),e.length){case 1:this.docs.splice(e[0],0,{outline:"",text:"",status:yr.ADD,childDocs:[]});break;case 2:this.docs[e[0]-1].childDocs.splice(e[1],0,{outline:"",text:"",status:yr.ADD})}this.$nextTick(function(){this.setEleInView()})}},editOutline:function(t){if(!this.isEditorAdd){this.isEditorAdd=!0,this.newOutline=t.outline;var e=t.path.split(".");switch(this.originDocs=JSON.parse(Y()(this.docs)),e.length){case 1:var n=this.docs[e[0]-1].childDocs;this.docs.splice(e[0]-1,1,{outline:t.outline,text:t.text,status:yr.EDIT,childDocs:n});break;case 2:this.docs[e[0]-1].childDocs.splice(e[1]-1,1,{outline:t.outline,text:t.text,status:yr.EDIT})}this.$nextTick(function(){this.setEleInView()})}},deleteOutline:function(t){var e=this;if(!this.isEditorAdd){var n=t.split(".");switch(n.length){case 1:if(0==this.docs[n[0]-1].childDocs.length)return this.docs.splice(n[0]-1,1),void(this.docs&&0===this.docs.length&&(this.pageStatus=wr));this.$confirm("当前为一级标题，删除此标题将会删除下属二级标题，确认删除?","提示",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then(function(){e.docs.splice(n[0]-1,1)}).catch(function(){});break;case 2:this.docs[n[0]-1].childDocs.splice(n[1]-1,1)}}},onOutlineChange:function(t){var e=t.replace(/\s/g,"");this.newOutline=e,this.$refs.outlineInput[0].setCurrentValue(e),""!==this.newOutline&&(this.isOutlineEmpty=!1),this.$nextTick(function(){this.setEleInView()})},onOutlineConfirm:function(t){var e=this;""!==this.newOutline.replace(/\s/g,"")?(isNaN(this.indexObject[t.path])&&(this.indexObject[t.path]=0),this.isOverMaxLength||this.updatingOutline===t.path||(this.updatingOutline=t.path,p.a.get("docGenerate").updateDoc({data:{title:this.title,outline:this.newOutline,clientId:"123456789",keywords:this.selectedTags,index:this.indexObject[t.path]++}}).then(function(n){var i=t.path.split(".");switch(i.length){case 1:var r=e.docs[i[0]-1].childDocs;e.docs.splice(i[0]-1,1,{outline:e.newOutline,text:n.data.contents[0].text.replace(new RegExp("\\n","gm"),'<br/><div style="width: 24px;display: inline-block;"></div>'),status:yr.VIEW,childDocs:r});break;case 2:e.docs[i[0]-1].childDocs.splice(i[1]-1,1,{outline:e.newOutline,text:n.data.contents[0].text.replace(new RegExp("\\n","gm"),'<br/><div style="width: 24px;display: inline-block;"></div>'),status:yr.VIEW})}e.isEditorAdd=!1,e.updatingOutline=""}).catch(function(t){e.isEditorAdd=!1,e.updatingOutline=""}))):this.isOutlineEmpty=!0},onOutlineCancel:function(t){this.updatingOutline!==t.path&&(this.docs=this.originDocs,this.isEditorAdd=!1,this.isOutlineEmpty=!1)},topNItems:function(t,e){for(var n=[],i=t.length>e?e:t.length,r=0;r<i;r++)n.push(t[r]);return n},showSecondOutline:function(t){return!(this.transferDocs.length<t+1)&&(!(this.transferDocs[t].path.split(".").length>1)&&!(this.transferDocs.length>t+1&&this.transferDocs[t+1].path.split(".").length>1))},toggleOLs:function(t){this.fullScreen=t}},components:{TagModal:hr,LoadingOutline:pr,EmptyOutline:_r}},br={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"outline-wrapper"},[t.fullScreen?t._e():n("div",{staticClass:"header-wrapper"},[n("div",{staticClass:"header-input-wrapper"},[n("el-input",{ref:"titleInput",staticClass:"header-input",attrs:{placeholder:"请输入标题，WPS AI 助手智能为您生成公文"},on:{input:function(e){t.onInputChange(t.title)}},nativeOn:{keyup:function(e){if(!("button"in e)&&t._k(e.keyCode,"enter",13,e.key,"Enter"))return null;t.onGenOADoc()}},model:{value:t.title,callback:function(e){t.title="string"==typeof e?e.trim():e},expression:"title"}}),t._v(" "),n("div",{class:{"header-gen-doc":!0,disable:!t.title||t.showLoading||!!this.error},on:{click:function(e){t.onGenOADoc()}}},[t._v(t._s(t.genOABtnText))])],1),t._v(" "),n("div",{staticClass:"error"},[t._v(t._s(t.error))]),t._v(" "),n("div",{staticClass:"header-tags"},[n("div",{staticClass:"header-tags-wrapper"},[t._l(t.topNItems(t.selectedTags,t.getRenderNum),function(e){return n("span",{key:e,staticClass:"header-tags-item"},[t._v(t._s(e))])}),t._v(" "),t.selectedTags.length>t.getRenderNum?n("el-popover",{attrs:{placement:"bottom-end",width:"130",trigger:"hover"}},[n("div",{staticStyle:{display:"flex","flex-direction":"column","font-size":"12px",color:"#7C7C7C"}},t._l(t.topNItems(t.selectedTags,t.getRenderNum),function(e){return n("span",{key:e,staticStyle:{margin:"5px 0"}},[t._v(t._s(e))])})),t._v(" "),n("div",{attrs:{slot:"reference"},slot:"reference"},[t._v("...")])]):t._e(),t._v(" "),n("a",{staticClass:"header-addtag",on:{click:function(e){t.dialogVisible=!0}}},[t._v("添加标签")])],2)])]),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.hasDocs,expression:"hasDocs"}],staticClass:"body"},[n("div",{staticClass:"left"},[n("div",{staticClass:"body-header"},[n("span",{staticClass:"body-header-title"},[t._v("提纲生成")]),t._v(" "),n("a",{staticClass:"change-next-btn",on:{click:t.onChangeNextDoc}},[t._v("换一个")])]),t._v(" "),n("div",{directives:[{name:"loading",rawName:"v-loading",value:"0"===t.updatingOutline,expression:"updatingOutline === '0'"}],staticClass:"body-content body-content-left"},t._l(t.transferDocs,function(e,i){return n("el-tooltip",{key:e.path,attrs:{content:e.outline,disabled:!0,placement:"right"}},[n("div",[n("div",{staticClass:"doc-item doc-item-single"},[n("span",{staticClass:"doc-item-index"},[t._v(t._s(t.getIndex(e.path)))]),t._v(" "),e.status===t.STATUS.VIEW?n("span",{attrs:{title:e.outline}},[t._v(t._s(e.outline))]):t._e(),t._v(" "),e.status!==t.STATUS.VIEW?n("div",{staticClass:"editBox"},[n("el-input",{ref:"outlineInput",refInFor:!0,attrs:{type:"textarea",id:"Edit",autofocus:"",autosize:"",placeholder:"请输入标题",disabled:t.updatingOutline===e.path},on:{input:t.onOutlineChange},model:{value:t.newOutline,callback:function(e){t.newOutline=e},expression:"newOutline"}}),t._v(" "),n("div",{staticClass:"editBtn"},[t.isOverMaxLength?n("span",{staticClass:"error"},[t._v("标题不可大于"+t._s(t.MAX_LENGTH)+"字")]):t._e(),t._v(" "),t.isOutlineEmpty?n("span",{staticClass:"error"},[t._v("标题不能为空")]):t._e(),t._v(" "),n("span",{staticStyle:{float:"right","padding-right":"5px"}},[n("a",{class:{comfirm:!0,haserror:t.isOverMaxLength,disable:t.isOverMaxLength||t.updatingOutline===e.path},on:{click:function(n){t.onOutlineConfirm(e)}}},[t._v("确定")]),t._v(" "),n("a",{class:{cancel:!0,haserror:t.isOverMaxLength,disable:t.updatingOutline===e.path},on:{click:function(n){t.onOutlineCancel(e)}}},[t._v("取消")])])])],1):t._e(),t._v(" "),e.status!==t.STATUS.VIEW||t.hasEditItem?t._e():n("div",{staticClass:"toolBar"},[n("a",{on:{click:function(n){t.editOutline(e)}}},[t._v("编辑")]),t._v(" "),n("a",{on:{click:function(n){t.deleteOutline(e.path)}}},[t._v("删除")]),t._v(" "),n("a",{on:{click:function(n){t.addOutline(e.path)}}},[t._v("新增")])])]),t._v(" "),t.showSecondOutline(i)?n("div",{staticClass:"doc-item doc-item-second"},[n("a",{on:{click:function(n){t.addOutline(e.path+".1")}}},[t._v("添加二级提纲")])]):t._e()])])}))]),t._v(" "),t._m(0),t._v(" "),n("div",{staticClass:"right"},[t._m(1),t._v(" "),n("div",{staticClass:"body-content body-content-right"},t._l(t.transferDocs,function(e){return n("div",{key:e.path,staticClass:"doc-item"},[n("div",{staticClass:"doc-item-multiple"},[n("div",{staticClass:"title"},[n("el-tooltip",{attrs:{content:e.outline,disabled:!0,placement:"left"}},[n("span",{attrs:{title:e.outline}},[t._v(t._s(t.getIndex(e.path))+t._s(e.outline)+t._s(t.genEditingTip(e.status)))])]),t._v(" "),n("a",{directives:[{name:"show",rawName:"v-show",value:!t.isEditorAdd&&!t.updatingOutline&&t.updatingOutline!==e.path,expression:"!isEditorAdd && !updatingOutline && updatingOutline !== outline.path"}],staticClass:"changeNextOutline",on:{click:function(n){t.onChangeNextOutline(e)}}},[t._v("换一换")])],1),t._v(" "),n("span",{directives:[{name:"loading",rawName:"v-loading",value:t.updatingOutline===e.path,expression:"updatingOutline === outline.path"}],staticClass:"text",domProps:{innerHTML:t._s(e.text)}})])])}))]),t._v(" "),n("div",{staticClass:"tools"},[t.fullScreen?t._e():n("a",{staticClass:"icon-fullscreen",on:{click:function(e){t.toggleOLs(!0)}}}),t._v(" "),t.fullScreen?n("a",{staticClass:"icon-fullscreen-exit",on:{click:function(e){t.toggleOLs(!1)}}}):t._e()])]),t._v(" "),t.showLoading?n("LoadingOutline"):t._e(),t._v(" "),t.showLoading||t.docs&&0!==t.docs.length?t._e():n("EmptyOutline",{attrs:{statusCode:t.pageStatus,onToggleOLs:t.toggleOLs,fullScreen:t.fullScreen}}),t._v(" "),n("div",{staticClass:"footer"},[n("a",{staticClass:"skip-btn",on:{click:function(e){t.onSkip(t.title)}}},[t._v("跳过")]),t._v(" "),t.hasDocs?n("el-button",{staticClass:"insert-doc-btn",attrs:{type:"primary",size:"small"},on:{click:function(e){t.downloadOADoc(t.title,t.insertOutlines)}}},[t._v("插入公文")]):t._e()],1),t._v(" "),n("TagModal",{attrs:{visible:t.dialogVisible,onClose:t.onCloseModal,selectedTags:t.selectedTags},on:{onSave:t.onSaveSelectedTags}})],1)},staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"middle"},[e("div",{staticClass:"gen-doc-btn"},[e("div",{staticClass:"rectangle"},[this._v("生成文章")]),this._v(" "),e("div",{staticClass:"triangle"})])])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"body-header"},[e("span",{staticClass:"body-header-title"},[this._v("公文生成")])])}]};var Cr=n("VU/8")(Or,br,!1,function(t){n("cP4B")},"data-v-3d6cbead",null).exports,Ir={wps:1,et:2,wpp:3}[v.b.name],Dr={name:"preview",mixins:[f.a],props:{previewThumbList:{type:Object,default:function(){return{}}}},data:function(){return{status:v.h.LOADING,item:{},listData:{},uMayLikeList:[],appName:v.b.name,previewStyle:{transform:"translateY(100%)",opacity:0},isPressional:!0,showOutline:!1,isOfficeGenerate:!1}},computed:h()({},Object(d.c)(["getPreviewData","getPreviewIndex","getIsDocerVip","getPreviewInfo","checkHasTplBought","getOfficialDocSetting"]),{currentItem:function(){var t=this.getPreviewData[this.getPreviewIndex]||{};return t.official=!0,t},isEasy:function(){return"wpp"===v.b.name}}),created:function(){this.updateSetting()},mounted:function(){this.getPreviewDetails(),window._ESC_HOOKS.push({method:this.close,key:"preview"}),_.a.$on(v.g.boardcastPurchaseStatus,this.downloadCallback),this.show();var t=this.currentItem.name,e=t.split("-");"讲话稿"===(e=(t=e[e.length-1]).split("."))[0]&&(this.isOfficeGenerate=!0),this.consolidateInfo("preview_show"),this.checkHasBought({mb_id:this.currentItem.id,doc_type:3}),m.a.setIsPreview(!0)},beforeDestroy:function(){_.a.$off(v.g.boardcastPurchaseStatus,this.downloadCallback),Object(g.f)("preview"),m.a.setIsPreview(!1)},methods:h()({},Object(d.b)(["setAppComponent","setOfficialDocElementInit","setPreviewIndex","addIdtoPurchasesList","checkHasBought","setOfficialDocType"]),{consolidateInfo:function(t){y.a.send.apply(y.a,u()([this.getPreviewInfo.from,t,this.getPreviewInfo.module,this.getPreviewInfo.tag,this.isFreeTpl?"free":"pay"].filter(function(t){return t})))},show:function(){var t=this;window.setTimeout(function(){t.previewStyle={transform:"translateY(0)",opacity:1}},100)},hide:function(){return this.previewStyle={transform:"translateY(100%)",opacity:0},new o.a(function(t){window.setTimeout(function(){t()},200)})},getPreviewDetails:function(){this.status=v.h.LOADING,this.isPressional?this.getDocerData():this.isEasy?this.getEasyData():(this.getDocerData(),this.getUMaylikeDocer())},getDocerData:function(){var t=this;p.a.get("preview").fetch({data:{id:this.currentItem.id}}).then(function(e){"ok"===e.result?(t.item=e.data.basic_data,t.listData=e.data.page_data,t.status=v.h.LOADED):t.status=v.h.ERROR}).catch(function(){t.status=v.h.ERROR})},getEasyData:function(){var t=this;p.a.get("easy").fetchDetail({data:{tid:+this.currentItem.id}}).then(function(e){"ok"===e.result?(t.uMayLikeList=e.data.detail.similars,t.item={c_avatar:e.data.detail.authorAvatar},t.listData={urls:e.data.detail.preUrls},t.status=v.h.LOADED):t.status=v.h.ERROR}).catch(function(){t.status=v.h.ERROR})},getUMaylikeDocer:function(){var t=this;return p.a.get("uMayLike").query({data:{id:this.currentItem.id,mb_app:Ir}}).then(function(e){"ok"===e.result&&e.data?t.uMayLikeList=e.data.data:t.uMayLikeList=[]}).catch(function(){t.uMayLikeList=[]})},upgrade:function(){this.buyVIP(),this.consolidateInfo("preview_member_click"),m.a.setBtnPos("preview_word")},nextStep:function(){this.isOfficeGenerate?this.showOutline=!0:this.downloadTpl()},onSkip:function(t){this.downloadTpl(t)},downloadTpl:function(t){this.consolidateInfo("preview_use_click");var e=this.getOfficialDocSetting,n=r()({});t&&(n=r()(e.elements,{11:{name:"正文标题",content:t}}),e=r()(e,{elements:n})),this.downloadOfficialDoc(this.currentItem,e)},downloadOADoc:function(t,e){var n=this.getOfficialDocSetting,i=r()(n.elements,{11:{name:"正文标题",content:t},13:{name:"正文",content:e}}),s=r()(n,{elements:i});this.downloadOfficialDoc(this.currentItem,s)},downloadCallback:function(t){var e=t.mbid;"finish"===t.paystatus&&(this.downloadOfficialDoc(this.currentItem,this.getOfficialDocSetting),this.addIdtoPurchasesList(e))},close:function(){var t=this;this.setOfficialDocElementInit(),this.hide().then(function(){t.setAppComponent("")})},resetTpl:function(){this.uMayLikeList=[],this.listData={}},updateSetting:function(){var t=this.currentItem.name,e=t.split("-"),n=(e=(t=e[e.length-1]).split("."))[0];for(var i in $.a)if($.a[i]["类别"]==n){this.setOfficialDocType(i);break}}}),watch:{"currentItem.id":function(t){t&&(this.resetTpl(),this.checkHasBought({mb_id:t,doc_type:3}),this.getPreviewDetails())}},components:{PreviewFooter:l.PreviewFooter,ImgList:l.ImgList,OfficialSettingList:F,Outline:Cr}},xr={render:function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"preview",style:t.previewStyle},[n("div",{staticClass:"min-width"},[n("div",{staticClass:"preview-body",class:t.appName},[n("div",{staticClass:"preview-header"},[n("div",{staticClass:"wrapper"},[n("div",{staticClass:"title g-txt-ellipsis"},[t._v(t._s(t.currentItem.name))]),t._v(" "),n("div",{staticClass:"tooltip"},[n("div",{attrs:{selectable:"tooltip"}},[t._v(t._s(t.currentItem.name))]),t._v(" "),n("div",{attrs:{selectable:"tooltip"}},[t._v("编号："+t._s(t.currentItem.id))])])]),t._v(" "),n("div",{staticClass:"desc"},[n("div",{staticClass:"author"},[n("img",{staticClass:"head-icon",attrs:{src:t.item.c_avatar}}),t._v(" "),n("span",{staticClass:"author-name"},[t._v(t._s(t.currentItem.author))]),t._v(" "),n("span",{staticClass:"data"},[t._v(t._s(t.currentItem.publish_time||t.currentItem.pubTime.slice(0,10)))])]),t._v(" "),n("div",{staticClass:"view"},[n("span",[t._v("被引用 "+t._s(t.currentItem.preview||t.currentItem.down_num)+" 次")])])]),t._v(" "),n("span",{staticClass:"icon icon-close",on:{click:t.close}})]),t._v(" "),t.showOutline?t._e():n("div",{staticClass:"preview-content"},[n("div",{staticClass:"preview-row"},[n("div",{staticClass:"row-left op-body"},[n("ImgList",{attrs:{previewThumbList:t.listData.urls,appName:t.appName}})],1),t._v(" "),n("div",{staticClass:"row-right"},[n("div",{staticClass:"set"},[t._v("设置")]),t._v(" "),n("div",{staticClass:"op-body"},[n("OfficialSettingList")],1)])]),t._v(" "),n("preview-footer",{attrs:{appName:t.appName,showTip:t.isOfficeGenerate},on:{nextStep:t.nextStep,downloadTpl:t.downloadTpl}})],1),t._v(" "),t.showOutline?n("Outline",{attrs:{item:t.item,currentItem:t.currentItem,onSkip:t.onSkip,downloadOADoc:t.downloadOADoc}}):t._e()],1)])])},staticRenderFns:[]};var Sr=n("VU/8")(Dr,xr,!1,function(t){n("R3w5")},"data-v-4e17dcce",null);e.default=Sr.exports},cP4B:function(t,e){},cXZo:function(t,e){},dWkZ:function(t,e){},jHgH:function(t,e){},lGK7:function(t,e){},qhKz:function(t,e,n){t.exports=n.p+"images/wps-avatar.svg"},rROH:function(t,e){},racU:function(t,e){}});