webpackJsonp([10], {
	"59SO": function(e, t, n) {
		"use strict";
		var o = n("wYMm"),
			r = n("s0MJ"),
			a = n("LB6H"),
			i = "dm=/2019/wps/client/knewdocs",
			s = "action=knewdocs",
			c = window.screen.width,
			u = window.screen.height,
			d = "",
			l = {};
		t.a = {
			send: function(e, t) {
				r.a.getUserProfessionFinish.then(function() {
					d || (d = Object(a.a)([1e7 * Math.random(), o.f.userid, o.c.hdid, (new Date).getTime()]));
					var n = "guid=" + o.c.guid,
						l = "hdid=" + o.c.hdid,
						p = "uuid=" + o.c.uuid,
						f = "version=" + o.c.version,
						m = "channel=" + o.c.dist,
						h = "workboard=" + o.c.name,
						v = "userid=" + o.f.userid,
						w = "p24=" + o.f.userGroup,
						g = "p25=" + o.f.memberid,
						E = "p26=" + c + "*" + u,
						_ = "p28=" + o.f.profession,
						y = "p29=" + d,
						A = "p30=testa",
						b = "pnum=" + (t + 6);
						a.a.getC64CodecEncodeData([s, c, n, o, p, f, v, m, h, e, g, w, E, _, b, y, D].join("&")).then(function(e) {
                        if ("ok" == e.status && e.value) {
                            var t = "http://ic.wps.cn/wpsv6internet/infos.ads?v=D1S1E2&d=" + e.value; (new Image).src = t
                        }
                    }).
                    catch(function(e) {})
				})
			},
			sendHome: function(e) {
				if (!e) return "";
				var t = r.a.extend({}, e, {
					p1: "home_page",
					p2: o.f.entrance,
					p4: o.c.name + "_client"
				});
				this.sendAddParam(t)
			},
			sendSearch: function(e) {
				if (!e) return "";
				var t = r.a.extend({}, e, {
					p1: "search_list",
					p2: o.f.entrance
				});
				this.sendAddParam(t)
			},
			sendClass: function(e) {
				if (!e) return "";
				var t = r.a.extend({}, e, {
					p1: "class_list",
					p2: o.f.entrance
				});
				this.sendAddParam(t)
			},
			sendPreview: function(e) {
				if (!e) return "";
				var t = r.a.extend({}, e, {
					p1: "preview_page",
					p2: o.f.entrance
				});
				this.sendAddParam(t)
			},
			sendSearchBox: function(e) {
				if (!e) return "";
				var t = r.a.extend({}, e, {
					p2: o.f.entrance,
					p12: "search"
				});
				this.sendAddParam(t)
			},
			sendDownloadParam: function(e) {
				l = e
			},
			sendDownload: function(e) {
				if (!l || !e) return "";
				var t = r.a.extend({}, l, {
					p1: "download"
				}, e);
				this.sendAddParam(t)
			},
			sendDevDownload: function(e) {
				if (!l) return "";
				var t = r.a.extend({}, l, {
					p1: "devdownload",
					p11: e
				});
				this.sendAddParam(t)
			},
			sendMyTemplate: function(e) {
				if (!e) return "";
				var t = r.a.extend({}, {
					p2: o.f.entrance
				}, e);
				this.sendAddParam(t)
			},
			sendOperAD: function(e) {
				if (!e) return ""
			},
			sendAddParam: function(e) {
				var t = "",
					n = 0;
				for (var o in e) t += "&" + o + "=" + e[o], n++;
				t = "" + t.slice(1), this.send(t, n)
			},
			createPayKey: function() {
				return Object(a.a)([1e7 * Math.random(), o.f.userid, o.c.hdid, (new Date).getTime()])
			}
		}
	},
	CXu2: function(e, t, n) {
		"use strict";
		var o = n("//Fk"),
			r = n.n(o),
			a = n("mvHQ"),
			i = n.n(a),
			s = n("w7XY"),
			c = s.a.isWpsEnv ? s.a.runtime : "browser",
			u = function(e, t) {
				t = i()(t), s.a.localStorageSet(e, t, (new Date).getTime())
			},
			d = function(e, t, n, o) {
				t().then(function(t) {
					var o, r;
					n(t), "browser" !== c && (o = e, (r = t) && "ok" == r.result && r.data && u(o, r))
				}).
				catch (function(e) {
					o(e)
				})
			};
		t.a = {
			setLocalCache: function(e, t) {
				t = i()(t), window.localStorage.setItem(e, t)
			},
			getLocalCache: function(e) {
				var t = null;
				try {
					t = JSON.parse(window.localStorage.getItem(e))
				} catch (e) {}
				return t
			},
			clearLocalCache: function(e) {
				window.localStorage.removeItem(e)
			},
			bind: function(e, t, n) {
				return new r.a(function(o, r) {
					if ("browser" === c) d(e, n, o, r);
					else {
						var a = !0;
						if (t && "[object function]" === Object.prototype.toString.call(t).toLowerCase()) {
							try {
								a = t()
							} catch (e) {}
							a ? s.a.localStorageGet(e).then(function(t) {
								if (void 0 !== t && null !== t && t.value) {
									a = null;
									var i = {};
									try {
										i = JSON.parse(t.value)
									} catch (e) {}
									o(i)
								} else d(e, n, o, r)
							}).
							catch (function() {
								d(e, n, o, r)
							}) : d(e, n, o, r)
						} else d(e, n, o, r)
					}
				})
			},
			get: function(e) {
				return new r.a(function(t) {
					s.a.localStorageGet(e).then(function(e) {
						if (void 0 !== e && null !== e && e.value) {
							var n = {};
							try {
								n = JSON.parse(e.value)
							} catch (e) {}
							t(n)
						} else t(null)
					}).
					catch (function() {
						t(null)
					})
				})
			},
			set: u
		}
	},
	Dzwp: function(e, t, n) {
		"use strict";
		var o = {
			appName: "zjnull",
			entrance: "rknull",
			tag: "newfile2019",
			func: "",
			detailPage: "",
			vip: "novip",
			profession: "occmiss",
			isPreview: !1,
			btnPos: "",
			btnPage: "",
			testVer: "testa",
			alg: ""
		},
			r = function(e) {
				return e.filter(function(e) {
					return !!e
				}).join("_")
			};
		t.a = {
			info: o,
			setAppName: function(e) {
				o.appName = e || "zjnull"
			},
			setEntrance: function(e) {
				o.entrance = e || "rknull"
			},
			setFunc: function(e) {
				o.func = e
			},
			setDetailPage: function(e) {
				o.detailPage = e
			},
			setVip: function(e) {
				o.vip = e || "novip"
			},
			setProfession: function(e) {
				o.profession = e || "occmiss"
			},
			setIsPreview: function(e) {
				o.isPreview = e
			},
			setBtnPos: function(e) {
				o.btnPos = e
			},
			setBtnPage: function(e) {
				o.btnPage = e
			},
			setAlg: function(e) {
				o.alg = e
			},
			getMemberCsource: function() {
				return r([o.appName, o.tag, o.btnPage, o.btnPos])
			},
			getMemberPosition: function() {
				return r([o.tag, o.func, o.profession + (o.alg ? "-" + o.alg : ""), o.testVer + "|", o.entrance, o.detailPage, o.vip])
			},
			getPosition: function() {
				return r([o.entrance, o.tag, o.vip, o.profession, o.testVer + (o.alg ? "-" + o.alg : "")])
			},
			getSubChannel: function() {
				return r([o.appName, o.tag, o.func, o.detailPage])
			},
			getChannel: function() {
				return r([o.entrance, o.tag, o.vip, o.profession, o.testVer + (o.alg ? "-" + o.alg : "")])
			}
		}
	},
	LB6H: function(e, t, n) {
		"use strict";

		function o(e, t) {
			var n = (65535 & e) + (65535 & t);
			return (e >> 16) + (t >> 16) + (n >> 16) << 16 | 65535 & n
		}
		function r(e, t, n, r, a, i) {
			return o((s = o(o(t, e), o(r, i))) << (c = a) | s >>> 32 - c, n);
			var s, c
		}
		function a(e, t, n, o, a, i, s) {
			return r(t & n | ~t & o, e, t, a, i, s)
		}
		function i(e, t, n, o, a, i, s) {
			return r(t & o | n & ~o, e, t, a, i, s)
		}
		function s(e, t, n, o, a, i, s) {
			return r(t ^ n ^ o, e, t, a, i, s)
		}
		function c(e, t, n, o, a, i, s) {
			return r(n ^ (t | ~o), e, t, a, i, s)
		}
		function u(e, t) {
			var n, r, u, d, l;
			e[t >> 5] |= 128 << t % 32, e[14 + (t + 64 >>> 9 << 4)] = t;
			var p = 1732584193,
				f = -271733879,
				m = -1732584194,
				h = 271733878;
			for (n = 0; n < e.length; n += 16) r = p, u = f, d = m, l = h, f = c(f = c(f = c(f = c(f = s(f = s(f = s(f = s(f = i(f = i(f = i(f = i(f = a(f = a(f = a(f = a(f, m = a(m, h = a(h, p = a(p, f, m, h, e[n], 7, -680876936), f, m, e[n + 1], 12, -389564586), p, f, e[n + 2], 17, 606105819), h, p, e[n + 3], 22, -1044525330), m = a(m, h = a(h, p = a(p, f, m, h, e[n + 4], 7, -176418897), f, m, e[n + 5], 12, 1200080426), p, f, e[n + 6], 17, -1473231341), h, p, e[n + 7], 22, -45705983), m = a(m, h = a(h, p = a(p, f, m, h, e[n + 8], 7, 1770035416), f, m, e[n + 9], 12, -1958414417), p, f, e[n + 10], 17, -42063), h, p, e[n + 11], 22, -1990404162), m = a(m, h = a(h, p = a(p, f, m, h, e[n + 12], 7, 1804603682), f, m, e[n + 13], 12, -40341101), p, f, e[n + 14], 17, -1502002290), h, p, e[n + 15], 22, 1236535329), m = i(m, h = i(h, p = i(p, f, m, h, e[n + 1], 5, -165796510), f, m, e[n + 6], 9, -1069501632), p, f, e[n + 11], 14, 643717713), h, p, e[n], 20, -373897302), m = i(m, h = i(h, p = i(p, f, m, h, e[n + 5], 5, -701558691), f, m, e[n + 10], 9, 38016083), p, f, e[n + 15], 14, -660478335), h, p, e[n + 4], 20, -405537848), m = i(m, h = i(h, p = i(p, f, m, h, e[n + 9], 5, 568446438), f, m, e[n + 14], 9, -1019803690), p, f, e[n + 3], 14, -187363961), h, p, e[n + 8], 20, 1163531501), m = i(m, h = i(h, p = i(p, f, m, h, e[n + 13], 5, -1444681467), f, m, e[n + 2], 9, -51403784), p, f, e[n + 7], 14, 1735328473), h, p, e[n + 12], 20, -1926607734), m = s(m, h = s(h, p = s(p, f, m, h, e[n + 5], 4, -378558), f, m, e[n + 8], 11, -2022574463), p, f, e[n + 11], 16, 1839030562), h, p, e[n + 14], 23, -35309556), m = s(m, h = s(h, p = s(p, f, m, h, e[n + 1], 4, -1530992060), f, m, e[n + 4], 11, 1272893353), p, f, e[n + 7], 16, -155497632), h, p, e[n + 10], 23, -1094730640), m = s(m, h = s(h, p = s(p, f, m, h, e[n + 13], 4, 681279174), f, m, e[n], 11, -358537222), p, f, e[n + 3], 16, -722521979), h, p, e[n + 6], 23, 76029189), m = s(m, h = s(h, p = s(p, f, m, h, e[n + 9], 4, -640364487), f, m, e[n + 12], 11, -421815835), p, f, e[n + 15], 16, 530742520), h, p, e[n + 2], 23, -995338651), m = c(m, h = c(h, p = c(p, f, m, h, e[n], 6, -198630844), f, m, e[n + 7], 10, 1126891415), p, f, e[n + 14], 15, -1416354905), h, p, e[n + 5], 21, -57434055), m = c(m, h = c(h, p = c(p, f, m, h, e[n + 12], 6, 1700485571), f, m, e[n + 3], 10, -1894986606), p, f, e[n + 10], 15, -1051523), h, p, e[n + 1], 21, -2054922799), m = c(m, h = c(h, p = c(p, f, m, h, e[n + 8], 6, 1873313359), f, m, e[n + 15], 10, -30611744), p, f, e[n + 6], 15, -1560198380), h, p, e[n + 13], 21, 1309151649), m = c(m, h = c(h, p = c(p, f, m, h, e[n + 4], 6, -145523070), f, m, e[n + 11], 10, -1120210379), p, f, e[n + 2], 15, 718787259), h, p, e[n + 9], 21, -343485551), p = o(p, r), f = o(f, u), m = o(m, d), h = o(h, l);
			return [p, f, m, h]
		}
		function d(e) {
			var t, n = "",
				o = 32 * e.length;
			for (t = 0; t < o; t += 8) n += String.fromCharCode(e[t >> 5] >>> t % 32 & 255);
			return n
		}
		function l(e) {
			var t, n = [];
			for (n[(e.length >> 2) - 1] = void 0, t = 0; t < n.length; t += 1) n[t] = 0;
			var o = 8 * e.length;
			for (t = 0; t < o; t += 8) n[t >> 5] |= (255 & e.charCodeAt(t / 8)) << t % 32;
			return n
		}
		function p(e) {
			var t, n, o = "";
			for (n = 0; n < e.length; n += 1) t = e.charCodeAt(n), o += "0123456789abcdef".charAt(t >>> 4 & 15) + "0123456789abcdef".charAt(15 & t);
			return o
		}
		function f(e) {
			return unescape(encodeURIComponent(e))
		}
		function m(e) {
			return function(e) {
				return d(u(l(e), 8 * e.length))
			}(f(e))
		}
		function h(e, t) {
			return function(e, t) {
				var n, o, r = l(e),
					a = [],
					i = [];
				for (a[15] = i[15] = void 0, r.length > 16 && (r = u(r, 8 * e.length)), n = 0; n < 16; n += 1) a[n] = 909522486 ^ r[n], i[n] = 1549556828 ^ r[n];
				return o = u(a.concat(l(t)), 512 + 8 * t.length), d(u(i.concat(o), 640))
			}(f(e), f(t))
		}
		t.a = function(e, t, n) {
			return t ? n ? h(t, e) : p(h(t, e)) : n ? m(e) : p(m(e))
		}
	},
	NHnr: function(e, t, n) {
		"use strict";
		Object.defineProperty(t, "__esModule", {
			value: !0
		});
		var o = n("mvHQ"),
			r = n.n(o),
			a = n("7+uW"),
			i = n("/ocq"),
			s = n("59SO");
		a.a.use(i.a);
		var c = new i.a({
			routes: [{
				path: "/",
				redirect: "/home"
			}, {
				path: "/home",
				name: "home",
				component: function() {
					return Promise.all([n.e(0), n.e(3)]).then(n.bind(null, "KR8f"))
				}
			}, {
				path: "/mytpl",
				name: "mytpl",
				component: function() {
					return Promise.all([n.e(0), n.e(1)]).then(n.bind(null, "7v5y"))
				}
			}, {
				path: "/search",
				name: "search",
				component: function() {
					return Promise.all([n.e(0), n.e(5)]).then(n.bind(null, "Rv23"))
				}
			}, {
				path: "/category",
				name: "category",
				component: function() {
					return Promise.all([n.e(0), n.e(7)]).then(n.bind(null, "RSVL"))
				}
			}, {
				path: "/recommend",
				name: "recommend",
				component: function() {
					return Promise.all([n.e(0), n.e(6)]).then(n.bind(null, "1dey"))
				}
			}]
		});
		c.beforeEach(function(e, t, n) {
			s.a.sendDownloadParam(null), n()
		});
		var u, d, l, p, f = c,
			m = n("NYxO"),
			h = n("bOdI"),
			v = n.n(h),
			w = n("wYMm"),
			g = n("s0MJ"),
			E = {
				nickname: "",
				isMember: !1,
				isDocerMember: !1,
				docerExpireTime: 0,
				isWpsMember: !1,
				wpsExpireTime: 0,
				isSuperMember: !1,
				superExpireTime: 0,
				isBronzeMember: !1,
				bronzeExpireTime: 0,
				isBusinessMember: !1,
				privilegeAdvFlag: !1,
				wealth: 0,
				pic: "",
				level: 0,
				isExpired: !1,
				expireTime: 0,
				serverTime: 0,
				userId: 0,
				group: []
			},
			_ = (u = {}, v()(u, "USER_INFO", function(e, t) {
				e.userInfo = g.a.extend({}, E, t)
			}), v()(u, "USER_LOGIN", function(e, t) {
				e.isLogin = t || !1
			}), v()(u, "USER_PROFESSION", function(e, t) {
				e.profession = t || "general"
			}), v()(u, "USER_WPS_SID", function(e, t) {
				e.wpsSid = t
			}), v()(u, "USER_GROUP", function(e, t) {
				e.userGroup = t || [], w.f.userGroup = t ? t.join("#") : ""
			}), u),
			y = n("//Fk"),
			A = n.n(y),
			b = n("n1nN"),
			D = n("v8ob"),
			S = n("CXu2"),
			T = n("Dzwp"),
			C = setTimeout("1"),
			P = setTimeout("1"),
			I = ["svip", "dvip", "wvip", "wsvip", "wdvip", "wwvip"],
			R = w.d.userExpireInfo,
			O = function(e) {
				w.f.profession = e, T.a.setProfession(e), D.a.$emit(w.h.finishProfession)
			},
			N = setTimeout("1"),
			L = {
				namespaced: !0,
				state: {
					userInfo: {
						nickname: "",
						isMember: !1,
						isDocerMember: !1,
						docerExpireTime: 0,
						isWpsMember: !1,
						wpsExpireTime: 0,
						isSuperMember: !1,
						superExpireTime: 0,
						isBronzeMember: !1,
						bronzeExpireTime: 0,
						isSuperExpired: !1,
						isDocerExpired: !1,
						isWpsExpired: !1,
						isBusinessMember: !1,
						privilegeAdvFlag: !1,
						wealth: 0,
						pic: "",
						level: 0,
						isExpired: !1,
						expireTime: 0,
						serverTime: 0,
						userId: 0
					},
					isLogin: !1,
					wpsSid: "",
					profession: "general",
					userGroup: []
				},
				mutations: _,
				actions: {
					setUserState: function(e, t) {
						(0, e.commit)("USER_LOGIN", t)
					},
					setUserInfo: function(e, t) {
						var n = e.commit,
							o = e.dispatch;
						t ? (n("USER_LOGIN", !1), n("USER_INFO", t), w.f.userid = "", w.f.identity = "", w.f.memberid = "", o("setExpireMemberInfo")) : b.a.get("user").allinfo({
							serialNumber: N
						}).then(function(e) {
							if (e && "ok" == e.result && e.data) {
								n("USER_LOGIN", !0);
								var t = {};
								if (e.data.vip) {
									t.isMember = !! (e.data.vip.memberid && e.data.vip.memberid > 10);
									var r = e.data.vip.enabled,
										a = {},
										i = [];
									for (var s in r && Array.isArray(r) && r.forEach(function(e) {
										12 == e.memberid && (t.expireTime = 1e3 * e.expire_time), a[e.memberid] = 1e3 * e.expire_time
									}), a[e.data.vip.memberid] = 1e3 * e.data.vip.expire_time, t.isDocerMember = !! a[12], t.docerExpireTime = a[12] || 0, t.isWpsMember = !! a[20], t.wpsExpireTime = a[20] || 0, t.isSuperMember = !! a[40], t.superExpireTime = a[40] || 0, t.isBronzeMember = !! a[14], t.bronzeExpireTime = a[14] || 0, t.isBusinessMember = e.data.companyid > 0, t.userId = e.data.userid, a) i.push(s);
									w.f.memberid = i.join("#")
								} else w.f.memberid = "";
								if (e.data.privilege && e.data.privilege.length > 0) for (var c = e.data.privilege, u = e.data.server_time || 0, d = 0, l = c.length; d < l; d++)"ads_free" == c[d].spid && c[d].expire_time - u > 0 && (t.privilegeAdvFlag = !0);
								t.isExpired = !(!e.data.monthcard || !e.data.monthcard.is_expired), t.nickname = e.data.nickname || "", t.wealth = e.data.wealth || 0, t.pic = e.data.pic || "", t.level = e.data.level || 0, t.serverTime = 1e3 * (e.data.server_time || 0), w.f.userid = e.data.userid, n("USER_INFO", t), D.a.$emit(w.h.login, e.data)
							} else w.f.userid = "", w.f.memberid = "", n("USER_LOGIN", !1), n("USER_INFO", {}), D.a.$emit(w.h.logout);
							o("setExpireMemberInfo")
						}).
						catch (function() {
							w.f.userid = "", w.f.memberid = "", n("USER_LOGIN", !1), n("USER_INFO", {}), D.a.$emit(w.h.logout), o("setExpireMemberInfo")
						})
					},
					setUserProfession: function(e) {
						var t = e.commit,
							n = (new Date).toDateString() + "v4",
							o = (new Date).getTime(),
							r = S.a.getLocalCache(w.d.userGroup),
							a = S.a.getLocalCache(w.d.userProfessionInfo);
						if (r = r && r.length ? r : null, S.a.getLocalCache(w.d.userGroupDate) <= o && (r = null), S.a.getLocalCache(w.d.lastCacheDate) === n && a) return t("USER_PROFESSION", a.tag || "general"), t("USER_GROUP", r || a.group || []), void O(a.tag || "occmiss");
						b.a.get("user").profession({
							serialNumber: P,
							data: {
								mb_app: w.c.mark,
								uuid: w.c.uuid,
								hdid: w.c.hdid,
								guid: w.c.guid,
								cv: "tags",
								pro_version: w.c.versionInfo
							}
						}).then(function(e) {
							if (e && "ok" == e.result && e.data) {
								var o = e.data || {},
									a = o.tag;
								t("USER_PROFESSION", a || "general"), t("USER_GROUP", r || o.group || []), O(a || "occmiss"), S.a.setLocalCache(w.d.lastCacheDate, n), S.a.setLocalCache(w.d.userProfessionInfo, o)
							} else O("occmiss")
						}).
						catch (function() {
							O("occmiss")
						})
					},
					setWpsSid: function(e, t) {
						(0, e.commit)("USER_WPS_SID", t)
					},
					setExpireMemberInfo: function(e) {
						var t, n, o, r = e.state;
						r.isLogin ? (t = r.userInfo, n = t && t.userId ? t.userId : 0, o = g.a.dateFormat(new Date, "yyyy-MM-dd"), new A.a(function(e) {
							if (!n) return e();
							var t = S.a.getLocalCache(R),
								r = n.toString();
							if (t && t[r] && t.currDay === o) return e(t[r]);
							var a = t || {
								currDay: o
							};
							b.a.get("user").getLastBuyVipTime({
								serialNumber: C
							}).then(function(t) {
								var n = {};
								t && "ok" === t.result && t.data && (n = t.data), a[r] = n, a.currDay = o, S.a.setLocalCache(R, a), e(n)
							}).
							catch (function() {
								e()
							})
						})).then(function(e) {
							var t = w.f.memberid && w.f.memberid.split("#") || [];
							if (e) {
								var n = r.userInfo;
								n.isSuperExpired = !n.isSuperMember && e.supervip > 0 && !! t.push(140), n.isDocerExpired = !n.isDocerMember && e.docervip > 0 && !! t.push(112), n.isWpsExpired = !n.isWpsMember && e.wpsvip > 0 && !! t.push(120)
							}
							w.f.memberid = t.join("#");
							var o = r.userInfo,
								a = [o.isSuperMember, o.isDocerMember, o.isWpsMember, o.isSuperExpired, o.isDocerExpired, o.isWpsExpired].findIndex(function(e) {
									return e
								});
							T.a.setVip(I[a] || "novip"), D.a.$emit(w.h.finishUserInfo)
						}).
						catch (function() {
							T.a.setVip("novip"), D.a.$emit(w.h.finishUserInfo)
						}) : (T.a.setVip("novip"), D.a.$emit(w.h.finishUserInfo))
					},
					setUserGroup: function(e, t) {
						(0, e.commit)("USER_GROUP", t || null), S.a.setLocalCache(w.d.userGroupDate, (new Date).getTime() + 6048e5), S.a.setLocalCache(w.d.userGroup, t)
					}
				},
				getters: {}
			},
			U = {
				state: {
					hot: [],
					filter: {}
				},
				mutations: (d = {}, v()(d, "SEARCH_HOT", function(e, t) {
					e.hot = t || []
				}), v()(d, "SEARCH_FILTER", function(e, t) {
					e.filter = t || {}
				}), d),
				actions: {
					setSearchData: function(e, t) {
						(0, e.commit)("SEARCH_HOT", t = t || {})
					},
					setSearchFilter: function(e) {
						var t = e.commit,
							n = e.state;
						n.filter.color && n.filter.scene && n.filter.style || b.a.get("rec").filters({
							data: {
								format: "jsonp"
							}
						}).then(function(e) {
							"2000" == e.code && e.data && t("SEARCH_FILTER", e.data)
						}).
						catch (function() {
							t("SEARCH_FILTER", {})
						})
					}
				},
				getters: {
					getSearchHot: function(e) {
						return e.hot
					},
					getSearchFilter: function(e) {
						return e.filter
					}
				}
			},
			k = {
				downloadProgress: 0,
				downloadStatus: w.a.IN_PROGRESS,
				downloadShow: !1,
				downloadCurrentId: 0,
				downloadData: {}
			},
			M = n("w7XY"),
			x = 0,
			F = {
				namespaced: !0,
				state: k,
				mutations: (l = {}, v()(l, "DOWNLOAD_SET_PROGRESS", function(e, t) {
					e.downloadProgress = +t || 0
				}), v()(l, "DOWNLOAD_SET_STATUS", function(e, t) {
					e.downloadStatus = +t
				}), v()(l, "DOWNLOAD_START", function(e, t) {
					e.downloadShow = !0, t && M.a.startDownload(t).then(function(t) {
						e.downloadCurrentId = t
					})
				}), v()(l, "DOWNLOAD_CANCEL", function(e, t) {
					e.downloadCurrentId && !t && M.a.cancelDownLoad(e.downloadCurrentId), e.downloadProgress = 0, e.downloadStatus = w.a.IN_PROGRESS, e.downloadShow = !1
				}), v()(l, "DOWNLOAD_FAILED", function(e) {
					e.downloadStatus = w.a.ERROR
				}), v()(l, "DOWNLOAD_DATA", function(e, t) {
					e.downloadData = t || {}
				}), l),
				actions: {
					setProgressBarHide: function(e) {
						(0, e.dispatch)("setDownloadHide", !1)
					},
					setDownloadHide: function(e, t) {
						(0, e.commit)("DOWNLOAD_CANCEL", t)
					},
					setDownloadProgress: function(e, t) {
						(0, e.commit)("DOWNLOAD_SET_PROGRESS", t)
					},
					setDownloadStatus: function(e, t) {
						(0, e.commit)("DOWNLOAD_SET_STATUS", t)
					},
					setDownload: function(e, t) {
						var n = e.commit;
						e.dispatch;
						return n("DOWNLOAD_START"), s.a.sendDevDownload("start"), s.a.sendDownload({
							p10: "start"
						}), b.a.get("collection").download({
							data: t
						}).then(function(e) {
							if ("ok" !== e.result || !e.data) {
								if (n("DOWNLOAD_SET_STATUS", w.a.ERROR), "今天的免费次数已用完" === e.msg && x < 3) return x += 1, new A.a(function(e, t) {
									t({
										type: 1
									})
								});
								if ("今天的免费次数已用完" === e.msg && x >= 3) return void(x = 0)
							}
							x = 0;
							var o = e.data.mb_url,
								r = e.data.md5;
							if (o) {
								var a = [{
									url: o,
									md5: r,
									id: t.id,
									name: t.name,
									class: t.class
								}];
								n("DOWNLOAD_START", a)
							} else s.a.sendDevDownload("url_empty")
						}).
						catch (function(e) {
							if (1 === e.type) s.a.sendDownload({
								p10: "times_cancel",
								p11: 0
							}), n("DOWNLOAD_CANCEL");
							else {
								var t = "err";
								try {
									t = r()(e)
								} catch (e) {}
								s.a.sendDevDownload(t), s.a.sendDownload({
									p10: t,
									p11: 0
								}), n("DOWNLOAD_SET_STATUS", w.a.ERROR)
							}
							return new A.a(function(t, n) {
								n(e)
							})
						})
					},
					setDownloadData: function(e, t) {
						(0, e.commit)("DOWNLOAD_DATA", t)
					},
					setDownloadFailed: function(e, t) {
						(0, e.commit)("DOWNLOAD_FAILED", t)
					}
				},
				getters: {}
			},
			W = (p = {}, v()(p, "FAV_ID", function(e) {
				var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
					n = t.tid,
					o = t.isCollect;
				n && (void 0 !== e.myFavIds[n] ? e.myFavIds[n] = o : e.myFavIds = g.a.extend({}, e.myFavIds, v()({}, n, o)))
			}), v()(p, "FAV_ALLIDS", function(e) {
				var t = {};
				(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : []).forEach(function(e) {
					t[e] = !0
				}), e.myFavIds = t
			}), v()(p, "FAV_MAP", function(e, t) {
				e.myFavMap = t
			}), v()(p, "FAV_MAP_NUM", function(e, t) {
				if (void 0 != e.myFavMap[t.id]) {
					var n = e.myFavMap[t.id] + (t.sign > 0 ? 1 : -1);
					e.myFavMap[t.id] = isNaN(n) || n < 0 ? 0 : n
				}
			}), p),
			j = n("gRE1"),
			G = n.n(j);
		D.a.$on(w.h.collectChange, function(e) {
			window.rootApp.$store.dispatch("fav/setFavId", {
				tid: e.tid,
				isCollect: e.isCollect
			})
		});
		var H, B, V, $ = {
			namespaced: !0,
			state: {
				myFavIds: {},
				myFavMap: {}
			},
			mutations: W,
			actions: {
				setFavId: function(e, t) {
					var n = e.commit;
					t && t.tid && n("FAV_ID", {
						tid: t.tid,
						isCollect: t.isCollect
					})
				},
				setFavAdd: function(e) {
					var t = e.commit,
						n = e.dispatch,
						o = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
					return b.a.get("user").addFav({
						url: w.j.docerPrevUrl + g.a.convertAppMark(o.moban_app) + "/ajax/add_fav",
						data: {
							doc_format: 3 == o.file_type ? 1 : 0,
							doc_type: o.moban_type,
							id: o.id,
							moban_id: o.id,
							name: o.name.substring(0, o.name.length - 4)
						}
					}).then(function(e) {
						return e && ("ok" == e.result ? (t("FAV_ID", {
							tid: o.id,
							isCollect: 1
						}), n("setFavMapChange", {
							id: o.id,
							sign: 1
						})) : "error" == e.result && "one" == e.msg ? (t("FAV_ID", {
							tid: o.id,
							isCollect: 1
						}), n("setFavMapChange", {
							id: o.id,
							sign: 1
						})) : "error" == e.result && "login" == e.msg && M.a.login()), e
					})
				},
				setFavRemove: function(e) {
					var t = e.commit,
						n = e.dispatch,
						o = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
					return b.a.get("user").delFav({
						data: {
							mb_ids: o.id
						}
					}).then(function(e) {
						return e && "ok" == e.result ? (t("FAV_ID", {
							tid: o.id,
							isCollect: 0
						}), n("setFavMapChange", {
							id: o.id,
							sign: -1
						})) : e && "error" == e.result && "login" == e.msg && M.a.login(), e
					})
				},
				setFavAllIds: function(e) {
					var t = e.commit;
					b.a.get("user").queryMyCollectID({
						data: {
							mb_app: "all"
						}
					}).then(function(e) {
						if (e && "ok" === e.result && e.data) {
							var n = [];
							G()(e.data).forEach(function(e) {
								n = n.concat(e.ids)
							}), t("FAV_ALLIDS", n || [])
						} else t("FAV_ALLIDS", [])
					}, function() {
						t("FAV_ALLIDS", [])
					})
				},
				setFavMap: function(e, t) {
					(0, e.commit)("FAV_MAP", t || {})
				},
				setFavMapChange: function(e, t, n) {
					var o = e.commit;
					t && o("FAV_MAP_NUM", t, n)
				},
				changeFav: function(e, t) {
					e.commit;
					D.a.$emit(w.h.collectChange, t)
				}
			},
			getters: {}
		},
			Q = {
				actions: {
					proceedPayment: function(e, t) {
						e.commit;
						var n = g.a.jsonToQuery({
							mbid: t.id,
							from: "beauty",
							app: t.appClass,
							client_type: "newdoc",
							dist: w.c.dist,
							version: w.c.version,
							appName: "newfile",
							channel: w.c.dist,
							component: w.c.name
						});
						M.a.navigateOnNewWidget("" + w.k.mbUrl + n)
					},
					upgradeVIP: function(e, t) {
						e.commit;
						M.a.openPdfUnifyPayDlg(t.csource, t.payconfig)
					},
					addIdtoPurchasesList: function(e, t) {
						var n = e.commit;
						t && n("PURCHASE_ADD_ID", t)
					}
				},
				state: {
					purchasedIds: []
				},
				getters: {
					hasBought: function(e) {
						return e.purchasedIds
					}
				},
				mutations: v()({}, "PURCHASE_ADD_ID", function(e, t) {
					e.purchasedIds.push(t)
				})
			},
			Y = {
				mutations: v()({}, "SET_FULL_DATA", function(e, t) {
					e.fullData = t
				}),
				state: {
					fullData: {}
				},
				actions: {
					setAllData: function(e, t) {
						(0, e.commit)("SET_FULL_DATA", t)
					},
					setMsgBoxShow: function(e, t) {
						var o = e.dispatch;
						t && t.data && t.content ? (o("common/setAppComponent", function() {
							return n.e(8).then(n.bind(null, "79uZ"))
						}, {
							root: !0
						}), o("setAllData", t)) : o("setAllData", {})
					}
				},
				getters: {
					msgContent: function(e) {
						return e.fullData.content || ""
					},
					postData: function(e) {
						return e.fullData.data || {}
					},
					eventType: function(e) {
						return e.fullData.eventType || ""
					}
				}
			},
			q = (H = {}, v()(H, "REC_LIKE_DATA", function(e, t) {
				e.likeData = t
			}), v()(H, "REC_MAIN_ALG", function(e, t) {
				e.mainAlg = t || ""
			}), v()(H, "REC_CATEGORY_ALG", function(e, t) {
				e.categoryAlg = t || ""
			}), v()(H, "REC_AD_SEARCH_BTN", function(e, t) {
				e.adSearchBtn = t || {}
			}), v()(H, "REC_AD_LEFTBTN", function(e, t) {
				e.adLeftBtn = t || {}
			}), v()(H, "REC_AD_LEFTNAV_PIC", function(e, t) {
				e.adLeftNavPic = t || {}
			}), v()(H, "REC_AD_PRIVILEGE_ICON", function(e, t) {
				e.adPrivilegeIcon = t || {}
			}), v()(H, "REC_AD_FIRSTSCREEN_READY", function(e, t) {
				e.adFirstscreenIsReady = t || !1
			}), H),
			K = [{
				flag: w.b.NEWFILE2019_SEARCH_BTN,
				type: "REC_AD_SEARCH_BTN"
			}, {
				flag: w.b.NEWFILE2019_LEFTBTN,
				type: "REC_AD_LEFTBTN"
			}, {
				flag: w.b.NEWFILE2019_LEFTNAV_PIC,
				type: "REC_AD_LEFTNAV_PIC"
			}, {
				flag: w.b.NEWFILE2019_PRIVILEGE_ICON,
				type: "REC_AD_PRIVILEGE_ICON"
			}],
			z = {
				namespaced: !0,
				state: {
					likeData: [],
					mainAlg: "",
					categoryAlg: "",
					adSearchBtn: {},
					adLeftBtn: {},
					adPrivilegeIcon: {},
					adLeftNavPic: {},
					adFirstscreenIsReady: !1
				},
				mutations: q,
				actions: {
					setRecLikeData: function(e, t) {
						(0, e.commit)("REC_LIKE_DATA", t)
					},
					setRecMainAlg: function(e, t) {
						(0, e.commit)("REC_MAIN_ALG", t)
					},
					setRecCategoryAlg: function(e, t) {
						(0, e.commit)("REC_CATEGORY_ALG", t)
					},
					setRecFirstScreenAds: function(e, t) {
						var n = e.commit,
							o = e.dispatch;
						t && K.forEach(function(e) {
							n(e.type, g.a.fillAdData(t[e.flag]))
						}), o("setRecFirstScreenAdsIs", !0)
					},
					setRecFirstScreenAdsIs: function(e, t) {
						(0, e.commit)("REC_AD_FIRSTSCREEN_READY", t)
					}
				},
				getters: {}
			},
			J = {
				namespaced: !0,
				state: {
					appComponent: "",
					appComponentShow: "",
					appName: ""
				},
				mutations: (B = {}, v()(B, "APP_COMPONENT", function(e, t) {
					e.appComponent = t
				}), v()(B, "APP_COMPONENT_SHOW", function(e, t) {
					e.appComponentShow = t
				}), v()(B, "APP_NAME", function(e, t) {
					e.appName = t || "wps"
				}), B),
				actions: {
					setAppComponent: function(e, t) {
						var n = e.dispatch;
						(0, e.commit)("APP_COMPONENT", t), n("setAppComponentShow", !0)
					},
					setAppComponentShow: function(e, t) {
						(0, e.commit)("APP_COMPONENT_SHOW", t)
					},
					setAppName: function(e, t) {
						(0, e.commit)("APP_NAME", t)
					}
				},
				getters: {}
			},
			X = {
				namespaced: !0,
				state: {
					appPreviewData: {},
					appPreviewIndex: 0,
					appPreviewCollect: {},
					appPreviewChangeApp: !1,
					appPreviewAd: {
						1: {},
						2: {},
						3: {}
					}
				},
				mutations: (V = {}, v()(V, "APP_PREVIEW_DATA", function(e, t) {
					e.appPreviewData = t || {}
				}), v()(V, "APP_PREVIEW_INDEX", function(e, t) {
					e.appPreviewIndex = t || {}
				}), v()(V, "APP_PREVIEW_COLLECT", function(e, t) {
					e.appPreviewCollect = t || {}
				}), v()(V, "APP_PREVIEW_CHANGEAPP", function(e, t) {
					e.appPreviewChangeApp = t || !1
				}), v()(V, "APP_PREVIEW_AD", function(e, t) {
					e.appPreviewAd[t.type] = t.data || {}
				}), V),
				actions: {
					setAppPreview: function(e, t) {
						e.commit;
						var o = e.dispatch,
							r = t.data,
							a = t.index,
							i = t.collect,
							s = t.changeApp;
						o("common/setAppComponent", function() {
							return Promise.all([n.e(0), n.e(4)]).then(n.bind(null, "pjYA"))
						}, {
							root: !0
						}), o("setAppPreviewData", r), o("setAppPreviewIndex", a || 0), o("setAppPreviewCollect", i), o("setAppPreviewChangeApp", s)
					},
					setAppPreviewData: function(e, t) {
						(0, e.commit)("APP_PREVIEW_DATA", t)
					},
					setAppPreviewIndex: function(e, t) {
						(0, e.commit)("APP_PREVIEW_INDEX", t)
					},
					setAppPreviewCollect: function(e, t) {
						(0, e.commit)("APP_PREVIEW_COLLECT", t)
					},
					setAppPreviewChangeApp: function(e, t) {
						(0, e.commit)("APP_PREVIEW_CHANGEAPP", t)
					},
					setAppPreviewAd: function(e, t) {
						(0, e.commit)("APP_PREVIEW_AD", t || {})
					}
				},
				getters: {}
			};
		a.a.use(m.a);
		var Z = new m.a.Store({
			modules: {
				common: J,
				preview: X,
				user: L,
				search: U,
				download: F,
				payment: Q,
				msgBox: Y,
				fav: $,
				recommend: z
			}
		}),
			ee = n("cTzj"),
			te = n.n(ee);
		a.a.filter("toSubStr", function(e) {
			if (!e) return "";
			var t = e.lastIndexOf(".");
			return -1 === t ? e : e.slice(0, t)
		}), a.a.filter("trillion", function(e) {
			return e ? (parseInt(e / 1e4, 10) / 100 || .01) + "MB" : ""
		}), a.a.filter("dateformate", function(e) {
			return e ? g.a.dateFormat(1e3 * e, "yyyy/MM/dd hh:mm") : ""
		}), a.a.filter("toUnitW", function(e) {
			return isNaN(e) ? 0 : e >= 1e3 ? (e / 1e4).toFixed(2) + "w" : e
		});
		var ne = {};
		M.a.setWindowCallBack("notifyJs", function(e) {
			var t = e;
			if (Array.isArray(e)) {
				if (!(t = e[0])) return;
				t = g.a.b64EncodeUnicode(t)
			}
			var n = t,
				o = n.eventName,
				r = n.eventArgs,
				a = ne[o];
			if (a) for (var i in a) a[i](r)
		});
		var oe = function(e, t) {
				var n = ne[e];
				n || (n = ne[e] = []), n.push(t)
			};
		document.body.addEventListener("click", function() {
			D.a && D.a.$emit(w.h.clickDocument)
		}, !1), window.onresize = function() {
			D.a.$emit(w.h.resizeWindow)
		}, window.onbeforeunload = function() {
			D.a.$emit(w.h.unloadWindow)
		}, oe(w.h.channelVipStateChange, function() {
			D.a.$emit(w.h.channelVipStateChange)
		}), oe(w.h.boardcastPurchaseStatus, function(e) {
			D.a.$emit(w.h.boardcastPurchaseStatus, e)
		});
		var re = {
			INPUT: !0,
			TEXTAREA: !0
		};
		document.oncontextmenu = function(e) {
			(e || event).returnValue = !1
		}, document.onselectstart = function(e) {
			var t = e || event,
				n = t.srcElement;
			if (t) {
				if (re[n.tagName]) return !0;
				if (n.parentElement.webkitMatchesSelector("[selectable]")) return !0
			}
			return !1
		}, document.ondragstart = function(e) {
			var t = e || event,
				n = t.srcElement;
			return !(!t || !re[n.tagName])
		}, document.onkeydown = function(e) {
			if (116 === e.keyCode) return !1;
			if (9 === e.keyCode || 8 === e.keyCode) {
				var t = document.activeElement.tagName;
				if ("INPUT" !== t && "TEXTAREA" !== t) return !1
			}
			if (27 === e.keyCode && Array.isArray(window._ESC_HOOKS)) {
				var n = window._ESC_HOOKS.length;
				if (n) {
					var o = window._ESC_HOOKS[n - 1];
					return o.method.call(o.context), window._ESC_HOOKS.pop()
				}
			}
		}, window.alert = function() {}, window._ESC_HOOKS = [];
		var ae = document.createElement("div");
		ae.className = "m-stick", ae.innerHTML = '<div class="m-stick_btn m-stick_top" delegate="scrollToTop"><svg aria-hidden="true" class="svg-icon" delegate="scrollToTop"><use xlink:href="#svg-stick-top-18"></use></svg></div><div class="m-stick_btn m-stick_feedback" delegate="feedback"><svg aria-hidden="true" class="svg-icon" delegate="feedback"><use xlink:href="#svg-stick-feedback-18"></use></svg></div>';
		var ie = 0,
			se = {
				scrollToTop: function(e) {
					g.a.scrollTo(e, 0)
				},
				feedback: function() {
					var e = "";
					try {
						e = window.rootApp.$store && window.rootApp.$store.state.user && window.rootApp.$store.state.user.wpsSid || ""
					} catch (e) {}
					var t = g.a.jsonToQuery({
						wpsid: e,
						product_name: "2019新建反馈",
						app_type: "client",
						app_name: w.c.name,
						app_dist: w.c.dist,
						app_version: w.c.version
					});
					window.open("https://qa.wps.cn/feedback/front?" + t);
				}
			},
			ce = function(e) {
				var t = e.target.getAttribute("delegate"),
					n = se[t];
				"function" == typeof n && n(e.currentTarget)
			},
			ue = {
				install: function(e) {
					e.directive("stick", {
						inserted: function(t) {
							e.nextTick(function() {
								var e = ae.cloneNode(!0);
								t._scrollHandler = function(e) {
									var t = this,
										n = e.currentTarget;
									if (n) {
										var o = n.scrollTop;
										if (D.a.$emit(w.h.scrollPanel, o), window.clearTimeout(ie), o > 50) return this.style.visibility = "visible", void(this.style.opacity = 1);
										o < 30 && (this.style.opacity = 0, ie = window.setTimeout(function() {
											t.style.visibility = "hidden"
										}, 400))
									}
								}.bind(e.childNodes[0]), t.appendChild(e), t.addEventListener("scroll", t._scrollHandler), t.addEventListener("click", ce)
							})
						},
						unbind: function(e) {
							e.removeEventListener("scroll", e._scrollHandler), e._scrollHandler = null, e.removeEventListener("click", ce)
						}
					})
				}
			},
			de = n("Dd8w"),
			le = n.n(de),
			pe = n("xK0O"),
			fe = {
				name: "download",
				props: {
					titleName: {
						type: String,
					default:


						function() {
							return "提示"
						}
					},
					errMsg: {
						type: String,
					default:


						function() {
							return "下载文件失败！"
						}
					},
					retryMsg: {
						type: String,
					default:


						function() {
							return "确定"
						}
					},
					isModal: {
						type: Boolean,
					default:
						!0
					}
				},
				mixins: [pe.a],
				data: function() {
					return {
						status: w.a.IN_PROGRESS,
						DOWNLOAD_STATUS: w.a
					}
				},
				mounted: function() {
					D.a.$on(w.h.boardcastPurchaseStatus, this.handleDownload)
				},
				computed: le()({}, Object(m.d)("common", ["appName"]), Object(m.d)("user", ["userInfo"]), Object(m.d)("download", ["downloadProgress", "downloadStatus", "downloadCurrentId", "downloadData"]), {
					progressMsg: function() {
						return "<span>" + g.a.formatString("正在下载，请稍后...", g.a.formatString('<span style="color:#41891a">{0}%</span>', this.downloadProgress)) + "</span>"
					},
					progressWidth: function() {
						return 354 * this.downloadProgress / 100 - 1
					}
				}),
				methods: le()({}, Object(m.b)("download", ["setProgressBarHide", "setDownloadData"]), {
					close: function(e) {
						"cancel" == e && (s.a.sendDownload({
							p11: 1
						}), s.a.sendDevDownload("cancel")), this.setProgressBarHide({
							id: this.downloadCurrentId
						})
					},
					handleDownload: function(e) {
						var t = this;
						if ("finish" == e.paystatus && e.mbid == this.downloadData.id) {
							var n = JSON.parse(r()(this.downloadData));
							this.$_download(n, n.moban_app, this.userInfo.isDocerMember || this.userInfo.isSuperMember).
							catch (function() {
								t.handleBuyMb()
							})
						}
					},
					handleBuyMb: function() {
						var e = JSON.parse(r()(this.downloadData));
						this.setDownloadData(e), window.gotoTag({
							type: "mb",
							id: e.id
						})
					}
				})
			},
			me = {
				render: function() {
					var e = this,
						t = e.$createElement,
						n = e._self._c || t;
					return n("div", {
						class: e.appName
					}, [e.isModal ? n("div", {
						staticClass: "mask-layer"
					}) : e._e(), e._v(" "), n("div", {
						staticClass: "download"
					}, [n("div", {
						staticClass: "download_header"
					}, [n("span", {
						staticClass: "download_header_title"
					}, [e._v(e._s(e.titleName))]), e._v(" "), n("a", {
						staticClass: "download_header_btn",
						attrs: {
							href: "javascript:void(0)"
						},
						on: {
							click: function(t) {
								e.close("cancel")
							}
						}
					}, [n("SvgIcon", {
						attrs: {
							svgName: "close-16"
						}
					})], 1)]), e._v(" "), n("div", {
						staticClass: "download_body"
					}, [e.DOWNLOAD_STATUS.IN_PROGRESS === e.downloadStatus ? n("div", {
						staticClass: "in-progress"
					}, [n("div", {
						staticClass: "progress-number"
					}, [e._v(e._s(e.downloadProgress) + "%")]), e._v(" "), n("div", {
						staticClass: "progress-bar"
					}, [n("div", {
						staticClass: "progress-bar-group"
					}, [e.progressWidth > 0 ? n("div", {
						staticClass: "out-progress",
						style: {
							width: e.progressWidth + "px"
						}
					}) : e._e()])]), e._v(" "), n("div", {
						staticClass: "progress-msg",
						domProps: {
							innerHTML: e._s(e.progressMsg)
						}
					})]) : e._e(), e._v(" "), e.DOWNLOAD_STATUS.ERROR === e.downloadStatus ? n("div", {
						staticClass: "download-error"
					}, [n("div", {
						staticClass: "err-desc"
					}, [n("span", {
						staticClass: "icon icon-warning-48"
					}), e._v(" "), n("span", {
						staticClass: "err-msg"
					}, [e._v(e._s(e.errMsg))])]), e._v(" "), n("div", {
						staticClass: "retry",
						on: {
							click: function(t) {
								e.close()
							}
						}
					}, [e._v(e._s(e.retryMsg))])]) : e._e()])])])
				},
				staticRenderFns: []
			};
		var he = n("VU/8")(fe, me, !1, function(e) {
			n("bWQT")
		}, "data-v-4393fd8c", null).exports,
			ve = {
				name: "app",
				data: function() {
					return {}
				},
				mounted: function() {
					M.a.checkUserLogin()
				},
				computed: le()({}, Object(m.d)("common", ["appComponent"]), Object(m.d)("download", ["downloadShow"])),
				components: {
					Download: he
				}
			},
			we = {
				render: function() {
					var e = this.$createElement,
						t = this._self._c || e;
					return t("div", {
						staticClass: "l-app-router",
						attrs: {
							id: "AppRouter"
						}
					}, [t("keep-alive", [t("router-view")], 1), this._v(" "), t(this.appComponent, {
						tag: "component"
					}), this._v(" "), t("Download", {
						directives: [{
							name: "show",
							rawName: "v-show",
							value: this.downloadShow,
							expression: "downloadShow"
						}]
					})], 1)
				},
				staticRenderFns: []
			};
		var ge = n("VU/8")(ve, we, !1, function(e) {
			n("w/Tt")
		}, null, null).exports,
			Ee = function(e) {
				return function(t) {
					var n = "",
						o = "";
					switch (t.type) {
					case "mb":
						var r = T.a.getMemberPosition(),
							a = T.a.getPosition(),
							i = T.a.getSubChannel(),
							s = T.a.getChannel();
						o = g.a.jsonToQuery({
							mbid: t.id,
							client_type: "newdoc",
							from: "beauty",
							appname: "newfile",
							app: w.c.name,
							position: t.position || a,
							csource: t.csource || i,
							member_position: t.memberPosition || r,
							member_csource: "newfile2019",
							dist: w.c.dist,
							version: w.c.version,
							downloadChannel: t.channel || s || "docer",
							profession: t.profession || T.a.info.profession
						}), t.payKey && (o += "&payKey=" + t.payKey), n = w.k.mbUrl + o, M.a.navigateOnNewWidget(n);
						break;
					case "link":
						M.a.navigateOnNewWindow(t.content, t.promebrowser);
						break;
					case "member":
						M.a.openPdfUnifyPayDlg(t.csource, t.payconfig);
						break;
					case "search":
						e.$router.push({
							path: "/search",
							query: {
								searchType: t.searchType || "",
								classify: t.type,
								content: t.content ? encodeURIComponent(t.content) : "",
								word: t.text,
								collect: t.collect || {}
							}
						});
						break;
					case "category":
						e.$router.push({
							path: "/category",
							query: {
								data: t.data,
								collect: t.collect
							}
						});
						break;
					case "recommend":
						e.$router.push({
							path: "/recommend",
							query: {
								catId: t.catId,
								word: t.word,
								content: t.content,
								mode: t.mode,
								originAlg: t.originAlg || "",
								collect: t.collect
							}
						})
					}
				}
			},
			_e = n("fZjL"),
			ye = n.n(_e),
			Ae = n("Zrlr"),
			be = n.n(Ae),
			De = n("wxAW"),
			Se = n.n(De),
			Te = g.a.debounce(200),
			Ce = "undefined" != typeof window,
			Pe = {
				install: function(e) {
					var t = new(function(e) {
						return function() {
							function t() {
								be()(this, t), this.ListenerQueue = [], this.TargetQueue = [], this.ListenEvents = ["mousewheel", "scroll", "resize"]
							}
							return Se()(t, [{
								key: "add",
								value: function(t, n, o) {
									var r = this;
									e.nextTick(function() {
										if (r.ListenerQueue.some(function(e) {
											return e.el === t
										})) return r.update(t, n), e.nextTick(function() {
											n.value.collect()
										});
										var a = ye()(n.modifiers)[0],
											i = void 0;
										a && (i = (i = o.context.$refs[a]) ? i.$el || i : document.getElementById(a)), i || (i = g.a.scrollParent(t));
										var s = {
											$parent: i,
											el: t,
											fn: n.value.collect ||
											function() {}
										};
										r.ListenerQueue.push(s), Ce && (r._addListenerTarget(window), r._addListenerTarget(i)), setTimeout(function() {
											r._collect()
										}, 0)
									})
								}
							}, {
								key: "update",
								value: function() {
									var e = this;
									setTimeout(function() {
										e._collect()
									}, 0)
								}
							}, {
								key: "remove",
								value: function(e) {
									if (!e) return !1;
									var t = this.ListenerQueue.some(function(t) {
										return t.el === e
									});
									t && (this._removeListenerTarget(t.$parent), this._removeListenerTarget(window), t.fn && (t.fn = null), g.a.remove(this.ListenerQueue, t))
								}
							}, {
								key: "_addListenerTarget",
								value: function(e) {
									if (!e) return !1;
									var t = this.TargetQueue.find(function(t) {
										return t.el === e
									});
									t ? t.count++ : (t = {
										el: e,
										count: 1
									}, this._initListen(t.el, !0), this.TargetQueue.push(t))
								}
							}, {
								key: "_removeListenerTarget",
								value: function(e) {
									var t = this;
									if (!e) return !1;
									this.TargetQueue.forEach(function(n, o) {
										n.el == e && (n.count--, n.count || (t._initListen(e, !1), t.TargetQueue.splice(o, 1), n = null))
									})
								}
							}, {
								key: "_initListen",
								value: function(e, t) {
									var n = this;
									this.ListenEvents.forEach(function(o) {
										g.a.eventListener[t ? "on" : "off"](e, o, n._collect.bind(n))
									})
								}
							}, {
								key: "_collect",
								value: function() {
									var e = this;
									setTimeout(function() {
										Te(function() {
											e.ListenerQueue && e.ListenerQueue.forEach(function(e) {
												g.a.checkInView(e.el, e.$parent) && e.fn()
											})
										})
									}, 0)
								}
							}]), t
						}()
					}(e));
					e.prototype.$DocerCollectShow = t, e.directive("docer-collect-show", {
						bind: t.add.bind(t),
						componentUpdated: t.update.bind(t),
						unbind: t.remove.bind(t)
					})
				}
			},
			Ie = {},
			Re = g.a.getUrlParam("app") || "wps",
			Oe = g.a.convertAppName(Re);
		Oe ? (w.c.name = Re, w.c.mark = Oe) : (w.c.name = "wps", w.c.mark = 1), T.a.setAppName(w.c.name), document.body.setAttribute("class", w.c.name + "-container"), a.a.config.productionTip = !1, a.a.component("SvgIcon", function() {
			return n.e(2).then(n.bind(null, "My2t"))
		}), a.a.use(te.a, {
			preLoad: 1.3,
			error: "./images/img-error.png",
			loading: "./images/loading-24.gif",
			attempt: 1,
			listenEvents: ["scroll", "wheel", "mousewheel", "resize"],
			throttleWait: 50
		}), a.a.use(ue), a.a.use(Pe);
		var Ne = function() {
				window.rootApp = new a.a({
					el: "#App",
					router: f,
					store: Z,
					template: "<App/>",
					components: {
						App: ge
					}
				}), window.gotoTag = Ee(window.rootApp), window.rootApp.$store.dispatch("user/setUserProfession"), window.rootApp.$store.dispatch("common/setAppName", w.c.name), M.a.isWpsEnv ? (M.a.setWindowCallBack("userStateChanged", function(e) {
					if (r()(e) !== r()(Ie)) if (e && e.result) {
						var t = e.result,
							n = t.logined,
							o = t.sid;
						w.f.wpssid = o, window.rootApp && (window.rootApp.$store.dispatch("user/setUserState", n), window.rootApp.$store.dispatch("user/setWpsSid", o), D.a.$on(w.h.channelVipStateChange, function() {
							window.rootApp.$store.dispatch("user/setUserInfo")
						}), n ? window.rootApp.$store.dispatch("user/setUserInfo") : (D.a.$emit(w.h.logout), window.rootApp.$store.dispatch("user/setUserInfo", {})))
					} else w.f.wpssid = "", D.a.$emit(w.h.logout), window.rootApp.$store.dispatch("user/setUserInfo", {})
				}), M.a.getNewDocsEntrance().then(function(e) {
					e && (T.a.setEntrance(e), w.f.entrance = e)
				})) : setTimeout(function() {
					window.rootApp && window.rootApp.$store.dispatch("user/setUserInfo")
				}, 10)
			},
			Le = function() {
				M.a.getVersionInfo().then(function(e) {
					e && "education" === e && (w.c.versionInfo = "edu"), Ne()
				}).
				catch (function() {
					Ne()
				})
			};
		M.a.getAppInfo().then(function(e) {
			e = e || {}, w.c.uuid = e.uuid || "", w.c.hdid = e.hdid || "", w.c.guid = e.guid || "", w.c.version = e.version || "0.0.0.0", w.c.dist = e.channelid || "", w.c.channelid = e.channelid || "", Le()
		}).
		catch (function() {
			Le()
		})
	},
	bWQT: function(e, t) {},
	n1nN: function(e, t, n) {
		"use strict";
		var o = n("W3Iv"),
			r = n.n(o),
			a = n("mvHQ"),
			i = n.n(a),
			s = n("//Fk"),
			c = n.n(s),
			u = n("Zrlr"),
			d = n.n(u),
			l = n("wxAW"),
			p = n.n(l),
			f = n("s0MJ"),
			m = n("hU7x"),
			h = n.n(m),
			v = n("w7XY"),
			w = {
				xho: function() {
					var e = null;
					return window.XMLHttpRequest && (e = new XMLHttpRequest).overrideMimeType && e.overrideMimeType("text/xml"), e
				},
				rsc: function(e, t, n) {
					return function() {
						if (4 == e.readyState) if (window.clearTimeout(n), 199 < e.status && e.status < 300) {
							var o = {};
							try {
								o = JSON.parse(e.responseText)
							} catch (e) {
								o = {
									result: "error",
									msg: "format error"
								}
							}
							t(o)
						} else 0 == e.status ? t({
							result: "NETWORK_TIMEOUT",
							msg: "timeout"
						}) : t({
							result: "error",
							msg: "status " + e.status
						})
					}
				},
				GET: function(e, t, n) {
					var o = w.xho();
					if (null != o) {
						var a = window.setTimeout(function() {
							o.abort()
						}, t.timeout);
						o.onreadystatechange = w.rsc(o, n, a), o.open("GET", e, !0), t.headers && r()(t.headers).forEach(function(e) {
							o.setRequestHeader(e[0], e[1])
						}), o.withCredentials = t.credentials || !1, o.send(null)
					} else n({
						result: "error",
						msg: "empty XMLHttpRequest"
					})
				},
				POST: function(e, t, n) {
					var o = w.xho();
					if (null != o) {
						var a = window.setTimeout(function() {
							o.abort()
						}, t.timeout);
						o.onreadystatechange = w.rsc(o, n, a), o.open("POST", e, !0), t.headers && r()(t.headers).forEach(function(e) {
							o.setRequestHeader(e[0], e[1])
						}), o.withCredentials = t.credentials || !1, o.send(t.data)
					} else n({
						result: "error",
						msg: "empty XMLHttpRequest"
					})
				}
			},
			g = function(e, t) {
				if (!e) return !1;
				"get" == e.method.toLowerCase() ? w.GET(e.url, e, t) : w.POST(e.url, e, t)
			},
			E = function(e, t, n, o, r, a) {
				return new c.a(function(i, s) {
					g({
						url: e,
						method: t,
						headers: n,
						data: o,
						dataType: "json",
						timeout: r,
						credentials: a
					}, function(e) {
						e ? i(e) : s(e)
					})
				})
			},
			_ = function(e, t, n) {
				return "get" == n.toLowerCase() ? E(e.url, "GET", t, null, e.timeout, e.credentials) : E(e.url, "POST", t, e.data, e.timeout, e.credentials)
			},
			y = {},
			A = function() {
				function e(t, n) {
					d()(this, e), this.timeout = 1e4, this.baseURL = t || "", this.action = f.a.extend({}, n), this.init()
				}
				return p()(e, [{
					key: "init",
					value: function() {
						for (var e in this.action) this[e] = function(e) {
							return function(t) {
								return this.send(t, e)
							}
						}(e)
					}
				}, {
					key: "send",
					value: function(e, t) {
						var n = f.a.extend(!0, {}, this.action[t], e),
							o = void 0,
							a = n.serialNumber;
						return a && (y[a] = setTimeout("1"), o = y[a]), n.url = this.baseURL + n.url, n.headers = this.headers = {
							"X-Requested-With": "XMLHttpRequest"
						}, n.timeout = n.timeout || this.timeout, new c.a(function(e, t) {
							if ("jsonp" == n.responseType) {
								if (-1 === n.url.indexOf("?") && (n.url += "?"), n.cache || (n.url += "t=" + Math.random()), !f.a.isEmptyObject(n.data)) {
									var s = [];
									for (var c in n.data) s.push(c + "=" + encodeURIComponent(n.data[c]));
									var u = s.length ? s.join("&") : "";
									n.url += n.url.split("?").length > 1 && u.length ? "&" + u : ""
								}
								h()(n.url, {
									timeout: n.timeout,
									name: n.jsonpCallbackName
								}, function(n, o) {
									n ? t(n.message) : e(o || {})
								})
							} else {
								if ("get" === n.method.toLowerCase()) {
									if (-1 === n.url.indexOf("?") && (n.url += "?"), n.cache || (n.url += "t=" + Math.random()), !f.a.isEmptyObject(n.data)) {
										var d = [];
										for (var l in n.data) d.push(l + "=" + encodeURIComponent(n.data[l]));
										var p = d.length ? d.join("&") : "",
											m = n.url.split("?");
										n.url += m[1] && p.length ? "&" + p : p
									}
								} else if (n.bJsonData) n.headers["Content-Type"] = "application/json; charset=utf8", n.data = i()(n.data), n.bRawData = !0;
								else {
									n.headers["Content-Type"] = "application/x-www-form-urlencoded; charset=utf8";
									var w = [];
									r()(n.data).forEach(function(e) {
										w.push(e[0] + "=" + encodeURIComponent(e[1]))
									}), n.data = w.join("&")
								}
								if ("browser" === v.a.runtime) n.bJsonData ? (n.credentials = !1, delete n.bJsonData) : n.credentials = !0, _(n, n.headers, n.method).then(function(t) {
									a ? o == y[a] && e(t || {}) : e(t || {})
								}, function(e) {
									a ? o == y[a] && t(e) : t(e)
								});
								else {
									var g = "jsCallback" + setTimeout("1");
									window[g] = function(n) {
										if (delete window[g], n = JSON.parse(f.a.b64DecodeUnicode(n)), a && o != y[a]) return !1;
										if (n && n.result) switch (n.result.error_code) {
										case "SUCCESS":
											try {
												e(n.result.data || {})
											} catch (e) {
												t({
													result: "error",
													msg: "format error"
												})
											}
											break;
										default:
											t({
												result: n.result.error_code,
												msg: n.result.msg
											})
										} else t({
											result: "error",
											msg: "empty"
										})
									}, n.callback = g, v.a.getUserInfoCookie().then(function(o) {
										var r = {};
										o.split(";").forEach(function(e) {
											if (e) {
												var t = e.split("=");
												r[t[0]] = t[1]
											}
										}), n.cookie = r, "get" === n.method.toLowerCase() ? v.a.httpGet(n, n.headers).then(function(t) {
											e(t)
										}).
										catch (function(e) {
											t(e)
										}) : v.a.httpPost(n, n.headers).then(function(t) {
											e(t)
										}).
										catch (function(e) {
											t(e)
										})
									})
								}
							}
						})
					}
				}]), e
			}(),
			b = function() {
				function e() {
					d()(this, e), this.dataServices = []
				}
				return p()(e, [{
					key: "add",
					value: function(e, t, n) {
						this.dataServices[e] = new A(t, n)
					}
				}, {
					key: "get",
					value: function(e) {
						return this.dataServices[e]
					}
				}]), e
			}(),
			D = n("wYMm"),
			S = new b;
		S.add("user", "", {
			allinfo: {
				method: "GET",
				cache: !0,
				url: D.j.docerPrevUrl + "api/user/allinfo"
			},
			queryMyBought: {
				method: "GET",
				url: D.j.docerPrevUrl + "api/my/v2/buy"
			},
			queryMyUsed: {
				method: "GET",
				url: D.j.docerPrevUrl + "api/my/v2/lateuse"
			},
			queryMyCollected: {
				method: "GET",
				url: D.j.docerPrevUrl + "api/my/v2/collect"
			},
			queryMyCollectID: {
				method: "GET",
				url: D.j.docerPrevUrl + "api/my/collect/all_ids"
			},
			queryMyBougthID: {
				method: "GET",
				url: D.j.docerPrevUrl + "api/my/user/buyids"
			},
			delFav: {
				method: "POST",
				url: D.j.docerPrevUrl + "api/my/v2/collect/delete"
			},
			addFav: {
				method: "POST",
				url: ""
			},
			profession: {
				method: "GET",
				url: D.j.startupPrev + "v2/pro_recommend/profession"
			},
			searchReccentUse: {
				method: "GET",
				url: D.j.docerPrevUrl + "api/my/v2/lateuse/search"
			},
			searchMyTmp: {
				method: "GET",
				url: D.j.docerPrevUrl + "api/my/v2/buy/search"
			},
			searchMyCollect: {
				method: "GET",
				url: D.j.docerPrevUrl + "api/my/v2/collect/search"
			},
			delRecentUse: {
				method: "POST",
				url: D.j.docerPrevUrl + "api/my/v2/lateuse/delete"
			},
			userIcons: {
				method: "GET",
				url: D.j.newDocCdnV5 + "recommend/user_icons",
				cache: !0,
				responseType: "jsonp",
				format: "jsonp",
				jsonpCallbackName: "jsonp_user_icons_callback",
				client: "pc"
			},
			getLastBuyVipTime: {
				method: "GET",
				url: "api/user/user_identity"
			}
		}), S.add("search", "", {
			likeSearchText: {
				method: "GET",
				url: D.j.docerPrevUrl + "wps/ajax/search_text/",
				cache: !0
			},
			searchHotWord: {
				method: "GET",
				url: D.j.startupPrev + "recommend/hot_search"
			},
			searchByNavTag: {
				method: "POST",
				url: D.j.startupPrev + "recommend/data_by_type"
			},
			searchList: {
				method: "GET",
				url: D.j.startupPrev + "v2/search/advanced"
			},
			searchFilter: {
				method: "GET",
				url: D.j.docerPrevUrl + "api/search/filter"
			}
		}), S.add("rec", "", {
			professionTag: {
				method: "GET",
				url: D.j.cntntRecom + "api/profession/tag/list",
				cache: !0,
				responseType: "jsonp",
				format: "jsonp",
				jsonpCallbackName: "jsonp_profession_tag_list_callback",
				client: "pc"
			},
			filters: {
				method: "GET",
				url: D.j.cntntRecom + "api/category/filters",
				cache: !0,
				responseType: "jsonp",
				format: "jsonp",
				jsonpCallbackName: "jsonp_category_filters_callback",
				client: "pc"
			},
			precMB: {
				method: "POST",
				url: D.j.recom + "v2.php/newdoc/prec_mb?t=" + (new Date).getTime(),
				bJsonData: 1
			},
			category: {
				method: "GET",
				cache: !0,
				responseType: "jsonp",
				format: "jsonp",
				jsonpCallbackName: "jsonp_recommend_nav_callback",
				url: D.j.newDocCdnV5 + "recommend/nav"
			},
			ad: {
				method: "POST",
				url: D.j.recom + "recommend",
				bJsonData: 1
			}
		}), S.add("preview", "", {
			allData: {
				method: "GET",
				url: D.j.newDoc + "preview/all_data"
			},
			likeData: {
				method: "GET",
				url: D.j.startupPrev + "preview/side_guess_like_scroll"
			},
			changeData: {
				method: "GET",
				url: D.j.startupPrev + "preview/side_change"
			}
		}), S.add("previewById", D.j.newDocCdn, {
			fetchTplsByIds: {
				method: "GET",
				url: "recommend/personal_recommendation",
				data: {}
			}
		}), S.add("collection", D.j.docerPrevUrl, {
			download: {
				method: "POST",
				url: "api/download"
			},
			checkBuy: {
				method: "POST",
				url: "wps/ajax/is_buy_mb"
			}
		}), S.add("wx", D.j.vipApi, {
			adapter: {
				method: "GET",
				url: "/wx_adapter/v1/mp/subscribe/wps_docer"
			}
		});
		t.a = S
	},
	s0MJ: function(e, t, n) {
		"use strict";
		var o, r, a, i = n("//Fk"),
			s = n.n(i),
			c = n("RRo+"),
			u = n.n(c),
			d = n("Zx67"),
			l = n.n(d),
			p = n("pFYg"),
			f = n.n(p),
			m = String.fromCharCode;

		function h(e) {
			for (var t, n, o = [], r = 0, a = e.length; r < a;)(t = e.charCodeAt(r++)) >= 55296 && t <= 56319 && r < a ? 56320 == (64512 & (n = e.charCodeAt(r++))) ? o.push(((1023 & t) << 10) + (1023 & n) + 65536) : (o.push(t), r--) : o.push(t);
			return o
		}
		function v(e) {
			if (e >= 55296 && e <= 57343) throw Error("Lone surrogate U+" + e.toString(16).toUpperCase() + " is not a scalar value")
		}
		function w(e, t) {
			return m(e >> t & 63 | 128)
		}
		function g(e) {
			if (0 == (4294967168 & e)) return m(e);
			var t = "";
			return 0 == (4294965248 & e) ? t = m(e >> 6 & 31 | 192) : 0 == (4294901760 & e) ? (v(e), t = m(e >> 12 & 15 | 224), t += w(e, 6)) : 0 == (4292870144 & e) && (t = m(e >> 18 & 7 | 240), t += w(e, 12), t += w(e, 6)), t += m(63 & e | 128)
		}
		function E() {
			if (a >= r) throw Error("Invalid byte index");
			var e = 255 & o[a];
			if (a++, 128 == (192 & e)) return 63 & e;
			throw Error("Invalid continuation byte")
		}
		function _() {
			var e, t;
			if (a > r) throw Error("Invalid byte index");
			if (a == r) return !1;
			if (e = 255 & o[a], a++, 0 == (128 & e)) return e;
			if (192 == (224 & e)) {
				if ((t = (31 & e) << 6 | E()) >= 128) return t;
				throw Error("Invalid continuation byte")
			}
			if (224 == (240 & e)) {
				if ((t = (15 & e) << 12 | E() << 6 | E()) >= 2048) return v(t), t;
				throw Error("Invalid continuation byte")
			}
			if (240 == (248 & e) && (t = (7 & e) << 18 | E() << 12 | E() << 6 | E()) >= 65536 && t <= 1114111) return t;
			throw Error("Invalid UTF-8 detected")
		}
		var y = {
			version: "3.0.0",
			encode: function(e) {
				for (var t = h(e), n = t.length, o = -1, r = ""; ++o < n;) r += g(t[o]);
				return r
			},
			decode: function(e) {
				o = h(e), r = o.length, a = 0;
				for (var t = [], n = _(); !1 !== n;) t.push(n), n = _();
				return function(e) {
					for (var t, n = e.length, o = -1, r = ""; ++o < n;)(t = e[o]) > 65535 && (r += m((t -= 65536) >>> 10 & 1023 | 55296), t = 56320 | 1023 & t), r += m(t);
					return r
				}(t)
			}
		},
			A = n("wYMm"),
			b = n("v8ob"),
			D = "undefined" != typeof window,
			S = function(e) {
				var t, n;
				return !(!e || "[object Object]" !== {}.toString.call(e)) && (!(t = l()(e)) || "function" == typeof(n = {}.hasOwnProperty.call(t, "constructor") && t.constructor) && {}.hasOwnProperty.toString.call(n) === {}.hasOwnProperty.toString.call(Object))
			},
			T = function(e) {
				var t;
				for (t in e) return !1;
				return !0
			},
			C = /\.(doc|docx|dot|dotx|wps|pdf|ppt|pptx|ppsx|pot|potx|pps|dps|dpt|ppsm|potm|pptm|xls|xlsx|xlt|xltx|et|csv|dbf|xlsm|dif|prn|xml|zip|wpt|ett)$/i,
			P = function(e, t) {
				return (e.currentStyle ? e.currentStyle : window.getComputedStyle(e, null))[t]
			},
			I = function(e) {
				return P(e, "overflow") + P(e, "overflow-y") + P(e, "overflow-x")
			},
			R = {
				on: function(e, t, n) {
					var o = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
					e.addEventListener(t, n, o)
				},
				off: function(e, t, n) {
					var o = arguments.length > 3 && void 0 !== arguments[3] && arguments[3];
					e.removeEventListener(t, n, o)
				}
			};
		var O, N, L, U, k, M = new s.a(function(e) {
			O = e
		}),
			x = new s.a(function(e) {
				U = e
			}),
			F = new s.a(function(e) {
				k = e
			});
		b.a.$on(A.h.finishProfession, function() {
			N = !0, k(), L && O()
		}), b.a.$on(A.h.finishUserInfo, function() {
			L = !0, U(), N && O()
		});
		t.a = {
			extend: function e() {
				var t, n, o, r, a, i, s, c = arguments[0] || {},
					u = 1,
					d = arguments.length,
					l = !1;
				for ("boolean" == typeof c && (l = c, c = arguments[u] || {}, u++), "object" !== (void 0 === c ? "undefined" : f()(c)) && "function" === !(null == (s = c) ? s + "" : "object" === (void 0 === s ? "undefined" : f()(s)) || "function" == typeof s ? (toString.call(s), 1) : void 0 === s || f()(s)) && (c = {}), u === d && (c = this, u--); u < d; u++) if (null != (t = arguments[u])) for (n in t) o = c[n], c !== (r = t[n]) && (a = Array.isArray(r), l && r && (S(r) || a) ? (a ? (a = !1, i = o && Array.isArray(o) ? o : []) : i = o && S(o) ? o : {}, c[n] = e(l, i, r)) : void 0 !== r && (c[n] = r));
				return c
			},
			isEmptyObject: T,
			throttle: function(e, t, n) {
				var o = null,
					r = 0;
				return function() {
					if (!o || n) {
						n && (window.clearTimeout(o), r = Date.now());
						var a = this,
							i = arguments,
							s = function() {
								r = Date.now(), o = !1, e.apply(a, i)
							};
						Date.now() - r >= t ? s() : o = setTimeout(s, t)
					}
				}
			},
			errorCode: function(e) {
				return {
					EMPTY: 1,
					NETWORK_ERROR: 1,
					NETWORK_TIMEOUT: 1
				}[e] && e || "UNKNOWN_ERROR"
			},
			delPrefixZero: function(e) {
				var t = (e || "").split(".");
				return (t = t.map(function(e) {
					var t = parseInt(e, 10);
					return u()(t) && t == e ? t : e
				})).join(".") || ""
			},
			dateFormat: function(e, t, n) {
				n && (e *= 1e3);
				var o = {
					M: (e = new Date(e)).getMonth() + 1,
					d: e.getDate(),
					h: e.getHours(),
					m: e.getMinutes(),
					s: e.getSeconds(),
					q: Math.floor((e.getMonth() + 3) / 3),
					S: e.getMilliseconds()
				};
				return t = t.replace(/([yMdhmsqS])+/g, function(t, n) {
					var r = o[n];
					return void 0 !== r ? (t.length > 1 && (r = (r = "0" + r).substr(r.length - 2)), r) : "y" === n ? (e.getFullYear() + "").substr(4 - t.length) : t
				})
			},
			getRoutePath: function(e) {
				return (e = e && e.match(/[^/]\w*[^/?]/g)) || []
			},
			getUrlParam: function(e, t) {
				return new RegExp("(^|\\?|&)" + e + "=([^&]*)(\\s|&|$)", "i").test(t || location.href) ? decodeURIComponent(RegExp.$2.replace(/\+/g, " ")) : ""
			},
			convertAppMark: function(e) {
				return {
					1: "wps",
					2: "et",
					3: "wpp",
					4: "pdf"
				}[e] || ""
			},
			convertAppName: function(e) {
				return {
					wps: 1,
					et: 2,
					wpp: 3,
					pdf: 4
				}[e = e && e.toLowerCase() || e] || ""
			},
			b64EncodeUnicode: function(e) {
				return btoa(y.encode(e))
			},
			b64DecodeUnicode: function(e) {
				return y.decode(atob(e))
			},
			jsonToQuery: function(e) {
				if (!e || T(e)) return "";
				var t = "";
				for (var n in e) t += "&" + n + "=" + encodeURIComponent(e[n]);
				return "" + t.slice(1)
			},
			debounce: function(e) {
				var t = null,
					n = 0;
				return function(o) {
					for (var r = arguments.length, a = Array(r > 1 ? r - 1 : 0), i = 1; i < r; i++) a[i - 1] = arguments[i];
					n || (n = Date.now());
					var s = Date.now() - n;
					t && clearTimeout(t), t = setTimeout(function() {
						n = null, t = null, o.apply(window, a)
					}, e - s)
				}
			},
			getUserProfessionFinish: M,
			formatName: function(e) {
				return e ? e.toString().replace(C, "") : ""
			},
			EncodeURL: function(e) {
				if ("string" == typeof e) return encodeURIComponent(e);
				if (e && "object" === (void 0 === e ? "undefined" : f()(e))) {
					var t = e instanceof Array ? [] : {};
					for (var n in e) e.hasOwnProperty(n) && (t[n] = encodeURIComponent(e[n]));
					return t
				}
			},
			getLimit: function(e) {
				if (e) {
					var t = e.getBoundingClientRect().width;
					if (t) {
						var n = parseInt(getComputedStyle(e, !1).paddingLeft || 0, 10);
						return Math.floor((t - 232 - 2 * n - 6) / 247) + 1
					}
					return 0
				}
				return 0
			},
			scrollAppRouter: function() {
				var e = document.getElementById("AppRouter");
				e && (e.scrollTop = 0)
			},
			getMediaLimit: function(e) {
				if (e) {
					var t = e.getBoundingClientRect().width;
					if (t) switch (A.c.mark) {
					case 1:
						return Math.floor((t - 6) / 360);
					case 2:
					case 3:
						return Math.floor((t - 6) / 270);
					default:
						return 0
					}
					return 0
				}
				return 0
			},
			formatString: function(e) {
				for (var t = arguments.length, n = Array(t > 1 ? t - 1 : 0), o = 1; o < t; o++) n[o - 1] = arguments[o];
				return e.replace(/{([^{}]*)}/g, function(e, t) {
					return String(n[t])
				})
			},
			getUserInfoFinish: x,
			getProfessionFinish: F,
			scrollTo: function(e, t) {
				var n = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 500;
				!
				function(e, t, n, o, r, a, i) {
					if (e) {
						var s = Date.now();
						window.requestAnimationFrame(function c() {
							var u = Math.min(1, (Date.now() - s) / a);
							i ? e[t] = o + u * (r - o) + n : e.style[t] = o + u * (r - o) + n, 1 !== u && window.requestAnimationFrame(c)
						})
					}
				}(e, "scrollTop", "", e.scrollTop, t, n, !0)
			},
			getFileNameForPath: function(e) {
				return (e + "").replace(/.*(\\|\/)(.*$)/g, "$2")
			},
			scrollParent: function(e) {
				if (D) {
					if (!(e instanceof HTMLElement)) return window;
					for (var t = e; t && t !== document.body && t !== document.documentElement && t.parentNode;) {
						if (/(scroll|auto)/.test(I(t))) return t;
						t = t.parentNode
					}
					return window
				}
			},
			checkInView: function(e, t) {
				if (e) {
					var n = e.getBoundingClientRect(),
						o = t && t.getBoundingClientRect && t.getBoundingClientRect(),
						r = o ? o.top : 0,
						a = o ? o.left : 0;
					return D && n.top < window.innerHeight - r && n.bottom > 0 && n.left < window.innerWidth - a && n.right > 0
				}
				return !1
			},
			eventListener: R,
			remove: function(e, t) {
				if (e.length) {
					var n = e.indexOf(t);
					return n > -1 ? e.splice(n, 1) : void 0
				}
			},
			fillAdData: function(e) {
				var t = {};
				return e && e.rec && e.rec[0] && (t = e.rec[0]), t.info = e && e.info ? e.info : {}, t
			}
		}
	},
	v8ob: function(e, t, n) {
		"use strict";
		var o = new(n("7+uW").a);
		t.a = o
	},
	"w/Tt": function(e, t) {},
	w7XY: function(e, t, n) {
		"use strict";
		var o = n("mvHQ"),
			r = n.n(o),
			a = n("//Fk"),
			i = n.n(a),
			s = n("s0MJ"),
			c = window.cefQuery,
			u = window.external && window.external.jsAsynCall,
			d = u || c,
			l = {
				isWpsEnv: d,
				jsAsynCall: function(e) {
					var t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
						n = arguments[2],
						o = arguments[3];
					return new i.a(function(a, i) {
						d || a(n ? n(null) : null);
						var l = {
							method: e,
							params: t
						},
							p = e + "_async_callback_" + setTimeout("1");
						window[p] = function(t) {
							delete window[p];
							var r = JSON.parse(s.a.b64DecodeUnicode(t));
							o && o(r), d ? "ok" === r.callstatus ? a(n ? n(r.result) : r.result) : i({
								result: r.errormsg,
								method: e
							}) : a(n ? n(null) : null)
						}, l.callback = p;
						var f = s.a.b64EncodeUnicode(r()(l));
						c ? c({
							request: 'jsAsynCall("' + f + '")',
							persistent: !1
						}) : u(f)
					})
				},
				getAppInfo: function() {
					return new i.a(function(e, t) {
						d ? !u && c ? c({
							request: 'cefInitialize("")',
							persistent: !1,
							onSuccess: function() {
								e(window.external.appInfo)
							}
						}) : e(window.external.appInfo) : t()
					})
				}
			},
			p = n("wYMm"),
			f = l.jsAsynCall,
			m = {
				isWpsEnv: l.isWpsEnv,
				getAppInfo: l.getAppInfo,
				runtime: window.cefQuery ? "cef" : window.external && window.external.jsAsynCall ? "webkit" : "browser",
				jsAsynCall: f,
				setWindowCallBack: function(e, t) {
					window[e] = function(e) {
						e ? (e = JSON.parse(s.a.b64DecodeUnicode(e)), t(e)) : t()
					}
				},
				httpGet: function(e, t) {
					return f("httpGet", {
						url: e.url,
						timeout: e.timeout,
						cookie: e.cookie,
						callback: e.callback,
						header: t
					})
				},
				httpPost: function(e, t) {
					return f("httpPost", {
						url: e.url,
						timeout: e.timeout,
						cookie: e.cookie,
						data: e.data,
						bRawData: e.bRawData || 1,
						callback: e.callback,
						header: t
					})
				},
				checkUserLogin: function() {
					f("checkUserLogin")
				},
				navigateOnNewWidget: function(e, t, n, o, r) {
					f("navigateOnNewWidget", {
						url: e,
						transferData: t || "",
						width: n || 780,
						height: o || 500,
						bModal: r || 1,
						closeBtn: !0
					})
				},
				openPdfUnifyPayDlg: function(e, t, n, o, r) {
					f("openPdfUnifyPayDlg", {
						csource: e,
						payconfig: t,
						bModal: r || 1
					})
				},
				login: function(e, t, n) {
					f("login", {
						loginSrc: e || p.c.name + "_newfile2019_testa",
						bizSrc: t || "new_document_mall19",
						bizSrcDetail: n || "",
						qrcode:"vip",
						from:"pdf_newtab",
					})
				},
				logout: function() {
					f("logout")
				},
				getUserInfoCookie: function() {
					return f("getUserInfoCookie")
				},
				newBlankDocument: function() {
					return f("newBlankDocument")
				},
				newPdfFromFile: function() {
					return f("newPdfFromFile")
				},
				newPdfFromScan: function() {
					return f("newPdfFromScan")
				},
				openDocument: function() {
					return f("openDocument")
				},
				clicktRecommendationFunction: function(e) {
					return f("clicktRecommendationFunction", {
							pluginId: e
						})
				},
				sendInfo: function(e,t) {
					return f("sendInfo", {
						p4: e,
						p6: t
					})
				},
				localStorageSet: function(e, t, n, o) {
					f("localStorageSet", {
						key: e,
						val: t,
						stamp: n,
						flag: o
					})
				},
				localStorageGet: function(e) {
					return f("localStorageGet", {
						key: e
					})
				},
				localStorageRemove: function(e) {
					f("localStorageRemove", {
						key: e
					})
				},
				startDownload: function(e) {
					return f("downloadTemplate", e)
				},
				cancelDownLoad: function(e) {
					f("cancelDownload", {
						id: e
					})
				},
				navigateOnNewWindow: function(e, t) {
					f("navigateOnNewWindow", {
						url: e,
						promebrowser: t || !1
					})
				},
				getRecentFileList: function(e) {
					return f("getRecommendedFiles", e)
				},
				checkAIReady: function() {
					return f("getAiReady")
				},
				openAiBeautify: function() {
					f("openAiBeautify")
				},
				callNative: function(e) {
					f(e)
				},
				getVersionInfo: function() {
					return f("getVersionInfo")
				},
				openDocerPage: function(e) {
					f("openDocerPage", {
						action: e.action || "homepage",
						params: e.param || {}
					})
				},
				openMyTemplateTab: function() {
					f("openMyTemplateTab")
				},
				getNewDocsEntrance: function() {
					return f("getNewDocsEntrance")
				},
				getRecommendedFunction: function(e) {
					return f("getRecommendedFunction", e)
				},
				getEncryptString: function(e) {
                return f("getEncryptString", {
                    key: e
                })
				},
				getC64CodecEncodeData: function(e) {
                return f("getC64CodecEncodeData", {
                    data: e
                })
				}
			};
		t.a = m
	},
	wYMm: function(e, t, n) {
		"use strict";
		n.d(t, "j", function() {
			return o
		}), n.d(t, "k", function() {
			return r
		}), n.d(t, "c", function() {
			return a
		}), n.d(t, "h", function() {
			return i
		}), n.d(t, "e", function() {
			return s
		}), n.d(t, "d", function() {
			return c
		}), n.d(t, "i", function() {
			return u
		}), n.d(t, "a", function() {
			return d
		}), n.d(t, "f", function() {
			return l
		}), n.d(t, "b", function() {
			return p
		}), n.d(t, "g", function() {
			return f
		});
		var o = {
			cdnUrl: "https://api.vas.wpscdn.cn/docer/startup/",
			docerPrevUrl: "https://docer.wps.cn/v3.php/",
			startupPrev: "https://docer.wps.cn/partner.php/api/startup/",
			newDoc: "https://docer.wps.cn/partner.php/api/newdoc/v3/",
			newDocCdn: "https://api.vas.wpscdn.cn/docer/newdoc/v3/",
			cntntRecom: "https://api.vas.wpscdn.cn/cntnt/recom/",
			recom: "https://recom.docer.wps.cn/",
			newDocCdnV5: "https://api.vas.wpscdn.cn/docer/newdoc/v5/",
			vipApi: "//vipapi.wps.cn"
		},
			r = {
				memberUrl: "https://vip.wps.cn/vcl_svr/static/docerpay?",
				daomiUrl: "https://docer.wps.cn/partner.php/daomipay/v1/?",
				couponUrl: "https://www.docer.com/api/coupon_pack/pay/alipay?",
				memberCenterUrl: "https://docer.wps.cn/partner.php/monthcard/v1?",
				mbUrl: "https://docer.wps.cn/partner.php/mbpay/v1/?"
			},
			a = {
				name: "wps",
				mark: 1,
				uuid: "",
				guid: "",
				hdid: "",
				channelid: "",
				dist: "",
				version: "",
				versionInfo: ""
			},
			i = {
				clickDocument: "CLICK-DOCUMENT",
				resizeWindow: "RESIZE-WINDOW",
				unloadWindow: "UNLOAD-WINDOW",
				logout: "LOGOUT",
				login: "LOGIN",
				finishProfession: "FINISH-PROFESSION",
				finishUserInfo: "FINISH-USERINFO",
				boardcastPurchaseStatus: "boardcastPurchaseStatus",
				channelVipStateChange: "channelVipStateChange",
				scrollRouter: "SCROLL-ROUTER",
				scrollPanel: "SCROLL-PANEL",
				sureCollect: "SURE-COLLECT",
				sureDelete: "SURE-DELETE",
				featureChange: "FEATURE-CHANGE",
				collectChange: "COLLECT-CHANGE"
			},
			s = {
				list: {
					wps: "84x120",
					wpp: "198x113",
					et: "196x138",
					pdf: "84x120"
				},
				itemWidth: {
					wps: "190",
					wpp: "262",
					et: "256",
					pdf: "190"
				},
				itemHeight: {
					wps: "270",
					wpp: "224",
					et: "197",
					pdf: "270"
				}
			},
			c = {
				userExpireInfo: "NEWFILE-USER-EXPIRE-INFO",
				userProfessionInfo: "NEWFILE-USER-PRO-INFO",
				userSex: "USER-SEX",
				lastCacheDate: "NEWFILE-LAST-CACHE-DATE",
				userGroup: "NEWFILE-USER-GROUP",
				userGroupDate: "NEWFILE-USER-GROUP-DATE",
				category: "NEWFILE-CATEGORY",
				categoryDate: "NEWFILE-CATEGORY-DATE",
				lastEffectDate: "NEWFILE-EFFECT_DATE"
			},
			u = {
				loading: "LOADING",
				empty: "EMPTY",
				unknow: "UNKNOWN_ERROR",
				timeout: "NETWORK_TIMEOUT",
				error: "NETWORK_ERROR",
				loaded: "LOADED",
				tmpEmpty: "TMP_EMPTY"
			},
			d = {
				IN_PROGRESS: 0,
				FINISHED: 1,
				ERROR: 2
			},
			l = {
				userid: "",
				profession: "",
				memberid: "",
				entrance: "rknull",
				userGroup: "",
				wpssid: ""
			},
			p = {
				NEWFILE2019_SEARCH_BTN: "newfile2019_search_btn",
				NEWFILE2019_PREVIEW_PIC: "newfile2019_preview_pic",
				NEWFILE2019_LEFTBTN: "newfile2019_leftbtn",
				NEWFILE2019_LEFTNAV_PIC: "newfile2019_leftnav_pic",
				NEWFILE2019_PRIVILEGE_ICON: "newfile2019_privilege_icon"
			};
		var f = function() {
				try {
					return 0 == document.createElement("canvas").toDataURL("image/webp").indexOf("data:image/webp")
				} catch (e) {
					return !1
				}
			}() ? ".webp" : ".jpg"
	},
	xK0O: function(e, t, n) {
		"use strict";
		var o = n("//Fk"),
			r = n.n(o),
			a = n("Dd8w"),
			i = n.n(a),
			s = n("NYxO"),
			c = n("w7XY"),
			u = n("wYMm"),
			d = n("Dzwp"),
			l = n("n1nN"),
			p = n("59SO"),
			f = "updateDownloadPercent",
			m = "downloadComplete",
			h = "downloadFailed",
			v = "downloadCancel";
		t.a = {
			mounted: function() {
				var e = this;
				c.a.setWindowCallBack(f, function(t) {
					var n = +t.result || 0;
					e.setDownloadProgress(n)
				}), c.a.setWindowCallBack(m, function() {
					e.setDownloadHide(!0)
				}), c.a.setWindowCallBack(h, function() {
					p.a.sendDownload({
						p10: "failed",
						p11: 0
					}), p.a.sendDevDownload("failed"), e.setDownloadFailed()
				}), c.a.setWindowCallBack(v, function() {
					e.setDownloadHide(!0)
				})
			},
			methods: i()({}, Object(s.b)("download", ["setDownload", "setDownloadProgress", "setDownloadHide", "setDownloadFailed"]), {
				$_download: function(e, t, n) {
					var o = this,
						a = d.a.getSubChannel(),
						i = d.a.getChannel(),
						s = !1;
					return 3 == e.moban_type ? new r.a(function(r, c) {
						l.a.get("collection").checkBuy({
							data: {
								mb_id: e.id,
								doc_type: 3
							}
						}).then(function(e) {
							s = "ok" === e.result
						}).
						catch (function() {
							s = !1
						}).
						finally(function() {
							o.setDownload({
								from: "newfile",
								position: a || "",
								sub_channel: a || "",
								channel: i || "docer",
								guid: u.c.guid,
								hdid: u.c.hdid,
								dist: u.c.dist || 0,
								version: u.c.version || 0,
								id: e.id,
								name: e.name,
								class: t,
								img_url: e.thumb_big_url,
								uuid: u.c.uuid,
								sn: s ? "" : n ? "privilege" : "",
								client_type: "newdoc"
							}).
							catch (function(e) {
								c(e)
							})
						})
					}) : this.setDownload({
						from: "newfile",
						position: a || "",
						sub_channel: a || "",
						channel: i || "docer",
						guid: u.c.guid,
						hdid: u.c.hdid,
						dist: u.c.dist || 0,
						version: u.c.version || 0,
						id: e.id,
						name: e.name,
						class: t,
						img_url: e.thumb_big_url,
						uuid: u.c.uuid,
						sn: s ? "" : n ? "privilege" : "",
						client_type: "newdoc"
					})
				}
			})
		}
	}
}, ["NHnr"]);
