(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[2],{"00ca":function(e,t,i){},"1f90":function(e,t,i){},"26e3":function(e,t,i){"use strict";i("00ca")},"28c4":function(e,t,i){},"2d4c":function(e,t,i){"use strict";i("7026")},"30d4":function(e,t,i){"use strict";i("c473")},"32d2":function(e,t,i){"use strict";i("32ed")},"32ed":function(e,t,i){},"33ca":function(e,t,i){},"3dfa":function(e,t,i){"use strict";i("a1cb")},"479b":function(e,t,i){},4806:function(e,t,i){"use strict";i("28c4")},"4b0f":function(e,t,i){"use strict";i("33ca")},"51e1":function(e,t,i){},"52db":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"preview",class:{"dark-skin":e.isDarkSkin},attrs:{id:"previewContentDom"},on:{click:function(t){return e.onClickClose()}}},[t("div",{staticClass:"preview__main",class:e.previewClass},[t("div",{staticClass:"preview__operate"},[t("div",{staticClass:"preview__arrow",class:{"btn-hide":e.currentIndex<=0},on:{click:function(t){return t.stopPropagation(),e.onClickLeft()}}},[t("span",{staticClass:"l-d-flex l-align-items-center l-justify-content-center"},[t("SvgIcon",{attrs:{svgName:"arrow-left-15",className:"g-font_size-24"}})],1)])]),t("Main",{ref:"rPreviewContent",attrs:{urls:e.urls,likeData:e.likeData,policy:e.policy,eventTime:e.eventTime,requestId:e.likeRequestId,item:e.data,tags:e.tags,isBought:e.isBought,videoUrl:e.videoUrl,hasChangeBought:e.hasChangeBought,catData:e.catData,currentAppName:e.currentAppName,statusCode:e.statusCode,index:e.currentIndex,otherCollectInfo:e.otherCollectInfo,payconfig:e.opPayconfig,freeTrialBtnText:e.freeTrialBtnText$,loadBeginTime:e.loadBeginTime,isPrivilegeFree:e.isPrivilegeFree,previewTextConfig:e.previewTextConfig},on:{wheelHandle:e.onWheel,download:e.onDownload,handlerGoto:e.handlerGoto,getFreeMember:e.$_getFreeMember}}),t("div",{staticClass:"preview__rightbar",on:{click:function(e){e.stopPropagation()}}},[t("div",{ref:"sidebar",staticClass:"preview__sidebar g-r",on:{scroll:e.onscroll}},[t("SidebarTop",{attrs:{item:e.data,isBought:e.isBought,index:e.currentIndex,hasChangeBought:e.hasChangeBought,otherCollectInfo:e.otherCollectInfo,isShowAd:e.isShowAd,isFav:e.isFav,resourceType:e.resourceType,statusCode:e.statusCode,downBtnTag:e.downBtnTag,isFixed:e.isFixed,fillDomHeight:e.fixedFillHeight,payconfig:e.opPayconfig,freeTrialBtnText:e.freeTrialBtnText$,freeTrialOpData:e.freeTrialOpData$,collectTipShow:e.collectTipShow,isPrivilegeFree:e.isPrivilegeFree,previewBtnConfig:e.previewBtnConfig,isFetchingMbStatus:e.isFetchingMbStatus},on:{handleDownload:e.onDownload,download:e.onDownload,handlerGoto:e.handlerGoto,changeFav:e.hanldeChangeFav,favClose:e.closeTip,FavGo:e.goCollection,getFreeMember:e.$_getFreeMember}}),t("SidebarBottom",{attrs:{data:e.data,catData:e.catData,index:e.currentIndex,features:e.features,tags:e.tags,isShowAd:e.isShowAd,otherCollectInfo:e.otherCollectInfo}}),t("SidebarLike",{attrs:{state:e.fetchLikeDataState,data:e.likeData,policy:e.policy,currentAppName:e.currentAppName,tmplData:e.data},on:{retry:e.fetchLikeData}})],1),t("span",{staticClass:"preview__close",on:{click:function(t){return t.stopPropagation(),e.onClickClose("close")}}},[t("SvgIcon",{attrs:{svgName:"close-16-2",className:"g-font_size-16"}})],1)]),t("div",{staticClass:"preview__operate"},[t("div",{staticClass:"preview__arrow",class:{"btn-hide":e.currentIndex>=e.listDataLen-1},on:{click:function(t){return t.stopPropagation(),e.onClickRight()}}},[t("span",{staticClass:"l-d-flex l-align-items-center l-justify-content-center"},[t("SvgIcon",{attrs:{svgName:"arrow-right-15",className:"g-font_size-24"}})],1)])]),e.showCopyRightTip?t("CopyRightTip",{staticClass:"preview__msg",on:{close:e.checkPreviewCopyRightTip},nativeOn:{click:function(e){e.stopPropagation()}}}):e._e()],1),t(e.previewModel,{tag:"component",attrs:{freeTrialOpData:e.freeTrialOpData$,freeTrialCollectInfo:e.freeTrialCollectInfo,serverTime:e.userInfo.serverTime},nativeOn:{click:function(e){e.stopPropagation()}}})],1)},s=[],o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"preview__in g-clearfloat",on:{click:function(e){e.stopPropagation()}}},[t("div",{ref:"previewContent",staticClass:"preview__content g-l",on:{wheel:e.wheelHandle}},[e.statusCode?t("div",{staticClass:"preview__dt"},[e.statusCode==e.netStatus.loading?t("div",{staticClass:"preview__loading",class:{"is-dark":e.isDarkSkin}}):t("div",{staticClass:"preview__error"},[t("SvgIcon",{attrs:{svgName:"preview-error"}}),e._v(" 资源已下架 ")],1)]):e._e(),t("ImgList",{directives:[{name:"show",rawName:"v-show",value:!e.statusCode,expression:"!statusCode"}],attrs:{data:e.urls,item:e.item,catData:e.catData,currentAppName:e.currentAppName,index:e.index,videoUrl:e.videoUrl}}),t("div",{staticClass:"relative-link",attrs:{id:"linkTarget"}},[e.statusCode||e.isFixed?e._e():t("BottomLink",{ref:"bottomLink",attrs:{isBought:e.isBought,item:e.item,urls:e.urls,btnDuration:e.loadDuration,payconfig:e.payconfig,isPrivilegeFree:e.isPrivilegeFree,previewTextConfig:e.previewTextConfig},on:{download:e.onDownload,handlerGoto:e.handlerGoto}})],1),t("RecList",{attrs:{tags:e.tags,currentAppName:e.currentAppName,tmplData:e.item,hasLike:e.likeData.length}}),t("LikeList",{attrs:{data:e.likeData,policy:e.policy,eventTime:e.eventTime,requestId:e.requestId,tmplData:e.item,currentAppName:e.currentAppName,otherCollectInfo:e.otherCollectInfo}})],1),e.statusCode||e.footerAdIsShow||!e.isFixed?e._e():t("BottomLink",{ref:"bottomLink",staticClass:"fix-bottom",attrs:{isBought:e.isBought,item:e.item,urls:e.urls,btnDuration:e.loadDuration,payconfig:e.payconfig,isPrivilegeFree:e.isPrivilegeFree,previewTextConfig:e.previewTextConfig},on:{download:e.onDownload,handlerGoto:e.handlerGoto}})],1)},r=[],n=function(){var e=this,t=e._self._c;return e.data.length?t("div",[t("ul",{staticClass:"preview__list"},e._l(e.urls,(function(i,a){return t("li",{key:a,staticClass:"preview__img l-d-flex l-align-items-center"},[t("img",{class:"preview__img-"+e.currentAppName,attrs:{loading:"lazy",src:i,alt:""}}),0==a?t("div",{staticClass:"preview__img-video"},[t("PlayVideo",{attrs:{videoHeight:e.videoHeight,url:e.videoUrl}})],1):e._e()])})),0),t("div",{staticClass:"preview__end l-d-flex l-justify-content-between"},[t("div",{staticClass:"preview__report"},[t("a",{attrs:{href:"javascript:void(0)"},on:{click:function(t){return e.onClickReport()}}},[t("SvgIcon",{attrs:{svgName:"exclamation-16",className:"g-font_size-16"}}),e._v("举报侵权 ")],1),t("a",{attrs:{href:"javascript:void(0)"},on:{click:function(t){return e.onClickFeedback()}}},[e._v(" 涉及未成年违法及不良信息 ")])]),e.adData.img_url?t("div",{staticClass:"preview__qrcode"},[t("div",{staticClass:"preview__qrcode-img"},[t("img",{attrs:{loading:"lazy",src:e.adData.img_url,alt:""}})]),t("p",[e._v(e._s(e.adData.title))])]):e._e()])]):e._e()},l=[],c=i("2f62"),d=i("fe0f"),p=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"show",rawName:"v-show",value:e.videoUrl&&!e.isEnded,expression:"videoUrl&&!isEnded"}],staticClass:"video"},[t("div",{staticClass:"video__player"},[t("video",{ref:"nVideo",attrs:{autoplay:"",muted:"",src:e.videoUrl},domProps:{muted:!0}}),t("div",{staticClass:"video__loading",class:{video__loading_show:e.isLoading}},[e._v("演示加载中...")])]),t("div",{staticClass:"video__wrap"},[t("div",{staticClass:"video__progress",style:{width:e.percent+"%"}})])])},h=[],_={data(){return{percent:0,isLoading:!0,isEnded:!1,videoUrl:""}},props:["url"],mounted(){this.videoUrl=this.url,this.$refs.nVideo.addEventListener("ended",this.onEnded,!1),this.$refs.nVideo.addEventListener("timeupdate",this.onTimeupdate,!1),this.$refs.nVideo.addEventListener("canplay",this.onCanplay,!1),this.$refs.nVideo.addEventListener("error",this.onCancel,!1),this.$refs.nVideo.addEventListener("abort",this.onCancel,!1)},methods:{onTimeupdate(){this.percent=this.$refs.nVideo.currentTime/this.$refs.nVideo.duration*100},onCanplay(){this.isLoading=!1},onEnded(){this.isEnded=!0},onCancel(){this.isLoading=!1,this.isEnded=!0,this.videoUrl=""}},watch:{url:function(){this.isEnded=!1,this.videoUrl=this.url||""}},beforeDestroy(){this.onCancel(),this.$refs.nVideo.removeEventListener("ended",this.onEnded),this.$refs.nVideo.removeEventListener("timeupdate",this.onTimeupdate),this.$refs.nVideo.removeEventListener("canplay",this.onCanplay),this.$refs.nVideo.removeEventListener("error",this.onCancel),this.$refs.nVideo.removeEventListener("abort",this.onCancel)}},u=_,g=(i("7cf2"),i("2877")),f=Object(g["a"])(u,p,h,!1,null,"4daa12e2",null),m=f.exports,v=i("13bc"),y={name:"previewImgList",mixins:[d["a"]],props:{data:{type:Array,default:function(){return[]}},item:{type:Object,default:function(){return{}}},catData:{type:Object,default:function(){return{}}},currentAppName:String,index:Number,videoUrl:String,videoHeight:Number},methods:{...Object(c["b"])("common",["setAppReport"]),onClickReport(){let e=null;try{e=JSON.parse(JSON.stringify(this.item))}catch(t){console.log(t)}this.setAppReport(e)},onClickFeedback(){v["a"].navigateOnNewWindow("http://www.gdjubao.cn/jb/?harml_tyupe=18",!1)}},computed:{...Object(c["c"])("preview",["appPreviewAd","appPreviewCollect"]),cropSuffix(){return{wps:"659x930",et:"659x411",wpp:"659x370",pdf:"652x924"}[this.currentAppName]},adData(){let e=this.appPreviewAd[this.item.moban_app]||{},t=e[this.catData.son_id]||e[this.catData.parent_id]||e.other||{};return t.bottom_qrcode||{}},urls(){let e=Array.isArray(this.data)?this.data:[],t=[];return e.forEach(e=>{if(e){let i=e.slice(e.lastIndexOf("."));".gif"==i?t.push(e):t.push(this.$_cropPic(e,this.cropSuffix))}}),t}},components:{PlayVideo:m}},w=y,b=(i("9071"),Object(g["a"])(w,n,l,!1,null,"71b6d8ca",null)),C=b.exports,k=function(){var e=this,t=e._self._c;return e.data.length&&e.data.length>5?t("div",{staticClass:"preview__like"},[e._m(0),t("ul",{staticClass:"preview__bd l-d-flex l-justify-content-between l-flex-wrap"},[e._l(e.currentData,(function(i,a){return t("li",{directives:[{name:"collect",rawName:"v-collect.display.hover",value:{displayHandler:e.collectMbDisplay(a)},expression:"{\n\t\t\t\tdisplayHandler: collectMbDisplay(index),\n\t\t\t}",modifiers:{display:!0,hover:!0}}],key:i.id+"_"+a,staticClass:"preview__item"},[e.isWpp?t("div",{staticClass:"preview__img",on:{click:function(t){return e.onClickMb(a)}}},[t("div",{staticClass:"preview__img--big"},[t("img",{attrs:{loading:"lazy",src:e.$_cropPic(i.preview_urls&&i.preview_urls[0]&&i.preview_urls[0].image_url||i.thumb_big_url,e.cropSuffix),alt:""}})]),t("div",{staticClass:"preview__img--small l-d-flex"},[t("span",[t("img",{attrs:{loading:"lazy",src:e.$_cropPic(i.preview_urls&&i.preview_urls[1]&&i.preview_urls[1].image_url||i.thumb_big_url,e.wppSuffix),alt:""}})]),t("span",[t("img",{attrs:{loading:"lazy",src:e.$_cropPic(i.preview_urls&&i.preview_urls[2]&&i.preview_urls[2].image_url||i.thumb_big_url,e.wppSuffix),alt:""}})])])]):t("div",{staticClass:"preview__img l-d-flex l-align-items-center",on:{click:function(t){return e.onClickMb(a)}}},[t("img",{attrs:{loading:"lazy",src:e.$_cropPic(i.thumb_big_url,e.cropSuffix),alt:""}})]),t("h5",{staticClass:"preview__item-title g-text-overflow",attrs:{title:i.name},on:{click:function(t){return e.onClickMb(a)}}},[e._v(e._s(e._f("toSubStr")(i.name)))])])})),e._l(e.limitLen,(function(e){return t("li",{key:e,staticClass:"preview__item"})}))],2)]):e._e()},F=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"preview__hd l-d-flex l-justify-content-between"},[t("h5",{staticClass:"preview__title"},[e._v("猜你喜欢")])])}],T=i("3813"),x=i("f348"),D=i("3387"),P={name:"previewFooter",data(){return{statusCode:"",newData:[],hasGet:!1,showData:{},wppSuffix:"61x33"}},props:{data:{type:Array,default:function(){return[]}},policy:String,requestId:String,tmplData:{type:Object,default:function(){return{}}},currentAppName:String,otherCollectInfo:{type:Object,default:function(){return{}}}},methods:{...Object(c["b"])("preview",["setAppPreview"]),handlePreview(e){let t={};t.source_page_element=this.hasGet?"similar_template_change":"similar_template",t.template_position=e+1,t.template_id=this.tmplData.id;let i=x["a"].extend({},this.appPreviewCollect,t);i.policy=this.policy||"",i.ai=this.policy||"",i.request_id=this.requestId||"",this.setAppPreview({data:JSON.parse(JSON.stringify(this.currentData)),index:e,disabledTag:this.appPreviewDisabledTag,collect:i})},onClickMb(e){let t=this.getCommonCollect(e);D["a"].send("click",Object.assign({},t,{act:"preview"})),T["a"].setOriginInfo({scenesEntrance:"prevrec"+(e+1),policy:this.policy}),this.handlePreview(e)},collectMbDisplay(e){return()=>{let t=this.currentData[e]||{};if(!this.showData[t.id]){this.showData[t.id]=!0;let i=this.getCommonCollect(e);D["a"].send("display",i)}}},getCommonCollect(e){let t=this.currentData[e]||{},i=x["a"].convertAppMark(t.moban_app);return{act:"preview",page_name:"preview_page",resource_id:t.id,$f_t:t.file_type,resource_app:t.moban_app,resource_type:52==t.file_type?"library":i,policy:this.policy,ai:this.policy,request_id:this.requestId,$e_n:"resource",$e_t:"resource",$m_n:"below_similar",$e_p:e+1,is_protected:52==t.file_type&&2==t.copyright,pre_resource_id:this.tmplData.id,...this.otherCollectInfo}}},computed:{...Object(c["c"])("preview",["appPreviewCollect","appPreviewDisabledTag"]),cropSuffix(){return{wps:"138x195",wpp:"124x68",et:"138x98"}[this.currentAppName]},isWpp(){return"wpp"==this.currentAppName},limitLen(){return this.currentData&&this.currentData.length?this.currentData.length%4:0},currentData(){return(this.newData&&this.newData.length?this.newData:this.hasGet?[]:this.data).slice(5)}}},$=P,B=(i("a2a5"),Object(g["a"])($,k,F,!1,null,"01f11882",null)),I=B.exports,O=function(){var e=this,t=e._self._c;return e.recList.length?t("div",{staticClass:"preview__rec",class:{"has-like":e.hasLike?"has-like":""}},[t("div",{staticClass:"preview__hd l-d-flex l-justify-content-between"},[t("h5",{staticClass:"preview__title"},[e._v(e._s(e.recTitle))])]),t("ul",{directives:[{name:"collect",rawName:"v-collect.display",value:{page_name:"preview_page",$m_n:"assistant_area",$e_t:"module",assistant_name:e.getAssistantName(e.recList[0]),tag:e.getGroupTags},expression:"{\n\t\t\tpage_name: 'preview_page',\n\t\t\t$m_n: 'assistant_area',\n\t\t\t$e_t: 'module',\n\t\t\tassistant_name: getAssistantName(recList[0]),\n\t\t\ttag: getGroupTags\n\t\t}",modifiers:{display:!0}}],staticClass:"preview__hd l-d-flex l-justify-content-between l-flex-wrap"},e._l(e.recList.slice(0,4),(function(i,a){return t("li",{directives:[{name:"collect",rawName:"v-collect.display.hover",value:{displayHandler:e.collectAdDisplay(a,0,i)},expression:"{\n\t\t\t\tdisplayHandler: collectAdDisplay(index, 0, item),\n\t\t\t}",modifiers:{display:!0,hover:!0}}],key:i.ad_text+"_"+a,staticClass:"preview__item",class:{rec__item:i.children&&i.children.length}},[t("div",{staticClass:"preview__img l-d-flex l-align-items-center",on:{click:function(t){return e.onClickAd(a,0,i)}}},[t("img",{attrs:{loading:"lazy",src:e.$_cropPic(i.ad_url,"138x195"),alt:""}})]),t("h5",{staticClass:"preview__item-title g-text-overflow",attrs:{title:i.ad_text},on:{click:function(t){return e.onClickAd(a,0,i)}}},[e._v(e._s(e._f("toSubStr")(i.ad_text)))]),i.children&&i.children.length?t("div",{staticClass:"rec__item-tags"},e._l(i.children.slice(0,4),(function(i,s){return t("div",{directives:[{name:"collect",rawName:"v-collect.display.hover",value:{displayHandler:e.collectAdDisplay(a,s,i)},expression:"{\n\t\t\t\t\t\tdisplayHandler: collectAdDisplay(index, i, tag),\n\t\t\t\t\t}",modifiers:{display:!0,hover:!0}}],key:i.ad_text+"_"+s,staticClass:"rec__item-tags-item",on:{click:function(t){return t.stopPropagation(),e.onClickAd(a,s,i)}}},[t("span",[e._v(e._s(i.ad_text))])])})),0):e._e()])})),0)]):e._e()},S=[],A=i("d7f7");const N={wps:"06396fb0735311eca2968b3f3eb05cad",et:"5cb42260746911ecb032b5740d443869",wpp:"6cbf57b0746911ecb032b5740d443869"};var E={name:"previewRec",data(){return{recTitle:"",recList:[],showData:{},adId:0}},props:{hasLike:[Boolean,Number],currentAppName:String,tmplData:{type:Object,default:function(){return{}}},tags:{type:Array,default:function(){return[]}}},mixins:[A["a"]],methods:{handleClick(e){this.$_clickNewAD(e)},initRecData(){this.recList=[];const e=N[this.currentAppName||"wps"];if(!e||!this.getGroupTags.length)return;const t={position:e,keyword_group:this.getGroupTags.join(";")};this.$_getNewAD({params:t,cacheTime:9e5}).then(t=>{if("ok"==t.result&&t.data&&t.data[e]&&t.data[e].rec&&t.data[e].rec[0]&&t.data[e].rec[0].extra)try{const i=t.data[e].rec[0].extra;if(this.recTitle=i.title||"",i.list&&i.list.length){this.adId=t.data[e].advertising_detail_id;for(let e of i.list){let t=[],i=this.$_previewRecAdDataInit(e);i.children&&i.children.length&&i.children.map(e=>{let i=this.$_previewRecAdDataInit(e);return i&&t.push(i),e}),i&&(i.children=t,this.recList.push(i))}}}catch(i){console.log(i)}}).catch(e=>{console.log(e)})},getAssistantName(e){return e&&e.ad_type?{resume_editor:"简历助手",resume:"简历助手",easyedit:"一键出图",chuangkit:"金山海报"}[e.ad_type]:""},getAssistantFileType(e){return e&&e.ad_type?{resume_editor:1,resume:1,easyedit:15,chuangkit:15}[e.ad_type]:""},getCommonCollect(e,t,i){let a=x["a"].convertAppMark(this.tmplData.moban_app),s=0==t?e+1:t+1;return{page_name:"preview_page",$m_n:`assistant_area[${e+1}]`,$e_n:"resource",$e_t:"resource",$e_p:s,resource_id:this.tmplData.id,resource_app:this.tmplData.moban_app,resource_type:52==this.tmplData.file_type?"library":a,resource_name:this.tmplData.name,$f_t:this.tmplData.file_type,ad_id:this.adId,ad_name:i.ad_text,ad_type:0==t?"banner":"text_link",assistant_name:this.getAssistantName(i),jump_type:i.ad_type,pic_url:i.ad_url,ad_url:this.$_getAdFullUrl(i)}},onClickAd(e,t,i){let a=this.getCommonCollect(e,t,i);D["a"].send("click",a),this.handleClick(i)},collectAdDisplay(e,t,i){return()=>{if(!this.showData[i.text]){this.showData[i.text]=!0;let a=this.getCommonCollect(e,t,i);D["a"].send("display",a)}}}},watch:{tags:{handler(e){e.length&&this.initRecData()},immediate:!0,deep:!0}},computed:{...Object(c["c"])("user",["isLogin"]),getGroupTags(){let e=[];return this.tags&&this.tags.length&&this.tags.forEach(t=>{t.name&&e.push(t.name)}),e}}},j=E,L=(i("bea8"),Object(g["a"])(j,O,S,!1,null,"62b470ed",null)),R=L.exports,M=i("beeb"),G=function(){var e=this,t=e._self._c;return t("div",{staticClass:"preview__link"},[e._v(" - "+e._s(e.previewTitle)+"， "),e.isPrivilegeSwitchOn$?[t("span",{staticClass:"preview__btn preview__newpri",class:[{"is-download":e.isDownload}],style:e.styleObj,on:{click:e.handleTextClick}},[e.textConfig.icon?t("img",{attrs:{loading:"lazy",src:e.textConfig.icon,alt:"",srcset:""}}):e._e(),e._v(" "+e._s(e.textConfig.title)+" ")])]:[e.previewBtnText?t("span",{staticClass:"preview__btn",class:[e.btnColor,{"is-download":e.isDownload}],on:{click:e.handleClick}},[e._v(" "+e._s(e.previewBtnText)+" ")]):t("Price",{ref:"retailEl",attrs:{isPrivilegeFree:e.isPrivilegeFree,mark:"link",item:e.item,isBought:e.isBought},on:{click:e.retailBuy}})],e._v(" - ")],2)},U=[],H=function(){var e=this,t=e._self._c;return t("div",{staticClass:"price",class:{"price_max-width":"preview"==e.mark}},["preview"==e.mark?t("div",{staticClass:"price-preview"},[e.originPrice&&!e.hidePriceLink?t("div",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:e.handleDisplay("retail")},expression:"{displayHandler: handleDisplay('retail')}",modifiers:{display:!0}}],staticClass:"price__link",class:{"text-undeline":!e.isCanuse$},on:{click:e.openLink}},[t("span",{class:{"price-del":e.finalPrice}},[e._v("￥"+e._s(e._f("changPrice")(e.originPrice)))]),t("p",[e._v(e._s(e.finalPrice||"立即购买"))])]):e._e()]):"link"==e.mark?t("div",{staticClass:"price__text",class:{"svip-link":"svip"==e.classType$},on:{click:function(t){return e.handleClick()}}},[e._v(" "+e._s(e.isDiscount?"折后":"原价")+"￥ "),e._l(e.btnPriceSplit,(function(i,a){return t("span",{key:a,staticClass:"price__btn__discount"},[e._v(e._s(`${i}${a?"":"."}`))])})),e.isDiscount?t("span",{staticClass:"price__btn__origin"},[e._v(" ￥"+e._s(e._f("changPrice")(e.originPrice)))]):e._e(),e._v(" 立即购买 ")],2):e._e()])},q=[],W={name:"price",props:{mark:{type:String,require:!0},item:{type:Object,default:()=>({})},isBought:{type:Boolean,default:!1},hideLink:{type:Boolean,default:!1},skeleton:{type:Boolean,default:!1},isPrivilegeFree:{type:Boolean,default:!1},isFetchingMbStatus:{type:Boolean,default:!1}},data(){return{memeberIdMap:{10:"nomal",12:"docer",20:"wps",40:"svip"},lowerPrice:0,lowerMember:10,svipDiscount:"",docerDiscount:""}},mixins:[d["a"]],computed:{...Object(c["c"])("user",["userInfo","isLogin","isClientLogin"]),freeOrEmptyPrice(){return this.isFree$?"免费":!this.originPrice||this.isOnlyMemberFree$?this.memberType+"免费":""},memberType(){return"svip"==this.classType$?"超级会员":"稻壳会员"},useTypeText(){return this.isCopyRight?"阅读全文":"下载"},isCopyRight(){return 52==this.item.file_type&&2==this.item.copyright},finalPrice(){if(this.isFree$||!this.originPrice||!this.isClientLogin)return"";if(this.isCanuse$&&!this.isPrivilegeSwitchOn$)return this.isBought$?"已购":this.userInfo.isSuperMember?"超级会员免费":(this.userInfo.isDocerMember?"稻壳会员免费":this.isPrivilegeFree$?"简历特权":"稻壳会员免费")+this.useTypeText;let e="";if(this.item.member_price_list&&this.item.member_price_list.length){let t=this.item.member_price_list.find(e=>e&&e.member_id==this.userInfo.memberid)||{};e=t.price&&t.price<this.originPrice?t.price:""}else this.item.discounted_price&&this.item.discounted_price<this.originPrice?e=this.item.discounted_price:this.userInfo.isDocerMember&&(e=.8*this.originPrice);return e?"￥"+(e/100).toFixed(2):""},originPrice(){return this.item.price||this.item.original_price||0},hidePriceLink(){return this.isPrivilegeSwitchOn$&&this.isCanuse$||this.isFetchingMbStatus}},methods:{handleClick(){let e={act:"retail",pay_type:"retail"};this.isLibrary$&&Object.assign(e,{report:this.getReportParams()}),this.$emit("click",e)},openLink(){!this.isCanuse$&&this.handleClick()},handleDisplay(e){return()=>{this.$emit("handleDisplay",e,this.isLibrary$?this.getReportParams():{})}},getReportParams(){return{resource_sell_type:{1:"免费",2:"仅会员",4:"仅零售",6:"会员免费加零售"}[this.item.downloadable||6],original_price:this.originPrice,buy_price:this.item.discounted_price||this.originPrice,discount:this.item.current_discount}}},filters:{changPrice(e){return(e/100).toFixed(2)||0}}},V=W,J=(i("a1fc"),Object(g["a"])(V,H,q,!1,null,"fade3fde",null)),z=J.exports,K={props:{isBought:{type:Boolean,default:!1},item:{type:Object,default:()=>({})},urls:{type:Array,default:()=>[]},btnDuration:{type:[Number,String],default:""},isPrivilegeFree:{type:Boolean,default:!1},previewTextConfig:{type:Object,default:function(){return{}}},payconfig:{type:String,default:""}},mixins:[d["a"]],methods:{...Object(c["b"])("download",["setDownloadData"]),...Object(c["b"])("user",["checkUserInfo"]),handleClick(){this.isClientLogin?this.isCanuse$?this.handleDownload():this.isOnlyRetail$||this.onClickMemberBuyBtn():v["a"].login()},handleTextClick(){var e;switch(this.btnConfig.btn_type){case"freeLoginBtn":case"loginBtn":let t=this.getCommonCollect();t=x["a"].extend(t,{$e_t:"button",act:"login",is_protected:this.isCopyRight}),D["a"].send("click",t),v["a"].login();break;case"memberPayBtn":this.onClickMemberBuyBtn(null===(e=this.btnConfig)||void 0===e?void 0:e.config);break;case"retailPayBtn":this.retailBuy();break;case"freeUseBtn":case"default":case"purchasedBtn":this.handleDownload();break;default:console.error("btn_type",this.previewTextConfig.btn_type);break}},handleBuyMember(e,t){let i=this.userInfo.isWpsMember?"opensvip":"opendvip";this.isCopyRight&&(i="library_read");let a=this.getCommonCollect();a=x["a"].extend(a,{$e_t:"button",$p_t:i,pay_key:e,act:"pay",is_protected:this.isCopyRight}),D["a"].send("click",a);let s=T["a"].getPosition(),o=T["a"].getCsource();D["a"].sendPayCollect({pay_key:e,mb_id:this.item.id,belong_func:this.item.file_type}),window.gotoTag({type:"member",csource:o,position:s,recommendid:this.recommendId$,payKey:e,resid:this.item.id,reason:"preview>imglist>buttom>template",fileType:this.item.file_type,sku_key:null===t||void 0===t?void 0:t.sku_key,payconfig:this.payconfig||(null===t||void 0===t?void 0:t.payconfig)||""})},handleBuyMb(e){let t=JSON.parse(JSON.stringify(this.item));this.setDownloadData({id:t.id,data:t});var i=T["a"].getSubChannel();window.gotoTag({type:"mb",id:t.id,fileType:t.file_type,payKey:e,csource:i+"_preview_end"})},onClickMemberBuyBtn(e){this.checkUserInfo().then(()=>{let t=D["a"].createPayKey();T["a"].setOriginInfo({btnName:"previewendtip",btnPage:"preview",preview:this.item.video_url?"dy1":"sc1"});let i=()=>{this.handleBuyMember(t,e)};this.handlerGoto(i)}).catch(()=>{console.log("request allinfo")})},handlerGoto(e){this.$emit("handlerGoto",e)},handleDownload(){this.isDownload||this.checkUserInfo().then(()=>{let e;e=this.isPrivilegeSwitchOn$?(this.isFree$?"free":this.isBought$?"retail":"vip")+"_download":this.isFree$?"free_download":this.userInfo.isMember?(this.userInfo.isSuperMember?"s":"d")+"vip_download":this.isPrivilegeFree$?"privilege_download":"retail_download",this.isCopyRight&&(e="library_read");let t=D["a"].createPayKey(),i=this.getCommonCollect();if(i=x["a"].extend(i,{$e_t:"button",$d_t:e,is_protected:this.isCopyRight}),this.isCanuse$)i.act="download",i.download_key=x["a"].getRandomKey(),this.$emit("download",i.download_key);else{i.act="pay_download",i.download_key=t,T["a"].setOriginInfo({btnName:"previewendtip",btnPage:"preview",preview:this.item.video_url?"dy1":"sc1"});let e=()=>{this.handleBuyMb(t)};this.handlerGoto(e)}D["a"].send("click",i)}).catch(()=>{console.log("request allinfo")})},retailBuy(e){let t=null;if(this.isClientLogin){t=D["a"].createPayKey();let e=()=>{this.handleBuyMb(t)};this.handlerGoto(e)}else v["a"].login();let i=this.getCommonCollect(),a="retail";this.isCopyRight&&(a="library_read"),D["a"].send("click",Object.assign(i,{$e_t:"button",$p_t:a,act:this.isClientLogin?"pay_download":"login",download_key:t,is_protected:this.isCopyRight},e.report))},getCommonCollect(){let e=this.item||{},t=x["a"].convertAppMark(e.moban_app);return{page_name:"preview_page",resource_id:e.id,$f_t:e.file_type,$e_n:"undertext",resource_app:e.moban_app,resource_type:52==e.file_type?"library":t,second_cat_id:e.catId||"",second_cat_name:e.catName||"",third_cat_name:e.childCatName||"",third_cat_id:e.childCatId||"",$m_n:"rightside_pay",$track:"",is_protected:52==e.file_type&&2==e.copyright,button_text:this.isPrivilegeSwitchOn$?this.textConfig.title:this.previewBtnText||this.getRetailBtnText(),duration:this.btnDuration,...this.appPreviewCollect||{}}},displayReport(){D["a"].send("display",{$e_t:"button",...this.getCommonCollect()})},getRetailBtnText(){const e=this.$refs.retailEl&&this.$refs.retailEl.retailPrice;return e?`${e.isDiscount?"折后":"原价"}￥${e.retailPrice}`:"仅零售"}},computed:{...Object(c["c"])("user",["userInfo","isClientLogin"]),...Object(c["c"])("download",["downloadList"]),...Object(c["c"])("common",["isDarkSkin"]),...Object(c["c"])("preview",["appPreviewCollect"]),btnColor(){return"preview__btn-"+this.classType$},previewBtnText(){return this.isCanuse$?this.downloadText:this.isOnlyRetail$?"":this.memberBtnText},downloadText(){return this.isDownload?"正在下载":(this.isFree$?"免费":"立即")+this.useTypeText},previewTitle(){return this.isCopyRight?"试读已结束":`${this.isLibrary$?"文库":"模板"}样式${this.countLessPage?"剩余"+this.countLessPage:"已全部展示"}`},countLessPage(){return!this.urls.length||!this.item.page||this.urls.length>=this.item.page?null:Math.floor(100*(1-this.urls.length/this.item.page))+"%"},isCopyRight(){return 52==this.item.file_type&&2==this.item.copyright},useTypeText(){return this.isCopyRight?"阅读全文":"下载"},memberBtnText(){return("svip"==this.classType$?"超级会员免费":"稻壳会员免费")+this.useTypeText},isDownload(){let e=this.item.id+"_"+(this.item.resource_type||1);return this.downloadList[e]&&this.downloadList[e].status==M["o"].progress},btnConfig(){var e;return(null===(e=Object.values(this.previewTextConfig))||void 0===e?void 0:e[0])||{}},textConfig(){var e;return(null===(e=this.btnConfig)||void 0===e?void 0:e.config)||{}},styleObj(){return{"--font-color":this.textConfig[`font${this.isDarkSkin?"_dark":""}_color`],"--font-color-hover":this.textConfig[`font${this.isDarkSkin?"_dark":""}_color_hover`],"--font-color-active":this.textConfig[`font${this.isDarkSkin?"_dark":""}_color_click`]}}},components:{Price:z}},X=K,Y=(i("2d4c"),Object(g["a"])(X,G,U,!1,null,"0488b198",null)),Q=Y.exports,Z={data(){return{netStatus:M["s"],footerAdIsShow:!1,isFixed:!1,visMonitor:null,linkElm:null,loadDuration:null,hasReport:!1}},props:{urls:{type:Array,default:function(){return[]}},likeData:{type:Array,default:function(){return[]}},policy:String,eventTime:String,requestId:String,item:{type:Object,default:function(){return{}}},catData:{type:Object,default:function(){return{}}},isBought:Boolean,statusCode:String,hasChangeBought:Boolean,currentAppName:String,index:Number,videoUrl:String,otherCollectInfo:{type:Object,default:function(){return{}}},tags:{type:Array,default:function(){return[]}},payconfig:{type:String,default:""},loadBeginTime:{type:[Number,String],default:""},isPrivilegeFree:{type:Boolean,default:!1},previewTextConfig:{type:Object,default:function(){return{}}}},methods:{onDownload(e){this.$emit("download",e)},handlerGoto(e){this.$emit("handlerGoto",e)},wheelHandle(e){this.$emit("wheelHandle",e)},footerCoverIsShow(e){this.footerAdIsShow=e},observeLinkPosition(){this.visMonitor=new IntersectionObserver(e=>{this.statusCode||e.forEach(e=>{this.isFixed=!e.isIntersecting})}),this.linkElm=document.getElementById("linkTarget"),this.linkElm&&this.visMonitor.observe(this.linkElm)}},computed:{...Object(c["c"])("common",["isDarkSkin"])},watch:{statusCode(){this.statusCode||this.hasReport?this.hasReport&&(this.hasReport=!1):(this.loadDuration=(new Date).getTime()-this.loadBeginTime,this.$nextTick(()=>{const e=this.$refs.bottomLink;e&&(e.displayReport&&e.displayReport(),this.hasReport=!0)}),!this.visMonitor&&this.observeLinkPosition())}},components:{ImgList:C,LikeList:I,RecList:R,BottomLink:Q},beforeDestroy(){this.linkElm&&this.visMonitor.unobserve(this.linkElm),this.visMonitor=null,this.linkElm=null}},ee=Z,te=(i("cd99"),Object(g["a"])(ee,o,r,!1,null,"f4ccde3c",null)),ie=te.exports,ae=function(){var e=this,t=e._self._c;return t("div",{staticClass:"preview__top"},[t("h4",{staticClass:"preview__title g-multiline-text g-multiline-text-2",attrs:{title:e.item.name}},[e._v(e._s(e._f("toSubStr")(e.item.name)))]),t("div",{staticClass:"preview__meta l-d-flex l-justify-content-between"},[2!=e.item.resource_type?t("div",{staticClass:"preview__meta-fav l-d-flex l-align-items-center",on:{click:e.onClickFav}},[t("SvgIcon",{attrs:{svgName:e.svgStarName,className:"g-font_size-16"}}),t("i",{staticClass:"preview__num"},[e._v("收藏")]),e.collectTipShow?t("div",{staticClass:"fav__sidetip",on:{click:function(e){e.stopPropagation()}}},[t("CollectTip",{attrs:{collectTipShow:e.collectTipShow,qrCodeImg:e.qrCodeImg,resourceType:e.resourceType},on:{favClose:e.favClose,FavGo:e.FavGo}})],1):e._e()],1):e._e(),t("span",{staticClass:"l-d-flex l-align-items-center"},[t("SvgIcon",{attrs:{svgName:"hot-16",className:"g-font_size-16"}}),t("i",{staticClass:"preview__num",attrs:{title:e._f("toUnitWInt")(e.item.p_count,"w+")}},[e._v(e._s(e._f("toUnitWInt")(e.item.p_count,"w+")))])],1),t("span",{staticClass:"l-d-flex l-align-items-center"},[t("SvgIcon",{attrs:{svgName:"page-16",className:"g-font_size-16"}}),t("i",{staticClass:"preview__num"},[e._v(e._s(e.item.page||0)+"页")])],1)]),t("dl",{staticClass:"preview__info"},[t("dt",[e._v("作者")]),t("dd",{staticClass:"g-text-overflow",attrs:{title:e.item.author||""}},[e._v(e._s(e.item.author||""))]),t("dt",[e._v("尺寸")]),t("dd",[e._v(e._s(2!=e.item.resource_type?"A4":"16:9"))]),t("dt",[e._v("授权类型")]),t("dd",[t("div",{staticClass:"message-icon l-d-flex l-align-items-center"},[e._v("个人学习不支持商用"),t("SvgIcon",{attrs:{svgName:"preview-info-16"}})],1),t("div",{staticClass:"right-tip"},[t("div",{staticClass:"right-tip__icon"}),e._v(" "+e._s(e.rightText)+" ")])]),t("dt",[e._v("提醒")]),t("dd",[e._v(e._s(e.remindText))])]),e.isFixed?t("div",{style:{height:e.fillDomHeight+"px"}}):e._e(),t("div",{class:{"is-fixed":e.isFixed},attrs:{id:"preview-btngroup"}},[e.isFixed?t("h4",{staticClass:"preview__title g-multiline-text g-multiline-text-2",attrs:{title:e.item.name}},[e._v(e._s(e._f("toSubStr")(e.item.name)))]):e._e(),e.isFree$||!e.hasChangeBought||e.statusCode?e._e():t("div",{staticClass:"preview__price l-d-flex l-justify-content-center"},[t("Price",{attrs:{mark:"preview",isPrivilegeFree:e.isPrivilegeFree,isFetchingMbStatus:e.isFetchingMbStatus,item:e.item,isBought:e.isBought},on:{click:e.priceLinkBuy,handleDisplay:e.priceDisplayCollect}})],1),e.statusCode!=e.netStatus.tempDisable?[e.isDownload?t("div",{staticClass:"preview__btn-wrap preview__download",class:{"preview__btn-free":e.isFree$}},[t("ItemDownload",{attrs:{id:e.item.id,resourceType:e.item.resource_type},on:{retry:e.handleRetry}})],1):e._e(),e.isShow&&e.hasChangeBought&&!e.isDownload?t("div",{staticClass:"preview__btn-wrap",class:{"preview__btn-free":e.isFree$}},[e.isPrivilegeSwitchOn$?[e.isFetchingMbStatus?e._e():t("PreviewOpBtn",{attrs:{previewBtnConfig:e.previewBtnConfig,freeTrialOpData:e.freeTrialOpData,freeTrialBtnText:e.freeTrialBtnText},on:{click:e.handleBtnClick}})]:[e.isCanuse$?t("a",{staticClass:"preview__btn",class:e.downBtnClass,attrs:{href:"javascript:void(0)"},on:{click:function(t){return t.stopPropagation(),e.handleDownload()}}},[e._v(e._s(e.downloadBtnText))]):t("a",{staticClass:"preview__btn",class:e.memberBtnClass,attrs:{href:"javascript:void(0)"},on:{click:function(t){return t.stopPropagation(),e.onClickMemberBuyBtn("previewbtn")}}},[e._v(e._s(e.memberBtnText))])],e.downBtnTag&&!e.isFree$&&e.isClientLogin?t("div",{directives:[{name:"collect",rawName:"v-collect.display",value:{$m_n:"rightside_pay",$e_n:"button_lable",$e_t:"button",text:e.downBtnTag,...e.getCommonCollect()},expression:"{$m_n: 'rightside_pay', $e_n: 'button_lable', $e_t: 'button', text: downBtnTag, ...getCommonCollect()}",modifiers:{display:!0}}],staticClass:"preview__btn-tag"},[e._v(e._s(e.downBtnTag))]):e._e()],2):e._e(),t("ResumeBtn",{attrs:{baseData:e.item}})]:t("div",{staticClass:"preview__btn-wrap preview__btn-disable"},[t("a",{staticClass:"preview__btn"},[e._v("资源已下架")])])],2),e.adData&&e.adData.ad_text&&e.isShowAd&&!e.isPdf?t("div",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:e.collectAdDisplay()},expression:"{\n\t\tdisplayHandler: collectAdDisplay()\n\t}",modifiers:{display:!0}}],staticClass:"preview__tip",on:{click:e.onClickAd}},[t("a",{attrs:{href:"javascript:void(0)"}},[t("p",[e._v(e._s(e.adData.children&&e.adData.children.dic_text))]),t("p",[e._v(e._s(e.adData.ad_text))])])]):e._e()])},se=[],oe=i("4f74"),re=function(){var e=this,t=e._self._c;return t("div",{staticClass:"download",class:{"download--error":!e.isDownloadErr}},[t("div",{staticClass:"download__progress-wrap"},[t("span",{staticClass:"download__progress",style:{width:e.progress+"%"}})]),e.isDownload?t("div",{staticClass:"download__tip"},[e._v("下载"+e._s(e.progress)+"%")]):t("div",{staticClass:"download__tip l-d-flex l-justify-content-between l-align-items-center"},[t("span",{staticClass:"download__error l-d-flex l-justify-content-between l-align-items-center"},[e._v(e._s(e.msg)),t("SvgIcon",{attrs:{svgName:"download-error"}})],1),t("span",{staticClass:"download__btns l-d-flex l-justify-content-between l-align-items-center"},[t("span",{on:{click:function(t){return t.stopPropagation(),e.onClickRetry.apply(null,arguments)}}},[t("SvgIcon",{attrs:{svgName:"download-retry"}})],1),t("span",{on:{click:function(t){return t.stopPropagation(),e.onClickClose.apply(null,arguments)}}},[t("SvgIcon",{attrs:{svgName:"download-cancel"}})],1)])])])},ne=[],le={data(){return{}},props:["id","resourceType"],methods:{...Object(c["b"])("download",["setDownloadStatus"]),onClickRetry(){this.$emit("retry")},onClickClose(){this.setDownloadStatus({id:this.id+"_"+(this.resourceType||1),status:M["o"].finish})}},computed:{...Object(c["c"])("download",["downloadList"]),downloadObj(){return this.downloadList[this.id+"_"+(this.resourceType||1)]},isDownload(){return this.downloadObj&&this.downloadObj.status==M["o"].progress},progress(){return this.downloadObj&&this.downloadObj.progress},isDownloadErr(){return this.downloadObj&&this.downloadObj.status==M["o"].error},msg(){return this.downloadObj&&this.downloadObj.msg||"下载失败"}}},ce=le,de=(i("4b0f"),Object(g["a"])(ce,re,ne,!1,null,"1f080d0e",null)),pe=de.exports,he=function(){var e=this,t=e._self._c;return e.collectTipShow?t("div",{staticClass:"fav kf-am-fade-in"},[t("SvgIcon",{staticClass:"item-svg",attrs:{svgName:"arrow-top-16"}}),t("div",{staticClass:"fav-main"},[t("div",{staticClass:"fav-close",on:{click:function(t){return t.stopPropagation(),e.close.apply(null,arguments)}}},[t("SvgIcon",{attrs:{svgName:"icon-close"}})],1),t("div",{staticClass:"fav-tip"},[t("SvgIcon",{attrs:{svgName:"fav-success-16"}}),e._v("收藏成功 ")],1),t("div",{staticClass:"collect-view"},[e._v(" 可前往"),t("a",{staticClass:"fav-a",on:{click:function(t){return t.stopPropagation(),e.goCollection.apply(null,arguments)}}},[e._v("收藏")]),e._v("查看 ")])])],1):e._e()},_e=[],ue={name:"collect-tip",data(){return{imgLoaded:!1}},props:{collectTipShow:Boolean,qrCodeImg:{type:String,default:""},resourceType:String},computed:{isLibrary(){return"library"===this.resourceType}},methods:{close(){this.$emit("favClose")},goCollection(){this.$emit("FavGo")}}},ge=ue,fe=(i("ffde"),Object(g["a"])(ge,he,_e,!1,null,"650ebcad",null)),me=fe.exports,ve=function(){var e=this,t=e._self._c;return e.btnText?t("button",{staticClass:"resume__btn",on:{click:function(t){return t.stopPropagation(),e.jumpToResumeEditor()}}},[e._v(e._s(e.btnText))]):e._e()},ye=[],we=i("c8fc"),be={name:"ResumeBtn",props:["baseData"],mixins:[A["a"]],data(){return{btnText:"",templateId:-1}},watch:{baseData:{handler(){this.resumeBtnPageInit()},deep:!0,immediate:!0}},methods:{resumeBtnPageInit(){this.btnText="",this.baseData&&this.baseData.id&&this.getOnlineTemplateConfig(this.baseData.id).then(()=>{this.getBtnOpConfig()}).catch(()=>!1)},getBtnOpConfig(){return new Promise((e,t)=>{let i=M["b"].PREVIEW_RESUME_ONLINE_EDIT_BUTTON,a=this._getNewADParams({position:i});we["a"].get("rec").fetchOpConfig({data:a}).then(a=>{a&&a.data&&a.data[i]&&a.data[i].rec&&a.data[i].rec[0]&&a.data[i].rec[0].pic_name?(this.btnText=a.data[i].rec[0].pic_name,D["a"].send("display",this.collectParams()),e()):t()}).catch(()=>{t()})})},getOnlineTemplateConfig(e){return new Promise((t,i)=>{we["a"].get("assess").getOnlineTemplateConfigByFileTemplateId({data:{id:e}}).then(a=>{a&&a.data&&a.data.id==e&&a.data.template_id&&a.data.resume?(this.templateId=a.data.template_id,t()):i()}).catch(()=>{i()})})},jumpToResumeEditor(){window.gotoTag({type:"resume",resume_type:"edit_page",link:`https://docerserver.wps.cn/resume_editor/template/${this.templateId}?entity_template_id=${this.baseData.id}`,entryTo:"writernew_prevrec_online_edit",position:"newfile2019_prevrec_online_edit"}),D["a"].send("click",this.collectParams())},collectParams(){return{$e_n:"resume_edit",$e_t:"button",resource_id:this.baseData.id,resource_name:this.baseData.name,resume_id:this.templateId}}}},Ce=be,ke=(i("4806"),Object(g["a"])(Ce,ve,ye,!1,null,"3c272016",null)),Fe=ke.exports,Te=function(){var e=this,t=e._self._c;return t("div",[t("a",{staticClass:"preview__btn preview__newpri",style:e.processStyleObj,attrs:{id:"newpri",href:"javascript:void(0)"},on:{click:function(t){return t.stopPropagation(),e.handleClick.apply(null,arguments)}}},[e.freeTrialBtnText?[t("span",[e._v(e._s(e.freeTrialBtnText))]),e.freeTrialBadgeText?t("div",{staticClass:"badge"},[e._v(" "+e._s(e.freeTrialBadgeText)+" ")]):e._e()]:[e.btnConfig.icon?t("img",{attrs:{loading:"lazy",src:e.btnConfig.icon,alt:"",srcset:""}}):e._e(),e._v(" "+e._s(e.btnConfig.title)+" ")]],2)])},xe=[],De={props:{previewBtnConfig:{type:Object,default:function(){return{}}},freeTrialOpData:{type:Object,default:function(){return{}}},freeTrialBtnText:{type:String,default:""}},computed:{...Object(c["c"])("common",["isDarkSkin"]),btnData(){var e;return console.log(Object.values(this.previewBtnConfig)),(null===(e=Object.values(this.previewBtnConfig))||void 0===e?void 0:e[0])||{}},freeTrialBadgeText(){return this.freeTrialOpData&&this.freeTrialOpData.new_btn_subscript||""},btnConfig(){var e;return(null===(e=this.btnData)||void 0===e?void 0:e.config)||{}},processStyleObj(){let e=this.btnConfig;return{"--background-color":e[`btn${this.isDarkSkin?"_dark":""}_color`],"--background-color-hover":e[`btn${this.isDarkSkin?"_dark":""}_color_hover`],"--background-color-active":e[`btn${this.isDarkSkin?"_dark":""}_color_click`],"--font-color":e[`font${this.isDarkSkin?"_dark":""}_color`],"--font-color-hover":e[`font${this.isDarkSkin?"_dark":""}_color_hover`],"--font-color-active":e[`font${this.isDarkSkin?"_dark":""}_color_click`]}}},methods:{handleClick(){this.$emit("click",this.btnData)}}},Pe=De,$e=(i("30d4"),Object(g["a"])(Pe,Te,xe,!1,null,"1159d3fe",null)),Be=$e.exports,Ie={name:"previewSidebarTop",data(){return{rightText:"数字产品本身（包括但不限于文字模板、表格模板、演示模板等）及其包含的全部素材（包括但不限于字体、图片、图标、文字框、艺术字等）均不支持商用，仅能为个人学习，研究或欣赏目的使用。",isShow:!1,linkTextStyle:{"--linktextcolor":'var(--theme-wps-01")'},netStatus:M["s"],adData:{},qrCodeImg:"",qrCodeADData:{}}},mixins:[d["a"],A["a"],oe["a"]],props:{item:{type:Object,default:function(){return{}}},isBought:Boolean,hasChangeBought:Boolean,index:Number,otherCollectInfo:{type:Object,default:function(){return{}}},isFav:Boolean,isShowAd:Boolean,resourceType:[String,Number],statusCode:{type:String,default:""},collectTipShow:Boolean,downBtnTag:{type:String,default:""},isFixed:{type:Boolean,default:!1},fillDomHeight:{type:Number,default:0},payconfig:{type:String,default:""},freeTrialBtnText:{type:String,default:""},freeTrialOpData:{type:Object,default:function(){return{}}},isPrivilegeFree:{type:Boolean,default:!1},isFetchingMbStatus:{type:Boolean,default:!1},previewBtnConfig:{type:Object,default:function(){return{}}}},mounted(){x["a"].getUserFinish.then(()=>{this.isShow=!0}),this.getAd()},methods:{...Object(c["b"])("download",["setDownloadData"]),...Object(c["b"])("user",["checkUserInfo"]),...Object(c["b"])("fav",["setFavAdd","setFavRemove","changeFav"]),getAd(){this.$_ad_getTiance({params:{position:M["b"].TIANCE_PREVIEW_DOWNLOAD_TEXT},timeout:3e3}).then(e=>{this.adData=this.$_tiance_dataInit(e&&e.data||[])[M["b"].TIANCE_PREVIEW_DOWNLOAD_TEXT]||{}})},handleBuyMember(e,t,i){let a=M["e"].isPrivilegeSwitchOn?{pay_type:"open_"+(null===i||void 0===i?void 0:i.sku_key)}:{},s=this.getCommonCollect();s=x["a"].extend(s,{$e_t:"button",$e_n:"open_vip_download",$m_n:"rightside_pay",pay_key:e,act:"pay",...a}),t&&D["a"].send("click",s);let o=T["a"].getPosition(),r=T["a"].getCsource();D["a"].sendPayCollect({pay_key:e,mb_id:this.item.id,belong_func:this.item.file_type}),window.gotoTag({type:"member",csource:r,position:o,recommendid:this.recommendId$,payKey:e,resid:this.item.id,reason:"preview>sidebar>top>template",payconfig:this.payconfig||(null===i||void 0===i?void 0:i.payconfig)||"",fileType:this.item.file_type,sku_key:null===i||void 0===i?void 0:i.sku_key})},handleBuyMb(e){T["a"].setOriginInfo({btnName:"previewretail"});let t=JSON.parse(JSON.stringify(this.item));this.setDownloadData({id:t.id,data:t}),window.gotoTag({type:"mb",id:t.id,fileType:t.file_type,payKey:e})},onClickMemberBuyBtn(e,t){if(this.freeTrialBtnText)return this.$emit("getFreeMember","right");if(!this.isLogin){let e=this.getCommonCollect();e=x["a"].extend(e,{$e_t:"button",$e_n:"login_download",$m_n:"rightside_pay",act:"login"}),D["a"].send("click",e)}this.checkUserInfo().then(()=>{let i=D["a"].createPayKey();T["a"].setOriginInfo({btnName:e,btnPage:"preview",preview:this.item.video_url?"dy1":"sc1"});let a=()=>{this.handleBuyMember(i,e,t)};this.handlerGoto(a)}).catch(e=>{console.log("request allinfo",e)})},copyData(){let e={};try{e=JSON.parse(JSON.stringify(this.adData))}catch(t){console.log(t)}return e},onClickAd(){let e=this.copyData(),t=D["a"].createPayKey();e.payKey=t,e.reason="preview>sidebarBottom>Ad",e.funcName="pretext",T["a"].setOriginInfo({scenesEntrance:"pretext"});let i=this.getCommonCollect();i=x["a"].extend(i,{ad_id:M["b"].NEWFILE2019_PREVIEW_RIGHT_TEXT,text:""+(e.children&&e.children.dic_text+e.children.button_text||""),$e_t:"ad",$e_n:"ad_link",$m_n:"rightside_pay",special_id:this.item.special_id,url:e.pic_link,jump_type:e.ad_type}),D["a"].send("click",i),this.$_clickNewAD(e)},collectAdDisplay(){return()=>{if(this.isShowAd){let e=this.copyData(),t=this.getCommonCollect();t=x["a"].extend(t,{ad_id:M["b"].NEWFILE2019_PREVIEW_RIGHT_TEXT,text:""+(e.children&&e.children.dic_text+e.children.button_text||""),$e_t:"ad",$e_n:"ad_link",$m_n:"rightside_pay",special_id:this.item.special_id,url:e.pic_link,jump_type:e.ad_type}),D["a"].send("display",t)}}},priceLinkBuy(e={}){let t=e.act,i=this.getCommonCollect();if(this.isClientLogin){let a=D["a"].createPayKey();i.pay_key=a,e.download_key=a,T["a"].setOriginInfo({btnName:"previewretail",btnPage:"preview",preview:this.item.video_url?"dy1":"sc1"});let s=()=>{"retail"==t?this.handleBuyMb(a):this.handleBuyMember(a)};this.handlerGoto(s)}else v["a"].login();let a="member"==t?"vip_discount":"retail_download";this.isCopyRight&&(a="library_read"),D["a"].send("click",Object.assign(i,{$e_t:"button",$e_n:a,$m_n:"rightside_pay",act:this.isClientLogin?"pay":"login",is_protected:this.isCopyRight},e.report))},priceDisplayCollect(e,t={}){let i=this.getCommonCollect();D["a"].send("display",Object.assign(i,{$e_t:"button",$e_n:"member"==e?"vip_discount":"retail_download",$m_n:"rightside_pay"},t))},handlerGoto(e){this.$emit("handlerGoto",e)},handleDownload(e){this.btnTagClickCollect(),this.checkUserInfo().then(()=>{let t=this.isFree$?"free_download":this.userInfo.isDocerMember?"vip_download":this.isPrivilegeFree?"download_open_privilege":"retail_download",i=D["a"].createPayKey(),a=this.getCommonCollect();if(a=x["a"].extend(a,{$e_t:"button",$e_n:t,$m_n:"rightside_pay"}),T["a"].setOriginInfo({btnName:"previewbtn",btnPage:"preview",preview:this.item.video_url?"dy1":"sc1"}),this.isCanuse$)a.act="download",a.download_key=x["a"].getRandomKey(),a.download_type=e+"_download",this.$emit("download",a.download_key||"");else{a.act="pay",a.pay_key=i;let e=()=>{this.handleBuyMb(i)};this.handlerGoto(e)}D["a"].send("click",a)}).catch(()=>{console.log("request allinfo")})},handleRetry(){this.$_buyOrDownload({retail:!0})},onClickFav(){if(!this.isClientLogin)return void v["a"].login();let e=this.getCommonCollect();e=x["a"].extend(e,{$e_t:"button",$e_n:"collect",$m_n:"rightside_pay",act:this.isFav?"uncollect":"collect"}),D["a"].send("click",e);let t=this.item.id;this.isFav?this.setFavRemove({id:t,file_type:this.item.file_type||1}).then(e=>{"ok"===e.result&&(this.$emit("changeFav",t,!1,-1),this.changeFav({tid:t,isCollect:!1}))}):this.setFavAdd(JSON.parse(JSON.stringify(this.item))).then(e=>{if(!e)return;let i="ok"===e.result||"one"===e.msg?1:0;"ok"===e.result&&this.changeFav({tid:t,isCollect:i}),this.$emit("changeFav",t,i,this.qrCodeADData)})},getCommonCollect(){let e=this.item||{},t=x["a"].convertAppMark(e.moban_app);return{page_name:"preview_page",resource_id:e.id,$f_t:e.file_type,resource_app:e.moban_app,resource_type:52==e.file_type?"library":t,second_cat_id:e.catId||"",second_cat_name:e.catName||"",third_cat_name:e.childCatName||"",third_cat_id:e.childCatId||"",is_protected:this.isCopyRight,$track:"",...this.appPreviewCollect||{}}},btnTagClickCollect(){this.downBtnTag&&!this.isFree$&&this.isClientLogin&&D["a"].send("click",{$m_n:"rightside_pay",$e_n:"button_lable",$e_t:"button",text:this.downBtnTag,act:this.isCanuse$?"download":"pay",...this.getCommonCollect()})},favClose(){this.$emit("favClose")},FavGo(){this.$emit("FavGo")},handleBtnClick(e){if(this.freeTrialBtnText)return this.$emit("getFreeMember","right");let t=null===e||void 0===e?void 0:e.btn_type;switch(t){case"freeLoginBtn":case"loginBtn":var i=this.getCommonCollect();i=x["a"].extend(i,{$e_t:"button",$e_n:"login_download",$m_n:"rightside_pay",act:"login"}),D["a"].send("click",i),v["a"].login();break;case"memberPayBtn":this.onClickMemberBuyBtn("previewbtn",null===e||void 0===e?void 0:e.config);break;case"retailPayBtn":var a=D["a"].createPayKey();this.handleBuyMb(a);break;case"freeUseBtn":case"default":case"purchasedBtn":var s="freeUseBtn"==t?"free":"purchasedBtn"==t?"reail":"vip";this.handleDownload(s);break;default:console.log("btn_type",t);break}}},computed:{...Object(c["c"])("preview",["appPreviewCollect","appPreviewNewAd"]),...Object(c["c"])("user",["isClientLogin","isLogin"]),...Object(c["c"])("download",["downloadList"]),...Object(c["c"])("common",["isDarkSkin"]),downloadBtnText(){return(this.isFree$?"免费":"立即")+this.useTypeText},remindText(){return this.isCopyRight?"数字产品不支持商用和退货;本文档仅支持在线阅读":"数字产品不支持商用和退货"},useTypeText(){return this.isCopyRight?"阅读全文":"下载"},isCopyRight(){return 52==this.item.file_type&&2==this.item.copyright},isDownload(){let e=this.item.id+"_"+(this.item.resource_type||1);return this.downloadList[e]&&this.downloadList[e].status!=M["o"].finish},memberBtnText(){return this.freeTrialBtnText?this.freeTrialBtnText:this.userInfo.isSuperMember||this.userInfo.isDocerMember||this.isFree$||this.isPrivilegeFree$?"":(this.userInfo.isWpsMember?"超级会员免费":"稻壳会员免费")+this.useTypeText},memberBtnClass(){return this.userInfo.isSuperMember||this.userInfo.isWpsMember?"preview__btn-super":" preview__btn-docer"},downBtnClass(){return this.isFree$?"preview__btn-only":this.userInfo.isSuperMember||this.userInfo.isWpsMember&&!this.userInfo.isDocerMember?"preview__btn-super":"preview__btn-docer"},svgStarName(){return this.isFav?"star-active-18-2":"star-18-2"},isPdf(){return 14==this.item.moban_app}},components:{ItemDownload:pe,Price:z,CollectTip:me,ResumeBtn:Fe,PreviewOpBtn:Be}},Oe=Ie,Se=(i("26e3"),Object(g["a"])(Oe,ae,se,!1,null,"0185a58e",null)),Ae=Se.exports,Ne=function(){var e=this,t=e._self._c;return!e.appPreviewDisabledTag||e.appPreviewNewAd.picture&&e.isShowAd?t("div",{staticClass:"preview__bottom"},[e.appPreviewNewAd.picture&&e.isShowAd?t("a",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:e.collectAdDisplay()},expression:"{\n\t\tdisplayHandler: collectAdDisplay()\n\t}",modifiers:{display:!0}}],staticClass:"preview__img",attrs:{href:"javascript:void(0)"},on:{click:function(t){return e.onClickAd()}}},[t("img",{attrs:{loading:"lazy",src:e.appPreviewNewAd.picture,alt:""}})]):e._e()]):e._e()},Ee=[],je=function(){var e=this,t=e._self._c;return t("span",{staticClass:"tag",class:e.themeClass,on:{click:function(t){return t.stopPropagation(),e.onClickTag()}}},[e._v(e._s(e.name))])},Le=[],Re={data(){return{}},props:["name","themeClass"],methods:{onClickTag(){this.$emit("tag")}}},Me=Re,Ge=(i("891a"),Object(g["a"])(Me,je,Le,!1,null,"30ce5fcf",null)),Ue=Ge.exports,He={name:"previewSidebarBottom",data(){return{}},mixins:[A["a"],oe["a"]],props:{data:{type:Object,default:function(){return{}}},tags:{type:Array,default:function(){return[]}},catData:{type:Object,default:function(){return{}}},index:Number,otherCollectInfo:{type:Object,default:function(){return{}}},isShowAd:Boolean},methods:{...Object(c["b"])("common",["setAppComponent"]),onClickTag(e,t){if(this.appPreviewDisabledTag)return!1;T["a"].setOriginInfo({funcName:"ptag5"}),window.gotoTag({type:"screensearch",searchType:"word",originFuncName:"ptag5",originFromPage:"preview",text:e,collect:{from:"template_tag_preview",index:t||"",word:e||""}}),this.setAppComponent("")},copyData(){let e={};try{e=JSON.parse(JSON.stringify(this.appPreviewNewAd))}catch(t){}return e},onClickAd(){let e=this.copyData(),t=D["a"].createPayKey();T["a"].setOriginInfo({scenesEntrance:"prepic",btnName:"previewprepic"}),e.csource=e.csource||T["a"].getCsource(),e.position=e.position||T["a"].getPosition(),e.payKey=t,e.reason="preview>sidebarBottom>Ad",e.funcName="prepic";let i=this.getCommonCollect();i=x["a"].extend(i,{ad_id:M["b"].NEWFILE2019_PREVIEW_RIGHT_PIC,ad_url:e.link,text:e.text,$e_t:"ad",$e_n:"ad_pic",$m_n:"rightside_pay",special_id:this.data.special_id,pic_url:e.picture,jump_type:e.jump_type}),D["a"].send("click",i),this.$_tiance_adClickHandler(e)},collectAdDisplay(){return()=>{if(this.isShowAd){let e=this.copyData(),t=this.getCommonCollect();t=x["a"].extend(t,{ad_id:M["b"].NEWFILE2019_PREVIEW_RIGHT_PIC,ad_url:e.link,text:e.text,$e_t:"ad",$e_n:"ad_pic",$m_n:"rightside_pay",special_id:this.data.special_id,pic_url:e.picture,jump_type:e.jump_type}),D["a"].send("display",t)}}},collectTagHover(e){return t=>{let i=this.getCommonCollect();i=x["a"].extend(i,{$e_t:"button",$e_n:"similar_key",tag_name:e,time:t}),D["a"].send("hover",i)}},getCommonCollect(){let e=this.data||{},t=x["a"].convertAppMark(e.moban_app);return{page_name:"preview_page",resource_id:e.id,$f_t:e.file_type,resource_app:e.moban_app,resource_type:52==e.file_type?"library":t,is_protected:52==e.file_type&&2==e.copyright}}},computed:{...Object(c["c"])("preview",["appPreviewCollect","appPreviewNewAd","appPreviewDisabledTag"])},components:{Tag:Ue}},qe=He,We=(i("d610"),Object(g["a"])(qe,Ne,Ee,!1,null,"ff30d8f4",null)),Ve=We.exports,Je=function(){var e=this,t=e._self._c;return t("div",{staticClass:"plike"},[e.data.length||e.state?t("p",{staticClass:"plike__title"},[e._v("大家都在看")]):e._e(),e.data.length?e._l(e.data.slice(0,5),(function(i,a){return t("div",{directives:[{name:"collect",rawName:"v-collect.display",value:{displayHandler:e.collectMbDisplay(a)},expression:"{\n\t\t\t\tdisplayHandler: collectMbDisplay(index),\n\t\t\t}",modifiers:{display:!0}}],key:a,staticClass:"plike__item",on:{click:function(t){return e.onClickMb(a)}}},[t("div",{staticClass:"plike__item__img",attrs:{title:i.name}},[t("img",{attrs:{loading:"lazy",src:e.$_cropPic(i.thumb_big_url,e.cropSuffix),alt:""}})]),t("div",{staticClass:"plike__item__p",attrs:{title:i.name}},[e._v(" "+e._s(i.name)+" ")])])})):"loading"==e.state?t("div",{staticClass:"skeleton"},e._l(5,(function(e){return t("div",{key:e,staticClass:"plike__item"},[t("div",{staticClass:"plike__item__img"}),t("div",{staticClass:"plike__item__p"})])})),0):"error"==e.state?t("div",{staticClass:"plike-error"},[t("SvgIcon",{attrs:{svgName:"net-error"}}),t("p",[e._v("网络连接失败")]),t("a",{on:{click:e.retry}},[e._v("请重试")])],1):e._e()],2)},ze=[],Ke={name:"sidebar-like",props:{data:{type:Array,default:function(){return[]}},policy:{type:String,default:function(){return[]}},currentAppName:String,tmplData:{type:Object,default:function(){return{}}},state:{type:String,default:""}},data(){return{showData:{}}},methods:{...Object(c["b"])("preview",["setAppPreview"]),handlePreview(e){let t={source_page_element:"similar_template"};t.template_position=e+1,t.template_id=this.tmplData.id;let i=x["a"].extend({},this.appPreviewCollect,t);i.policy=this.policy||"",i.ai=this.policy||"",i.request_id=this.requestId||"",this.setAppPreview({data:JSON.parse(JSON.stringify(this.data)),index:e,disabledTag:this.appPreviewDisabledTag,collect:i})},onClickMb(e){let t=this.getCommonCollect(e);D["a"].send("click",Object.assign({},t,{act:"preview"})),T["a"].setOriginInfo({scenesEntrance:"prertp"+(e+1),policy:this.policy}),this.handlePreview(e)},collectMbDisplay(e){return()=>{let t=this.data[e]||{};if(!this.showData[t.id]){this.showData[t.id]=!0;let i=this.getCommonCollect(e);D["a"].send("display",i)}}},getCommonCollect(e){let t=this.data[e]||{},i=x["a"].convertAppMark(t.moban_app);return{act:"preview",page_name:"preview_page",resource_id:t.id,$f_t:t.file_type,resource_app:t.moban_app,resource_type:52==t.file_type?"library":i,policy:this.policy,ai:this.policy,request_id:this.requestId,$e_n:"resource",$e_t:"resource",$m_n:"right_similar",$e_p:e+1,is_protected:52==t.file_type&&2==t.copyright,pre_resource_id:this.tmplData.id,...this.otherCollectInfo}},retry(){this.$emit("retry")}},computed:{...Object(c["c"])("preview",["appPreviewCollect","appPreviewDisabledTag"]),cropSuffix(){return{wps:"193x195",wpp:"193x116",et:"193x138"}[this.currentAppName]}}},Xe=Ke,Ye=(i("32d2"),Object(g["a"])(Xe,Je,ze,!1,null,"d0ab8976",null)),Qe=Ye.exports,Ze=i("62cd"),et=i("3aea"),tt=i("a6c6"),it=function(){var e=this,t=e._self._c;return t("div",{staticClass:"copy-right-tip js-preview-no-hide"},[t("SvgIcon",{staticClass:"close",attrs:{svgName:"preview-copy-right-close"},nativeOn:{click:function(t){return e.close("close")}}}),t("div",{staticClass:"copy-right-tip__info"},[e._v("应版权方要求，部分资源仅支持在线阅读，暂不支持下载、打印")]),t("div",{staticClass:"copy-right-tip__edit"},[t("div",{staticClass:"copy-right-tip__remind",on:{click:function(t){e.isSelected=!e.isSelected}}},[t("div",{staticClass:"copy-right-tip__remind--radio"},[t("SvgIcon",{attrs:{svgName:e.svgName}})],1),t("span",[e._v("不再提醒")])]),t("div",{staticClass:"copy-right-tip__know",on:{click:function(t){return e.close("btn")}}},[e._v("我知道了")])])],1)},at=[],st={data(){return{isSelected:!1}},methods:{close(e){this.isSelected&&"btn"==e&&et["a"].set(M["g"].previewCopyRightTip,!0),this.$emit("close",e)}},computed:{svgName(){return this.isSelected?"preview-copyRightTip-selected":"preview-copyRightTip-unSelect"}}},ot=st,rt=(i("3dfa"),Object(g["a"])(ot,it,at,!1,null,"ef4b0f08",null)),nt=rt.exports;const lt="登录后使用",ct="免费使用",dt="立即使用",pt="会员专享",ht="购买资源",_t={freeLoginBtn:{btn_type:"freeLoginBtn",config:{title:lt,icon:"",btn_color:"#2C71FA",btn_color_hover:"#2C71FA",btn_color_click:"#2C71FA",btn_dark_color:"#2C71FA",btn_dark_color_hover:"#2C71FA",btn_dark_color_click:"#2C71FA",font_color:"#FFFFFF",font_color_hover:"#FFFFFF",font_color_click:"#FFFFFF",font_dark_color:"#FFFFFF",font_dark_color_hover:"#FFFFFF",font_dark_color_click:"#FFFFFF"}},freeUseBtn:{btn_type:"freeUseBtn",config:{title:ct,icon:"",btn_color:"#2C71FA",btn_color_hover:"#2C71FA",btn_color_click:"#2C71FA",btn_dark_color:"#2C71FA",btn_dark_color_hover:"#2C71FA",btn_dark_color_click:"#2C71FA",font_color:"#FFFFFF",font_color_hover:"#FFFFFF",font_color_click:"#FFFFFF",font_dark_color:"#FFFFFF",font_dark_color_hover:"#FFFFFF",font_dark_color_click:"#FFFFFF"}},loginBtn:{btn_type:"loginBtn",config:{title:lt,icon:"",btn_color:"#464343",btn_color_hover:"#464343",btn_color_click:"#464343",btn_dark_color:"#464343",btn_dark_color_hover:"#464343",btn_dark_color_click:"#464343",font_color:"#F5CC87",font_color_hover:"#F5CC87",font_color_click:"#F5CC87",font_dark_color:"#F5CC87",font_dark_color_hover:"#F5CC87",font_dark_color_click:"#F5CC87"}},memberPayBtn:{btn_type:"memberPayBtn",config:{title:pt,icon:"",btn_color:"#464343",btn_color_hover:"#464343",btn_color_click:"#464343",btn_dark_color:"#464343",btn_dark_color_hover:"#464343",btn_dark_color_click:"#464343",font_color:"#F5CC87",font_color_hover:"#F5CC87",font_color_click:"#F5CC87",font_dark_color:"#F5CC87",font_dark_color_hover:"#F5CC87",font_dark_color_click:"#F5CC87"}},retailPayBtn:{btn_type:"retailPayBtn",config:{title:ht,icon:"",btn_color:"#464343",btn_color_hover:"#464343",btn_color_click:"#464343",btn_dark_color:"#464343",btn_dark_color_hover:"#464343",btn_dark_color_click:"#464343",font_color:"#F5CC87",font_color_hover:"#F5CC87",font_color_click:"#F5CC87",font_dark_color:"#F5CC87",font_dark_color_hover:"#F5CC87",font_dark_color_click:"#F5CC87"}},purchasedBtn:{btn_type:"purchasedBtn",config:{title:dt,icon:"",btn_color:"#464343",btn_color_hover:"#464343",btn_color_click:"#464343",btn_dark_color:"#464343",btn_dark_color_hover:"#464343",btn_dark_color_click:"#464343",font_color:"#F5CC87",font_color_hover:"#F5CC87",font_color_click:"#F5CC87",font_dark_color:"#F5CC87",font_dark_color_hover:"#F5CC87",font_dark_color_click:"#F5CC87"}},default:{btn_type:"default",config:{title:dt,icon:"",btn_color:"#464343",btn_color_hover:"#464343",btn_color_click:"#464343",btn_dark_color:"#464343",btn_dark_color_hover:"#464343",btn_dark_color_click:"#464343",font_color:"#F5CC87",font_color_hover:"#F5CC87",font_color_click:"#F5CC87",font_dark_color:"#F5CC87",font_dark_color_hover:"#F5CC87",font_dark_color_click:"#F5CC87"}}},ut={freeLoginBtn:{btn_type:"freeLoginBtn",config:{title:lt,icon:"",font_color:"#FF4E37",font_color_hover:"#FF705E",font_color_click:"#EE4D38",font_dark_color:"#FF4E37",font_dark_color_hover:"#FF705E",font_dark_color_click:"#EE4D38"}},freeUseBtn:{btn_type:"freeUseBtn",config:{title:ct,icon:"",font_color:"#FF4E37",font_color_hover:"#FF705E",font_color_click:"#EE4D38",font_dark_color:"#FF4E37",font_dark_color_hover:"#FF705E",font_dark_color_click:"#EE4D38"}},loginBtn:{btn_type:"loginBtn",config:{title:lt,icon:"",font_color:"#FF4E37",font_color_hover:"#FF705E",font_color_click:"#EE4D38",font_dark_color:"#FF4E37",font_dark_color_hover:"#FF705E",font_dark_color_click:"#EE4D38"}},memberPayBtn:{btn_type:"memberPayBtn",config:{title:pt,icon:"",font_color:"#FF4E37",font_color_hover:"#FF705E",font_color_click:"#EE4D38",font_dark_color:"#FF4E37",font_dark_color_hover:"#FF705E",font_dark_color_click:"#EE4D38"}},retailPayBtn:{btn_type:"retailPayBtn",config:{title:ht,icon:"",font_color:"#FF4E37",font_color_hover:"#FF705E",font_color_click:"#EE4D38",font_dark_color:"#FF4E37",font_dark_color_hover:"#FF705E",font_dark_color_click:"#EE4D38"}},purchasedBtn:{btn_type:"purchasedBtn",config:{title:dt,icon:"",font_color:"#FF4E37",font_color_hover:"#FF705E",font_color_click:"#EE4D38",font_dark_color:"#FF4E37",font_dark_color_hover:"#FF705E",font_dark_color_click:"#EE4D38"}},default:{btn_type:"default",config:{title:dt,icon:"",font_color:"#FF4E37",font_color_hover:"#FF705E",font_color_click:"#EE4D38",font_dark_color:"#FF4E37",font_dark_color_hover:"#FF705E",font_dark_color_click:"#EE4D38"}}};var gt=i("6eeb"),ft=i("10de"),mt={data(){return{freeTrialOpData$:{}}},mounted(){this._checkFreeTrialRight()},methods:{async _checkFreeTrialRight(){if(this.freeTrialOpData$={},!this.isLogin||this.userInfo.isMember)return;const e=M["g"].freeTrialedCache+this.userInfo.userid,t=await et["a"].get(e);t||we["a"].get("freeTrial").getConf({data:{hdid:M["e"].hdid,pos:M["e"].isPrivilegeSwitchOn?"pc_free_trial_new":"pc_free_trial"}}).then(e=>{console.log(e),e&&"ok"===e.result&&e.data&&e.data.can_trial&&e.data.conf&&e.data.conf.open&&(this.freeTrialOpData$=e.data.conf)}).catch(e=>{console.log("err",e)})},$_getFreeMember(e){D["a"].send("click",{...this.freeTrialCollectInfo,$e_t:"button",$e_n:e}),this.isLogin?ft["a"].$emit(M["p"].showFreeTrialPop):v["a"].login()}},computed:{...Object(c["c"])("user",["userInfo","isLogin"]),freeTrialBtnText$(){return!this.noLoginOrMemberOrLib&&this.freeTrialOpData$.new_btn_text||""},noLoginOrMemberOrLib(){return!this.isLogin||this.userInfo.isMember||this.isCopyRight||this.isOnlyRetail}},watch:{userInfo(){this._checkFreeTrialRight()}}};let vt,yt=setTimeout("1"),wt=x["a"].debounce(300),bt=x["a"].debounce(100);var Ct={data(){return{statusCode:"",data:{},urls:[],isFav:!1,tags:[],catData:{},isBought:!1,hasChangeBought:!1,likeData:[],fetchLikeDataState:"",policy:"",eventTime:"",currentIndex:-1,hasFetch:!1,offset:0,totalNum:0,videoUrl:"",originFunc:"",originAlg:"",originNewAlg:"",originNewFuncName:"",otherCollectInfo:{},features:[],likeRequestId:"",isShowAd:!1,collectTipShow:!1,catMsg:{},resourceType:"template",showCopyRightTip:!1,showPreviewCopyRightNext:null,downBtnTag:"",isFixed:!1,fixedFillHeight:0,fixedElTop:0,opPayconfig:"",loadBeginTime:"",isPrivilegeFree:!1,isFetchingMbStatus:!1,previewBtnConfig:{},previewTextConfig:{}}},mixins:[Ze["a"],A["a"],mt,d["a"],oe["a"]],mounted(){T["a"].setOriginInfo({btnPage:"preview"}),this.handleIndex(this.appPreviewIndex),this.getDonwloadBtnAd(),tt["a"].$on(M["p"].reloadPreview,this.reGetPreview),tt["a"].$on(M["p"].login,this.reGetPreview),tt["a"].$on(M["p"].logout,this.reGetPreview),tt["a"].$on(M["p"].privilegeChange,()=>{this.fetchPageData()})},beforeDestroy(){T["a"].setOriginInfo({btnPage:"view"}),tt["a"].$off(M["p"].reloadPreview,this.reGetPreview),tt["a"].$off(M["p"].login,this.reGetPreview),tt["a"].$off(M["p"].logout,this.reGetPreview),tt["a"].$off(M["p"].privilegeChange)},methods:{...Object(c["b"])("common",["setAppComponent","setAppName"]),...Object(c["b"])("preview",["setAppPreviewIndex","setAppPreviewCollect","setAppPreviewNewAd"]),...Object(c["b"])("download",["setDownloadData"]),...Object(c["b"])("user",["userDone"]),initAD(){vt&&(vt=!1),this.setAppPreviewNewAd({data:{},type:this.data.moban_app}),this.isShowAd=!1;let e=0;this.catData.son_id&&(e=parseInt(this.catData.son_id,10)||0),this.$_ad_getTiance({params:{position:M["b"].TIANCE_PREVIEW_RIGHT_BANNER},timeout:3e3,isClientCache:!0}).then(e=>{let t=this.$_tiance_handleAdRes(e);this.setAppPreviewNewAd(t[M["b"].TIANCE_PREVIEW_RIGHT_BANNER]||{})}).finally(()=>{this.isShowAd=!0})},getDonwloadBtnAd(){let e={};this.$_getNewAD({params:{position:M["b"].NEWFILE2021_PREVIEW_BUTTON_TAG},cacheTime:9e5}).then(t=>{if("ok"==t.result&&t.data)try{e=this.$_newAdDataInit(t.data[M["b"].NEWFILE2021_PREVIEW_BUTTON_TAG])}catch(i){console.log(i)}}).finally(()=>{this.downBtnTag=e&&e.ad_text||""})},handleIndex(e){this.currentIndex=e>0?e>=this.listDataLen?this.listDataLen-1:e:0,this.setAppPreviewIndex(this.currentIndex)},hanldeChangeFav(e,t,i){if(e==this.data.id&&(this.isFav=!!t),this.isFav?this.collectTipShow=!0:this.collectTipShow=!1,this.data.favNumer+=t,Object.keys(i).length){let e=this.getCommonCollect();e=x["a"].extend(e,{$e_n:"fav_success",$e_t:"ad",...i}),D["a"].send("display",e)}},fetchAllData(){this.statusCode=M["s"].loading,this.urls=[],this.isFav=!1,this.tags=[],this.videoUrl="";let e=3==this.data.moban_app?1:0;return new Promise(t=>{we["a"].get("preview")["allData"]({data:{id:this.data.id,is_video:e}}).then(e=>{if("ok"==e.result&&e.data){let i=e.data;this.urls=i.page_data&&i.page_data.urls||[],this.isFav=!(!i.basic_data||!i.basic_data.is_my_fav),this.catData=i.cats_data||{};try{let e=i.tags_data||i.basic_data&&i.basic_data.tags||this.data.tags||[];e.forEach(e=>{let t="";e.name?t=e.name:"string"==typeof e&&e.constructor==String&&(t=e),t&&this.tags.push({name:t})})}catch(t){console.log(t)}this.videoUrl=Array.isArray(i.video_url)?"":i.video_url||"";let a=i.features||[];a.forEach(e=>{e&&e.content&&(e.content=e.content.replace(/(\n)/g,"<br>"))}),this.features=a;let s=this.getCommonCollect();s=x["a"].extend(s,{$e_t:"page"}),D["a"].send("display",s),this.data=Object.assign({},this.data,i.page_data,i.basic_data),this.statusCode=this.data.state&&1!=this.data.state?M["s"].tempDisable:""}else this.urls=[],this.isFav=!1,this.tags=[],this.features=[],this.statusCode=M["s"].tempDisable;this.initAD()}).catch(()=>{this.urls=[],this.isFav=!1,this.tags=[],this.features=[],this.statusCode=M["s"].tempDisable,this.initAD()}).finally(()=>{this.checkUserPrivilege().finally(()=>{t()})})})},fetchLikeData(){let e=this.data.id;this.hasFetch=!0;let t={id:e,limit:this.offset?12:17,offset:this.offset,add_preview:1,mb_app:this.data.moban_app};this.fetchLikeDataState="loading",we["a"].get("preview")["likeData"]({data:t}).then(t=>{if(e!=this.data.id||0==this.hasFetch)return e=null,this.hasFetch=!1,!1;e=null,"ok"==t.result&&t.data?(this.fetchLikeDataState="",this.setPageInfo(t.data),this.policy=t.data.type||t.data.alg?`${t.data.type||""}_${t.data.alg||""}`:"",this.eventTime=t.data.event_time,this.likeRequestId=t["wps-docer-request-id"]||""):(this.fetchLikeDataState="error",this.setPageInfo({}),this.policy="",this.eventTime="",this.likeRequestId=""),this.offset<this.totalNum&&(this.hasFetch=!1)}).catch(()=>{this.fetchLikeDataState="error",this.setPageInfo({}),this.hasFetch=!1,this.policy="",this.eventTime="",this.likeRequestId=""})},setPageInfo(e){0==this.offset&&(this.totalNum=e.total||0),e.mbs&&e.mbs.length&&(this.likeData=this.likeData.concat(e.mbs),this.offset=this.likeData.length)},fetchBought(){if(2!=this.data.resource_type)return this.hasChangeBought=!1,we["a"].get("orderLib").checkBuy({data:{mb_id:this.data.id,mb_platform:8},serialNumber:yt}).then(e=>{this.isBought=!("ok"!=e.result||!e.data||!e.data.is_buy),this.hasChangeBought=!0}).catch(()=>{this.isBought=!1,this.hasChangeBought=!0});this.hasChangeBought=!0},async handleClose(){this.setAppComponent("")},fetchPayconfig(){1!=this.data.moban_type&&we["a"].get("payconfig").payconfigWidthMb({data:{mb_id:this.data.id,scene:"new",file_type:this.data.file_type||1,hdid:M["e"].hdid}}).then(e=>{this.opPayconfig=e&&e.data&&e.data.payconfig||""}).catch(()=>{this.opPayconfig=""})},async emitShowTemplateTip(){if(!this.isCopyRight||!this.isLogin)return;const e=await et["a"].get(M["g"].showSideBarTemplateTip);e||et["a"].set(M["g"].showSideBarTemplateTip,!0)},onClickClose(e){if("close"==e){let e=this.getCommonCollect();e=x["a"].extend(e,{$e_n:"close",$e_t:"button"}),D["a"].send("click",e)}this.handleClose()},onClickLeft(){let e=this.getCommonCollect();e=x["a"].extend(e,{act:"preview",$e_n:"left",$e_t:"button",$m_n:"operate"}),D["a"].send("click",e),this.handleIndex(this.currentIndex-1)},onClickRight(){let e=this.getCommonCollect();e=x["a"].extend(e,{act:"preview",$e_n:"right",$e_t:"button",$m_n:"operate"}),D["a"].send("click",e),this.handleIndex(this.currentIndex+1)},onDownload(e){let t=JSON.parse(JSON.stringify(this.data));t.downloadKey=e;let i=()=>{this.$_download(t,t.moban_app)};this.handlerGoto(i)},handlerGoto(e){this.isCopyRight?this.checkPreviewCopyRightTip("check",e):e()},async checkPreviewCopyRightTip(e,t){if("check"==e&&t){this.showPreviewCopyRightNext=null;const e=await et["a"].get(M["g"].previewCopyRightTip);e?t():(this.showPreviewCopyRightNext=t,this.showCopyRightTip=!0)}else"btn"==e&&this.showPreviewCopyRightNext&&this.showPreviewCopyRightNext(),this.showCopyRightTip=!1},onWheel(e){wt(()=>{if(!this.hasFetch&&this.previewEleProxy){let t=this.previewEleProxy.getBoundingClientRect(),i=this.previewEleProxy.scrollHeight-t.height;e.deltaY&&i>0&&i<=this.previewEleProxy.scrollTop-6&&this.fetchLikeData()}})},getCommonCollect(){let e=this.data||{},t=x["a"].convertAppMark(e.moban_app);return{page_name:"preview_page",resource_id:e.id,$f_t:e.file_type,resource_app:e.moban_app,resource_type:t,second_cat_id:e.catId||"",second_cat_name:e.catName||"",third_cat_name:e.childCatName||"",third_cat_id:e.childCatId||"",is_protected:this.isCopyRight,$track:"",...this.appPreviewCollect||{}}},closeTip(){this.collectTipShow=!1},goCollection(){this.$router.push({path:"/screenmytpl",query:{type:"collect",appName:M["e"].name,fromPage:M["e"].name,collect:{from:"home_page_"+M["e"].name},t:Math.random()}})},reGetPreview(){this.fetchPageData(),this.$nextTick(()=>{this.previewEleProxy.scrollTop=0})},onscroll(e){bt(()=>{if(!e.target)return;let t=e.target.scrollTop;if(!this.fixedElTop||!this.fixedFillHeight){let e=document.getElementById("preview-btngroup"),{top:i,height:a}=e&&e.getBoundingClientRect()||{};this.fixedElTop=i+t,this.fixedFillHeight=a}this.isFixed=t>this.fixedElTop})},async checkUserPrivilege(){if(x["a"].isEmptyObject(this.item))return;await this.userDone();let e={};try{e=await gt["authService"].common.check(this.item)}catch(t){}e&&"privilege_limit_error"===e.errcode?this.isPrivilegeFree=!0:this.isPrivilegeFree=!!e.isSuc},initAllBtnConfigParams(){let e=["previewPageBtn","previewPageText"],t=[];return e.forEach(e=>{let i={platform:"pc",scene:"newdoc",area:e,refresh:0,id:this.data.id};t.push(i)}),t},fetchPreviewBtnConfig(){let e=this.initAllBtnConfigParams(),t=[];return e.forEach(async e=>{let i=this.getBtnConfig(e);t.push(i)}),Promise.all(t).then(([e,t])=>{this.previewBtnConfig=e,this.previewTextConfig=t}).catch(e=>{console.log(e)})},getBtnConfig(e){return new Promise(async t=>{let i;try{i=await we["a"].get("channel").getBtnConfig({data:e})}catch(s){console.log(s)}if(i&&"ok"===i.result&&i.data){var a;let s=null===(a=i.data.btn_conf)||void 0===a?void 0:a.default;s=s||this.processDefaultConfig(e.area),t(s)}else{let i=this.processDefaultConfig(e.area);t(i)}})},processDefaultConfig(e){let t={};const i="previewPageBtn"===e?_t:ut;let a;return a=this.isLogin?this.isCanuse$?this.isFree$?"freeUseBtn":this.isBought$?"purchasedBtn":"default":"memberPayBtn":this.isFree$?"freeLoginBtn":"loginBtn",t[a]=i[a],t},fetchPageData(){this.isFetchingMbStatus=!0;let e=[this.fetchAllData(),this.fetchBought()];this.isPrivilegeSwitchOn$&&e.push(this.fetchPreviewBtnConfig()),Promise.all(e).finally(()=>{this.isFetchingMbStatus=!1})}},computed:{...Object(c["c"])("preview",["appPreviewData","appPreviewIndex","appPreviewCollect","appPreviewChangeApp"]),...Object(c["c"])("user",["isLogin","userInfo"]),...Object(c["c"])("common",["isDarkSkin"]),previewClass(){return"preview_"+x["a"].convertAppMark(this.data.moban_app)},currentAppName(){return x["a"].convertAppMark(this.data.moban_app)},listDataLen(){return this.appPreviewData&&this.appPreviewData.length||0},isCopyRight(){return 52==this.data.file_type&&2==this.data.copyright},previewEleProxy(){return this.$refs.rPreviewContent&&this.$refs.rPreviewContent.$refs.previewContent||{}},previewModel(){if(this.freeTrialBtnText$)return()=>i.e(5).then(i.bind(null,"7573"))},freeTrialCollectInfo(){const e=T["a"].info.btnName;T["a"].setOriginInfo({btnName:"vipsign"});const t={resource_type:this.currentAppName,file_type:this.data.file_type,policy:this.policy,act:"pay",pay_key:x["a"].getRandomKey(),userid:this.userInfo.userId,freeTrialPosition:T["a"].getPosition(),preview_type:"pic_flow"};return T["a"].setOriginInfo({btnName:e}),t},item(){return this.data},btnType(){let e;return e=this.isClientLogin?this.isOnlyRetail$?"retailPayBtn":this.isFree$?"freeUseBtn":this.isBought$?"purchasedBtn":this.isCanuse$?"default":"memberPayBtn":this.isFree$?"freeLoginBtn":"loginBtn",e}},watch:{currentIndex:function(){let e="editrec"==this.originFunc;if(this.videoUrl="",this.offset=0,this.likeData=[],this.hasFetch=!1,this.data=this.appPreviewData[this.currentIndex]||{},this.resourceType=x["a"].converResourceType(this.data.file_type),this.$refs.sidebar&&(this.$refs.sidebar.scrollTop=0),!Object.prototype.hasOwnProperty.call(this.data,"moban_app")&&(this.data.moban_app=this.data.mb_app||1),!Object.prototype.hasOwnProperty.call(this.data,"moban_type")&&(this.data.moban_type=this.data.mb_type||3),Object.prototype.hasOwnProperty.call(this.data,"request_mbId")&&(this.data.id=this.data.request_mbId),Object.prototype.hasOwnProperty.call(this.data,"catId")?this.catMsg={catId:this.data.catId||"",catName:this.data.catName||"",childCatName:this.data.childCatName||"",childCatId:this.data.childCatId||""}:this.data=Object.assign({},this.data,this.catMsg),this.originFunc||(this.originFunc=T["a"].info.scenesEntrance),this.originAlg||(this.originAlg=T["a"].info.policy),e&&(this.data.isAd||this.data.isRecent)?this.otherCollectInfo={source_page_element:this.data.isRecent?"rec_template":"artificial",ai:""}:this.otherCollectInfo={},e&&this.data.isAd?T["a"].setOriginInfo({policy:"OCC_bktj"}):e&&this.data.isRecent?T["a"].setOriginInfo({policy:"OCC_zjsy"}):T["a"].setOriginInfo({policy:this.originAlg}),this.appPreviewChangeApp){let e=x["a"].convertAppMark(this.data.moban_app);T["a"].setOriginInfo({appName:e}),e&&this.setAppName(e)}setTimeout(()=>{this.loadBeginTime=(new Date).getTime(),this.fetchPageData(),2==this.data.resource_type?this.hasFetch=!0:this.fetchLikeData(),this.fetchPayconfig(),this.$nextTick(()=>{this.previewEleProxy.scrollTop=0})},0)},userInfo(){this.$nextTick(()=>{this.previewEleProxy.scrollTop=0,this.$refs.sidebar&&(this.$refs.sidebar.scrollTop=0),this.fixedFillHeight=0,this.isFixed=!1,this.fixedElTop=0})}},components:{Main:ie,SidebarTop:Ae,SidebarBottom:Ve,CopyRightTip:nt,SidebarLike:Qe}},kt=Ct,Ft=(i("a685"),Object(g["a"])(kt,a,s,!1,null,"385a080f",null));t["default"]=Ft.exports},"672f":function(e,t,i){},7026:function(e,t,i){},"7cf2":function(e,t,i){"use strict";i("8eda")},"7da0":function(e,t,i){},"87d2":function(e,t,i){},"891a":function(e,t,i){"use strict";i("d7d5")},"89ae":function(e,t,i){},"8eda":function(e,t,i){},9071:function(e,t,i){"use strict";i("1f90")},a101:function(e,t,i){},a1cb:function(e,t,i){},a1fc:function(e,t,i){"use strict";i("89ae")},a2a5:function(e,t,i){"use strict";i("51e1")},a685:function(e,t,i){"use strict";i("a101")},bea8:function(e,t,i){"use strict";i("87d2")},c473:function(e,t,i){},cd99:function(e,t,i){"use strict";i("479b")},d610:function(e,t,i){"use strict";i("7da0")},d7d5:function(e,t,i){},ffde:function(e,t,i){"use strict";i("672f")}}]);