#ifndef _KPDFMOBILESCAN_WIDGET_H__
#define _KPDFMOBILESCAN_WIDGET_H__

class KCefViewWidget;
class KxMobileScanWidget : public QDialog
{
	DECLARE_COUNT_ASSERT(KxMobileScanWidget)
	Q_OBJECT
public:
	explicit KxMobileScanWidget(QWidget* parent = nullptr);
	~KxMobileScanWidget();
	bool init();
	QStringList getScanfiles();

private slots:
	void onDialogClose();
	void onSizeChange(int, int);

private:
	void unInit();
	void initShadowBorder();
	void initWebView(const QString&);
	void resetGeomotry();

private:
	KCefViewWidget* m_cefView = nullptr;
};
#endif // _KPDFMOBILESCAN_WIDGET_H__