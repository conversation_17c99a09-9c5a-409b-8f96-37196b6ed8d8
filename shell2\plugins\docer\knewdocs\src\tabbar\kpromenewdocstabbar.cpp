﻿#include "stdafx.h"
#include "kpromenewdocstabbar.h"
#include "kpromenewdocstabbarconfig.h"
#include <public_header/kliteui/kdrawhelper.h>
#include <kprometheus/kpromeapplication.h>
#include <kprometheus/kpromestartup.h>
#include "perftool/perftool.h"
#include "utility/kxsettings.h"
#include "pdf/kxpdfnewdochelper.h"
#include "kprometheus/kpromestyle.h"
#include <kprometheus/kpromeskin.h>
#include "kprometheus/kpromeinfocollcethelper.h"
#include <kprometheus/kpromeapplication.h>
#include <kprometheus/kpromecloudsvrproxy.h>
#include <ksolite/kdcinfoc.h>
#include "ksolite/ksupportutil.h"
#include "krt/kconfigmanagercenter.h"

KTabSeperator::KTabSeperator(QWidget* parent /*=nullptr*/)
	:QWidget(parent)
{
	setFixedHeight(KLiteStyle::dpiScaled(16));
	setFixedWidth(KLiteStyle::dpiScaled(1));
	setAutoFillBackground(true);
	connect(promeApp->promeSkin(), SIGNAL(skinChanged()),
		this, SLOT(onSkinChanged()));
	onSkinChanged();
}

void KTabSeperator::onSkinChanged()
{
	QPalette palette;
	QColor bgColor = KDrawHelper::getColorFromTheme("KPromeNewDocsTabBarNew", "seperator", QColor(0x00, 0x00, 0x1A, 0x00));
	palette.setColor(backgroundRole(), bgColor);
	setPalette(palette);
}

KPromeNewDocsTabBar::KPromeNewDocsTabBar(QWidget* parent)
	: QWidget(parent)
	, m_borderHeight(KLiteStyle::dpiScaled(1))
	, m_bIgnoreMouseHover(false)
	, m_currentTabType(KPromeNewDocsTabInfoObj::INVALID_NEWDOCS_TAB)
	, m_movingAreaBkColorAnimation(nullptr)
	, m_movingAreaAnimation(nullptr)
	, m_bMoving(false)
{
	initUIWidget();
	connect(promeApp->cloudSvrProxy(), SIGNAL(sigCurrentWorkspaceChanged(const QString&, const QString&, bool)),
		this, SLOT(onCurrentWorkspaceChanged(const QString&, const QString&)));
	connect(promeApp->cloudSvrProxy(), SIGNAL(loginStatusChanged()),
		this, SLOT(onUserInfoChange()));
}

KPromeNewDocsTabBar::~KPromeNewDocsTabBar()
{

}

void KPromeNewDocsTabBar::initTab()
{
	QList<QPair<QString, QList<KPromeNewDocsTabInfoObj*>>> groupTabInfos = KPromeNewDocsTabBarConfig::getInstance()->getTabInfo();
	QList<KPromeNewDocsTabInfoObj*> infoObjList;
	for (auto tabInfo : groupTabInfos)
	{
		if (tabInfo.first.compare("OfficeDocumentType", Qt::CaseInsensitive) == 0)
		{
			infoObjList = tabInfo.second;
			break;
		}
	}

	if (infoObjList.isEmpty())
		return;

	m_movingAreaBkColorAnimation = new QPropertyAnimation(this, "movingAreaBkColor", this);
	m_movingAreaAnimation = new QPropertyAnimation(this, "moveArea", this);
	connect(m_movingAreaAnimation, SIGNAL(finished()), this, SLOT(onAnimFinished()), Qt::UniqueConnection);
	m_docsLayout->addStretch();
	bool bAddSeperator = false;
	for (auto infoObj : infoObjList)
	{
		if (infoObj->getTabType() == KPromePluginWidget::pageMytpl)
		{
			if (krt::kcmc::support("MyTemplatePageSeperator"))
			{
				bAddSeperator = false;
				continue;
			}
		}
		else if (infoObj->getTabType() == KPromePluginWidget::widgetCloudFolderShare && !showShareFolderTab()){
			continue;
		}
		else if (krt::kcmc::support("VisioReadOnly") &&
						(infoObj->getTabType() == KPromePluginWidget::widgetProcessonlocal_Flow ||
						 infoObj->getTabType() == KPromePluginWidget::widgetProcessonlocal_Mind))
		{
			continue;
		}
		else if (infoObj->getTabType() == KPromePluginWidget::pageDocerPdf)
		{
			continue;
		}

		KPromeNewDocsTab* newDocsTab = new KPromeNewDocsTab(this);
		newDocsTab->initByTabInfo(infoObj);

		connect(newDocsTab, SIGNAL(tabClicked(int)), this, SLOT(onTabClicked(int)));
		m_docsLayout->addWidget(newDocsTab);
		if (bAddSeperator)
		{
			m_docsLayout->addWidget(new KTabSeperator());
			bAddSeperator = false;
		}
		m_tabMap[infoObj->getTabType()] = newDocsTab;
		m_typeColorMap[infoObj->getTabType()] = KDrawHelper::getColorFromTheme(newDocsTab->metaObject()->className() + infoObj->styleName(), "containerBkColor");

		if (!infoObj->relyPlugin().isEmpty() && !infoObj->isRelyPluginExit())
		{
			newDocsTab->setVisible(false);
			connect(infoObj, SIGNAL(relyPluginExist(bool)), newDocsTab, SLOT(setVisible(bool)));
		}
	}
	updateNewDocsTabBar();

	m_docsLayout->addStretch();

	if (promeApp->startupInfo()->isIndependentMode())
	{
		int nTabType = KPromePluginWidget::pageDocerWps;//默认显示WPS Tab页
		int firstTabType = nTabType;
		KPromeStartup::IndependentAppType appType =
			promeApp->startupInfo()->getIndependentAppType();
		switch (appType)
		{
		case KPromeStartup::IndependentWps:
			if (promeApp->isOfficialIndepentComponent())
				nTabType = KPromePluginWidget::pageDocerOfficial;
			else
				nTabType = KPromePluginWidget::pageDocerWps;
			break;
		case KPromeStartup::IndependentEt:
			nTabType = KPromePluginWidget::pageDocerEt;
			break;
		case KPromeStartup::IndependentWpp:
			nTabType = KPromePluginWidget::pageDocerWpp;
			break;
		case KPromeStartup::IndependentPdf:
			nTabType = KPromePluginWidget::pageDocerPdf;
			break;
		case KPromeStartup::IndependentFlow:
			nTabType = KPromePluginWidget::widgetProcessonlocal_Flow;
			break;
		case KPromeStartup::IndependentMind:
			nTabType = KPromePluginWidget::widgetProcessonlocal_Mind;
			break;
		default:
			break;
		}

		if (nTabType == KPromePluginWidget::widgetProcessonlocal_Flow ||
				nTabType == KPromePluginWidget::widgetProcessonlocal_Mind)
		{
			if (krt::kcmc::support("VisioReadOnly"))
			{
				nTabType = KPromePluginWidget::pageDocerWps;
			}
			else if (!krt::kcmc::support("FlowMind"))
			{
				if (firstTabType != KPromePluginWidget::widgetProcessonlocal_Flow &&
						firstTabType != KPromePluginWidget::widgetProcessonlocal_Mind)
				{
					nTabType = firstTabType;
				}
				else
				{
					nTabType = KPromePluginWidget::pageDocerWps;
				}
			}
		}

		if (!m_tabMap.contains(nTabType))
			nTabType = KPromePluginWidget::pageDocerWps;
		setCurrentTab(nTabType);

	}
	else
	{
		int lastTab = KPromeNewDocsTabBarConfig::getInstance()->getLastSelectedTab();
		if (lastTab == KPromeNewDocsTabInfoObj::INVALID_NEWDOCS_TAB)
			lastTab = KPromePluginWidget::pageDocerWps;//默认显示WPS Tab页

		if (lastTab == KPromePluginWidget::widgetCloudFolderShare && !showShareFolderTab()) 
			lastTab = KPromePluginWidget::pageMytpl;
		setCurrentTab(lastTab);
	}
}

int KPromeNewDocsTabBar::getCurrentTabType()
{
	return m_currentTabType;
}

KPromeNewDocsTab* KPromeNewDocsTabBar::getCurrentSelecteTab()
{
	if (m_tabMap.contains(m_currentTabType))
		return m_tabMap[m_currentTabType];
	else
		return nullptr;
}

KPromeNewDocsTab* KPromeNewDocsTabBar::getTabByType(int tabType)
{
	if (m_tabMap.contains(tabType))
		return m_tabMap[tabType];
	else
		return nullptr;
}

void KPromeNewDocsTabBar::paintEvent(QPaintEvent* ev)
{
	QPainter painter(this);
	painter.setRenderHint(QPainter::Antialiasing);
	QRect rc = rect();
	QColor bgColor = KDrawHelper::getColorFromTheme("KPromeNewDocsTabBarNew", KDrawHelper::Prop_Background, KDrawHelper::stringToColor("#FFFFFFFF"));
	QColor borderColor = KDrawHelper::getColorFromTheme("KPromeNewDocsTabBarNew", "bottom-border", KDrawHelper::stringToColor("#FFFFFFFF"));
	QRect borderRect = rc.adjusted(0, rc.height() - m_borderHeight, 0, 0);
	painter.fillRect(rc, bgColor);
	painter.fillRect(borderRect, borderColor);

	if (m_bMoving)
	{
		painter.fillRect(m_movingArea, m_curMovingTabBkColor);
	}
}

void KPromeNewDocsTabBar::initUIWidget()
{
	setFixedHeight(KLiteStyle::dpiScaled(60));
	setMinimumWidth(KLiteStyle::dpiScaled(900));
	m_docsLayout = new QHBoxLayout();
	m_docsLayout->setContentsMargins(QMargins(0, 14, 0, 14));
	m_docsLayout->setSpacing(KLiteStyle::dpiScaled(16));

	QVBoxLayout* mainLayout = new QVBoxLayout(this);
	mainLayout->setMargin(0);
	mainLayout->setSpacing(0);
	mainLayout->addLayout(m_docsLayout);
}

void KPromeNewDocsTabBar::setCurrentTab(int tabType)
{
	onTabClicked(tabType);
}

void KPromeNewDocsTabBar::setMouseHoverIgnore(bool state)
{
	m_bIgnoreMouseHover = state;
}

bool KPromeNewDocsTabBar::ignoreMouseHover() const
{
	return m_bIgnoreMouseHover;
}

void KPromeNewDocsTabBar::updateNewDocsTabBar()
{
	KxPluginsSettings regSetting;
	regSetting.beginGroup("kstartpage");
	regSetting.beginGroup("officenav");

	regSetting.beginGroup("custEntry");
	QStringList removedEntry = regSetting.allKeys();
	regSetting.endGroup();

	regSetting.beginGroup("userEntry");
	QStringList userEntry = regSetting.allKeys();
	regSetting.endGroup();

	regSetting.endGroup();
	regSetting.endGroup();
	for (auto it = m_tabMap.constBegin(); it != m_tabMap.constEnd(); ++it)
	{
		bool tabVisible = true;
		KPromeNewDocsTab* pItemTab = it.value();

		if (!pItemTab->getRelyPlugin().isEmpty() && !pItemTab->isRelyPluginExit())
			tabVisible = false;
		else if (!pItemTab->getRelyPlugin().isEmpty() &&
			removedEntry.contains(pItemTab->getRelyPlugin()) &&
			!userEntry.contains(pItemTab->getRelyPlugin()))
			tabVisible = false;

		// 在原有流程上 判断共享文件夹tab的动态可见
		if (tabVisible && (it.key() == KPromePluginWidget::widgetCloudFolderShare))
			setCloudFolderShareTabVisible(pItemTab, it.key(), isCurrentWorkspacePersonal());
		else
			pItemTab->setVisible(tabVisible);
	}
}

void KPromeNewDocsTabBar::clearUnReadMessageCount(int tabType)
{
	if (!m_tabMap.contains(tabType))
		return;

	m_tabMap[tabType]->clearUnReadMessageCount();
}

void KPromeNewDocsTabBar::updateUnReadMessageCount(int tabType)
{
	if (!m_tabMap.contains(tabType))
		return;

	m_tabMap[tabType]->updateUnReadMessageCount();
}

void KPromeNewDocsTabBar::setUnReadMessageCount(int tabType, int count)
{
	if (!m_tabMap.contains(tabType))
		return;

	m_tabMap[tabType]->setUnReadMessageCount(count);
}

int KPromeNewDocsTabBar::getUnReadMessageCount(int tabType)
{
	if (!m_tabMap.contains(tabType))
		return 0;

	return m_tabMap[tabType]->getUnReadMessageCount();
}

void KPromeNewDocsTabBar::onTabClicked(int tabType)
{
	int unReadMessageCount = 0;
	if (tabType == KPromePluginWidget::pageMytpl)
	{
		unReadMessageCount = getUnReadMessageCount(tabType);
		if (unReadMessageCount != 0)
		{
			m_tabMap[tabType]->setUnReadMessageCount(0);
			emit refreshWebWidget(tabType);
		}
	}

	if (m_currentTabType == tabType)
	{
		emit tabClicked(tabType);
		return;
	}
	if (tabType == KPromeNewDocsTabInfoObj::INVALID_NEWDOCS_TAB)
		return;

	if (!m_tabMap.contains(tabType))
		return;

	// 新增上报选中tab
	QString collect = m_tabMap[tabType]->collect();
	QHash<QString, QString> args;
	args.insert("select", collect);
	promeApp->getDcInfo()->postGeneralEvent("newpage_tabbar_click", args);

	KPromeNewDocsTab* tab = m_tabMap[tabType];
	if (tab->isEntrance())
	{
		emit tabClicked(tabType);
		return;
	}
	QString str = KPromePluginWidget::convertWidgetType((KPromePluginWidget::WidgetType)tabType);
	QByteArray ba = str.toLatin1();
	PERFLOGBEGIN2("PrometheusNewDocsTabChange", ba.constData());

	if (m_tabMap.contains(m_currentTabType))
		m_tabMap[m_currentTabType]->setSelectedStatus(false);

	m_movingAreaBkColorAnimation->stop();
	m_movingAreaBkColorAnimation->setDuration(100);
	if (m_typeColorMap.contains(m_currentTabType))
		m_movingAreaBkColorAnimation->setStartValue(m_typeColorMap[m_currentTabType]);
	else
		m_movingAreaBkColorAnimation->setStartValue(QColor(0xFFFFFF));
	m_movingAreaBkColorAnimation->setEndValue(m_typeColorMap[tabType]);

	m_movingAreaAnimation->stop();
	m_movingAreaAnimation->setDuration(100);

	if (m_tabMap.contains(m_currentTabType))
	{
		m_movingAreaAnimation->setStartValue(m_tabMap[m_currentTabType]->geometry());
	}
	else
	{
		m_movingAreaAnimation->setStartValue(m_tabMap[tabType]->geometry());
	}
	m_movingAreaAnimation->setEndValue(m_tabMap[tabType]->geometry());

	m_movingAreaBkColorAnimation->start();
	if (m_currentTabType != KPromeNewDocsTabInfoObj::INVALID_NEWDOCS_TAB)
	{
		m_bMoving = true;
		m_currentTabType = tabType;
		m_movingAreaAnimation->start();
	}
	else
	{
		m_currentTabType = tabType;
		onAnimFinished();
	}

#ifdef Q_OS_MACOS
	//v6更新一下tab标签
	if (ksolite::isSupported(ksolite::NewIntranet) && !ksolite::isSupported(ksolite::IntranetVersion7))
		emit tabTitleChanged(tab->getTitleText(), tab->getIcon());
#endif
}

QColor KPromeNewDocsTabBar::movingAreaBkColor()
{
	return m_curMovingTabBkColor;
}

void KPromeNewDocsTabBar::setMovingAreaBkColor(QColor color)
{
	m_curMovingTabBkColor = color;
	update();
}

QRect KPromeNewDocsTabBar::moveArea()
{
	return m_movingArea;
}

void KPromeNewDocsTabBar::setMoveArea(const QRect& rt)
{
	m_movingArea = rt;
}

void KPromeNewDocsTabBar::setCloudFolderShareVisible(bool visible)
{
	QString plugin;
	for (auto it = m_tabMap.constBegin(); it != m_tabMap.constEnd(); it++)
	{
		KPromeNewDocsTab* tab = it.value();
		int tabType = it.key();
		plugin = tab->getRelyPlugin();
		if (tabType == KPromePluginWidget::widgetCloudFolderShare)
		{
			setCloudFolderShareTabVisible(tab, tabType, visible);
			break;
		}
	}
}

void KPromeNewDocsTabBar::setCloudFolderShareTabVisible(KPromeNewDocsTab* tab, int tabType, bool visible)
{
	if (!tab)
		return;

	tab->setVisible(visible);
	// 如果当前选中了共享文件夹tab且需要隐藏，切换到默认tab
	if ((tabType == getCurrentTabType()) && !visible)
		onTabClicked(KPromePluginWidget::pageMytpl);
}

bool KPromeNewDocsTabBar::isCurrentWorkspacePersonal()
{
	// 未登录用户默认返回个人空间
	auto cloudSvrProxy = promeApp->cloudSvrProxy();
	if (!cloudSvrProxy)
		return true;

	// 已登录判断用户工作空间 ID为空或者个人空间
	if (cloudSvrProxy->isLogined())
	{
		bool isPersonalWorkspace = false;
		QString curId = cloudSvrProxy->currentWorkspaceId();
		if (curId.isEmpty() || cloudSvrProxy->isPersonalWorkspace(curId))
			isPersonalWorkspace = true;
		return isPersonalWorkspace;
	}
	else
	{
		return true;
	}
}

bool KPromeNewDocsTabBar::showShareFolderTab()
{
	if (!krt::kcmc::support("NewDocsShareFolderTab"))
		return false;

	bool show = false;
	KxPluginsSettings regSetting;
	regSetting.beginGroup("knewdocs");
	QString showValue = regSetting.value("showShareFolderTab", "true").toString();
	regSetting.endGroup();
	if (showValue == "true") 
		show = true;

	return show;
}

void KPromeNewDocsTabBar::onAnimFinished()
{
	m_bMoving = false;

	if (m_tabMap.contains(m_currentTabType))
	{
		m_tabMap[m_currentTabType]->setSelectedStatus(true);
		KPromeNewDocsTabBarConfig::getInstance()->updateLastSelectedTab(m_currentTabType);
		emit tabClicked(m_currentTabType);
	}
}

void KPromeNewDocsTabBar::onCurrentWorkspaceChanged(const QString& curId, const QString& oldId)
{
	auto cloudSvrProxy = promeApp->cloudSvrProxy();
	if (!cloudSvrProxy)
		return;

	bool isCloudFolderShareTabVisible = false;
	if (curId.isEmpty() || cloudSvrProxy->isPersonalWorkspace(curId))
		isCloudFolderShareTabVisible = true;
	setCloudFolderShareVisible(isCloudFolderShareTabVisible);
}

void KPromeNewDocsTabBar::onUserInfoChange()
{
	setCloudFolderShareVisible(isCurrentWorkspacePersonal());
}
