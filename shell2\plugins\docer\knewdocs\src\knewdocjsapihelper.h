﻿#ifndef __KNEWDOCS_JSAPIHELPER_H__
#define __KNEWDOCS_JSAPIHELPER_H__

#include <ksolite/kcommonwebwidget.h>
#include <ksolite/kcommonwebapi.h>
#include <kprometheus/kpromepluginwidget.h>
#include <kdownload/kdownload.h>
#include "kdocerresnetwork.h"

class KxNewDocJsApi;
class KxPromePdfWindowJsApi;
class KPromeMainWindow;
struct DownloadFileInfo;

class KxNewDocJsApiHelper : public ksolite::KxCommonJsApiHelper
{
public:
	static KxNewDocJsApiHelper& instance()
	{
		static KxNewDocJsApiHelper inst;
		return inst;
	}

	QString getDownloadID(const QString& templateID);
	void addDownloadTask(const QString& downloadId, const QString& templateId);
	bool removeByResId(const QString& id);
	bool removeByTaskId(const QString& id);
	int downloadCount() const;
	//给jsapi用
	void notifyCancelDownload(KxNewDocJsApi* notifier, const QString& templateId);
	void notifyDownloadProgress(KxNewDocJsApi* notifier, const QString& templateId, int nPercent);
	void notifyDownloadSuccess(KxNewDocJsApi* notifier, const QString& templateId, const QString& fileName);
	void notifyDownloadError(KxNewDocJsApi* notifier, const QString& templateId, kdocerresnetwork::DownloadFailInfo info);
	//给KxPromePdfWindowJsApi用
	void notifyCancelDownload(KxPromePdfWindowJsApi* notifier, const QString& templateId);
	void notifyDownloadProgress(KxPromePdfWindowJsApi* notifier, const QString& templateId, int nPercent);
	void notifyDownloadSuccess(KxPromePdfWindowJsApi* notifier, const QString& templateId, const QString& fileName);
	void notifyDownloadError(KxPromePdfWindowJsApi* notifier, const QString& templateId, kdocerresnetwork::DownloadFailInfo info);
	QStringList openFileTabText(KxNewDocJsApi* jsAPi, KPromePluginWidget::WidgetType wt, int maxCount = 20);
	void openFileTabText(KPromeMainWindow* mw, KPromePluginWidget::WidgetType wt, int maxCount, QStringList& openTabList);
private:
	explicit KxNewDocJsApiHelper();
	~KxNewDocJsApiHelper();
	QMap<QString, QString> downloadIDs;
};

#endif // __KNEWDOCS_JSAPIHELPER_H__