(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[7],{"267c":function(t,e,a){t.exports=a.p+"images/wps.png"},"3a9b":function(t,e,a){t.exports=a.p+"images/wpp.png"},"4a78":function(t,e,a){"use strict";var i=a("13bc"),s=a("c8fc"),r=a("2f62"),l=a("beeb");const o=a("267c"),n=a("3a9b"),c=a("db6c"),p=function(t){if(t){var e=l["u"][t];if(e){"?"==e.slice(-1)?l["u"].appUrl=e.slice(0,-1):l["u"].appUrl=e;var a=e.indexOf("v3.php/");l["u"].privateUrl=-1!=a?e.slice(0,a+7):l["u"].appUrl.slice(0,-4),l["u"].isSupportIntranetTpl=!0}}},u=function(){i["a"].IsSupportIntranetTemplate().then(t=>{t&&i["a"].getCreateDocUrl().then(t=>{t&&(l["u"].wps=t.url_wps,l["u"].et=t.url_et,l["u"].wpp=t.url_wpp,p(l["e"].name))}).catch(t=>{console.log(t)})}).catch(t=>{console.warn(t)})};u(),e["a"]={computed:{...Object(r["c"])("common",["isEnterprise"])},data:()=>({localTplData:[],customTplInfo:[],customTplData:[],recentlyData:[]}),methods:{_getDefaultPreviewImgSrc(){switch(l["e"].name){case"wps":return o;case"wpp":return n;case"et":return c;default:return n}},_loadMoreData(t,e,a){if(l["u"].isSupportIntranetTpl)return this._getPrivateTplByTag(t,e,a)},_getRecentlyOpenFile(){this._getFile().then(t=>{this.recentlyData=t},()=>{console.error("获取最近打开文档失败")})},_getTplData(){return l["u"].isSupportIntranetTpl?this._getPrivateTpl():this._getLocalTpl()},_getFile(){return new Promise((t,e)=>{i["a"].getRecentFiles({maxCount:-1}).then(e=>{e&&t(e),t([])}).catch(t=>{e(t)})})},_getLocalRecentTpl(t){return i["a"].getRecentUseTemplate({appName:t})},_removeRecentTemplate(t){return i["a"].removeRecentTemplate({templatePath:t})},_getLocalTpl(){return i["a"].getLocalTemplateFileInfo({appName:l["e"].name}).then(t=>(Array.isArray(t)&&t.forEach(t=>{Array.isArray(t.fileList)?t.total_num=t.fileList.length:t&&(t.total_num=0)}),t))},_getCustomTpl(){return i["a"].getCustomTemplateFileInfo({appName:l["e"].name})},_getPrivateTags(){return new Promise(t=>{s["a"].get("pritag").getPrivateTags({data:{mb_app:l["e"].mark},privateUrl:{url:l["u"].privateUrl}}).then(e=>{if("ok"===e.result){let i=0;var a=[];e.data.forEach(t=>{t[0].item.forEach(t=>{if(i++,i>3)return!1;a.push(t)})}),t(a)}})})},_getPrivateTplByTag(t,e=1,a=10){return new Promise(i=>{s["a"].get("pritpl").getPriTplByTags({data:{term:t.k,sort_by:"mix",moban_type:"",sort_way:"down",limit:a,page:e,vip:1},privateUrl:{url:l["u"].appUrl}}).then(e=>{var a={tagName:t.v};"ok"===e.result&&e.data&&0!==e.data.total_num?(a.fileList=e.data.data,a.total_num=e.data.total_num):(a.fileList=[],a.total_num=0),i(a)})})},_getPrivateTpl(){this.localTplData=[];let t=[];return this._getPrivateTags().then(e=>(e.forEach(a=>{t.push(this._getPrivateTplByTag(a).then(t=>{e.forEach(e=>{e.v==t.tagName&&(e.tagName=t.tagName,e.fileList=t.fileList)})}))}),Promise.all(t).then(()=>(this.localTplData=e,e))))},_getCustomOfficialTemplate(){this.customData=[],i["a"]._getCustomOfficialTemplate().then(t=>{t&&t.length>0&&(console.log("自定义模板：",t),this.customData=t)})},_searchPrivateTplData(t,e,a){return s["a"].get("pritpl").searchPriTpl({data:{term:t,page:e,is_push:1,limit:a,vip:1},privateUrl:{url:l["u"].appUrl}}).then(t=>("ok"===t.result&&t.data&&Array.isArray(t.data.moban)&&(t.data.fileList=t.data.moban),t.data))},_searchlocalTplData(t){return i["a"].localTemplateSearch({keyWord:t,appName:l["e"].name}).then(t=>(Array.isArray(t.fileList)?t.total_num=t.fileList.length:t&&(t.total_num=0),t))},_searchTplDataFn(t,e,a){return l["u"].isSupportIntranetTpl?this._searchPrivateTplData(t,e,a):this._searchlocalTplData(t)},_localSearch(t,e,a){return this._searchTplDataFn(t,e,a).then(t=>(console.log("_localSearch: "+JSON.stringify(t)),t))},_formartPrevieImage(t){const e=t=>{t.thumb_img="data:image/png;base64,"+t.preview};return Array.isArray(t)?t.forEach(t=>{t.preview&&e(t)}):t&&t.preview&&e(t),t}}}},"5f86":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{ref:"rMainContentCon",staticClass:"screen g-component-bg"},[e("div",{staticClass:"screen__container"},[e("FilterBar",{ref:"filterBar",attrs:{pageType:t.pageType,pageId:t.pageId,cateList:t.cateList,filterParams:t.filterParams,isFixed:t.isFixed,limit:t.limit},on:{change:t.handleChange}}),e("div",{directives:[{name:"stick",rawName:"v-stick"}],staticClass:"screen__list",class:{screen__scroll:t.isFixed}},[e("List",{attrs:{list:t.list,limit:t.limit,maxCount:t.maxCount,pageName:"topic",pageSize:t.pageSize,isMyBought:!0,commonCollectParams:t.commonCollectParams},on:{loadMore:t.handleLoadData}}),t.statusCode?e("DataStatus",{attrs:{statusCode:t.statusCode},on:{retry:function(e){return t.initList(!1)}}}):t._e()],1)],1)])},s=[],r=a("beeb"),l=a("2f62"),o=a("f348"),n=a("f905"),c=(a("c8fc"),a("eff4")),p=a("a4c7"),u=a("a6c6"),h=a("e400"),m=a("4a78");a("3387");const f=o["a"].debounce(100);let g;var d={mixins:[h["a"],m["a"]],nonReactive:{localData:[]},data(){return{list:[],maxCount:0,pageId:0,pageType:"",filterBarData:{},filterParams:{activeCateId:"",activeTagId:"",activeColorId:"",activeBgId:"",activePayId:""},resource_params:[],statusCode:"",isInterfaceLoading:!1,isFixed:!1,limit:0,pageSize:0,isScroll:!1,policy:"",request_id:"",cateList:[]}},beforeRouteEnter(t,e,a){a(e=>{e.$nextTick(()=>{let a=t&&t.query||{};e.routeInit(a),e.ajustTplLayout(),e.init(!0)})})},beforeRouteUpdate(t,e,a){let i=t&&t.query||{};this.routeInit(i),this.ajustTplLayout(),this.init(!0),a()},mounted(){g=new ResizeObserver(()=>{f(()=>{console.log("The rMainContentCon was resized"),this.ajustTplLayout()})}),g.observe(this.$refs.rMainContentCon),u["a"].$on(r["p"].scrollTop,()=>{this.isScroll=!0,this.isFixed=!1;let t=this.$refs.filterBar.$el;t&&(t.style.top=0,t.style.padding=0)}),u["a"].$on(r["p"].resizeWindow,()=>{this.ajustTplLayout(),this.$nextTick(()=>{this.scrollHome()})})},beforeDestroy(){g.unobserve(this.$refs.rMainContentCon),u["a"].$off(r["p"].scrollTop),u["a"].$off(r["p"].resizeWindow)},computed:{...Object(l["c"])("common",["appName"]),...Object(l["c"])("user",["isLogin","isClientLogin"]),...Object(l["c"])("category",["currentCategory"]),isWpp(){return"wpp"==this.appName},activeCateItem(){return this.cateList.find(t=>t.value==this.filterParams["activeCateId"])},pageOffset(){return this.list.filter(t=>!!t.id).length},commonCollectParams(){var t,e;return{$m_n:"resource_list",$e_i:{display:10613,click:10614},$track:"",resource_count:this.maxCount,first_cat_id:null===(t=this.activeCateItem)||void 0===t?void 0:t.id,first_cat_name:null===(e=this.activeCateItem)||void 0===e?void 0:e.label,color:this.getSelectedLabel("filter_color"),style:this.getSelectedLabel("filter_style"),payment_type:this.getSelectedLabel("filter_mb_type"),recommend_id:this.request_id,policy:this.policy,category_name:this.currentCategory.name}}},watch:{isLogin:function(){this.init()}},methods:{...Object(l["b"])("common",["setAppName"]),init(){this.isEnterprise&&this._getTplData().then(t=>{console.log("_getTplData: "+JSON.stringify(t));const e=t||[];let a=[];e.forEach((t,e)=>{a[e]={label:t.tagName,value:t.tagName},this._formartPrevieImage(t.fileList)}),this.$options.nonReactive.localData=e;const i=e[0],s=i.fileList;this.filterParams["activeCateId"]=i.tagName,this.list=[{isNewDoc:!0}].concat(s),this.maxCount=i.total_num,this.statusCode="",this.cateList=a,this._getCustomTpl().then(t=>{console.log("getCustTpl: "+JSON.stringify(t));let e=[];const a=t||[];a.forEach((t,a)=>{e[a]={label:t.tagName,value:t.tagName},this._formartPrevieImage(t.fileList)}),this.$options.nonReactive.localData.push(...a),this.cateList.push(...e)}).catch(t=>{console.warn(t)})})},handleLoadData(){if(!r["u"].isSupportIntranetTpl)return;if(this.isInterfaceLoading)return;this.isInterfaceLoading=!0;const t=this.filterParams["activeCateId"].value,e=this.$options.nonReactive.localData.find(e=>e.tagName===t);this._loadMoreData(e,this.pageOffset/this.pageSize+1,this.pageSize).then(t=>{const a=t||[];this.list=this.list.concat(a.fileList),e.fileList=e.fileList.concat(a.fileList)}).catch(t=>{console.warn(t)}).finally(()=>{this.isInterfaceLoading=!1})},handleChange(t,e){var a;console.log(`handleChage: key:${t}, vale:${e}`),this.resetScroll(),"activeCateId"==t&&Object.keys(this.filterParams).forEach(t=>{"activeCateId"!==t&&(this.filterParams[t]="")}),this.filterParams[t]=e,this.pageId=null===(a=this.filterBarData)||void 0===a?void 0:a.page_id;const i=this.$options.nonReactive.localData.find(t=>t.tagName===e);if(i){const t=i.fileList;this.list=[{isNewDoc:!0}].concat(t),this.maxCount=i.total_num,this.statusCode=""}},routeInit(t){this.resetPageStatus(),this.resetScroll(),this.pageId=+(null===t||void 0===t?void 0:t.id)||0,this.pageType=t.pageType||""},resetPageStatus(){this.statusCode="",this.maxCount=0,this.isInterfaceLoading=!1,Object.keys(this.filterParams).forEach(t=>{this.filterParams[t]=""}),this.pageSize=0,this.resource_params=[],this.policy="",this.request_id=""},scrollHome(){},resetScroll(){let t=this.$refs&&this.$refs.rMainContentCon;t&&(t.scrollTop=0),this.isFixed=!1;let e=this.$refs.filterBar.$el;e&&(e.style.top=0,e.style.padding=0)},ajustTplLayout(){let[t,e,a]=o["a"].getScreenDisplay(this.$refs.rMainContentCon);if(this.limit!=e){this.limit=e;let t=this.$refs.filterBar.$el;t&&(t.style.width=a+"px")}this.pageSize||(this.pageSize=t*e||30)},getSelectedLabel(t){var e,a,i;let s=null===(e=this.activeCateItem)||void 0===e||null===(a=e.props)||void 0===a?void 0:a[t];if(null!==s&&void 0!==s&&null!==(i=s.options)&&void 0!==i&&i.length&&null!==s&&void 0!==s&&s.state){var r;let e={filter_color:"activeColorId",filter_style:"activeTagId",filter_mb_type:"activePayId"}[t];return null===(r=s.options.find(t=>t.value==this.filterParams[e]))||void 0===r?void 0:r.label}return""}},components:{FilterBar:n["a"],List:c["a"],DataStatus:p["a"]}},v=d,_=(a("ce1b"),a("2877")),y=Object(_["a"])(v,i,s,!1,null,"679a6a4b",null);e["default"]=y.exports},7707:function(t,e,a){},ce1b:function(t,e,a){"use strict";a("7707")},db6c:function(t,e,a){t.exports=a.p+"images/et.png"}}]);