﻿#include "stdafx.h"
#include "kpromenewdocstab.h"
#include "kpromenewdocstabbar.h"
#include "kpromenewdocstabbarconfig.h"
#include <public_header/kliteui/kdrawhelper.h>
#include "kprometheus/kpromestyle.h"
#include "kprometheus/kpromestyleoption.h"
#include "kprometheus/kpromeapplication.h"
#include "ksolite/kdcinfoc.h"
#include "ksolite/kdomainmgr.h"
#include "kprometheus/kpromeinfocollcethelper.h"
#include "utility/kxsettings.h"
#include "knewdocjsapihelper.h"
#include "ksolite/kpluginmgr.h"
#include "kprometheus/kpromeapplication.h"
#include "kprometheus/kpromeskin.h"

#define szpluginname		"knewdocs"
static const QString g_strUnReadMessage = "unReadMessage_";

KPromeNewDocsTab::KPromeNewDocsTab(KPromeNewDocsTabBar* parent)
	: QWidget(parent)
	, m_parentTab<PERSON><PERSON>(parent)
	, m_bSelected(false)
	, m_tabInfoObj(nullptr)
	, m_bGhostIsComing(false)
	, m_bNeedUnReadPoint(false)
	, m_unReadMessageCount(0)
	, m_bDcInfoSended(false)
{
	setAttribute(Qt::WA_Hover, true);
	setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
#ifdef Q_OS_DARWIN
	setAttribute(Qt::WA_TranslucentBackground);
	setFixedSize(KLiteStyle::dpiScaledSize(QSize(180, 40)));
	setCursor(QCursor(Qt::PointingHandCursor));
#endif
}

KPromeNewDocsTab::~KPromeNewDocsTab()
{
	if (m_bNeedUnReadPoint)
		saveRedPointNum();
}

void KPromeNewDocsTab::saveRedPointNum()
{
	KxPluginsSettings regSetting;
	regSetting.beginGroup(szpluginname);
	regSetting.setValue(g_strUnReadMessage + m_tabInfoObj->tabType(), m_unReadMessageCount);
}

void KPromeNewDocsTab::initByTabInfo(KPromeNewDocsTabInfoObj* tabInfoObj)
{
	m_tabInfoObj = tabInfoObj;
	if (m_tabInfoObj)
		setProperty("qtspyName", "KPromeNewDocsTab_" + m_tabInfoObj->id());
	//读取注册表中的下载数目
	if (m_bNeedUnReadPoint)
	{
		KxPluginsSettings regSetting;
		regSetting.beginGroup(szpluginname);
		bool ok = false;
		QString strRedPointNum = regSetting.value(g_strUnReadMessage + m_tabInfoObj->tabType(), 0).toString();
		m_unReadMessageCount = strRedPointNum.toInt(&ok);
		if (!ok)
			m_unReadMessageCount = 0;
	}
}

bool KPromeNewDocsTab::getSelectedStatus()
{
	return m_bSelected;
}

void KPromeNewDocsTab::setSelectedStatus(bool selected)
{
	m_bSelected = selected;
	m_bGhostIsComing = false;
	update();
}

void KPromeNewDocsTab::clearUnReadMessageCount()
{
	setUnReadMessageCount(0);
}

void KPromeNewDocsTab::updateUnReadMessageCount()
{
	m_unReadMessageCount += 1;
	saveRedPointNum();
}

int KPromeNewDocsTab::getUnReadMessageCount()
{
	return m_unReadMessageCount;
}

void KPromeNewDocsTab::setUnReadMessageCount(int num)
{
	if (m_unReadMessageCount != num)
	{
		m_unReadMessageCount = num;
		saveRedPointNum();
	}
}

QString KPromeNewDocsTab::getStyleName()
{
	return m_tabInfoObj->styleName();
}

QString KPromeNewDocsTab::getRelyPlugin()
{
	return m_tabInfoObj->relyPlugin();
}

bool KPromeNewDocsTab::isRelyPluginExit()
{
	return m_tabInfoObj->isRelyPluginExit();
}

QString KPromeNewDocsTab::getTitleText()
{
	return m_tabInfoObj->text();
}

QString KPromeNewDocsTab::collect()
{
	return m_tabInfoObj->collect();
}

bool KPromeNewDocsTab::isEntrance()
{
	return false;
}

QIcon KPromeNewDocsTab::getIcon()
{
	return m_tabInfoObj->icon();
}

QSize KPromeNewDocsTab::sizeHint() const
{
	KPromeStyleOptionNewDocsTab option;
	initStyleOption(&option);
	//因爲KPromeNewDocsTab的parent是QScrollArea，QScrollArea的style会被应用到子widget的style()中，导致拿不到正确的sizehint
	if (const KPromeStyle* promeStyle = dynamic_cast<const KPromeStyle*>(promeApp->style()))
	{
		return promeStyle->sizeMetric(KPromeStyle::SM_KPromeNewDocsTab, &option, this);
	}
	return QSize(0, 0);
}

QSize KPromeNewDocsTab::minimumSizeHint() const
{
	KPromeStyleOptionNewDocsTab option;
	initStyleOption(&option);
	return KPromeStyle::sizeMetric(
		style(), KPromeStyle::SM_KPromeNewDocsTab,
		&option, this);
}

void KPromeNewDocsTab::paintEvent(QPaintEvent* ev)
{
	QPainter dc(this);
	KPromeStyleOptionNewDocsTab option;
	initStyleOption(&option);
	style()->drawControl(
		static_cast<QStyle::ControlElement>(KPromeStyle::CE_KPromeNewDocsTab),
		&option, &dc, this);
}

void KPromeNewDocsTab::mousePressEvent(QMouseEvent* ev)
{
	if (ev->button() != Qt::LeftButton || m_parentTabBar->ignoreMouseHover()) {
		ev->ignore();
		return;
	}

	if (rect().contains(ev->pos()))
	{
		if (m_tabInfoObj->getTabType() !=  -1)
		{
			// 发送一条信息收集
			{
				const QString& collect = m_tabInfoObj->collect();
				QHash<QString, QString> args;
				args.insert("click", collect);
			}
			emit tabClicked(m_tabInfoObj->getTabType());
			m_bGhostIsComing = true;
		}
		ev->accept();
	}
	else
	{
		ev->ignore();
	}
}

void KPromeNewDocsTab::initStyleOption(KPromeStyleOptionNewDocsTab* option) const
{
	option->init(this);
#ifdef Q_OS_MACOS
	option->icon = m_tabInfoObj->icon();
	option->text = m_tabInfoObj->text();
	option->spacing = KPromeStyle::dpiScaled(8);
	option->textHeight = KPromeStyle::dpiScaled(16);
	option->iconSize = KPromeStyle::dpiScaledSize(16, 16);
#else
	if (promeApp->promeSkin() && promeApp->promeSkin()->isDarkSkin())
		option->icon = KDrawHelper::loadIcon(m_tabInfoObj->icon().name() + "_dark");
	else
		option->icon = m_tabInfoObj->icon();
	option->text = m_tabInfoObj->text();
	option->spacing = KPromeStyle::dpiScaled(2);
	option->textHeight = KPromeStyle::dpiScaled(16);

	bool bIsIntranet = false;
	if (promeApp && promeApp->getDomainMgr())
		bIsIntranet = promeApp->getDomainMgr()->isIntranet();
	if (bIsIntranet)
		option->iconSize = KPromeStyle::dpiScaledSize(24, 24);
	else
		option->iconSize = KPromeStyle::dpiScaledSize(16, 16);
#endif
	option->textMargins = KPromeStyle::dpiScaledMargins(1, 0, 1, 0);
	option->tabStyleName = m_tabInfoObj->styleName();
	
	if (m_bNeedUnReadPoint && m_unReadMessageCount > 0)
		option->unReadMessageCount = m_unReadMessageCount > 9 ? "..." : QString::number(m_unReadMessageCount);
	else
		option->unReadMessageCount = "";

	option->unReadMessageCount = "";

	if (m_parentTabBar)
	{
		if (m_parentTabBar->getCurrentSelecteTab())
			option->hoverTabStyleName = m_parentTabBar->getCurrentSelecteTab()->getStyleName();
	}

	if (m_bSelected)
	{
		option->state |= QStyle::State_Selected;
		option->state |= QStyle::State_Sunken;
		option->icon = KDrawHelper::loadIcon(m_tabInfoObj->icon().name() + "_down");
	}
	if (m_bGhostIsComing)
	{
		option->state ^= QStyle::State_MouseOver;
	}
}

void KPromeNewDocsTab::setVisible(bool visible)
{
	if (!m_bDcInfoSended && visible)
	{
		KPromeNewDocsTabBarConfig::getInstance()->sendDisplayDcInfo(m_tabInfoObj);
		m_bDcInfoSended = true;
	}
	QWidget::setVisible(visible);
}
