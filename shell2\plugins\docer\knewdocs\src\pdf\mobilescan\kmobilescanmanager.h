#ifndef _KPDFMOBILESCAN_MANAGER_H_
#define _KPDFMOBILESCAN_MANAGER_H_

class KMobileScanManager : public QObject
{
	Q_OBJECT
	DECLARE_COUNT_ASSERT(KMobileScanManager)

public:
	void init();
	static KMobileScanManager* getMobileScanManager();
	void sendGeneralEvent(const QString& eventName, const QVariantMap& arg);
	void onMobileScanFinish(const QVariantMap& arg);
	QStringList& getScanfiles();
	void asynTempSpaceDownload(const QVariantMap& arg);
	void cancelAllTempSpaceDownload();
	void onSizeChange(const QVariantMap& arg);
	void onNearFieldFileTaskNotice(const QVariantMap& arg);
	void onNetWorkRequest(const QVariantMap& arg);
	void clear();

signals:
	void webDialogClose();
	void sigSizeChange(int, int);

private slots:
	void onDownloadStateChange(const QVariantMap& argMap);

private:
	explicit KMobileScanManager(QObject* parent = nullptr);
	~KMobileScanManager();
	bool initJsApiObj();
	void onGenerateFinish(bool bAllSuccess);

private:
	QStringList m_listFile;
	QStringList m_listTempPic;
};

#endif // _KPDFMOBILESCAN_MANAGER_H_