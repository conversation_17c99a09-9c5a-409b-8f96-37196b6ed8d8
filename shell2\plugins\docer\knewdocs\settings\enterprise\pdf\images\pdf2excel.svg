<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.4 (67378) - http://www.bohemiancoding.com/sketch -->
    <title>PDF转Excel</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="64" height="64" rx="6"></rect>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="#01" transform="translate(-871.000000, -1130.000000)">
            <g id="1366768" transform="translate(214.000000, 602.000000)">
                <g id="#01">
                    <g id="推荐模版" transform="translate(275.000000, 191.000000)">
                        <g id="分组-7" transform="translate(3.000000, 304.000000)">
                            <g id="PDF转Execl" transform="translate(347.000000, 0.000000)">
                                <g id="PDF转Excel" transform="translate(32.000000, 33.000000)">
                                    <mask id="mask-2" fill="white">
                                        <use xlink:href="#path-1"></use>
                                    </mask>
                                    <use id="Mask" fill="#4BBC4B" xlink:href="#path-1"></use>
                                    <g id="分组" mask="url(#mask-2)">
                                        <g transform="translate(16.000000, 16.000000)">
                                            <path d="M28,6 L28,8 C28,9.104 28.896,10 30,10 L32,10" id="Fill-3" fill="#FFFFFF"></path>
                                            <path d="M19.5,19 L24.5,19 C26.43,19 28,20.57 28,22.5 C28,24.43 26.43,26 24.5,26 L17,26 C16.448,26 16,25.552 16,25 C16,24.448 16.448,24 17,24 L24.5,24 C25.328,24 26,23.328 26,22.5 C26,21.672 25.328,21 24.5,21 L19.5,21 C17.57,21 16,19.43 16,17.5 C16,15.57 17.57,14 19.5,14 L27,14 C27.552,14 28,14.448 28,15 C28,15.552 27.552,16 27,16 L19.5,16 C18.672,16 18,16.672 18,17.5 C18,18.328 18.672,19 19.5,19 Z M30,12 C27.792,12 26,10.208 26,8 L26,6 L14,6 C12.896,6 12,6.896 12,8 L12,28 C12,29.104 12.896,30 14,30 L30,30 C31.104,30 32,29.104 32,28 L32,12 L30,12 Z" id="Fill-5" fill="#FFFFFF"></path>
                                            <g id="分组" stroke-width="1" fill="none">
                                                <path d="M9,14 L8,14 L8,17.2 C8,17.642 7.642,18 7.2,18 L5,18 C3.342,18 2,16.658 2,15 C2,13.342 3.342,12 5,12 L6,12 L6,4.8 C6,4.358 6.358,4 6.8,4 L9,4 C9.976,4 10.866,4.306 11.634,4.792 C12.298,4.3 13.112,4 14,4 L20,4 L20,2 C20,0.896 19.104,0 18,0 L2,0 C0.896,0 0,0.896 0,2 L0,20 C0,21.104 0.896,22 2,22 L10,22 L10,13.9 C9.676,13.964 9.342,14 9,14" id="Fill-7" fill="#FFFFFF"></path>
                                                <path d="M10,10 L10,8 C10,7.408 10.136,6.852 10.366,6.346 C9.958,6.13 9.496,6 9,6 L8,6 L8,12 L9,12 C9.352,12 9.686,11.928 10,11.816 L10,10 Z" id="Fill-9" fill="#FFFFFF"></path>
                                                <path d="M4,15 C4,15.552 4.448,16 5,16 L6,16 L6,14 L5,14 C4.448,14 4,14.448 4,15" id="Fill-11" fill="#FFFFFF"></path>
                                            </g>
                                            <path d="M10.036,28.9688 C10.042,29.2368 9.938,29.4968 9.748,29.6868 C9.738,29.6968 9.72,29.6928 9.708,29.7028 L6.494,32.8388 C6.104,33.2288 5.472,33.2288 5.082,32.8388 C4.692,32.4488 4.692,31.8168 5.082,31.4268 L6.586,30.0008 L4,30.0008 C1.79,30.0008 0,28.2088 0,26.0008 L0,25.0008 C0,24.4488 0.448,24.0008 1,24.0008 C1.552,24.0008 2,24.4488 2,25.0008 L2,26.0008 C2,27.1048 2.896,28.0008 4,28.0008 L6.65,28.0008 L5.066,26.5408 C4.676,26.1528 4.674,25.5188 5.064,25.1288 C5.452,24.7388 6.084,24.7368 6.476,25.1268 L6.48,25.1288 L9.708,28.2348 C9.72,28.2448 9.738,28.2388 9.748,28.2508 C9.938,28.4408 10.042,28.7008 10.036,28.9688" id="Fill-13" fill="#FFFFFF"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>