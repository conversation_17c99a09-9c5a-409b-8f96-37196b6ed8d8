﻿#ifndef __KTRANSFERTASK_H__
#define __KTRANSFERTASK_H__

#include <communication/file/kfiletaskinfo.h>
using namespace wps::qing;
struct KDscFileSendMsg
{
	enum class Operation
	{
		Unknown,
		Cancel,
	};
	enum class Status
	{
		Unknown,
		UploadStart,
		UploadFinish,
	};

	// 文件id，开始上传时为空
	QString fid;
	// 文件名
	QString fname;
	// 文件大小
	int64_t fsize{ -1 }; // 现有云文件实现逻辑，传 -1 时进行全文件下载
	// 生成规则跟近场一致
	QString transferId;
	// 1：取消发送（后续有需求再定），默认为0
	Operation operation{ Operation::Unknown };
	// 1：开始上传  2：完成上传
	Status status{ Status::Unknown };
	// 业务具体消息动作
	QString action;
	// 这里填业务具体的协议版本
	int version{ 2 };
	// 业务具体消息协议
	QString content;

	JSON_REGISTER(
		fid, "fid", false,
		fname, "fname", false,
		fsize, "fsize", false,
		transferId, "transferId", false,
		operation, "operation", false,
		status, "status", false,
		action, "action", false,
		version, "version", false,
		content, "content", false
	)
};

class KTransferTask : public QObject
{
	Q_OBJECT

public:
	enum class KFileTaskType
	{
		Unknown = 0,
		Receive = 1,
		Send = 2,
	};

	// 消息类型
	enum class KSpaceType
	{
		Unknown = 0,
		TempSpace = 1,
		NearField = 2,
	};

	// 临时空间任务状态
	enum class TempSpaceTaskStatus
	{
		OtherError = 1,  // 其它错误
		Waiting = 2,	 // 等待中
		Pause = 3,		 // 暂停
		Transfer = 4,	 // 传输中
		Error = 5,		 // 错误
		Cancel = 6,		 // 取消
		Finish = 7,		 // 完成
		Destroy = 256,	 // 删除
	};

	// 统一临时空间和近场状态
	enum class TaskStatus
	{
		Finish = 1,		// 传输完成
		Error = 2,		// 错误
		Transferring = 3,// 传输中
	};

	// 临时空间操作
	enum class TransTempSpaceAction
	{
		PauseTask = 0,		// 暂停任务
		ResumeTask = 1,		// 恢复任务
		CancelTask = 2,		// 取消任务
		RetryTask = 3,		// 重试任务
		DeleteErrorTask = 4,// 删除错误任务
		DeleteTask = 6,// 删除任务
	};

	struct TaskInfo
	{
		KSpaceType spaceType;
		QString taskId;
		QString fileId;
		QString path;
		qint64 fileSize;
	};


public:
	KTransferTask();
	~KTransferTask();

	void asynTempSpaceDownload(const QString& fileId,
		const QString& fileSize, const QString& path);
	void asynCancelAllTransfer();
	void nearFieldFileTaskNotice(const QVariantMap& argMap);
	void init();
	void uninit();
	void listen(const QString& action);

signals:
	void sigDownloadStateChange(const QVariantMap& argMap);
	void sigNearFieldMsgContent(const QString& content, const QString& ownerId);


private slots:
	void tempSpaceDownload(const QString& fileId,
		const QString& fileSize, const QString& path);
	void cancelAllTransfer();

	void onGetDownStateTimerTimeout();
	void onThreadStarted();

	void onDownTempSpaceCallBack(bool success, const QString& requestId,
		const QingIpcResult& result);
	void onGetDownTempTaskStateCallBack(bool success, const QString& requestId,
		const QingIpcResult& result);

	void onNearFieldFileTaskNotice(const SoftBus::Protocol::KFileTaskInfo& info);

private:
	void buildTask(const KSpaceType& spaceType, const QString& fileId, const QString& path, qint64 size);
	void deleteTask(const QString& fileId);
	void notifyDownloadError(int nType, const QString& fileId);
	void deleteTransferTask(const KSpaceType& spaceType, const QString& fileId, const QString& taskId);

private:
	QSet<QString> m_downingTaskId;
	QPointer<QTimer> m_downCheckTimer;
	QMap<QString, QString> m_stateRequestId;
	QMap<QString, QString> m_transferRequestId;
	QMap<QString, TaskInfo> m_taskMrg;
	QThread m_workerThread;

	QSet<QString> m_listenedActions;
};

KTransferTask& getTransferTask();

#endif//__KTRANSFERTASK_H__
