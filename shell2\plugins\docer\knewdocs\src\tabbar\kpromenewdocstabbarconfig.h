﻿#ifndef __KPROMETHEUS_NEW_DOCS_TAB_BAR_CONFIG_H__
#define __KPROMETHEUS_NEW_DOCS_TAB_BAR_CONFIG_H__

#include <QDomElement>
#include "kprometheus/kpromepluginwidget.h"
#include <kdownload/kdownload.h>

class KPromeNewDocsTabInfoObj : public QObject
{
	Q_OBJECT
	Q_PROPERTY(QString id READ id WRITE setId)
	Q_PROPERTY(QString text READ text WRITE setText)
	Q_PROPERTY(QString webTitleText READ webTitleText WRITE setWebTitleText)
	Q_PROPERTY(QString styleName READ styleName WRITE setStyleName)
	Q_PROPERTY(QIcon icon READ icon WRITE setIcon)
	Q_PROPERTY(QString tabType READ tabType WRITE setTabType)
	Q_PROPERTY(QString relyPlugin READ relyPlugin WRITE setRelyPlugin)
	Q_PROPERTY(QString collect READ collect WRITE setCollect)
	Q_PROPERTY(bool isOpenUrl READ isOpenUrl WRITE setIsOpenUrl)
	Q_PROPERTY(QString openUrl READ openUrl WRITE setOpenUrl)
	Q_PROPERTY(bool isNeedSeparator READ isNeedSeparator WRITE setIsNeedSeparator)
	Q_PROPERTY(bool waitJs READ waitJs WRITE setWaitJs)
public:
	enum NewDocTabType {
		INVALID_NEWDOCS_TAB = KPromePluginWidget::invalidWidgetType,
		//不走KPromePluginWidgetType的入口，入口的代码添加整合到新建插件中
		newdocExtendWidgetTypeBase = KPromePluginWidget::promePluginWidgetEnd + 1,
		newdocWidgetDocerKDocs,
		pageQuickCreate = newdocWidgetDocerKDocs+1,
		// 轻文档
		pageKOtl,
		// 轻表格
		pageKSheet,
		// 轻维表
		pageKDbSheet,
		// 固定显示模版库
		pageTemplateLib,
		NewdocsExtentEnd
	};
	Q_ENUM(NewDocTabType)
	static QMetaEnum& getTabTypeEnumrator();
	static NewDocTabType convertTabType(const QString& strTabType);
	static QString convertTabType(NewDocTabType type);
	static int convertTabIntType(const QString& strTabType);
	static QString convertTabIntType(int type);
public:
	KPromeNewDocsTabInfoObj(QObject* parent);
	~KPromeNewDocsTabInfoObj();

	void setUpConfigs(const QJsonObject& jsonObject);
	bool isValid() const;

	QString id() const;
	void setId(const QString& id);

	QString webTitleText() const;
	void setWebTitleText(const QString& webTitleText);

	QString text() const;
	void setText(const QString& text);

	QString styleName() const;
	void setStyleName(const QString& styleName);

	QIcon icon() const;
	void setIcon(const QIcon& icon);

	QString tabType();
	void setTabType(const QString& tabType);

	QString relyPlugin();
	void setRelyPlugin(const QString& relyPlugin);

	QString collect() const;
	void setCollect(const QString& collect);

	bool isOpenUrl() const;
	void setIsOpenUrl(bool b);

	bool isNeedSeparator() const;
	void setIsNeedSeparator(bool b);

	bool waitJs() const;
	void setWaitJs(bool b);

	QString openUrl() const;
	void setOpenUrl(const QString& s);

	int getTabType();
	QVariantMap getPageTabIconInfo() const;

	virtual QIcon parseIcon(const QString& val);
	virtual bool parseBool(const QString& val);
	virtual int parseInt(const QString& val, bool* isSucceed = NULL);

	void setRelyPluginExit(bool b);
	bool isRelyPluginExit();

public slots:
	void onRelyPluginLoaded();
	void onReplyPluginLoadfailed();

signals:
	void relyPluginExist(bool);
	void updateIconSuccess();

private slots:
	QVariant& initIcon(const QJsonObject& jsonObject, const QString& value, QVariant& varint);
	void loadIcon(const QString& iconName);

private:
	int m_tabType;
	QIcon m_icon;
	QString m_id;
	QString m_webTitleText; //我的资源，最近使用和我的收藏中的字段
	QString m_text;
	QString m_styleName;
	QString m_strTabType;
	QString m_bkColor;
	QString m_relyPlugin;
	QString m_collect;
	bool m_relyPluginExit;
	bool m_bOpenUrl;
	QString m_openUrl;
	bool m_bNeedSeparator;
	bool m_waitJs = true;
	QString m_iconName;
	QString m_defaultIconName;
};

class KPromeNewDocsTabBarConfig : public QObject
{
	Q_OBJECT
		typedef QList<QPair<QString, QList<KPromeNewDocsTabInfoObj*>>> KPromeNewDocsTabData;
public:
	static KPromeNewDocsTabBarConfig* getInstance();

	QList<KPromeNewDocsTabInfoObj*> getTabInfoList();
	KPromeNewDocsTabData getTabInfo() const;
	KPromeNewDocsTabInfoObj* getInfoObj(int tabType);
	KPromeNewDocsTabInfoObj* getInfoObjById(const QString& idTab);

	int getLastSelectedTab();
	void updateLastSelectedTab(int tabType);

	void sendDisplayDcInfo(KPromeNewDocsTabInfoObj* obj);
	void sendClickedDcInfo(KPromeNewDocsTabInfoObj* obj);
	void onLoginChangedUpDateData();
	void configuredRelyPlugin(KPromeNewDocsTabInfoObj* infoObj);
	QJsonObject getFpcCombData();

private slots:
	void onAppAboutToQuit();
	void onAppManagerLoadFinished();
	void onAppMgrLoadFinished();

signals:
	void tabInfoUpdateSuccess(const KPromeNewDocsTabData& data);
	void loadProgress(long long totalBytes, long long receivedBytes);

private:
	void init();
	void initLastSelectedTab();
	void saveLastSelectedTab();

	//网络请求结束后的解析函数
	void handleQueryOnlineConfigSuccess(const QString& rawJson);

	void updateOnlineConfigTime();
	void setUpConfigs(const QJsonArray& jsonArray);
	void parseEntryGroup(const QJsonObject& jsonObject);
	void updateNewEntryData();

private:
	KPromeNewDocsTabBarConfig(QObject* parent);
	~KPromeNewDocsTabBarConfig();

private:
	QMap<QString, KPromeNewDocsTabInfoObj*> m_tabIdMap;
	KPromeNewDocsTabData m_tabGroupInfoList;
	int m_bInited;
	int m_lastSelectedTab;
	QStringList m_tabIdList;
	QList<KPromeNewDocsTabInfoObj*> m_tabInfoList;
};

#endif//__KPROMETHEUS_NEW_DOCS_TAB_BAR_CONFIG_H__
