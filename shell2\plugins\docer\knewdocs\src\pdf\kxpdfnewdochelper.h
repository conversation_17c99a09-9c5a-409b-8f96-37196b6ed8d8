﻿#ifndef __KXPDFNEWDOCHELPER_H__
#define __KXPDFNEWDOCHELPER_H__

class KxPdfNewDocHelper
{
#if QT_VERSION < 0x050000
	class _FakeQSpacerItem : public QLayoutItem
	{
	public:
		int width;
		int height;
		QSizePolicy sizeP;
		QRect rect;
	};
#endif
public:
	static QString toHdpiStyle(const QString& style);
	static void fitHdpiWidget(QWidget *widget);
	static void fitHdpiLayout(QLayout *layout);
	static void CreateDcInfo(const QString &eventName, const QString &key, const QString &value);
	static void CreatePDFDcInfo(const QString &eventName, QHash<QString, QString> &customArgs, 
								quint32 retryCount = 0, quint32 timeout = 0);
};

#endif //__KXPDFNEWDOCHELPER_H__