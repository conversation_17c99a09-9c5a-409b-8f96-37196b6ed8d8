﻿#ifndef __KNEWDOCS_JSAPI_H__
#define __KNEWDOCS_JSAPI_H__

#include <ksolite/kcommonwebwidget.h>
#include <ksolite/kcommonwebapi.h>
#include <QNetworkCookieJar>
#include <QNetworkCookie>
#include <kprometheus/kpromepluginwidget.h>
#include "knewdocpagejsapibase.h"
#include "legacy/async/kpromise.h"
#include <ksolite/kdocer/kdocerresnetwork/kdocernetworkwrapper.h>

class KPromeNewDocsStartup;
class KDocerCommonHttpAccess;
class KxCloudFileProxyExLib;

class KxNetworkCookieJar : public QNetworkCookieJar
{
public:
	KxNetworkCookieJar(QObject* parent)
		: QNetworkCookieJar(parent)
	{}

	void removeAllCookies()
	{
		QNetworkCookieJar::setAllCookies(QList<QNetworkCookie>());
	}
};

class KxNewDocJsApiBase : public KNewDocPageJsApiBase
{
	Q_OBJECT
public:
	explicit KxNewDocJsApiBase(KxWebViewContainer* webView, bool bNewWindow = false);
	~KxNewDocJsApiBase();

private slots:
	/*
		//向所有相关联的webview发送自定义消息
		arg[context]
		{
			params:
			{
				msgProc: mthod(msgProcArgs)
				msgProcArgs: json serialization
			}
		}
	*/
	void boardcastCustomMessage(KxWebViewJSContext& context);

	void httpGet(KxWebViewJSContext& context);
	void httpPost(KxWebViewJSContext& context);
	void getResouceDownloadDiskInfo(KxWebViewJSContext& context);

	void onUserInfoChange(int);
	void onVipInfoChange(const QVariantMap& newVipInfo) override;

	void onHttpAccessDone(KDocerCommonHttpAccess* httpAccess);
public slots:
	void openDocerPage(KxWebViewJSContext& context);
	//判断是否暗黑皮肤
	void isDarkSkin(KxWebViewJSContext& context);
	void getCurrentSkinName(KxWebViewJSContext& context);
protected:
	virtual QWidget* widgetParent() override;
	virtual ksolite::KxCommonWebDialog* createNewUrlWidget(QWidget* parent, int nWidth, int nHeight, bool bModal, bool bCloseBtn) override;
	quint64 getDiskSize(const QString& strDirName);
	QString getLocalStoragePath() const override;
	void httpAction(QNetworkReply* netReply, const QVariantMap& args, const QString& httpMethod = "get");
	void addHttpAccess(KDocerCommonHttpAccess* httpAccess);
	void releaseHttpAccess(KDocerCommonHttpAccess* httpAccess);
	void handleHttpAccess(KDocerCommonHttpAccess* httpAccess);
private:
	KxNetworkCookieJar* m_cookieJar;
	qint64 m_loginTimeStamp;
	QString m_lastVipInfo;
	bool m_bNewWindow;
	QVector<KDocerCommonHttpAccess*> m_vecHttpAccess;
};

class KPromeNewDocsPage;

class KxNewDocJsApi : public KxNewDocJsApiBase
{
	Q_OBJECT
public:
	explicit KxNewDocJsApi(KPromeNewDocsStartup* window, KxWebViewContainer* webView);
	~KxNewDocJsApi();
	void insertOnlineResource(const ResourceInfo& resourceInfo) override;
public slots:
	void notifyUpdate(bool isControled, const QString& entryPosition, const QString& reason);
	void notifyEntryStatusUpdate();

signals:
	void openDocument(const QString& file, const QString& fileKey, const QString& form, const QString& browserType, const QString& callBack);
	void downloadUpdate(bool state);
	void execOfficialDoc(const QString& data);
	void openRecentDocument(const QString& file, const QString&);

private slots:
	/*
	arg[context]
	{
		params:
		{
			maxCount:int,
		}
		callback:method(json serialization in base64: result)
		arg[result]
		{
			items:[
			{
				id:int
				fileName:string,
				time:string
			}
			...
			]
		}
	}
	*/
	void getRecommendedFiles(KxWebViewJSContext& context);
	/*
		arg[context]
		{
			callback:method(json serialization in base64: result)
				arg[result] : bool
			params:
			{
				profession: string
			}
		}
	*/
	//deprecated
	void userProfession(KxWebViewJSContext& context);
	/*
		arg[context]
		{
			params:
			{
			}
		}
	*/
	void newBlankDocument(KxWebViewJSContext& context);
	/*
		arg[context]
		{
			params:
			{
				filePath:string
				mod:int -- 0 打开  1打开副本
			}
		}
	*/
	void openLocalFile(KxWebViewJSContext& context);

	/*
		arg[context]
		{
			params:
			{
				url:string
				name:string
				md5:string
			}
			callback:method
			arg[result]
			{
				id:string
			}
		}
	*/
	void downloadTemplate(KxWebViewJSContext& context);

	/*
		arg[context]
		{
			params:
			{
				id:string
			}
		}
	*/
	void cancelDownload(KxWebViewJSContext& context);
	
	void openSpecificTab(KxWebViewJSContext& context);
	//根据前端指定类型，获取目标tab文案
	/*
	arg[context]
		{
			params:
			[
				tabType //
			]
		}
	*/
	void getTabText(KxWebViewJSContext& context);
	void openMyTemplateTab(KxWebViewJSContext& context);

	/*arg[context]
	{
		params:
		{
			appName:string,
		}
		arg[result]
		{
			items:[
			{
				tagName:string
				filePath:string,
				fileList:[
					{
						fileName:string
						dispalyName:string
					}
					...
				]
			}
			...
			]
		}
	}*/
	void getLocalTemplateFileInfo(KxWebViewJSContext& context);

		/*
	arg[context]
	{
		params:
		{
			maxCount:int,
		}
		callback:method(json serialization in base64: result)
		arg[result]
		{
			items:[
			{
				id:int
				name:string,
				publish_time:string
			}
			...
			]
		}
	}
	*/
	void getRecentFiles(KxWebViewJSContext& context);
	void getNewDocRegeditValue(KxWebViewJSContext& context);
	void getCreateDocUrl(KxWebViewJSContext& context);
	void recentFileSearch(KxWebViewJSContext& context);
	void localTemplateSearch(KxWebViewJSContext& context);
	void IsSupportIntranetTemplate(KxWebViewJSContext& context);

	//void navigateOnNewWindow(KxWebViewJSContext& context);
	void execOfficialDoc(KxWebViewJSContext& context);
	void getOemSupport(KxWebViewJSContext& context);
	void getCfgsFileData(KxWebViewJSContext& context);
	//模板目录相关
	void setTemplateDownDir(KxWebViewJSContext& context);
	void getTemplateDownDir(KxWebViewJSContext& context);
	void getDefaultTemplateDownDir(KxWebViewJSContext& context);
	void getEncryptString(KxWebViewJSContext& context);

	void getCustomOfficialTemplate(KxWebViewJSContext& context);
	void getCustomTemplateFileInfo(KxWebViewJSContext& context);
	void getNationalOfficialTemplate(KxWebViewJSContext& context);
	void registPushUIEventHandler(KxWebViewJSContext& context);
	void openPromeResumePage(KxWebViewJSContext& context);
	void openByCloudDocsPage(KxWebViewJSContext& context);
	//打开创客贴TAB页（包含插件下载交互）
	void openChuangKitPage(KxWebViewJSContext& context);
	//打开脑图\流程图TAB页
	void openProcessonPage(KxWebViewJSContext& context);
	void newProcessOnFile(KxWebViewJSContext& context);

	void getCorpSpecialGroupId(KxWebViewJSContext& context);
	void getCorpId(KxWebViewJSContext& context);
	void getKLMEntryId(KxWebViewJSContext& context);

	// 金山文档离线页
	void isSupportDOPFunc(KxWebViewJSContext& context);
	void openDocsOnlineFile(KxWebViewJSContext& context);
	void newDocsOnlineFile(KxWebViewJSContext& context);

	void openBrowserPage(KxWebViewJSContext& context);

	void newBlankOfficialDocument(KxWebViewJSContext& context);
	void getLocalTemplateClassify(KxWebViewJSContext& context);
	void removeRecentTemplate(KxWebViewJSContext& context);
	void getRecentUseTemplate(KxWebViewJSContext& context);
	void openRecentFile(KxWebViewJSContext& context);

	/*
		functionCode 代表识别完的意图
		content 标识内容，详见：
		{
		"functionCode": "WPS_generate_writeDoc_gProofOfEmploy", https://www.kdocs.cn/l/ckrrJygTP47H
		"content": {
				"new_tags":{
					"a": "b",
					"c": "d"
				},
				"ai_tags":{
				"xx":"xx"
				}
			}
		"userInput':"用户输入的语句"
		}
	*/
	void getKDocerAiTemplate(KxWebViewJSContext& context);
	void closePage(KxWebViewJSContext& context);

	// 新建上云
	void setNewCloudDocSwitchStatus(KxWebViewJSContext& context);
	// 用于新建上云前端页面跳转到首页。
	void navigateToStartPage(KxWebViewJSContext& context);
	void getCompanyLogoInfo(KxWebViewJSContext& context);
	void addWatchToEntry(KxWebViewJSContext& context);

	//v7私网，模板库使用
	void changeDocType(KxWebViewJSContext& context);
	void getInitialDocType(KxWebViewJSContext& context);
	void showLocalTemplates(KxWebViewJSContext& context);
	void useTemplateFile(KxWebViewJSContext& context);
	/**
	 * @brief V7 私网模板库使用，展示指定类型的模版
	 * @param context JS回调类型
	 **/
	void getDisplayDocType(KxWebViewJSContext& context);

	void prepareCloudDocsPage(KxWebViewJSContext& context);

signals:
	void jsScriptReady();
	void closeTab();

private slots:
	void onResizeWindow();
	void onDownloadProgress(const QString& id, int);
	void onDownloadSuccess(const QString& id, const QString& fileName);
	void onDownloadError(const QString& id, DownloadFailInfo info);
	void onThumbnailGenerated(int, const QString&, const QString&, const QByteArray&);
	void onSkinChanged();
	void onPluginInstanceObjectRegisted(const QString& name);
	void onAIEntryVisibleChanged(const QString&, bool);
	void onAIEntryEnabledChanged(bool enable);
	void doInitCopilotEntry();
	void notifyGetWorkspaceStatusFinished(bool bCurWorkspaceChanged,
		const QString& requestId,
		bool isCompanyAccountInMultiAccount,
		const QString& loginSource);
	void onV7NewDoc(const QString& callBack, const QVariantMap& result);

public:
	void notifyCancelDownload(const QString& templateId);
	void notifyDownloadProgress(const QString& templateId, int nPercent);
	void notifyDownloadSuccess(const QString& templateId, const QString& fileName);
	void notifyDownloadError(const QString& templateId, KDownloadErrorInfo info);
	virtual void init() override;

private:
	void newBlankDocument(const QVariantMap& args, const QString& callBack = "");
	void openLocalFile(const QString& filePath, bool copy, const QString& callBack = "");
	void openLocalFile(const QString& filePath, const QString& fileKey);
	QString downloadTemplate(const DownloadArgs& downloadData, const QString& fileKey, bool downloadFont, const QString& callBack = "");
	void removeDownloadTask(const QString& id);
	void clearAllDownloadTask();
	bool isFileOpen(QString& filePath);
	void clearGroupFileKey();

	HRESULT getTemplatesPath(const QString& appName, QStringList& list, bool bForSearch);
	bool getFilterType(const QString& appName, QStringList& list, bool bForSearch);

	void getFloderAllTemplateFiles(
		const QString&,
		const QStringList&,
		const QSettings&,
		QList<QVariant>&,
		bool bNeedTranslate = true,
		QDir::SortFlags sortFlag = QDir::NoSort);

	void templateTabSort(const QString& appName, QStringList& inputList, QStringList& outputList);
	void invokeGlobalWebPageCallBack(const QString& param);
	bool exceptByOfficialfilter(const QString& strFile);
	bool switchToPageByFileId(const QString& fileId);
	void contextStateRsp(KxWebViewJSContext& context,
		bool reslut, const std::string& errMsg);

	void openProcessonPage(const QVariantMap& variantMap);

	void watchCopilotEntryState(QObject* entry);
	void notifyCopilotEntryState();
	void initCopilotEntry();
	void createLocalTemplateTable();
	void addRecentUseTempalte(const QString& filePath);
	QString getTemplateTypeBySuffix(const QString& buffix);
	void retriveAiTips();
	void clearWatchToEntry();
	bool hasOfficialFunctionPrivilege();
	bool appendDownloadArgHeaders(DownloadArgs& downloadData);

private:
	KPromeNewDocsStartup*		m_window;
	QSet<QString>				m_downloadfileIdSet;
	QMap<QString, QString>		m_downloadTaskfileIdMap;
	QMap<QString, QString>		m_fileIdKeysMap;
	QMap<QString, QString>		m_callbackMap;
	QMap<QString, QJsonObject>	m_customPropsMap;
	QMap<QString, bool>         m_entryWatchList;
	bool						m_bScriptReady;
	QList<docer::base::closure>		m_onScriptReadyTasks;
	KxCloudFileProxyExLib* m_proxyExLib = nullptr;
};
#endif // __KNEWDOCS_JSAPI_H__
