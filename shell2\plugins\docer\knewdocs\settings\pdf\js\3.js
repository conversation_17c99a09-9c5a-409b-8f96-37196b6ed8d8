webpackJsonp([3], {
	"17Ab": function(e, t) {},
	"1SDU": function(e, t) {},
	FG9c: function(e, t) {},
	KR8f: function(e, t, a) {
		"use strict";
		Object.defineProperty(t, "__esModule", {
			value: !0
		});
		var n = a("Dd8w"),
			o = a.n(n),
			i = a("mvHQ"),
			s = a.n(i),
			c = a("//Fk"),
			r = a.n(c),
			l = a("X5+7"),
			d = a("w7XY"),
			u = a("wYMm"),
			p = a("NYxO"),
			m = a("s0MJ"),
			f = a("v8ob"),
			h = a("59SO"),
			_ = {},
			v = void 0,
			g = {
				data: function() {
					return {
						isAIReady: !1,
						limit: 0
					}
				},
				props: {
					data: {
						type: Array,
					default:


						function() {
							return []
						}
					},
					precType: Number,
					isShowAi: Boolean
				},
				mounted: function() {
					var e = this;
					this.ajustTplLayout(), "wpp" == u.c.name && d.a.checkAIReady().then(function(t) {
						e.isAIReady = t
					})
				},
				activated: function() {
					var e = this;
					this.ajustTplLayout(), f.a.$on(u.h.resizeWindow, this.ajustTplLayout), this.$nextTick(function() {
						e.$Lazyload.lazyLoadHandler()
					})
				},
				deactivated: function() {
					_ = {}, f.a.$off(u.h.resizeWindow, this.ajustTplLayout)
				},
				methods: {
					ajustTplLayout: function() {
						this.limit = m.a.getMediaLimit(this.$refs && this.$refs.rTop)
					},
					handlePreview: function(e) {
						this.collectClickBtn(e, this.collectPrefix + "_template_list", this.collectPrefix + "_rec")
					},
					handleBuyMember: function(e, t, a) {
						var n = {
							3: this.collectPrefix + "_open_vip",
							0: this.collectPrefix + "_discount_vip"
						}[t] || "",
							o = {
								3: "open_vip",
								0: "retail"
							}[t] || "";
						this.collectClickBtn(e, n, o, a)
					},
					handleBuyMb: function(e, t, a) {
						var n = this.showData[e] || {};
						h.a.sendDownloadParam({
							p4: this.collectPrefix + "_download_open_home",
							p9: n.moban_app,
							p12: "event",
							p13: n.moban_type,
							p27: this.mainAlg
						}), this.collectClickBtn(e, this.collectPrefix + "_download_open", "download", a)
					},
					handleDownloadMb: function(e, t, a) {
						var n = {
							1: this.collectPrefix + "_download_free",
							2: this.collectPrefix + "_download_open"
						}[t] || "",
							o = {
								1: "download",
								2: "download"
							}[t] || "",
							i = this.showData[e] || {},
							s = 1 == t ? "_download_free" : 2 == t ? "_download_vip" : "_download_open";
						h.a.sendDownloadParam({
							p4: this.collectPrefix + s + "_home",
							p9: i.moban_app,
							p12: "event",
							p13: i.moban_type,
							p27: this.mainAlg
						}), this.collectClickBtn(e, n, o, a)
					},
					handleCollectHoverMb: function(e, t) {
						this.collectHoverMb(e, this.collectPrefix + "_template_list", this.collectPrefix + "_rec", t)
					},
					handleCollectHoverMbBtn: function(e, t, a) {
						var n = {
							1: this.collectPrefix + "_download_free",
							2: this.collectPrefix + "_download_open",
							3: this.collectPrefix + "_open_vip",
							4: this.collectPrefix + "_download_open",
							5: this.collectPrefix + "_download_open",
							0: this.collectPrefix + "_discount_vip"
						}[a] || "",
							o = {
								1: "download",
								2: "download",
								3: "open_vip",
								4: "download",
								5: "download",
								0: "retail"
							}[a] || "";
						this.collectHoverMb(e, n, o, t)
					},
					handleCollectHoverMbBtnBuy: function(e, t) {
						this.collectHoverMb(e, this.collectPrefix + "_download_open", "download", t)
					},
					handleCollectShowMb: function(e) {
						var t = this.showData[e] || {};
						!_[t.id] && t.id && (_[t.id] = 1, h.a.sendHome({
							p7: this.collectPrefix + "_template_list",
							p8: "display",
							p9: t.id,
							p10: e + 2,
							p11: t.author_id,
							p12: this.collectPrefix + "_rec",
							p27: this.mainAlg
						}))
					},
					collectHoverMb: function(e, t, a, n) {
						var o = this.showData[e] || {};
						h.a.sendHome({
							p7: t,
							p8: "hover",
							p9: o.id,
							p10: e + 2,
							p11: o.author_id,
							p12: a,
							p15: n,
							p27: this.mainAlg
						})
					},
					collectClickBtn: function(e, t, a, n) {
						var o = this.showData[e] || {};
						h.a.sendHome({
							p7: t,
							p8: "click",
							p9: o.id,
							p10: e + 2,
							p11: o.author_id,
							p12: a,
							p13: n,
							p16: 1 == o.moban_type ? 0 : 1,
							p27: this.mainAlg
						})
					}
				},
				computed: o()({}, Object(p.d)("recommend", ["mainAlg"]), {
					showData: function() {
						return this.limit ? this.data.slice(0, 4 * this.limit - 1) : this.data
					},
					collectPrefix: function() {
						return 1 == this.precType ? "ai" : "artificial"
					},
					collect: function() {
						return {
							p4: this.collectPrefix + "_template_list_home",
							p27: this.mainAlg,
							p6: ""
						}
					},
					isShow: function() {
						return this.isShowAi && this.isAIReady && !v && (v = !0, h.a.sendHome({
							p7: "new_ai",
							p8: "display",
							p12: "new"
						})), this.isShowAi && this.isAIReady
					}
				}),
				watch: {
					data: function() {
						_ = {}
					}
				},
				components: {
					List: l.a
				}
			},
			y = {
				render: function() {
					var e = this,
						t = e.$createElement,
						a = e._self._c || t;
					return a("div", {
						ref: "rTop",
						staticClass: "newfile l-flex"
					}, [a("List", {
						attrs: {
							data: e.showData,
							limit: e.limit,
							hasNewFile: !0,
							isAIReady: e.isShow,
							originFunc: "editrec",
							originAlg: e.mainAlg,
							collect: e.collect
						},
						on: {
							preview: e.handlePreview,
							buyMember: e.handleBuyMember,
							buyMb: e.handleBuyMb,
							downloadMb: e.handleDownloadMb,
							collectHoverMb: e.handleCollectHoverMb,
							collectHoverMbBtn: e.handleCollectHoverMbBtn,
							collectHoverMbBtnBuy: e.handleCollectHoverMbBtnBuy,
							collectShowMb: e.handleCollectShowMb
						}
					})], 1)
				},
				staticRenderFns: []
			};
		var b = a("VU/8")(g, y, !1, function(e) {
			a("pXtb")
		}, "data-v-2e9129df", null).exports,
			w = a("RcFJ"),
			C = {
				data: function() {
					return {}
				},
				props: {
					item: {
						type: Object,
					default:


						function() {
							return {}
						}
					},
					index: Number,
					externalAppName: String
				},
				mixins: [w.a],
				computed: o()({}, Object(p.d)("common", ["appName"]), {
					cropSuffix: function() {
						return "/" + {
							wps: "152x216",
							wpp: "198x113",
							et: "198x140",
                            pdf: "218x178.9"
						}[this.externalAppName || this.appName] + u.g
					},
					isWpp: function() {
						return "wpp" == this.appName
					},
					wppSuffix: function() {
						return "/98x55" + u.g
					},
					btnClass: function() {
						return {
							1: "m-btn-secondary",
							2: "m-btn-secondary",
							3: "m-btn-member",
							4: "m-btn-secondary",
							5: "m-btn-secondary",
							0: "m-btn-member"
						}[this.mbSt$]
					}
				})
			},
			x = {

			};
		var M = a("VU/8")(C, x, !1, function(e) {
			a("FG9c")
		}, "data-v-e5880dc6", null).exports,
			k = {

			},
			P = {
				render: function() {
					var e = this,
						t = e.$createElement;
					return (e._self._c || t)("CommonItem", {
						key: e.data.id + "_cat_" + e.index + "_" + e.sIndex,
						attrs: {
							item: e.data,
							index: e.sIndex
						},
						on: {
							preview: e.onPreview,
							buyMember: e.onBuyMember,
							buyMb: e.onBuyMb,
							downloadMb: e.onDownloadMb,
							collectHoverMb: e.onHoverMb,
							collectHoverMbBtnBuy: e.onHoverMbBtnBuy,
							collectHoverMbBtn: e.onHoverMbBtn,
							collectShowMb: e.onCollectShowMb
						}
					})
				},
				staticRenderFns: []
			},
			S = a("VU/8")(k, P, !1, null, null, null).exports,
			D = a("xK0O"),
			L = a("Dzwp"),
			E = void 0,
			T = {},
			A = {},
			I = !1,
			B = !1,
			H = {

			},
			N = {

			};
		var j = a("VU/8")(H, N, !1, function(e) {
			a("Vy69")
		}, "data-v-4d7edcbe", null).exports,
			R = a("G1qf"),
			$ = a("n1nN"),
			F = a("CXu2"),
			U = {
				data: function() {
					return {
						precType: null,
						recType: "",
						recData: [],
						recAlg: "",
						categoryData: [],
						catAlg: "",
						statusCode: u.i.loading,
						editDocs: [],
						isShowAi: !1
					}
				},
				mounted: function() {
					var e = this;
					this.init(), m.a.getUserProfessionFinish.then(function() {
						f.a.$on(u.h.featureChange, e.onFeatureChange), f.a.$on(u.h.login, e.onFeatureChange)
					})
				},
				computed: o()({}, Object(p.d)("user", ["userInfo", "userGroup"])),
				components: {
					Top: b,
					MyCategory: j,
					DataStatus: R.a
				}
			},
			O = {
				render: function() {
					var e = this,
						t = e.$createElement,
						a = e._self._c || t;
					return a("div", [a("Top", {
						ref: "nTopContent",
						attrs: {
							data: e.recData,
							precType: e.precType,
							isShowAi: e.isShowAi
						}
					}), e._v(" "), a("DataStatus", {
						attrs: {
							statusCode: e.statusCode,
							module: "newfile"
						},
						on: {
							retry: e.onFeatureChange
						}
					}), e._v(" "), a("MyCategory", {
						attrs: {
							data: e.categoryData,
							precType: e.precType
						}
					})], 1)
				},
				staticRenderFns: []
			},
			z = a("VU/8")(U, O, !1, null, null, null).exports,
			G = null,
			W = {
				data: function() {
					return {
						tagGroup: [],
						data: [],
						statusCode: ""
					}
				},
				mounted: function() {
					var e = this;
					m.a.getUserInfoFinish.then(function() {
						var t = JSON.parse(s()(e.userGroup));
						e.tagGroup = t, setTimeout(function() {
							e.fetchProfessionTag()
						}, 300)
					})
				},
				methods: o()({}, Object(p.b)("user", ["setUserGroup"]), {
					fetchProfessionTag: function() {
						var e = this;
						this.statusCode = u.i.loading, $.a.get("rec").professionTag({
							data: {
								mb_app: u.c.mark,
								format: "jsonp"
							}
						}).then(function(t) {
							"2000" == t.code && t.data ? e.data = t.data.list || [] : e.data = [], e.statusCode = e.data.length ? "" : u.i.empty
						}).
						catch (function() {
							e.data = [], e.statusCode = u.i.error
						})
					},
					handleUserGroup: function() {
						this.setUserGroup(this.tagGroup)
					},
					onChangeRadio: function() {
						this.handleUserGroup(), setTimeout(function() {
							f.a.$emit(u.h.featureChange), h.a.sendHome({
								p7: "identity_update",
								p8: "click",
								p12: "identity"
							})
						}, 0)
					},
					onEnterProfession: function() {
						G = (new Date).getTime()
					},
					onLeaveProfession: function() {
						(new Date).getTime() - G > 1e3 && h.a.sendHome({
							p7: "identity_update",
							p8: "hover",
							p12: "identity"
						})
					}
				}),
				computed: o()({}, Object(p.d)("user", ["userGroup"]))
			},
			V = {
				render: function() {
					var e = this,
						t = e.$createElement,
						a = e._self._c || t;
					return this.data && this.data.length ? a("div", {
						staticClass: "profession__dropdown"
					}, [a("span", {
						staticClass: "profession__list_triangle"
					}), e._v(" "), a("dl", {
						staticClass: "profession__list m-box-content",
						on: {
							mouseenter: e.onEnterProfession,
							mouseleave: e.onLeaveProfession
						}
					}, [a("dt", {
						staticClass: "profession__list_title"
					}, [e._v("选择您的行业")]), e._v(" "), a("dd", [a("ul", {
						staticClass: "profession__list_sex l-d-flex l-justify-content-start l-flex-wrap"
					}, e._l(e.data, function(t, n) {
						return a("li", {
							staticClass: "profession__list_item"
						}, [a("label", {
							staticClass: "l-d-flex l-align-items-center",
							attrs: {
								for :"profession" + n
							}
						}, [a("input", {
							directives: [{
								name: "model",
								rawName: "v-model",
								value: e.tagGroup,
								expression: "tagGroup"
							}],
							attrs: {
								type: "checkbox",
								name: "profession",
								id: "profession" + n
							},
							domProps: {
								value: t,
								checked: Array.isArray(e.tagGroup) ? e._i(e.tagGroup, t) > -1 : e.tagGroup
							},
							on: {
								change: [function(a) {
									var n = e.tagGroup,
										o = a.target,
										i = !! o.checked;
									if (Array.isArray(n)) {
										var s = t,
											c = e._i(n, s);
										o.checked ? c < 0 && (e.tagGroup = n.concat([s])) : c > -1 && (e.tagGroup = n.slice(0, c).concat(n.slice(c + 1)))
									} else e.tagGroup = i
								}, function(t) {
									e.onChangeRadio()
								}]
							}
						}), e._v(" "), a("SvgIcon", {
							attrs: {
								svgName: "radio-normal-16",
								className: "radio-normal g-font_size-16"
							}
						}), e._v(" "), a("SvgIcon", {
							attrs: {
								svgName: "radio-hover-16",
								className: "radio-hover g-font_size-16"
							}
						}), e._v(" "), a("SvgIcon", {
							attrs: {
								svgName: "radio-active-16",
								className: "radio-active g-font_size-16"
							}
						}), e._v(" "), a("span", [e._v(e._s(t))])], 1)])
					}))])])]) : e._e()
				},
				staticRenderFns: []
			};
		var K = a("VU/8")(W, V, !1, function(e) {
			a("negx")
		}, "data-v-642bfb9a", null).exports,
			J = a("Phds"),
			X = a("5n6o"),
			Y = null,
			q = void 0,
			Q = void 0,
			Z = void 0,
			ee = {
				mixins: [X.a],
				data: function() {
					return {
						isShowProfession: !1
					}
				},
				mounted: function() {
					var e = this;
					m.a.getUserProfessionFinish.then(function() {
						e.collectMemberShow(), e.collectUserShow(), q = !0
					}), f.a.$on(u.h.clickDocument, this.changePlane)
				},
				methods: {
					handleBuyMember: function(e, t) {
						var csourceSuffix = "super" == e ? "_SuperM" : "_WPSM";
						var payConfigSuffix = "super" == e ? "_svip" : "_vip";
						window.gotoTag({
							type: "member",
							servicetype: e,
							csource: "pc_pdf" + csourceSuffix,
							position: L.a.info.entrance + "_newfile2019_mydocer_" + L.a.info.vip + "_" + L.a.info.profession + "_" + L.a.info.testVer,
							payKey: t,
							payconfig: "pc" + payConfigSuffix,
						})
					},
					onClickMember: function(e) {
						var t = h.a.createPayKey(),
							a = "super" == e ? "super_vip" : "vip";
						h.a.sendHome({
							p7: "open_" + a,
							p8: "click",
							p11: this.memberSt,
							p12: a,
							p13: h.a.createPayKey()
						}), this.isLogin ? this.handleBuyMember(e, t) : d.a.login()
					},
					onClickLogin: function() {
						h.a.sendHome({
							p7: "user",
							p8: "login",
							p9: this.isLogin ? 1 : 0,
							p11: this.userInfo.nickname,
							p12: "user"
						}), this.isLogin || d.a.login()
					},
					onClickProfession: function() {
						this.changePlane(!this.isShowProfession)
					},
					changePlane: function(e) {
						e && this.collectIdUpdataShow(), this.isShowProfession = e
					},
					collectIdUpdataShow: function() {
						h.a.sendHome({
							p7: "identity_update",
							p8: "display",
							p12: "identity"
						})
					},
					onClickPrivilege: function(e, t, a) {
						if (t) {
							var n = {};
							try {
								n = JSON.parse(s()(t))
							} catch (e) {}
							var o = h.a.createPayKey();
							n.csource = u.c.name + "_" + L.a.info.tag + "_" + a, n.channel = n.position = L.a.info.tag + "_" + a + "_" + L.a.info.profession + "_" + L.a.info.testVer + "|" + L.a.info.entrance + "_" + L.a.info.vip, n.payKey = o, h.a.sendHome({
								p7: "privilege",
								p8: "click",
								p9: t.name,
								p10: e + 1,
								p11: encodeURIComponent(t.content),
								p12: "privilege",
								p13: o
							}), this.$_clickAD(n)
						}
					},
					onEnterPrivilege: function() {
						Y = (new Date).getTime()
					},
					onLeavePrivilege: function(e, t) {
						var a = (new Date).getTime() - Y;
						a > 1e3 && h.a.sendHome({
							p7: "privilege",
							p8: "hover",
							p9: t,
							p10: e + 1,
							p12: "privilege",
							p15: a
						})
					},
					collectMemberShow: function() {
						this.isLogin && (h.a.sendHome({
							p7: "open_super_vip",
							p8: "display",
							p12: "super_vip"
						}), this.memberText && h.a.sendHome({
							p7: "open_vip",
							p8: "display",
							p12: "vip"
						}))
					},
					collectUserShow: function() {
						h.a.sendHome({
							p7: "user",
							p8: "display",
							p9: this.isLogin ? 1 : 0,
							p11: this.userInfo.nickname,
							p12: "user",
							p16: this.userInfo.pic ? 1 : 0
						})
					},
					onEnterMember: function() {
						Q = (new Date).getTime()
					},
					onLeaveMember: function(e) {
						var t = (new Date).getTime() - Q;
						if (t > 1e3) {
							var a = "super" == e ? "super_vip" : "vip";
							h.a.sendHome({
								p7: "open_" + a,
								p8: "hover",
								p12: a,
								p15: t
							})
						}
					},
					onEnterUser: function() {
						Z = (new Date).getTime()
					},
					onLeaveUser: function() {
						var e = (new Date).getTime() - Z;
						e > 1e3 && h.a.sendHome({
							p7: "user",
							p8: "hover",
							p12: "user",
							p15: e
						})
					},
					sendInfo: function(e, t) {
						this.isLogin ? {} : d.a.sendInfo(e, t);
					}
				},
				components: {
					Profession: K
				},
				computed: o()({}, Object(p.d)("user", ["userInfo", "userGroup", "isLogin"]), Object(p.d)("recommend", ["adPrivilegeIcon"]), {
					nickname: function() {
						return this.isLogin ? this.userInfo.nickname : "未登录"
					},
					userGroupText: function() {
						var e = this.userGroup && this.userGroup.slice(0, 3);
						return e && e.length ? "#" + e.join("、#") : ""
					},
					memberSt: function() {
						return this.userInfo.isSuperMember ? 2 : this.userInfo.isWpsMember ? 3 : 1
					},
					superMemberText: function() {
						return {
							1: "开通",
							2: "续费",
							3: "升级"
						}[this.memberSt] || ""
					},
					memberText: function() {
						return {
							1: "开通",
							2: "",
							3: "续费"
						}[this.memberSt] || ""
					},
					data: function() {
						return this.adPrivilegeIcon && this.adPrivilegeIcon.ad_extend || []
					}
				}),
				watch: {
					isLogin: function() {
						q && (this.isLogin && this.collectMemberShow(), this.collectUserShow())
					},
					data: function() {
						var e = this;
						m.a.getUserProfessionFinish.then(function() {
							e.isLogin && e.data.forEach(function(e, t) {
								h.a.sendHome({
									p7: "privilege",
									p8: "display",
									p9: e.name,
									p10: t + 1,
									p12: "privilege"
								})
							})
						})
					}
				}
			},
			te = {
				render: function() {
					var e = this,
						t = e.$createElement,
						a = e._self._c || t;
					return a("div", {
						staticClass: "userinfo"
					}, [a("div", {
						staticClass: "userinfo__all"
					}, [e.userInfo.pic ? a("img", {
						staticClass: "userinfo__all_img",
						attrs: {
							src: e.userInfo.pic || "",
							alt: ""
						},
						on: {
							click: function(t) {
								e.onClickLogin();
								e.sendInfo("Home|Login", "0");
							},
							mouseenter: function(t) {
								e.onEnterUser()
							},
							mouseleave: function(t) {
								e.onLeaveUser()
							}
						}
					}) : a("span", {
						on: {
							click: function(t) {
								e.onClickLogin();
								e.sendInfo("Home|Login", "0");
							},
							mouseenter: function(t) {
								e.onEnterUser()
							},
							mouseleave: function(t) {
								e.onLeaveUser()
							}
						}
					}, [a("SvgIcon", {
						class: {
							active: e.isShowProfession
						},
						attrs: {
							svgName: "avatar-50",
							className: "userinfo__all_avator"
						}
					})], 1), e._v(" "), a("h4", {
						staticClass: "userinfo__all_nickname",
						class: {
							"g-pointer": "未登录" == e.nickname
						},
						on: {
							click: function(t) {
								e.onClickLogin();
								e.sendInfo("Home|Login", "1");
							}
						}
					}, [e._v(e._s(e.nickname || ""))]), e._v(" ")/*, a("div", {
						staticClass: "l-d-flex l-align-items-center"
					}, [a("SvgIcon", {
						attrs: {
							svgName: "label-14",
							className: "g-font_size-14"
						}
					}), e._v(" "), e.userGroupText ? a("p", {
						staticClass: "userinfo__all_label",
						attrs: {
							title: e.userGroupText
						}
					}, [e._v(e._s(e.userGroupText))]) : e._e(), e._v(" "), a("span", {
						staticClass: "userinfo__profession",
						on: {
							click: function(t) {
								t.stopPropagation(), e.onClickProfession()
							}
						}
					}, [a("SvgIcon", {
						class: {
							active: e.isShowProfession
						},
						attrs: {
							svgName: "arrow-bottom-14",
							className: "g-font_size-14 userinfo__all_arrow"
						}
					}), e._v(" "), a("Profession", {
						directives: [{
							name: "show",
							rawName: "v-show",
							value: e.isShowProfession,
							expression: "isShowProfession"
						}]
					})], 1)], 1)*/]), e._v(" "), e.isLogin ? a("div", {
						staticClass: "userinfo__member"
					}, [e.memberText ? a("div", {
						staticClass: "userinfo__member_item l-d-flex l-align-items-center l-justify-content-between"
					}, [a("span", {
						staticClass: "userinfo__member_text l-d-flex l-align-items-center "
					}, [a("SvgIcon", {
						attrs: {
							svgName: "wpsmember-16",
							className: "g-font_size-16"
						}
					}), e._v("WPS会员")], 1), a("a", {
						staticClass: "userinfo__btn userinfo__btn_wps",
						attrs: {
							href: "javascript:void(0)"
						},
						on: {
							click: function(t) {
								e.onClickMember("monthcard")
							},
							mouseenter: function(t) {
								e.onEnterMember()
							},
							mouseleave: function(t) {
								e.onLeaveMember("monthcard")
							}
						}
					}, [e._v(e._s(e.memberText))])]) : e._e(), e._v(" "), a("div", {
						staticClass: "userinfo__member_item l-d-flex l-align-items-center l-justify-content-between"
					}, [a("span", {
						staticClass: "userinfo__member_text l-d-flex l-align-items-center "
					}, [a("SvgIcon", {
						attrs: {
							svgName: "super-16",
							className: "g-font_size-16"
						}
					}), e._v("超级会员")], 1), a("a", {
						staticClass: "userinfo__btn userinfo__btn_super",
						attrs: {
							href: "javascript:void(0)"
						},
						on: {
							click: function(t) {
								e.onClickMember("super")
							},
							mouseenter: function(t) {
								e.onEnterMember()
							},
							mouseleave: function(t) {
								e.onLeaveMember("super")
							}
						}
					}, [e._v(e._s(e.superMemberText))])]), e._v(" ")/*, a("ul", {
						staticClass: "userinfo__privilegelist l-d-flex l-justify-content-between l-align-items-center"
					}, e._l(e.data, function(t, n) {
						return a("li", {
							staticClass: "userinfo__privilegelist_item",
							on: {
								mouseenter: e.onEnterPrivilege,
								mouseleave: function(a) {
									e.onLeavePrivilege(n, t.name)
								}
							}
						}, [t.icon_url ? a("img", {
							staticClass: "userinfo__privilegelist_icon",
							attrs: {
								src: t.icon_url,
								alt: t.name
							},
							on: {
								click: function(a) {
									e.onClickPrivilege(n, t, "icon" + (n + 1))
								}
							}
						}) : e._e(), e._v(" "), a("div", {
							staticClass: "userinfo__privilegelist_tip"
						}, [a("div", {
							staticClass: "userinfo__privilegelist_tip-in"
						}, [a("span", {
							staticClass: "userinfo__privilegelist_triangle"
						}), e._v(" "), t.title ? a("h5", [e._v(e._s(t.title))]) : e._e(), e._v(" "), t.desc ? a("h6", [e._v(e._s(t.desc))]) : e._e(), e._v(" "), t.img_url ? a("img", {
							attrs: {
								src: t.img_url,
								alt: ""
							},
							on: {
								click: function(a) {
									e.onClickPrivilege(n, t, "icon" + (n + 1) + "pic")
								}
							}
						}) : e._e(), e._v(" "), a("p", {
							staticClass: "l-d-flex l-justify-content-between l-align-items-center"
						}, [a("span", [e._v(e._s(t.text || ""))]), a("a", {
							attrs: {
								href: "javascript:void(0)"
							},
							on: {
								click: function(a) {
									e.onClickPrivilege(n, t, "icon" + (n + 1) + "word")
								}
							}
						}, [e._v(e._s(t.link_text || ""))])])])])])
					}))*/]) : e._e()])
				},
				staticRenderFns: []
			};
		var ae = a("VU/8")(ee, te, !1, function(e) {
			a("v18D")
		}, "data-v-08fb414a", null).exports,
			ne = void 0,
			oe = void 0,
			ie = void 0,
			se = void 0,
			ce = {},
			re = {
				data: function() {
					return {}
				},
				mounted: function() {
					var e = this;
					m.a.getUserProfessionFinish.then(function() {
						e.getRecommendedFunction()
					})
				},
				methods: o()({}, Object(p.b)("recommendedFunction", ["setrecommendedFunction"]), {
					getRecommendedFunction: function() {
						var e = this;
						return new r.a(function(t) {
							d.a.getRecommendedFunction().then(function(a) {
								a ? (e.editDocs = a.map(function(e) {
									return {
										pluginIcon: e.pluginIcon,
										pluginTitle: e.pluginTitle,
										pluginBrief: e.pluginBrief
									}
								}), t()) : (e.editDocs = [], t())
							}).
							catch (function(a) {
								e.editDocs = [], t(a)
							})
						})
					},
					clicktRecommendationFunction: function(e) {
						d.a.clicktRecommendationFunction(e)
					},
				}),
			},
			le = {
				render: function() {
					var e = this,
						t = e.$createElement,
						n = e._self._c || t
					return n("div", {
							staticClass: "recommendedFunctionContainer"
						}, [n("button", {
							staticClass: "recommendedFunctionContainer_tablecontainer1_pdf2wordBtn",
							on: {
								click: function(t) {
									e.clicktRecommendationFunction("1");
								}
							}
						}, [n("img",
						{
							staticClass: "recommendedFunctionContainer_tablecontainer1_pdf2wordImg",
							attrs: {
								src: "images\\pdf2word.svg",
								//src: e.pluginIcon || [],
								width: "64px",
								height: "64px"
							}
						}
						), n("div",{
							staticClass: "recommendedFunctionContainer_introductionContainer1_pdf2word"
						}, [n("span", {
							staticClass: "recommendedFunctionContainer_introductionContainer1_pdf2wordTitle"
						}, [e._v("PDF转Word")]),n("span", {
							staticClass: "recommendedFunctionContainer_introductionContainer1_pdf2wordBrief"
						}, [e._v("将PDF文件转换为Word文件，支持OCR图像识别")])])]), n("button", {
							staticClass: "recommendedFunctionContainer_tablecontainer1_pdf2execlBtn",
							on: {
								click: function(t) {
									e.clicktRecommendationFunction("2");
								}
							}
						}, [n("img",
						{
							staticClass: "recommendedFunctionContainer_tablecontainer1_pdf2execlImg",
							attrs: {
								src: "images\\pdf2excel.svg",
								width: "64px",
								height: "64px"
							}
						}
						), n("div",{
							staticClass: "recommendedFunctionContainer_introductionContainer1_pdf2excel"
						}, [n("span", {
							staticClass: "recommendedFunctionContainer_introductionContainer1_pdf2excelTitle"
						}, [e._v("PDF转Excel")]),n("span", {
							staticClass: "recommendedFunctionContainer_introductionContainer1_pdf2excelBrief"
						}, [e._v("将PDF文件转换为Excel表格进行编辑")])]) ]), n("button", {
							staticClass: "recommendedFunctionContainer_tablecontainer1_pdf2pptBtn",
							on: {
								click: function(t) {
									e.clicktRecommendationFunction("3");
								}
							}
						}, [n("img",
						{
							staticClass: "recommendedFunctionContainer_tablecontainer1_pdf2pptImg",
							attrs: {
								src: "images\\pdf2ppt.svg",
								width: "64px",
								height: "64px"
							}
						}
						), n("div",{
							staticClass: "recommendedFunctionContainer_introductionContainer1_pdf2ppt"
						}, [n("span", {
							staticClass: "recommendedFunctionContainer_introductionContainer1_pdf2pptTitle"
						}, [e._v("PDF转PPT")]),n("span", {
							staticClass: "recommendedFunctionContainer_introductionContainer1_pdf2pptBrief"
						}, [e._v("将PDF文件转换为PPT幻灯片")])]) ]), n("button", {
							staticClass: "recommendedFunctionContainer_tablecontainer2_pdfCombineBtn",
							on: {
								click: function(t) {
									e.clicktRecommendationFunction("5");
								}
							}
						}, [n("img",
						{
							staticClass: "recommendedFunctionContainer_tablecontainer2_pdfCombineImg",
							attrs: {
								src: "images\\pdf_combine.svg",
								width: "64px",
								height: "64px"
							}
						}
						), n("div",{
							staticClass: "recommendedFunctionContainer_introductionContainer2_pdfCombine"
						}, [n("span", {
							staticClass: "recommendedFunctionContainer_introductionContainer2_pdfCombineTitle"
						}, [e._v("PDF合并")]),n("span", {
							staticClass: "recommendedFunctionContainer_introductionContainer2_pdfCombineBrief"
						}, [e._v("将多个PDF文件合并成一个PDF文件")])]) ]), n("button", {
								staticClass: "recommendedFunctionContainer_tablecontainer2_pdfSplitBtn",
								on: {
									click: function(t) {
										e.clicktRecommendationFunction("6");
									}
								}
							}, [n("img",
							{
								staticClass: "recommendedFunctionContainer_tablecontainer2_pdfSplitImg",
								attrs: {
									src: "images\\pdf_split.svg",
									width: "64px",
									height: "64px"
								}
							}
							), n("div",{
								staticClass: "recommendedFunctionContainer_introductionContainer2_pdfSplit"
							}, [n("span", {
								staticClass: "recommendedFunctionContainer_introductionContainer2_pdfSplitTitle"
							}, [e._v("PDF拆分")]),n("span", {
								staticClass: "recommendedFunctionContainer_introductionContainer2_pdfSplitBrief"
							}, [e._v("将一个PDF文件拆分成多个PDF文件，支持自定义页数拆分")])]) ]), n("button", {
								staticClass: "recommendedFunctionContainer_tablecontainer2_pdf2photoBtn",
								on: {
									click: function(t) {
										e.clicktRecommendationFunction("4");
									}
								}
							}, [n("img",
							{
								staticClass: "recommendedFunctionContainer_tablecontainer2_pdf2photoImg",
								attrs: {
									src: "images\\pdf2photo.svg",
									width: "64px",
									height: "64px"
								}
							}
							), n("div",{
								staticClass: "recommendedFunctionContainer_introductionContainer2_pdf2photo"
							}, [n("span", {
								staticClass: "recommendedFunctionContainer_introductionContainer2_pdf2photoTitle"
							}, [e._v("PDF转图片")]),n("span", {
								staticClass: "recommendedFunctionContainer_introductionContainer2_pdf2photoBrief"
							}, [e._v("将PDF文件转换为图片格式文件，支持逐页输出和合成长图")])]) ]), n("button", {
								staticClass: "recommendedFunctionContainer_tablecontainer3_photo2pdfBtn",
								on: {
									click: function(t) {
										e.clicktRecommendationFunction("8");
									}
								}
							}, [n("img",
							{
								staticClass: "recommendedFunctionContainer_tablecontainer3_photo2pdfImg",
								attrs: {
									src: "images\\photo2pdf.svg",
									width: "64px",
									height: "64px"
								}
							}
							), n("div",{
								staticClass: "recommendedFunctionContainer_introductionContainer3_photo2pdf"
							}, [n("span", {
								staticClass: "recommendedFunctionContainer_introductionContainer3_photo2pdfTitle"
							}, [e._v("图片转PDF")]),n("span", {
								staticClass: "recommendedFunctionContainer_introductionContainer3_photo2pdfBrief"
							}, [e._v("将图片格式文件一键转换成PDF文件")])]) ]), n("button", {
								staticClass: "recommendedFunctionContainer_tablecontainer3_translateAllBtn",
								on: {
									click: function(t) {
										e.clicktRecommendationFunction("7");
									}
								}
							}, [n("img",
							{
								staticClass: "recommendedFunctionContainer_tablecontainer3_translateAllImg",
								attrs: {
									src: "images\\pdfcompress.svg",
									width: "64px",
									height: "64px"
								}
							}
							), n("div",{
								staticClass: "recommendedFunctionContainer_introductionContainer3_translateAll"
							}, [n("span", {
								staticClass: "recommendedFunctionContainer_introductionContainer3_translateAllTitle"
							}, [e._v("压缩")]),n("span", {
								staticClass: "recommendedFunctionContainer_introductionContainer3_translateAllBrief"
							}, [e._v("减小PDF文件体积")])]) ])])
				},
				staticRenderFns: []
			};
		var de = a("VU/8")(re, le, !1, function(e) {
			a("17Ab")
		}, "data-v-173d1880", null).exports,
			ue = void 0,
			pe = void 0,
			me = void 0,
			fe = void 0,
			he = void 0,
			_e = void 0,
			ve = void 0,
			ge = {
				mixins: [X.a],
				data: function() {
					return {}
				},
				mounted: function() {
					var e = this;
					m.a.getUserProfessionFinish.then(function() {
						var newpdfbtn = document.getElementById("gotocontainer__gotoNewFile__btnId");
						newpdfbtn.className = "active";
					})
				},
				methods: o()({}, Object(p.b)("gotoFunction", ["setgotoFunction"]), {
					sendInfo: function(e, t) {
						d.a.sendInfo(e, t)
					},
				window:onscroll= function(){
					var newPdf = document.getElementById("mainRightTopContainer");
					var recommendedFunction = document.getElementById("mainRightBottomContainer");
					var arrOffsetTop = [
						newPdf.offsetTop,
						recommendedFunction.offsetTop
					];
					var newpdfbtn = document.getElementById("gotocontainer__gotoNewFile__btnId");
					var recommandbtn = document.getElementById("gotocontainer__gotoRecommend__btnId");
					//window.confirm(window.pageYOffset  + "	" +arrOffsetTop[0] + " " +arrOffsetTop[1]);
					if (window.pageYOffset >= 100)
					{
						recommandbtn.className = "active";
						newpdfbtn.className = "gotocontainer__gotoNewFile__btn";
					}
					else if (window.pageYOffset < 100)
					{
						newpdfbtn.className = "active";
						recommandbtn.className = "gotocontainer__gotoRecommend__btn";
					}
					var sidebar = document.getElementById("sidebarContainer");
					var sl=-Math.max(document.body.scrollLeft,document.documentElement.scrollLeft);
					sidebar.style.left=sl+'px';
				}
				}),
			},
			ye = {
				render: function() {
					var e = this,
						t = e.$createElement,
						a = e._self._c || t;
					return a("div", {
						staticClass: !e.userInfo.isWpsMember ? "gotocontainer" : "gotocontainer1"
					}, [a("div", {
						staticClass: "gotocontainer__gotoNewFile__container"
					}, [a("button", {
							staticClass: "gotocontainer__gotoNewFile__btn",
							attrs: {
								id: "gotocontainer__gotoNewFile__btnId"
							},
							on: {
								click: function () {
									var newPdf = document.getElementById("mainRightTopContainer");
									var recommendedFunction = document.getElementById("mainRightBottomContainer");
									var arrOffsetTop = [
										newPdf.offsetTop,
										recommendedFunction.offsetTop
									];
									window.scrollTo(0,arrOffsetTop[0]);
									var newpdfbtn = document.getElementById("gotocontainer__gotoNewFile__btnId");
									var recommandbtn = document.getElementById("gotocontainer__gotoRecommend__btnId");
									newpdfbtn.className = "active";
									recommandbtn.className = "gotocontainer__gotoRecommend__btn";
									e.sendInfo("Home|NewPDF_TabNewPDF", "");
								}
							}
					}, [e._v("新建PDF")])], 2),
						a("div", {
							staticClass: "gotocontainer__gotoRecommend__container"
						},[a("button", {
							staticClass: "gotocontainer__gotoRecommend__btn",
							attrs: {
								id: "gotocontainer__gotoRecommend__btnId"
							},
							on: {
								click: function () {
									var newPdf = document.getElementById("mainRightTopContainer");
									var recommendedFunction = document.getElementById("mainRightBottomContainer");
									var arrOffsetTop = [
										newPdf.offsetTop,
										recommendedFunction.offsetTop
									];
									window.scrollTo(0,arrOffsetTop[1]);
									var recommandbtn = document.getElementById("gotocontainer__gotoRecommend__btnId");
									var newpdfbtn = document.getElementById("gotocontainer__gotoNewFile__btnId");
									recommandbtn.className = "active";
									newpdfbtn.className = "gotocontainer__gotoNewFile__btn";
									e.sendInfo("Home|NewPDF_TabRecommend", "");
								}
							}
					}, [e._v("推荐功能")])],2 ),  !e.userInfo.isWpsMember ? a("div", {
							staticClass: "gotocontainer__advertisement__container"
						}, [a("div", {
							staticClass: "gotocontainer__advertisement__link",
							/*on: {
								click: function () {
									//window.open("https://www.baidu.com/");
								}
							}*/
						}, [a("img", {
							staticClass: "gotocontainer__advertisement__img",
							attrs: {
								src: "images\\advertisement.svg",
								alt: "",
								width: "186px",
								height: "186px"
							}
						})/*, a("img", {
							staticClass: "gotocontainer__advertisementicon__img",
							attrs: {
								src: "images\\pdf_ad_icon.svg",
								alt: "",
								width: "37px",
								height: "20px"
							}
						})*/
						], 2)], 2) :e._e()
					], 2)
				},
				staticRenderFns: []
			};
		var be = a("VU/8")(ge, ye, !1, function(e) {
			a("dF9k")
		}, "data-v-7dcdd233", null).exports,
			we = void 0,
			Ce = void 0,
			xe = void 0,
			Me = !0,
			ke = [u.b.NEWFILE2019_SEARCH_BTN, u.b.NEWFILE2019_LEFTBTN, u.b.NEWFILE2019_LEFTNAV_PIC, u.b.NEWFILE2019_PRIVILEGE_ICON],
			Pe = {
				name: "home",
				mixins: [X.a],
				data: function() {
					return {
						isShowHot: !0,
						isShowRecent: !0,
						hasShowEffect: !0,
						collectInfo: {
							p1: "home_page",
							p4: u.c.name + "_client"
						},
						categoryLimit: 0
					}
				},
				mounted: function() {
					var e = this;
					f.a.$on(u.h.featureChange, this.onChangeShowEffect), f.a.$on(u.h.finishUserInfo, function() {
						Me || e.initAD()
					})
				},
				beforeRouteEnter: function(e, t, a) {
					we = (new Date).getTime(), a(function(e) {
						Ce && e.$nextTick(function() {
							e.$DocerCollectShow.update()
						}), m.a.getUserProfessionFinish.then(function() {
							e.setCategoryLimit(), e.initAD()
						}), F.a.get(u.d.lastEffectDate).then(function(t) {
							t < (new Date).getTime() && (e.hasShowEffect = !1, h.a.sendHome({
								p7: "usefulness",
								p8: "display",
								p12: "button",
								p27: e.mainAlg
							}))
						}), f.a.$on(u.h.unloadWindow, e.collectStay), f.a.$on(u.h.resizeWindow, e.setCategoryLimit), f.a.$on(u.h.login, e.setCategoryLimit), f.a.$on(u.h.logout, e.setCategoryLimit), h.a.sendHome({
							p8: "display",
							p12: "page"
						}), h.a.sendHome({
							p7: "new_empty",
							p8: "display",
							p12: "new"
						})
					})
				},
				beforeRouteLeave: function(e, t, a) {
					this.collectStay(), Ce = !0, f.a.$off(u.h.unloadWindow, this.collectStay), f.a.$off(u.h.resizeWindow, this.setCategoryLimit), f.a.$off(u.h.login, this.setCategoryLimit), f.a.$off(u.h.logout, this.setCategoryLimit), a()
				},
				methods: o()({}, Object(p.b)("recommend", ["setRecFirstScreenAds"]), {
					initAD: function() {
						var e = this;
						Me && (Me = !1), this.$_getAD({
							params: {
								position: ke.join(";")
							},
							cacheTime: 9e5
						}).then(function(t) {
							e.setRecFirstScreenAds(t.data || "")
						})
					},
					setCategoryLimit: function() {
						var e = this;
						setTimeout(function() {
							if ("home" == m.a.getRoutePath(e.$route.path)[0] && e.$refs.nSidebar && e.$refs.nUserInfo) try {
								var t = e.$refs.nSidebar.getBoundingClientRect().height - e.$refs.nUserInfo.getBoundingClientRect().height - 145;
								e.categoryLimit = Math.floor((t > 0 ? t : 0) / 50)
							} catch (e) {}
						}, 0)
					},
					onClickRec: function(e) {
						if (!this.hasShowEffect) {
							this.changeShowEffect(!0);
							var t = [];
							this.likeData && this.likeData.forEach(function(e) {
								e && e.id && t.push(e.id)
							}), h.a.sendHome({
								p7: "usefulness",
								p8: "click",
								p9: t.join("|"),
								p11: e,
								p12: "button",
								p27: this.mainAlg
							}), F.a.set(u.d.lastEffectDate, (new Date).getTime() + 6048e5)
						}
					},
					onEnterUseful: function() {
						xe = (new Date).getTime()
					},
					onLeaveUseful: function() {
						(new Date).getTime() - xe > 1e3 && h.a.sendHome({
							p7: "usefulness",
							p8: "hover",
							p12: "button",
							p27: this.mainAlg
						})
					},
					onChangeShowEffect: function() {
						F.a.set(u.d.lastEffectDate, (new Date).getTime()), h.a.sendHome({
							p7: "usefulness",
							p8: "display",
							p12: "button",
							p27: this.mainAlg
						}), this.changeShowEffect(!1)
					},
					changeShowEffect: function(e) {
						this.hasShowEffect = e
					},
					collectStay: function() {
						h.a.sendHome({
							p8: "stay",
							p12: "page",
							p15: (((new Date).getTime() - we) / 1e3).toFixed(2)
						})
					}
				}),
				components: {
					MainContent: z,
					UserInfo: ae,
					RecommendedFunction: de,
					Category: be,
				}
			},
			Se = {
				render: function() {
					var e = this,
						t = e.$createElement,
						a = e._self._c || t;
					return a("div", {
						staticClass: "container l-justify-content-between l-row l-flex",
						attrs: {
							id: "container"
						}
					}, [a("div", {
						ref: "nSidebar",
						staticClass: "sidebar",
						attrs: {
							id: "sidebarContainer"
						}
					}, [a("div", {
						ref: "nUserInfo"
					}, [a("UserInfo"), e._v(" ")], 1), e._v(" "), a("div", [a("Category", {
						attrs: {
							categoryLimit: e.categoryLimit
						}
					})], 1)]), a("div", {
						staticClass: "mainContainer",
						attrs: {
							id: "mainContentContainer"
						}
					}, [a("div", {
						staticClass: "mainRightTopContainer",
						attrs: {
							id: "mainRightTopContainer"
						}
					}, [a("div", {
						staticClass: "mainTopRightContainer__titleContainer"
					}, [a("a", {
						staticClass: "mainTopRightContainer__titleContainer__title",
						attrs: {
							style: "color:#ff0000",
						}
					}), e._v("新建PDF")]), e._v(" "), a("div", {
						directives: [{
							name: "stick",
							rawName: "v-stick"
						}],
						staticClass: "mainRightTop__content"
					}, [a("MainContent")], 1)
					]),a("div", {
						staticClass: "mainRightBottomContainer",
						attrs: {
							id: "mainRightBottomContainer"
						}
					}, [a("div", {
						staticClass: "mainBottomRightContainer__titleContainer"
					}, [a("a", {
						staticClass: "mainBottomRightContainer__titleContainer__title",
						attrs: {
							style: "color:#ff0000",
						}
					}), e._v("推荐功能")]), e._v(" "), a("div", {
						directives: [{
							name: "stick",
							rawName: "v-stick"
						}],
						staticClass: "mainRightBottom__content"
					}, [a("RecommendedFunction")], 1)
					])])])
				},
				staticRenderFns: []
			};
		var De = a("VU/8")(Pe, Se, !1, function(e) {
			a("1SDU")
		}, "data-v-cfde272c", null);
		t.
	default = De.exports
	},
	Vy69: function(e, t) {},
	dF9k: function(e, t) {},
	negx: function(e, t) {},
	pXtb: function(e, t) {},
	v18D: function(e, t) {}
});
