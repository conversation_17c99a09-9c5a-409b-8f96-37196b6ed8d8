import os
import sys
cwd = os.path.dirname(__file__)
sys.path.append(os.path.join(cwd, '../../../../../../resource_bundle/shell2/mui/bin'))
from update_plugin_kui_ts import update_plugin_kui_ts

component_list = ['knewdocs']
lang_list = ['zh_CN', 'en_US', 'ja_JP', 'zh_TW', 'fr_FR', 'de_DE', 'es_ES', 'ru_RU', 
    'pt_PT', 'pt_BR', 'pl_PL', 'id_ID']
update_plugin_kui_ts(cwd + os.sep + '../res/kuip', cwd + os.sep + '../mui',
    component_list, lang_list, True)