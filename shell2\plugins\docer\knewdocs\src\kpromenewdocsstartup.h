﻿#ifndef __KPROMETHEUS_NEWDOCSTARTUP_H__
#define __KPROMETHEUS_NEWDOCSTARTUP_H__

#include <kprometheus/kpromebrowser.h>
#include "knewdocswebwidget.h"
#include <ksolite/ikdcsvrproxy.h>
class KPromeBrowserWidgetBase;
class KxPromePdfWindowJsApi;

class KPromeNewDocsStartup;
class KPromeNewDocsWaitingWidget : public QWidget
{
	Q_OBJECT
public:
	KPromeNewDocsWaitingWidget(QWidget* parent);
private slots:
	void onSkinChanged();
protected:
	virtual void resizeEvent(QResizeEvent* event) override;
	virtual bool event(QEvent* e) override;
	void resetBackground();
private:
	QMovie* m_movie;
	QMovie* m_darkMovie;
	QLabel* m_label;
};

class KNewDocsWebWidget;
class KPromeNewDocsStartup	: public KPromePluginWidget
{
	Q_OBJECT
public:
	KPromeNewDocsStartup(QWidget* parent);
	~KPromeNewDocsStartup();

public:
	void initBrowser(int type);
	void initKNewDocsBrowser(int type);
	void changeEntry(int type);
	void refreshUrl(int type, bool bForce = false, const QString& url = QString());
	void setExpectedUrl(const QString& url);
	void loadErrorPage(KNewDocsWebWidget::KNewdocsloadErrorCode errorCode);
	QString getBrowserType();
	KPromePluginWidget::WidgetType getBrowserRawType();
	void createWaitingWidget();
	void setOnlineWebPage(bool bOnline);
	bool isOnlineWebPage();
	void invokeWebPageCallback(const QString& param);
	void openDocument(const QString&, const QString&, const QString&);
	/**
	 * @brief 设置来源，用于初始化JS API
	 * @param from QString 来源
	 **/
	void setFrom(const QString& from);
	QString getFrom();
	void reloadNewDocsWebWidget(int type);
	QVariantMap buildKNewDocsJsParams(int type);

public slots:
	void showWebWidget();
	void onOpenDocument(const QString&, const QString&, const QString&, const QString&, const QString& = QString());
	void onOpenPromeBrowser(const QString&);
	void onSubPageActived();
	void onExecOfficialDoc(const QString&);
	void onCreatingApi(KxWebViewContainer* webview, QObject** api);
	void onCreatingPdfApi(KxWebViewContainer* webview, QObject** api);
	void onOpenRecentDocument(const QString&, const QString&);
signals:
	void cefKeyEvent(Qt::KeyboardModifiers modifyKey, int key, bool keyPress, bool &handle);
	void closeTab();
	void downloadUpdate(bool);
	void sigInitBrowser();
	void signalOpenDocument(const QString&, const QString&, const QString&);
	void sigShowWidget(const QWidget*);
	void mainResourceVerificationFaile(const QString& url);
	void sigV7NewDoc(const QString& callBack, const QVariantMap& result);

protected slots:
	void onCreateSucceeded(KPromeBrowserWidgetBase*);
	void onJsInit();
	void onLoadFinished(bool ok);
	void onCreateFileFinished(bool bOk, const QString& requestId, const string& strResult);
	void onGetCloudFileInfoFinished(bool bOk, const QString& requestId, const string& strResult);
	void onGetUploadStateTimerTimeout();
	void onGetBatchUploadFileListState(bool bOk, const QString& uuid, const KRpcResponse& res);
	void onChromeWidgetWinMessage(unsigned int hwnd, unsigned int message, unsigned int wparam, unsigned int lparam);
	void onV7NotifyUploadFileListState(bool bOk, const QString& filePath, const QString& fileInfo);

protected:
	void paintEvent(QPaintEvent* ev) override;
	void resizeEvent(QResizeEvent* event) override;
	QString getModuleName(QString strPageType);
	void getFileLocParamFromUrl(const QString& url, QString& driveId, QString& parentId);
	void openV7TemplateDocument(const QString& from, const QString& filePath, const QString& browserType, const QString& callBack = QString());
	void onOpenV7Document(const QString& fileId, const QString& fileName, const QString& cloudPath);
	QString getTargetUrlArgsType(const QString& browserType);
private:
	KPromeMainWindow* loadMainWindow(KPromePluginWidget::WidgetType plgType, 
		KPromePluginWidget::WidgetType &currentPageType, 
		const QString &from, bool &bcreateNew);
	bool _canShowOnlineTemplate();
	bool _networkIsOk();
	bool _hasOnlineTemplatePrivilege();
	QString getNewCreateFileNameByType(const QString& browserType);
	void _uploadFiles(const QStringList& curFileList, const QString& groupId,
		const QString& parentId, const QString& driveId,
		const QString& fileId = "", const QString& callBack = "");
	void _startGetUploadState();
	void _getUploadState();
	void _stopGetUploadState();
	void _addFileListToUploadingList(const QStringList& fileList, const QString& callBack);

private:
	KPromeBrowserCreator m_browserCreator;
	KPromeBrowserWidgetBase* m_browser = nullptr;
	KNewDocsWebWidget* m_webwidget = nullptr;
	QStackedLayout* m_layout;
	int m_browserType = KPromePluginWidget::invalidWidgetType;
	QWidget* m_pParent;
	bool m_bOnlineWebPage;
	bool m_bRunOnce;
	qint64 m_nLoadDuration;
	qint64 m_initBrowserTime = 0;
	qint64 m_browserCreatedTime = 0;
	QTcpSocket* m_netTestSocket;
	QString	m_expectedUrl;
	QString m_from;
	QTimer m_checkUploadStatusTimer;
	QSet<QString> m_uploadingFiles;
	QMap<QString, QString> m_v7NewBlankDocCallBacks;
	QMap<QString, QString> m_v7NewLocalDocCallBacks;
};

#endif//__KPROMETHEUS_NEWDOCSPAGE_H__
