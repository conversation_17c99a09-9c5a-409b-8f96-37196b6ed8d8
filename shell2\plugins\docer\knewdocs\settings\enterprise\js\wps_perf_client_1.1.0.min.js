/*!
 * wps_perf_client v1.1.0
 * (c) 2021-2022 guangzhou-fe
 * Released under the MIT License.
 */
var t, e;
t = this, e = function () {
	"use strict";
	var t = 1,
		e = function (t) {
			setTimeout(function () {
				t && t()
			}, 0)
		},
		n = function () {
			this.map = {}, this.rmap = {}
		};
	n.prototype.waitFor = function (e, n) {
		var r = this.map,
			i = this.rmap;
		"string" == typeof e && (e = [e]);
		var o = (t++).toString(16);
		r[o] = {
			waiting: e.slice(0),
			callback: n
		};
		for (var a = 0, s = e.length; a < s; ++a) {
			var c = e[a];
			(i[c] || (i[c] = [])).push(o)
		}
		return this
	}, n.prototype.trigger = function (t) {
		if (!t) return this;
		var e = this.rmap;
		"string" == typeof t && (t = [t]);
		for (var n = 0, r = t.length; n < r; ++n) {
			var i = t[n];
			void 0 !== e[i] && (this._release(i, e[i]), delete e[i])
		}
		return this
	}, n.prototype._release = function (t, n) {
		for (var r = this.map, i = 0, o = n.length; i < o; ++i) {
			var a = n[i],
				s = r[a],
				c = s.waiting,
				u = Array.prototype.indexOf.call(c, t);
			c.splice(u, 1), 0 === c.length && (e(s.callback), delete r[a])
		}
	};
	var r = new n,
		i = Object.create(null);

	function o(t) {
		return t ? i[t] : null
	}

	function a(t, e) {
		var n;
		return i = Object.assign(i, ((n = {})[t] = e, n))
	}
	var s = 0,
		c = ["img", "script", "iframe", "link", "audio", "video", "source"];

	function u(t, e) {
		for (var n in t) {
			var r = t[n];
			if (e.includes(r.nodeName.toLowerCase()) || u(r.children, e)) return !0
		}
		return !1
	}
	var l = function (t) {
			return void 0 === t && (t = 5e3), new Promise(function (e) {
				setTimeout(function () {
					e()
				}, t)
			})
		},
		f = function (t) {
			var e = t.nodeName.toLowerCase(),
				n = t.id || t.className;
			return n || (n = t.innerHTML.substr(0, 64), "img" === e && (n = t.currentSrc)), e + ":" + n
		},
		p = 5e3,
		d = function (t, e) {
			if (t.length > 2) return performance.now();
			var n = [];
			for (var r in e) {
				var i = e[r];
				n.push({
					timestamp: i.start,
					type: "requestStart"
				}), n.push({
					timestamp: i.end,
					type: "requestEnd"
				})
			}
			for (var o in t) {
				var a = t[o];
				n.push({
					timestamp: a,
					type: "requestStart"
				})
			}
			n.sort(function (t, e) {
				return t.timestamp - e.timestamp
			});
			for (var s = t.length, c = n.length - 1; c >= 0; c--) {
				var u = n[c];
				switch (u.type) {
					case "requestStart":
						s--;
						break;
					case "requestEnd":
						if (++s > 2) return u.timestamp;
						break;
					default:
						throw Error("Internal Error: This should never happen")
				}
			}
			return 0
		},
		h = function (t) {
			void 0 === t && (t = {}), this._useMutationObserver = !!t.useMutationObserver, this._minValue = t.minValue || null;
			var e = window.__tti && window.__tti.e,
				n = window.__tti && window.__tti.o;
			this._longTasks = e ? e.map(function (t) {
				return {
					start: t.startTime,
					end: t.startTime + t.duration
				}
			}) : [], n && n.disconnect(), this._networkRequests = [], this._incompleteJSInitiatedRequestStartTimes = new Map, this._timerId = null, this._timerActivationTime = -1 / 0, this._scheduleTimerTasks = !1, this._firstConsistentlyInteractiveResolver = null, this._performanceObserver = null, this._mutationObserver = null, this._registerListeners()
		},
		m = {
			_incompleteRequestStarts: {
				configurable: !0
			}
		};
	h.prototype.getFirstConsistentlyInteractive = function () {
		var t = this;
		return new Promise(function (e, n) {
			t._firstConsistentlyInteractiveResolver = e, "complete" == document.readyState ? t.startSchedulingTimerTasks() : window.addEventListener("load", function () {
				t.startSchedulingTimerTasks()
			})
		})
	}, h.prototype.startSchedulingTimerTasks = function () {
		this._scheduleTimerTasks = !0;
		var t = this._longTasks.length > 0 ? this._longTasks[this._longTasks.length - 1].end : 0,
			e = d(this._incompleteRequestStarts, this._networkRequests);
		this.rescheduleTimer(Math.max(e + p, t))
	}, h.prototype.setMinValue = function (t) {
		this._minValue = t
	}, h.prototype.rescheduleTimer = function (t) {
		var e = this;
		this._scheduleTimerTasks && (this._timerActivationTime > t || (clearTimeout(this._timerId), this._timerId = setTimeout(function () {
			e._checkTTI()
		}, t - performance.now()), this._timerActivationTime = t))
	}, h.prototype.disable = function () {
		clearTimeout(this._timerId), this._scheduleTimerTasks = !1, this._unregisterListeners()
	}, h.prototype._registerPerformanceObserver = function () {
		var t = this;
		this._performanceObserver = new PerformanceObserver(function (e) {
			var n = e.getEntries();
			for (var r in n) {
				var i = n[r];
				"resource" === i.entryType && t._networkRequestFinishedCallback(i), "longtask" === i.entryType && t._longTaskFinishedCallback(i)
			}
		}), this._performanceObserver.observe({
			entryTypes: ["longtask", "resource"]
		})
	}, h.prototype._registerListeners = function () {
		var t, e, n, r, i, o, a, l, f;
		t = this._beforeJSInitiatedRequestCallback.bind(this), e = this._afterJSInitiatedRequestCallback.bind(this), n = XMLHttpRequest.prototype.send, r = s++, XMLHttpRequest.prototype.send = function () {
			for (var i = this, o = [], a = arguments.length; a--;) o[a] = arguments[a];
			return t(r), this.addEventListener("readystatechange", function () {
				4 === i.readyState && e(r)
			}), n.apply(this, o)
		}, i = this._beforeJSInitiatedRequestCallback.bind(this), o = this._afterJSInitiatedRequestCallback.bind(this), a = fetch, fetch = function () {
			for (var t = [], e = arguments.length; e--;) t[e] = arguments[e];
			return new Promise(function (e, n) {
				var r = s++;
				i(r), a.apply(void 0, t).then(function (t) {
					o(r), e(t)
				}, function (t) {
					o(t), n(t)
				})
			})
		}, this._registerPerformanceObserver(), this._useMutationObserver && (this._mutationObserver = (l = this._mutationObserverCallback.bind(this), (f = new MutationObserver(function (t) {
			for (var e in t = t) {
				var n = t[e];
				"childList" == n.type && u(n.addedNodes, c) ? l(n) : "attributes" == n.type && c.includes(n.target.tagName.toLowerCase()) && l(n)
			}
		})).observe(document, {
			attributes: !0,
			childList: !0,
			subtree: !0,
			attributeFilter: ["href", "src"]
		}), f))
	}, h.prototype._unregisterListeners = function () {
		this._performanceObserver && this._performanceObserver.disconnect(), this._mutationObserver && this._mutationObserver.disconnect()
	}, h.prototype._beforeJSInitiatedRequestCallback = function (t) {
		this._incompleteJSInitiatedRequestStartTimes.set(t, performance.now())
	}, h.prototype._afterJSInitiatedRequestCallback = function (t) {
		this._incompleteJSInitiatedRequestStartTimes.delete(t)
	}, h.prototype._networkRequestFinishedCallback = function (t) {
		this._networkRequests.push({
			start: t.fetchStart,
			end: t.responseEnd
		}), this.rescheduleTimer(d(this._incompleteRequestStarts, this._networkRequests) + p)
	}, h.prototype._longTaskFinishedCallback = function (t) {
		var e = t.startTime + t.duration;
		this._longTasks.push({
			start: t.startTime,
			end: e
		}), this.rescheduleTimer(e + p)
	}, h.prototype._mutationObserverCallback = function (t) {
		this.rescheduleTimer(performance.now() + p)
	}, h.prototype._getMinValue = function () {
		if (this._minValue) return this._minValue;
		if (performance.timing.domContentLoadedEventEnd) {
			var t = performance.timing;
			return t.domContentLoadedEventEnd - t.navigationStart
		}
		return null
	}, m._incompleteRequestStarts.get = function () {
		return [].concat(this._incompleteJSInitiatedRequestStartTimes.values())
	}, h.prototype._checkTTI = function () {
		var t = performance.timing.navigationStart,
			e = d(this._incompleteRequestStarts, this._networkRequests),
			n = (window.chrome && window.chrome.loadTimes ? 1e3 * window.chrome.loadTimes().firstPaintTime - t : 0) || performance.timing.domContentLoadedEventEnd - t,
			r = this._getMinValue(),
			i = performance.now();
		null === r && this.rescheduleTimer(Math.max(e + p, i + 1e3));
		var o = function (t, e, n, r, i) {
			if (r - n < p) return null;
			var o = 0 === i.length ? t : i[i.length - 1].end;
			return r - o < p ? null : Math.max(o, e)
		}(n, r, e, i, this._longTasks);
		o && (this._firstConsistentlyInteractiveResolver(o), this.disable()), this.rescheduleTimer(performance.now() + 1e3)
	}, Object.defineProperties(h.prototype, m);
	var g = function (t) {
		return void 0 === t && (t = {}), "PerformanceLongTaskTiming" in window ? new h(t).getFirstConsistentlyInteractive() : Promise.resolve(-1)
	};

	function v(t) {
		var e = (t.includes("?") ? t.split("?")[0] : t.includes("#") ? t.split("#")[0] : t).split("/").pop().split(".");
		return e.length >= 2 ? e.pop() : ""
	}
	var w = 0,
		_ = function (t) {
			var e = (new Date).getTime(),
				n = Math.max(w + 16, e);
			return setTimeout(function () {
				t(w = n)
			}, n - e)
		},
		y = function (t) {
			var e = this;
			this.callbacks = [], this.isPaintBool = !1, this.isImage = !1, this.currentNode = null, this.target = t, this.now = (new Date).getTime(), String.prototype.trim || (String.prototype.trim = function () {
				return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "")
			}), _(function () {
				e.checkPaint()
			})
		};
	y.prototype.getPageIsPaint = function (t) {
		this.callbacks.push(t), this.tracePaint()
	}, y.prototype.tracePaint = function () {
		var t = this;
		this.isPaintBool && (this.callbacks.forEach(function (e) {
			e(t.isPaintBool, t.currentNode)
		}), this.callbacks = [])
	}, y.prototype.isPainted = function (t) {
		return t.offsetHeight > 0
	}, y.prototype.isImageCompleted = function () {
		var t = this;
		this.currentNode.complete && this.isPainted(this.currentNode) ? (this.isPaintBool = !0, this.tracePaint()) : _(function () {
			return t.isImageCompleted()
		})
	}, y.prototype.checkPaint = function () {
		var t = this,
			e = document.createNodeIterator(this.target, NodeFilter.SHOW_ALL, function (e) {
				if (!e) return !1;
				if (e.nodeType === Node.TEXT_NODE && !t.isPainted(e.parentNode)) return !1;
				if (e.nodeType !== Node.TEXT_NODE && !t.isPainted(e)) return !1;
				var n = e.nodeType === Node.TEXT_NODE && /[^s]/.test(e.nodeValue.trim());
				if (n) return n;
				var r = e instanceof HTMLImageElement && "" !== e.src;
				return !!r && (t.isImage = !0, r)
			}, !1);
		if (this.currentNode = e.nextNode(), (new Date).getTime() - this.now > 1e4) return !1;
		null !== this.currentNode ? this.isImage ? _(function () {
			t.isImageCompleted()
		}) : _(function () {
			t.isPaintBool = !0, t.tracePaint()
		}) : _(function () {
			t.checkPaint()
		})
	};
	var b, T, C, S = ["SCRIPT", "STYLE", "META", "HEAD", "LINK"],
		k = /url\(\"(.*?)\"\)/,
		x = window.innerWidth,
		E = window.innerHeight,
		P = function (t) {
			this.mutationTimeCalculates = [], this.flag = !0, this.observer = null, this.callbackCount = 1, this.mp = {}, this.lcpTiming = 0, this.lcpEle = "", this.lcpTimeStamp = 0, this.runMutationObserver = !1, this.locked = !0, this.now = (new Date).getTime(), this.reWait = t
		};

	function O(t) {
		var e = 0,
			n = !0,
			i = "",
			o = "",
			s = p,
			c = t;
		return new Promise(function (u) {
			try {
				var d = new PerformanceObserver(function (t) {
						var n = t.getEntries(),
							r = n[n.length - 1],
							a = r.element;
						e = r.renderTime || r.loadTime, a && (i = f(a)), o = (new Date).getTime()
					}),
					h = function () {
						if (!n) return d.disconnect(), u({
							lcp: 0
						});
						var t = 0 === e ? -1 : e;
						u({
							lcp: Number(t.toFixed(2))
						}), a("lcp", {
							lcpEle: i,
							value: Number(t.toFixed(2)),
							lcpTimeStamp: o
						}), r.trigger("lcp"), d.disconnect()
					};
				l(s).then(function (t) {
					0 === e && c ? (c = !1, l(s).then(function (t) {
						h()
					})) : h()
				}), d.observe({
					entryTypes: ["largest-contentful-paint"]
				})
			} catch (e) {
				n = !1,
					function (t) {
						return new Promise(function (e) {
							if (window.MutationObserver && performance.getEntries) {
								var n = new P(t);
								n.initObserver(), r.waitFor(["LCPTiming"], function () {
									e(n.getLcpTiming())
								})
							} else l(p).then(function (t) {
								e(-1)
							})
						})
					}(t).then(function (t) {
						var e = t.rt,
							n = t.rEle,
							i = t.rts;
						u({
							lcp: e,
							lcpEle: n,
							lcpTimeStamp: i
						}), a("lcp", {
							lcpEle: n,
							value: Number(e.toFixed(2)),
							lcpTimeStamp: i
						}), r.trigger("lcp")
					})
			}
		})
	}
	P.prototype.getLcpTiming = function () {
		return {
			rt: this.lcpTiming,
			rEle: this.lcpEle,
			rts: this.lcpTimeStamp
		}
	}, P.prototype.firstSnapshot = function () {
		var t = this,
			e = document.body;
		new y(e).getPageIsPaint(function () {
			e && t.doTag(e, t.callbackCount++), t.mutationTimeCalculates.push({
				t: performance.now(),
				ts: (new Date).getTime()
			}), t.locked = !1
		})
	}, P.prototype.removeTag = function (t) {
		var e = t.children ? t.children.length : 0;
		if (e > 0)
			for (var n = t.children, r = e - 1; r >= 0; r--) null !== n[r].getAttribute("lcp_do_tag") && n[r].removeAttribute("lcp_do_tag"), this.removeTag(n[r])
	}, P.prototype.initObserver = function () {
		var t = this;
		this.firstSnapshot(), this.observer = new MutationObserver(function () {
			t.locked || (t.runMutationObserver || (t.runMutationObserver = !0, t.callbackCount = 1, t.mutationTimeCalculates = [], t.removeTag(document.body)), _(function () {
				var e = performance.now(),
					n = document.body;
				n && t.doTag(n, t.callbackCount++), t.mutationTimeCalculates.push({
					t: e,
					ts: (new Date).getTime()
				})
			}))
		}), this.observer.observe(document, {
			childList: !0,
			subtree: !0,
			attributes: !0,
			attributeFilter: ["style", "href", "src"]
		}), "complete" === document.readyState ? this.calculateFinalScore() : window.addEventListener("load", function () {
			t.calculateFinalScore()
		}, !0)
	}, P.prototype.calculateResourceMap = function () {
		var t = this;
		performance.getEntries().forEach(function (e) {
			t.mp[e.name] = e.responseEnd
		})
	}, P.prototype.doTag = function (t, e) {
		var n = t.tagName;
		if (-1 === S.indexOf(n)) {
			var r = t.children ? t.children.length : 0;
			if (r > 0)
				for (var i = t.children, o = r - 1; o >= 0; o--) null === i[o].getAttribute("lcp_do_tag") && i[o].setAttribute("lcp_do_tag", e), this.doTag(i[o], e)
		}
	}, P.prototype.calculateFinalScore = function () {
		var t = this;
		if (MutationObserver && this.flag)
			if (this.checkCanCalculateLcp()) {
				var e = (new Date).getTime();
				this.observer.disconnect(), this.flag = !1;
				var n = this.deepTraversal(document.body),
					i = {};
				n.dpss.forEach(function (t) {
					i && i.st ? i.st < t.st && (i = t) : i = t
				}), this.calculateResourceMap();
				var o = this.filterTheResultSet(i.els || []),
					a = this.calculateResult(o),
					s = a.rt,
					c = a.rEle,
					u = a.rts;
				this.lcpTiming = s, this.lcpEle = c, this.lcpTimeStamp = u, window.__lcp_required_time__ = (new Date).getTime() - e, r.trigger("LCPTiming")
			} else setTimeout(function () {
				t.calculateFinalScore()
			}, 500)
	}, P.prototype.calculateResult = function (t) {
		var e = this,
			n = -1,
			r = "",
			i = 0;
		return t.forEach(function (t) {
			var o, a, s = -1,
				c = 0,
				u = f(t.node),
				l = +t.node.getAttribute("lcp_do_tag") - 1;
			if ("IMG" === t.node.tagName) s = e.mp[t.node.src], e.mutationTimeCalculates[l] && (c = e.mutationTimeCalculates[l].ts);
			else {
				var p = (o = t.node, a = "background-image", window.getComputedStyle ? window.getComputedStyle(o)[a] : o.currentStyle[a]).match(k);
				if (p && p[1]) {
					var d = p[1]; - 1 === d.indexOf("http") && (d = location.protocol + p[1]), s = e.mp[d], e.mutationTimeCalculates[l] && (c = e.mutationTimeCalculates[l].ts)
				} else e.mutationTimeCalculates[l] && (s = e.mutationTimeCalculates[l].t, c = e.mutationTimeCalculates[l].ts)
			}
			n < s && (n = s, r = u, i = c)
		}), {
			rt: n,
			rEle: r,
			rts: i
		}
	}, P.prototype.filterTheResultSet = function (t) {
		if (t && 1 === t.length) return t;
		var e = 0;
		t.forEach(function (t) {
			e += t.st
		});
		var n = e / t.length;
		return t.filter(function (t) {
			return t.st > n
		})
	}, P.prototype._hackNodeChildren = function (t) {
		if (t.children) return t.children;
		for (var e = t.childNodes, n = [], r = null, i = 0; r = e[i++];) 1 === r.nodeType && n.push(r);
		return n
	}, P.prototype.deepTraversal = function (t) {
		if (t) {
			for (var e = [], n = 0, r = void 0; r = this._hackNodeChildren(t)[n]; n++) {
				var i = this.deepTraversal(r);
				i.st && e.push(i)
			}
			return this.calculateScore(t, e)
		}
		return {}
	}, P.prototype.calculateScore = function (t, e) {
		var n = t.getBoundingClientRect(),
			r = n.width,
			i = n.height,
			o = n.left,
			a = n.top,
			s = 1;
		(E < a || x < o) && (s = 0);
		var c = 0;
		e.forEach(function (t) {
			c += t.st
		});
		var u = r * i * s,
			l = [{
				node: t,
				st: u
			}],
			f = this.calculateAreaPercent(t);
		return (c > u * f || 0 === f) && (u = c, l = [], e.forEach(function (t) {
			l = l.concat(t.els)
		})), {
			dpss: e,
			st: u,
			els: l
		}
	}, P.prototype.checkCanCalculateLcp = function () {
		var t = (new Date).getTime() - this.now,
			e = t > p;
		if (e) {
			var n = this.getLcpTiming().rt <= 0 && this.reWait;
			return this.reWait = !1, this.now = (new Date).getTime(), !n
		}
		return e || this.mutationTimeCalculates && this.mutationTimeCalculates.length && t - this.mutationTimeCalculates[this.mutationTimeCalculates.length - 1].t > 1e3
	}, P.prototype.calculateAreaPercent = function (t) {
		var e = t.getBoundingClientRect(),
			n = e.left,
			r = e.right,
			i = e.top,
			o = e.bottom,
			a = e.width,
			s = e.height,
			c = x,
			u = E,
			l = r - n + (c - 0) - (Math.max(r, c) - Math.min(n, 0));
		if (l <= 0) return 0;
		var f = o - i + (u - 0) - (Math.max(o, u) - Math.min(i, 0));
		return f <= 0 ? 0 : l * f / (a * s)
	};
	var F = new Date,
		N = [];

	function I(t, e) {
		b || (b = e, T = t, C = new Date, J(window.removeEventListener), A())
	}

	function A() {
		T >= 0 && T < C - F && (N.forEach(function (t) {
			t(T, b)
		}), N = [])
	}
	var D = {
			passive: !0,
			capture: !0
		},
		q = "pointerup",
		L = "pointercancel",
		R = window.addEventListener,
		M = window.removeEventListener;

	function j(t) {
		if (t.cancelable) {
			var e = (t.timeStamp > 1e12 ? new Date : performance.now()) - t.timeStamp;
			"pointerdown" == t.type ? function (t, e) {
				function n() {
					I(t, e), i()
				}

				function r() {
					i()
				}

				function i() {
					M(q, n, D), M(L, r, D)
				}
				R(q, n, D), R(L, r, D)
			}(e, t) : I(e, t)
		}
	}

	function J(t) {
		["click", "mousedown", "keydown", "touchstart", "pointerdown"].forEach(function (e) {
			t(e, j, D)
		})
	}

	function B() {
		return new Promise(function (t) {
			r.waitFor(["getFID"], function () {
				var e;
				e = function (e, n) {
					var i = {
						fid: Number(e.toFixed(2)),
						fidEventName: n.type || "UNKNOW"
					};
					for (var o in t(i), i) a(o, i[o]);
					r.trigger("fid")
				}, N.push(e), A()
			})
		})
	}
	J(window.addEventListener);
	var V = String.fromCharCode,
		W = function (t) {
			if (t.length < 2) return (e = t.charCodeAt(0)) < 128 ? t : e < 2048 ? V(192 | e >>> 6) + V(128 | 63 & e) : V(224 | e >>> 12 & 15) + V(128 | e >>> 6 & 63) + V(128 | 63 & e);
			var e = 65536 + 1024 * (t.charCodeAt(0) - 55296) + (t.charCodeAt(1) - 56320);
			return V(240 | e >>> 18 & 7) + V(128 | e >>> 12 & 63) + V(128 | e >>> 6 & 63) + V(128 | 63 & e)
		},
		H = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,
		z = function (t) {
			return window.btoa(function (t) {
				return t.replace(H, W)
			}(t))
		},
		K = new RegExp(["[\xc0-\xdf][\x80-\xbf]", "[\xe0-\xef][\x80-\xbf]{2}", "[\xf0-\xf7][\x80-\xbf]{3}"].join("|"), "g"),
		Q = function (t) {
			switch (t.length) {
				case 4:
					var e = ((7 & t.charCodeAt(0)) << 18 | (63 & t.charCodeAt(1)) << 12 | (63 & t.charCodeAt(2)) << 6 | 63 & t.charCodeAt(3)) - 65536;
					return V(55296 + (e >>> 10)) + V(56320 + (1023 & e));
				case 3:
					return V((15 & t.charCodeAt(0)) << 12 | (63 & t.charCodeAt(1)) << 6 | 63 & t.charCodeAt(2));
				default:
					return V((31 & t.charCodeAt(0)) << 6 | 63 & t.charCodeAt(1))
			}
		},
		U = function (t) {
			return window.atob(t).replace(K, Q)
		},
		X = {
			VERSION: "wps.2.1.9",
			encode: function (t, e) {
				return e ? z(String(t)).replace(/[+\/]/g, function (t) {
					return "+" == t ? "-" : "_"
				}).replace(/=/g, "") : z(String(t))
			},
			decode: function (t) {
				return U(String(t).replace(/[-_]/g, function (t) {
					return "-" == t ? "+" : "/"
				}).replace(/[^A-Za-z0-9\+\/]/g, ""))
			}
		},
		Z = function (t, e) {
			this.encode = t || X.encode.bind(X), this.decode = e || X.decode.bind(X), this.queryKey = "cefQuery"
		};
	Z.prototype.setQueryKey = function (t) {
		this.queryKey = t
	}, Z.prototype.setEncode = function (t) {
		this.encode = t
	}, Z.prototype.setDecode = function (t) {
		this.decode = t
	}, Z.prototype._exceptionHandler = function (t, e, n) {
		return t.message = "Client Api Exec Catch Error: \n          method: " + e + ", error: " + t.message, n && (t.message += ", response: " + JSON.stringify(n), t.response = n), t
	}, Z.prototype.execJson = function (t, e) {
		try {
			return t = this.encode(JSON.stringify(t)), window[this.queryKey]({
				request: 'fromJSAsynCallBase64("' + t + '")',
				persistent: !1
			})
		} catch (n) {
			e && e(this._exceptionHandler(n, t.method))
		}
	}, Z.prototype.exec = function (t, e, n) {
		var r = this,
			i = {
				method: t
			};
		if ("function" == typeof e ? n = e : void 0 !== e && (i.params = e), "function" == typeof n) {
			var o = t + "_async_callback_" + setTimeout(function () {}, 0);
			window[o] = function (e) {
				delete window[o];
				var i = null;
				try {
					if (e = r.decode(e), "ok" !== (e = JSON.parse(e)).callstatus) throw new Error("execute api failed")
				} catch (n) {
					i = r._exceptionHandler(n, t, e)
				}
				n(i, e)
			}, i.callback = 'window["' + o + '"]'
		}
		return this.execJson(i, n)
	}, Z.prototype.execPromise = function (t, e) {
		var n = this;
		return new Promise(function (r, i) {
			n.exec(t, e, function (t, e) {
				t ? i(t) : r(e)
			})
		})
	};
	var G, Y, $ = new Z,
		tt = ("qt" == (G = window.qwebkit && window.qwebkit.jsAsynCall || !window.cefQuery && window.external && window.external.jsAsynCall, Y = window.cefQuery && "function wpsQuery() { [native code] }" === window.cefQuery.toString(), G ? "qt" : Y ? "cef" : "browser") && ($.execJson = function (t, e) {
			try {
				if (t = this.encode(JSON.stringify(t)), window.external && window.external.jsAsynCall) return window.external.jsAsynCall(t);
				if (window.qwebkit && window.qwebkit.fromJSAsynCallBase64) return window.qwebkit.fromJSAsynCallBase64(t);
				var n = "\u5f53\u524d\u7684Qt\u5bb9\u5668\u4e0d\u652f\u6301\u4f7f\u7528`window.external.jsAsynCall`\u6216`window.qwebkit.fromJSAsynCallBase64`\u65b9\u6cd5\u8c03\u7528\u5ba2\u6237\u7aefAPI";
				throw console.warn(n), n
			} catch (n) {
				e && e(this._exceptionHandler(n, t.method))
			}
		}), $),
		et = function (t) {
			return new Promise(function (e) {
				t ? tt.execPromise("common.plugin.getPluginInfo", {
					pluginName: t
				}).then(function (t) {
					e(t)
				}).catch(function (t) {
					console.warn(t), e({
						callstatus: "error",
						err: t
					})
				}) : e({})
			})
		},
		nt = function (t) {
			var e = [et(t), new Promise(function (t) {
				tt.execPromise("common.wpsoffice.getAppInfo").then(function (e) {
					t(e)
				}).catch(function (e) {
					console.warn(e), t({
						callstatus: "error",
						err: e
					})
				})
			})];
			return Promise.all(e).then(function (t) {
				var e = t[0],
					n = t[1];
				return "ok" === e.callstatus ? {
					pluginInfo: e,
					appInfo: n
				} : {
					pluginInfo: {},
					appInfo: n
				}
			})
		},
		rt = function () {
			return new Promise(function (t) {
				tt.execPromise("common.profile.getBrowserTraceInfo").then(function (e) {
					var n = e.callstatus,
						r = e.result;
					if ("ok" === n && r && "{}" !== JSON.stringify(r)) return t(r);
					throw Error({
						callstatus: "error",
						msg: "common.profile.getBrowserTraceInfo\u8c03\u7528\u5f02\u5e38"
					})
				}).catch(function (e) {
					console.warn(e), tt.execPromise("common.profile.getProfileInfo").then(function (e) {
						var n = e,
							r = n.callstatus,
							i = n.result;
						if ("ok" !== r || !i) throw Error({
							callstatus: "error",
							msg: "common.profile.getProfileInfo\u8c03\u7528\u5931\u8d25"
						});
						var o = i.c_cb_ts;
						(JSON.parse(sessionStorage.getItem("clientVitals")) || {}).c_cb_ts === o ? t({
							c_cb_ts: i.c_cb_ts,
							c_cbf_ts: i.c_cbf_ts,
							c_l_ts: i.c_l_ts,
							c_lf_ts: i.c_lf_ts,
							c_cb: 0,
							c_l: 0
						}) : (t(i), sessionStorage.setItem("clientVitals", JSON.stringify(n.result)))
					}).catch(function (e) {
						return console.warn(e), t({
							callstatus: "error",
							err: e
						})
					})
				})
			})
		},
		it = function (t) {
			var e = t.appInfo,
				n = t.pluginInfo,
				r = e.version || "",
				i = e.channel || "",
				o = n.version || "",
				a = window.innerWidth + "_" + window.innerHeight;
			return delete t.appInfo, delete t.pluginInfo, t.version = r, t.dist = i, t.winSize = a, t.pluginVersion = o, t.isLocal = /^(file?:\/\/)/.test(location.href), t
		},
		ot = function (t) {
			return new Promise(function (e) {
				r.waitFor(["load", "DOMContentLoaded"], function () {
					e(!0)
				});
				var n = function () {};
				window.addEventListener("load", function () {
					var e = function (t) {
						t && (new Promise(function (t) {
							var e = {};
							if (window.performance && window.performance.getEntries) {
								var n = window.performance.getEntries(),
									i = n.filter(function (t) {
										return "script" === t.initiatorType || "js" === v(t.name)
									}),
									o = n.filter(function (t) {
										return "link" === t.initiatorType || "css" === v(t.name)
									}),
									s = o.length ? Math.max.apply(Math, o.map(function (t) {
										return t.responseEnd
									})) - Math.min.apply(Math, o.map(function (t) {
										return t.startTime
									})) : 0,
									c = i.length ? Math.max.apply(Math, i.map(function (t) {
										return t.responseEnd
									})) - Math.min.apply(Math, i.map(function (t) {
										return t.startTime
									})) : 0;
								e = {
									js: Number(c.toFixed(2)),
									css: Number(s.toFixed(2))
								}
							} else e = {
								js: 0,
								css: 0,
								ttfb: 0
							};
							r.waitFor(["getNecessaryResourceTiming"], function () {
								t(e), a("jsCss", e), r.trigger("js_css_timing")
							})
						}), new Promise(function (t) {
							if (!window.performance) return {};
							var e = window.performance.timing,
								n = {};
							n.domReadyTime = e.domContentLoadedEventEnd - e.fetchStart, n.parseDomTime = e.domComplete - e.domInteractive, n.initDomTreeTime = e.domInteractive - e.responseEnd, n.readyStart = e.fetchStart - e.navigationStart, n.redirectTime = e.redirectEnd - e.redirectStart, n.appcacheTime = e.domainLookupStart - e.fetchStart, n.lookupDomainTime = e.domainLookupEnd - e.domainLookupStart, n.connectTime = e.connectEnd - e.connectStart, n.requestTime = e.responseEnd - e.requestStart, n.requestDocumentTime = e.responseStart - e.requestStart, n.responseDocumentTime = e.responseEnd - e.responseStart, n.TTFB = e.responseStart - e.navigationStart, r.waitFor(["getOtherResourceTiming"], function () {
								t(n), a("otherTime", n), r.trigger("other_timing")
							})
						})), r.trigger("load")
					};
					t ? t().then(e).catch(n) : e(!0)
				}), window.addEventListener("DOMContentLoaded", function () {
					var e = function (t) {
						t && (g().then(function (t) {
							return t < 0 ? r.waitFor(["getTTI"], function () {
								r.trigger("tti"), a("tti", Number(t.toFixed(2)))
							}) : (r.trigger("tti"), a("tti", Number(t.toFixed(2)))), Number(t.toFixed(2))
						}), new Promise(function (t) {
							var e = 0;
							try {
								var n = new PerformanceObserver(function (t) {
									var n = (t.getEntries() || []).filter(function (t) {
										return "first-contentful-paint" === t.name
									});
									if (n.length) {
										var r = n[0];
										e = r.startTime
									} else e = 0
								});
								r.waitFor(["getFCP"], function () {
									l(p).then(function (i) {
										0 === e && (performance.getEntries() || []).forEach(function (t) {
											"first-contentful-paint" === t.name && (e = t.startTime)
										}), t(Number(e.toFixed(2))), a("fcp", Number(e.toFixed(2))), r.trigger("fcp"), n.disconnect()
									})
								}), n.observe({
									entryTypes: ["paint"]
								})
							} catch (n) {
								r.waitFor(["getFCP"], function () {
									l(p).then(function (n) {
										if (performance && performance.timing && "number" == typeof performance.timing.msFirstPaint) {
											var i = performance.timing,
												o = i.msFirstPaint,
												s = i.navigationStart;
											e = o - s
										}
										t(Number(e.toFixed(2))), a("fcp", Number(e.toFixed(2))), r.trigger("fcp")
									})
								})
							}
						}), O(o("reWait"), o("fullScreen")), B()), r.trigger("DOMContentLoaded")
					};
					t ? t().then(e).catch(n) : e(!0)
				})
			})
		},
		at = function () {
			r.trigger("getTTI");
			return new Promise(function (t) {
				r.waitFor(["tti"], function () {
					t({
						tti: o("tti")
					})
				})
			})
		},
		st = function () {
			r.trigger("getFCP");
			return new Promise(function (t) {
				r.waitFor(["fcp"], function () {
					t({
						fcp: o("fcp")
					})
				})
			})
		},
		ct = function () {
			r.trigger("getLCP");
			return new Promise(function (t) {
				r.waitFor(["lcp"], function () {
					t({
						lcp: o("lcp").value,
						lcpEle: o("lcp").lcpEle,
						lcpTimeStamp: o("lcp").lcpTimeStamp
					})
				})
			})
		},
		ut = function () {
			r.trigger("getNecessaryResourceTiming");
			return new Promise(function (t) {
				r.waitFor(["js_css_timing"], function () {
					t(o("jsCss"))
				})
			})
		},
		lt = function () {
			r.trigger("getOtherResourceTiming");
			return new Promise(function (t) {
				r.waitFor(["other_timing"], function () {
					t(o("otherTime"))
				})
			})
		},
		ft = function (t) {
			return void 0 === t && (t = ""),
				function (t) {
					return new Promise(function (e) {
						Promise.all([nt(t), rt()]).then(function (t) {
							var n = t[0],
								r = t[1];
							if ("error" !== r.callstatus)
								for (var i in r) Object.prototype.hasOwnProperty.call(r, i) && (n[i] = r[i]);
							e(it(n))
						})
					})
				}(t)
		},
		pt = {
			elementtiming: "",
			autoSend: !0,
			bussiness: "",
			version: "1.0.0",
			appVersion: "1.0.0",
			pluginName: "",
			extendedFields: []
		},
		dt = {
			fcp: "fcp",
			lcp: "lcp",
			tti: "tti",
			fid: "fid",
			js: "js",
			css: "css",
			domReadyTime: "dom",
			parseDomTime: "parse_dom",
			redirectTime: "redirect",
			appcacheTime: "appcache",
			lookupDomainTime: "lookup_domain",
			connectTime: "connect",
			requestTime: "request",
			requestDocumentTime: "request_document",
			responseDocumentTime: "response_document",
			TTFB: "ttfb",
			initDomTreeTime: "init_dom_tree",
			readyStart: "ready_start",
			usedJSHeapSize: "used_js_heap_size",
			jsHeapSizeLimit: "js_heap_size_limit",
			totalJSHeapSize: "total_js_heap_size",
			c_cb: "c_cb",
			c_l: "c_l",
			c_l_rd: "c_l_rd",
			c_cb_ld: "c_cb_ld",
			allTime: "all_time",
			isLocal: "is_local",
			isCache: "is_cache",
			winSize: "win_size",
			version: "wps_ver",
			pluginVersion: "plg_ver",
			ua: "ua",
			pid: "pid",
			browserid: "browserid",
			appid: "appid",
			sessionid: "sessionid",
			dist: "dist",
			extra_attr_1: "extra_attr_1",
			extra_attr_2: "extra_attr_2",
			extra_attr_3: "extra_attr_3",
			lcpEle: "lcp_e",
			createBrowserStartToLCP: "c_lcp"
		},
		ht = [1, 100, 1e3],
		mt = function () {
			this.uuid = ""
		};
	mt.prototype.generate = function () {
		var t = this._getRandomInt,
			e = this._hexAligner;
		this.uuid = e(t(32), 8) + e(t(16), 4) + e(16384 | t(12), 4) + e(32768 | t(14), 4) + e(t(48), 12)
	}, mt.prototype.getUuid = function () {
		if (this.uuid) return "" + this.uuid
	}, mt.prototype.resetUuid = function () {
		this.uuid = ""
	}, mt.prototype._getRandomInt = function (t) {
		if (t < 0 || t > 53) return NaN;
		var e = 0 | 1073741824 * Math.random();
		return t > 30 ? e + 1073741824 * (0 | Math.random() * (1 << t - 30)) : e >>> 30 - t
	}, mt.prototype._hexAligner = function (t, e) {
		for (var n = t.toString(16), r = e - n.length, i = "0"; r > 0; r >>>= 1, i += i) 1 & r && (n = i + n);
		return n
	};
	var gt = new mt,
		vt = String.fromCharCode,
		wt = function (t) {
			if (t.length < 2) return (e = t.charCodeAt(0)) < 128 ? t : e < 2048 ? vt(192 | e >>> 6) + vt(128 | 63 & e) : vt(224 | e >>> 12 & 15) + vt(128 | e >>> 6 & 63) + vt(128 | 63 & e);
			var e = 65536 + 1024 * (t.charCodeAt(0) - 55296) + (t.charCodeAt(1) - 56320);
			return vt(240 | e >>> 18 & 7) + vt(128 | e >>> 12 & 63) + vt(128 | e >>> 6 & 63) + vt(128 | 63 & e)
		},
		_t = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,
		yt = function (t) {
			return window.btoa(function (t) {
				return t.replace(_t, wt)
			}(t))
		},
		bt = new RegExp(["[\xc0-\xdf][\x80-\xbf]", "[\xe0-\xef][\x80-\xbf]{2}", "[\xf0-\xf7][\x80-\xbf]{3}"].join("|"), "g"),
		Tt = function (t) {
			switch (t.length) {
				case 4:
					var e = ((7 & t.charCodeAt(0)) << 18 | (63 & t.charCodeAt(1)) << 12 | (63 & t.charCodeAt(2)) << 6 | 63 & t.charCodeAt(3)) - 65536;
					return vt(55296 + (e >>> 10)) + vt(56320 + (1023 & e));
				case 3:
					return vt((15 & t.charCodeAt(0)) << 12 | (63 & t.charCodeAt(1)) << 6 | 63 & t.charCodeAt(2));
				default:
					return vt((31 & t.charCodeAt(0)) << 6 | 63 & t.charCodeAt(1))
			}
		},
		Ct = function (t) {
			return window.atob(t).replace(bt, Tt)
		},
		St = {
			VERSION: "wps.2.1.9",
			encode: function (t, e) {
				return e ? yt(String(t)).replace(/[+\/]/g, function (t) {
					return "+" == t ? "-" : "_"
				}).replace(/=/g, "") : yt(String(t))
			},
			decode: function (t) {
				return Ct(String(t).replace(/[-_]/g, function (t) {
					return "-" == t ? "+" : "/"
				}).replace(/[^A-Za-z0-9\+\/]/g, ""))
			}
		},
		kt = function (t, e) {
			this.encode = t || St.encode.bind(St), this.decode = e || St.decode.bind(St), this.queryKey = "cefQuery"
		};
	kt.prototype.setQueryKey = function (t) {
		this.queryKey = t
	}, kt.prototype.setEncode = function (t) {
		this.encode = t
	}, kt.prototype.setDecode = function (t) {
		this.decode = t
	}, kt.prototype._exceptionHandler = function (t, e, n) {
		return t.message = "Client Api Exec Catch Error: \n          method: " + e + ", error: " + t.message, n && (t.message += ", response: " + JSON.stringify(n), t.response = n), t
	}, kt.prototype.execJson = function (t, e) {
		try {
			return t = this.encode(JSON.stringify(t)), window[this.queryKey]({
				request: 'fromJSAsynCallBase64("' + t + '")',
				persistent: !1
			})
		} catch (n) {
			e && e(this._exceptionHandler(n, t.method))
		}
	}, kt.prototype.exec = function (t, e, n) {
		var r = this,
			i = {
				method: t
			};
		if ("function" == typeof e ? n = e : void 0 !== e && (i.params = e), "function" == typeof n) {
			var o = t + "_async_callback_" + setTimeout(function () {}, 0);
			window[o] = function (e) {
				delete window[o];
				var i = null;
				try {
					if (e = r.decode(e), "ok" !== (e = JSON.parse(e)).callstatus) throw new Error("execute api failed")
				} catch (n) {
					i = r._exceptionHandler(n, t, e)
				}
				n(i, e)
			}, i.callback = 'window["' + o + '"]'
		}
		return this.execJson(i, n)
	}, kt.prototype.execPromise = function (t, e) {
		var n = this;
		return new Promise(function (r, i) {
			n.exec(t, e, function (t, e) {
				t ? i(t) : r(e)
			})
		})
	};
	var xt = new kt,
		Et = function () {
			this.apiPrefix = "common.report", this.apis = {
				dataWarehouseSend: this.apiPrefix + ".dataWarehouseSend"
			}
		};
	Et.prototype.dataWarehouseSend = function (t, e, n) {
		var r, i = [];
		if (r = e, "[object object]" === Object.prototype.toString.call(r).toLowerCase())
			for (var o in e) e.hasOwnProperty(o) && i.push({
				attribute: o,
				value: e[o]
			});
		xt.exec(this.apis.dataWarehouseSend, {
			eventName: t,
			content: i,
			prob: n
		})
	};
	var Pt = new Et,
		Ot = function (t) {
			var e = this;
			this.config = Object.assign({}, pt, t), this.probability = void 0 !== this.config.probability ? this.config.probability : 1e3, this._checkConfig(this.config), this._apis = {
					reportName: "front_perf"
				}, this._reportData = {
					bussiness: this.config.bussiness || ""
				}, this._handleExtraAttr(), this.fid = 0, this.mode = this.config.mode || "production", this.reson = "",
				function (t) {
					for (var e in void 0 === t && (t = {}), t) a(e, t[e])
				}({
					reWait: !!this.config.re_wait
				}), this._initData().then(function (t) {
					t ? (e._senData(), "function" == typeof e.config.complete && e.config.complete(e._reportData)) : "function" == typeof e.config.fail && e.config.fail({
						msg: e.reson
					})
				})
		};
	return Ot.prototype._log = function (t, e) {
		if ("development" === this.mode) {
			console.log(":::::::[WebVitalsWithClient]::::::" + e);
			var n = [];
			for (var r in t) Object.prototype.hasOwnProperty.call(t, r) && n.push({
				text: r,
				value: t[r]
			});
			for (var i = 0; i < n.length; i++) {
				var o = n[i];
				console.log("%c" + o.text + "%c" + o.value, "background:#35495e ; padding: 2px; border-radius: 3px 0 0 3px;  color: #fff", "background:#41b883 ; padding: 2px; border-radius: 0 3px 3px 0;  color: #fff")
			}
		}
	}, Ot.prototype._handleExtraAttr = function () {
		for (var t in this.config) Object.prototype.hasOwnProperty.call(this.config, t) && ["extra_attr_1", "extra_attr_2", "extra_attr_3"].indexOf(t) > -1 && this.config[t] && (this._reportData[t] = this.config[t].substr(0, 64))
	}, Ot.prototype._checkConfig = function (t) {
		var e = ["bussiness"],
			n = 0;
		for (var r in t) e.indexOf(r) > -1 && t[r] && n++;
		if (!this._checkIsProbabilityConfigured(this.probability)) throw new Error("[WebVitalsWithClient]:\u5f53\u524d\u6982\u7387\u4ec5\u652f\u6301\u30101\uff0c 100\uff0c 1000\u3011");
		if (n === e.length) return !0;
		throw new Error("\u8bf7\u68c0\u67e5sdk\u5fc5\u8981\u914d\u7f6e\u9879\u662f\u5426\u51c6\u5907\u597d")
	}, Ot.prototype._checkIsProbabilityConfigured = function () {
		if (ht.indexOf(this.probability) > -1) return !0
	}, Ot.prototype._initData = function () {
		var t = this;
		return new Promise(function (e) {
			ot().then(function (n) {
				gt.generate(), t._getFid(), t._getVitals().then(function (n) {
					t._reportData = Object.assign(t._reportData, n, {
						fid: t.fid,
						uid4report: gt.getUuid()
					}), e(!0)
				})
			}).catch(function (n) {
				t.reson = n.toString(), e(!1)
			})
		})
	}, Ot.prototype._getCacheValue = function () {
		if ("production" === this.env) return 2;
		if (!window.performance) return 2;
		var t = performance.getEntriesByType("resource"),
			e = t.filter(function (t) {
				return 0 === t.transferSize
			});
		return e.length ? e.length !== t.length ? 2 : e.length === t.length ? 1 : void 0 : 0
	}, Ot.prototype._getUA = function () {
		var t = navigator.userAgent.match(/Chrome\/(\S{0,2})\./);
		return t ? t[1] : ""
	}, Ot.prototype._getReportDataByNormalize = function (t, e) {
		for (var n = {}, r = Object.keys(t), i = 0, o = r.length; i < o; i++) {
			var a = r[i];
			Object.prototype.hasOwnProperty.call(e, a) && (n[e[a]] = t[a])
		}
		return n
	}, Ot.prototype._senData = function () {
		Pt.dataWarehouseSend(this._apis.reportName, this._reportData, this.probability)
	}, Ot.prototype.sendData = function () {}, Ot.prototype._getDesktopInfo = function (t, e) {
		var n = this;
		return ft(this.config.pluginName).then(function (r) {
			return r.allTime = t + r.c_cb, r.createBrowserStartToLCP = e - r.c_cb_ts, r.isCache = n._getCacheValue(), r.ua = n._getUA(), r = n._getReportDataByNormalize(r, dt), n._log(r, "\u5ba2\u6237\u7aef\u76f8\u5173\u6027\u80fd\u6307\u6807\u5df2\u51c6\u5907\u5c31\u7eea"), r
		})
	}, Ot.prototype._getMetricByTarget = function () {
		var t = this,
			e = [at(), st(), ct(), ut(), lt()];
		return Promise.all(e).then(function (e) {
			var n = e.reduce(function (t, e) {
				return Object.assign(t, e)
			}, {});
			return t._log(n, "\u524d\u7aef\u76f8\u5173\u6027\u80fd\u6307\u6807\u5df2\u51c6\u5907\u5c31\u7eea"), Object.assign(n, {})
		})
	}, Ot.prototype._getFid = function () {
		var t = this;
		(function () {
			r.trigger("getFID");
			return new Promise(function (t) {
				r.waitFor(["fid"], function () {
					t(o("fid"))
				})
			})
		})().then(function (e) {
			t.fid = e
		})
	}, Ot.prototype._getVitals = function () {
		var t = this,
			e = {};
		return this._getMetricByTarget().then(function (n) {
			var r = (e = t._getReportDataByNormalize(Object.assign({}, n), dt)).tti;
			return t._getDesktopInfo(r, n.lcpTimeStamp)
		}).then(function (t) {
			return Object.assign(e, t)
		})
	}, Ot
}, "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define(e) : t.WebVitalsWithClient = e();