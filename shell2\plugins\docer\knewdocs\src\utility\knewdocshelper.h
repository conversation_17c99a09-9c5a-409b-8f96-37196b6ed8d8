﻿#ifndef __KNEWFILE_UTILITY_NEWDOCSHELPER_H__
#define __KNEWFILE_UTILITY_NEWDOCSHELPER_H__

class KPromeNewDocsPage;

namespace KNewDocsHelper
{	
	QString getTabbarConfigFilePath();
	QString getPaySourceComponent(KPromeNewDocsPage* page);
	QString getPayConfig(KPromeNewDocsPage* page);
	QString getPayCSource(KPromeNewDocsPage* page);
	QString getPaySourceUserType();
	QString getPaySourceEntrance(KPromeNewDocsPage* page);
	QString getPageEntryId(KPromeNewDocsPage* page);
	QString getMemberIdentity();
	void sendDocerPayAction(
		const QString& entryId,
		const QString& payKey,
		const QString& workbord
	);
	KPromeNewDocsPage* getCurrentNewDocPage();
	QString getPageNameInfo(int tabType);
	QString getPageName(int tabType);
	QString getPageName(KPromeNewDocsPage* page);
	QMap<QString, QString> getNewdocsTimeStamp(KPromeNewDocsPage* page);
	
	QString getComponentName(int tabType);
	QVariantMap getAppInfo();
#ifdef Q_OS_LINUX
	QString getFontInfo();
#endif
	bool showSideBar();
	bool canAddSupportInterface(const QString& pluginName);
	QStringList querySupportInterface();
	QVariantMap getUserLoginInfo();
	bool isNewCloudDocSwitchEnabled();
	bool isNewCloudDocSwitchOpened();
	bool isNewCloudDocSwitchChangeable();
	void setNewCloudDocSwitchStatus(bool bOpen);
	bool isThisDeviceFirstOpen();
	void newCloudDocSwitchRegisterNotifyObj(QObject* obj, const QString& slotName);
	void newCloudDocSwitchUnregisterNotifyObj(QObject* obj);
	bool hasPrivileges(const QStringList& privilegeIds);
	QString componentErrorPage();
	bool isSupportDefaultPage(int tabType);
	QString getCompanyUserCategory();
	void getCurrentPageEntryControlStatus(int pageType, QJsonObject& jsonObj);
	QString getNonAdaptedWebSkinName();
	bool isSupportCtrlAltAKey();
};

#endif//__KNEWFILE_UTILITY_NEWDOCSHELPER_H__
