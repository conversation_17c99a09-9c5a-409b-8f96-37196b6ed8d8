﻿#ifndef __KPROMETHEUS_PLUGINLOADDIALOG_H__
#define __KPROMETHEUS_PLUGINLOADDIALOG_H__

#include <QDialog>
#include <QWidget>
#include <QProgressBar>
#include <public_header/kliteui/uicontrol/klitebutton.h>
#include "ksolite/kappmgr/kappobject.h"
#include "ksolite/kappmgr/kappdef.h"
class KProxyPugin;
class KPromePluginLoadWidget;
namespace PluginWidgetColorHelper
{
	QString getNameColorFromTheme(const QString& className, const QString& attributeName, const QColor& defaultColor);
	QString toHdpiStyle(const QString& style);
	QString getOkPushButtonCss();
	QString getCancelQPushButtonCss();
	QString getQlabelStyleCss(int fontSize, const QString& colorName, int lineHeight = 22, const QString& fontFamily = "Microsoft YaHei");
};

class KPluginLoadingProgressBar;

class KPluginLoadFailedWidget : public QWidget
{
	Q_OBJECT
public:
	explicit KPluginLoadFailedWidget(QWidget* parent, KAppObj* appObj);
	virtual ~KPluginLoadFailedWidget();
	void setContentText(const QString& content);
	void setShowIsWebBtn(bool isShow = true);
protected:
	void initUI();
	void initConnect();
private:
	void adjustPos();
	void mousePressEvent(QMouseEvent* e) override;
	void mouseReleaseEvent(QMouseEvent* e) override;
	void mouseMoveEvent(QMouseEvent* e) override;
signals:
	void openUrl();

protected slots:
	void onCloseBtnClicked();
	void onBottomBtnClicked();

protected:
	QLabel* m_titleLbl;
	QLabel* m_contentLbl;
	QPushButton* m_oKBtn;
	QPushButton* m_cancelBtn;
	QPushButton* m_closeBtn;
	bool m_bDraging;
	QPoint m_dragStartPos;
	int m_posIndex;
	QVector<bool> s_vecWindowPosMgr;
	QPointer<KAppObj> m_appObj;
};


class KPluginLoadingProgressBar : public QProgressBar
{
	Q_OBJECT
public:
	explicit KPluginLoadingProgressBar(QWidget* parent);
	~KPluginLoadingProgressBar();

	void setbgColor(const QColor& color);
	void setChunkColor(const QColor& color);
	void setRadius(int x, int y);

protected:
	void paintEvent(QPaintEvent* e) override;

protected:
	QColor m_bgColor;
	QColor m_chunkColor;
	int m_xRadius;
	int m_yRadius;
};

class KPluginLoadWidget : public QWidget
{
	Q_OBJECT
public:
	explicit KPluginLoadWidget(QWidget* parent);
	virtual ~KPluginLoadWidget();

	void setTitleText(const QString& title);
	QString contentText() const;
	void setContentText(const QString& content);
	void setBtnText(const QString& btnText);
	void setProgressValue(double v);

protected:
	void initUI();
	void updateToFailedMode();
private:
	void initStyleSheet();
protected:
	QVBoxLayout* m_mainLayout;
	QHBoxLayout* m_titleLayout;
	QLabel* m_titleLbl;
	QLabel* m_contentLbl;
	QPushButton* m_closeBtn;
	QPushButton* m_bottomBtn;

	QWidget* m_progressWidget;
	KPluginLoadingProgressBar* m_progressBar;
	bool m_needShadowBorder;
};


class KPluginLoadWindow : public KPluginLoadWidget
{
	Q_OBJECT
public:
	explicit KPluginLoadWindow(QWidget* parent, KAppObj* appObj, const QString& src);
	virtual ~KPluginLoadWindow();
	virtual void init();
	void setLoadTitleText(const QString& titleTr);
	void setAutoShowFailedMode(bool bSwitch);

	// 标记用户是否手动关闭窗口, 外界判断是否继续调起应用
	bool isCloseBtnClicked() const;

	void showLoadFailed();

signals:
	void cancelBtnClicked();
	void backwardBtnClicked();
signals:
	void loadSuccess();
	void loadFailed();
	void loadProgress(double progress);
	void loadCanceled();
	void showPopupFailedWindow();
	void loadProgressUpdate(double progress);

protected:
	virtual void connectSignals();
	void adjustPos();
	void mousePressEvent(QMouseEvent* e) override;
	void mouseReleaseEvent(QMouseEvent* e) override;
	void mouseMoveEvent(QMouseEvent* e) override;
	void showEvent(QShowEvent* e) override;

protected slots:
	void onProgressUpdate(double progress);
	void onLoadSuccess();
	void onLoadFailed();
	void onLoadCanceled();
	void onCloseBtnClicked();
	void onBottomBtnClicked();

protected:
	QPointer<KAppObj> m_appObj;
	QString m_src;
	bool m_bLoadFailedMode;	// 标记当前窗口是否是在加载失败的状态, 以便在重入的时候进行判断
	bool m_bLoadingCloseBtnClick;
	bool m_bAutoShowFailedMode;

	bool m_bDraging;
	QPoint m_dragStartPos;
	int m_posIndex;
	QVector<bool> s_vecWindowPosMgr;
};
#endif //__KPROMETHEUS_PLUGINLOADDIALOG_H__
