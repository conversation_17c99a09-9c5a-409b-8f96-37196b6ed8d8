webpackJsonp([4],{"R+Qh":function(t,a,e){"use strict";var i={data:function(){return{}},props:{item:{type:Object,default:function(){return{}}},index:Number},methods:{openFile:function(){void 0===this.item.is_exit?this.$emit("openFile",this.item):this.$emit("openFile",this.item.filePath,this.item.id,this.item.fileType)}},computed:{show:function(){return void 0!==this.item.is_exit&&!this.item.is_exit}}},s={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"list-item",on:{click:function(a){t.openFile()}}},[e("span",{staticClass:"icon icon-document"}),t._v(" "),e("div",{staticClass:"list-info"},[t.show?e("h5",{class:{"no-name":t.show},attrs:{title:t.item.name}},[t._v(t._s(t.item.name))]):t._e(),t._v(" "),t.show?t._e():e("h5",{class:{"no-h5":!t.item.author&&!t.item.publish_time},attrs:{title:t.item.name}},[t._v(t._s(t.item.name))]),t._v(" "),e("p",[t.item.author?e("span",{staticClass:"name"},[t._v(t._s(t.item.author))]):t._e(),t.item.publish_time?e("span",{staticClass:"time"},[t._v(t._s(t._f("replaceDate")(t.item.publish_time)))]):t._e()])])])},staticRenderFns:[]},o={data:function(){return{}},props:["limit","data","csource","position","servicetype"],computed:{limitCount:function(){return this.limit&&this.data.length&&this.data.length%this.limit!=0?this.limit-this.data.length%this.limit:0}},methods:{openFile:function(t,a,e){void 0==a?this.$emit("openFile",t):this.$emit("openFile",t,a,e)}},components:{Item:e("VU/8")(i,s,!1,null,null,null).exports}},n={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("ul",{staticClass:"m-txtlist"},[t._l(t.data,function(a,i){return e("li",{key:i,staticClass:"m-txtlist-item"},[e("Item",{attrs:{item:a,index:i},on:{openFile:t.openFile}})],1)}),t._v(" "),t._l(t.limitCount,function(t){return[e("li",{staticClass:"m-txtlist-item"})]})],2)},staticRenderFns:[]},r=e("VU/8")(o,n,!1,null,null,null);a.a=r.exports},Y4cK:function(t,a,e){"use strict";Object.defineProperty(a,"__esModule",{value:!0});var i=e("Dd8w"),s=e.n(i),o=e("NYxO"),n=e("n1nN"),r=e("mMDT"),l=e("G1qf"),c=e("gEr2"),u=e("cMGX"),h=e("R+Qh"),p=e("UNaj"),g=e("iWRZ"),d=e("wYMm"),m=e("s0MJ"),f=e("w7XY"),v=e("v8ob"),_=e("59SO"),T={mixins:[p.a,g.a],data:function(){return{word:"",statusCode:"",limit:0,query:{},currentTag:"",totalPage:0,page:1,localTags:[],tagsData:[],totalData:[],privateTags:[],data:[],filter:{},isCustomTpl:!0}},beforeRouteEnter:function(t,a,e){e(function(a){_.a.sendLocal("show"),v.a.$on(d.g.resizeWindow,a.ajustTplLayout),a.initData(t)})},beforeRouteUpdate:function(t,a,e){this.initData(t),e()},beforeRouteLeave:function(t,a,e){v.a.$off(d.g.resizeWindow,this.ajustTplLayout),e()},methods:s()({},Object(o.b)(["setPreviewShow"]),{initQuery:function(t){t=t||{},this.word=t.word||"",this.query=t},initData:function(t){var a=this;this.filter={},this.$refs.nColorFilter&&this.$refs.nColorFilter.resetColor(),this.$refs.nOrderFilter&&this.$refs.nOrderFilter.resetOrderBy(),this.ajustTplLayout(),this.initQuery(t.query),this.$nextTick(function(){a.page=1,a.getData()})},clickTag:function(t,a){this.page=1,this.currentTag=t,_.a.sendLocal("local_tag_click"),void 0===t.value?(""===this.word.replace(/\s/g,"")&&(this.TagsData[a].fileList?this.totalData=this.TagsData[a].fileList:this.totalData=[]),this.data=this.totalData.slice(0,this.pageSize),this.totalPage=Math.ceil((this.totalData.length||0)/this.pageSize)):this.setPriData(this.page,t.value)},gotoPage:function(t){t>0&&t<=this.totalPage&&(this.page!=t||1==t)&&(this.page=t,void 0===this.currentTag.value?(this.data=this.totalData.slice((t-1)*this.pageSize,t*this.pageSize),this.totalPage=Math.ceil((this.totalData.length||0)/this.pageSize)):""===this.word.replace(/\s/g,"")?this.setPriData(this.page,this.currentTag.value):this.searchPriData(this.page,this.currentTag.value))},retry:function(){this.getData()},getData:function(){var t=this;this.word=this.word.replace(/\s/g,""),this.data=[],this.totalPage=0,this.statusCode=d.k.loading,f.a.IsSupportIntranetTemplate().then(function(a){a?t.getPrivateTpl(t.word):t.getLocalTpl(t.word)})},getLocalTpl:function(t){var a=this;t?(this.localTags=[],f.a.localTemplateSearch({keyWord:t,appName:d.b.name}).then(function(e){0!==e.count?(a.currentTag={tagName:t},a.totalData=e.fileList,a.totalPage=Math.ceil((e.count||0)/a.pageSize),a.statusCode="",_.a.sendLocal("local_result_successed"),a.clickTag(a.currentTag,0)):(a.totalData=[],a.totalPage=0,a.statusCode=d.k.empty,_.a.sendLocal("local_result_empty"))}).catch(function(){a.totalData=[],a.totalPage=0,a.statusCode=d.k.error})):f.a.getLocalTemplateFileInfo({appName:d.b.name}).then(function(t){a.localTags=[],a.TagsData=t,t.forEach(function(t){var e={};e.tagName=t.tagName,a.localTags.push(e)}),a.currentTag=a.localTags[0],a.statusCode=t.length>=0?"":d.k.empty,a.clickTag(a.currentTag,0)}).catch(function(){a.totalData=[],a.totalPage=0,a.statusCode=d.k.error})},getPrivateTpl:function(t){var a=this;if(t)return this.privateTags=[],this.currentTag={tagName:t,value:t},void this.searchPriData(1,t);n.a.get("pritag").getPrivateTags({data:{mb_app:d.b.mark},privateUrl:{url:d.m.privateUrl}}).then(function(t){a.privateTags=[],"ok"===t.result&&(t.data.forEach(function(t){t[0].item.forEach(function(t){var e={};if(""===t.v||""===t.k)return!1;e.tagName=t.v,e.value=t.k,a.privateTags.push(e)})}),a.currentTag=a.privateTags[0],a.setPriData(1,a.currentTag.value))}).catch(function(t){})},setPriData:function(t,a){var e=this;n.a.get("pritpl").getPriTplByTags({data:{term:a,sort_by:"mix",moban_type:"",sort_way:"down",limit:this.pageSize,page:t,vip:1},privateUrl:{url:d.m.appUrl}}).then(function(t){"ok"===t.result&&t.data&&0!==t.data.total_num?(e.data=t.data.data,e.totalPage=Math.ceil((t.data.total_num||0)/e.pageSize),e.statusCode=""):(e.data=[],e.totalPage=0,e.statusCode=d.k.empty)}).catch(function(){e.data=[],e.totalPage=0,e.statusCode=d.k.error})},searchPriData:function(t,a){var e=this;n.a.get("pritpl").searchPriTpl({data:{term:a,is_push:0,limit:this.pageSize,page:t,vip:1},privateUrl:{url:d.m.appUrl}}).then(function(t){"ok"===t.result&&t.data&&0!==t.data.total_num?(e.data=t.data.moban,e.totalPage=Math.ceil((t.data.total_num||0)/e.pageSize),e.statusCode=""):(e.data=[],e.totalPage=0,e.statusCode=d.k.empty,_.a.sendLocal("private_result_empty",a))}).catch(function(){e.data=[],e.totalPage=0,e.statusCode=d.k.error})},isCurrentTag:function(t){return this.currentTag==t},ajustTplLayout:function(){this.limit=m.b.getLimit(this.$refs&&this.$refs.nSearch,"menu")},openFile:function(t){"word"===this.query.searchType?_.a.send("local_search_page","local_template_click"):_.a.sendLocal("local_template_click"),void 0===this.currentTag.value?f.a.openLocalFile({filePath:t.filePath,mod:1}).then(function(t){}):this.downloadPri(t)},addWindowStatusListener:function(){var t=this;window.onresize=function(){t.ajustTplLayout()}}}),mounted:function(){this.addWindowStatusListener()},computed:s()({},Object(o.c)({appName:"getAppName"}),{pageSize:function(){return 5*this.limit}}),components:{GoHome:r.a,Pagination:u.a,DataStatus:l.a,EpTxtList:h.a,SearchEp:c.a},watch:{limit:function(){"local"==m.b.getRoutePath(this.$route.path)[0]&&this.limit&&(this.ajustTplLayout(),this.page=1,this.getData())}}},w={render:function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"m-container"},[e("div",{directives:[{name:"goto-top",rawName:"v-goto-top"}],ref:"scroller",staticClass:"m-main"},[e("div",{ref:"nSearch",staticClass:"m-main-in"},[e("GoHome"),t._v(" "),e("div",{staticClass:"top-search"},[e("SearchEp",{attrs:{theme:"search-box",defaultValue:t.word,from:"local_page"}})],1),t._v(" "),e("div",{ref:"fixBar",staticClass:"top-filter g-clearfloat g-should-fix-wrapper-1"},[e("div",{staticClass:"g-should-fix",class:{"g-fixed":t.isFixed}},[e("SearchEp",{directives:[{name:"show",rawName:"v-show",value:t.isFixed,expression:"isFixed"}],style:{float:"right",marginTop:"-8px"},attrs:{defaultValue:t.word,from:"local_page"}})],1)]),t._v(" "),e("div",{staticStyle:{"padding-top":"30px"}},[e("ul",{directives:[{name:"show",rawName:"v-show",value:t.privateTags.length||t.localTags.length,expression:"privateTags.length || localTags.length"}],staticClass:"theme-tag g-clearfloat"},[e("li",[e("p",[t._l(t.privateTags,function(a,i){return[e("a",{class:{active:t.isCurrentTag(a)},attrs:{href:"javascript:void(0)"},on:{click:function(e){t.clickTag(a,i)}}},[t._v(t._s(a.tagName))]),t._v(" "),e("span",{staticClass:"line"})]}),t._v(" "),t._l(t.localTags,function(a,i){return[e("a",{class:{active:t.isCurrentTag(a)},attrs:{href:"javascript:void(0)"},on:{click:function(e){t.clickTag(a,i)}}},[t._v(t._s(a.tagName))]),t._v(" "),e("span",{staticClass:"line"})]})],2)])])]),t._v(" "),e("div",[e("DataStatus",{attrs:{statusCode:t.statusCode},on:{retry:t.retry}})],1),t._v(" "),e("div",{directives:[{name:"show",rawName:"v-show",value:!t.statusCode,expression:"!statusCode"}],staticClass:"theme-list"},[e("EpTxtList",{attrs:{limit:t.limit,data:t.data},on:{openFile:t.openFile}}),t._v(" "),e("Pagination",{attrs:{totalPage:t.totalPage,page:t.page},on:{gotoPage:t.gotoPage}})],1)],1)])])},staticRenderFns:[]};var C=e("VU/8")(T,w,!1,function(t){e("foya")},"data-v-fff80688",null);a.default=C.exports},foya:function(t,a){}});