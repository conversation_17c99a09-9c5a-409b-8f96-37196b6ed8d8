#include <stdafx.h>
#include "kxmobilescanwidget.h"
#include "kmobilescanmanager.h"
#include "ksolite/kcoreapplication.h"
#include <ksolite/kcefview/kcefviewwidget.h>
#include <kliteui/klitestyle.h>
#include <public_header/kliteui/kdrawhelper.h>
#include <public_header/kliteui/kliteshadowborder.h>
#include <public_header/kliteui/uicontrol/klitecontentdlg.h>

namespace
{
	constexpr const int k_widgetWidth = 440;
	constexpr const int k_widgetHeight = 400;
	constexpr const int k_miniradius = 4;
}

KxMobileScanWidget::KxMobileScanWidget(QWidget* parent)
	: QDialog(parent)
{
}

KxMobileScanWidget::~KxMobileScanWidget()
{
	unInit();
}

bool KxMobileScanWidget::init()
{
	QString resPath;
#ifdef Q_OS_DARWIN
	setAttribute(Qt::WA_TranslucentBackground, true);
	setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
	resPath = krt::dirs::resources() + "/addons/kwebmobilescan/entry.html";
#else
	resPath = krt::pluginconfig::getPluginPath("kwebmobilescan") + QDir::separator() + "entry.html";
#endif
	QFile file(resPath);
	if (!file.exists())
	{
		KxLoggerLite::writeInfo(KPluginNameW,
			QString("%1:%2 not exists").arg(__FUNCTION__).arg(resPath).toStdWString());
		return false;
	}
	const QString htmlPath = "file:///" + resPath;
	setObjectName("MobileScanWidget");
#ifndef Q_OS_DARWIN
	initShadowBorder();
#endif
	setFixedSize(KLiteStyle::dpiScaledSize(QSize(k_widgetWidth, k_widgetHeight)));
	auto layout = new QHBoxLayout(this);
	layout->setContentsMargins(QMargins(0, 0, 0, 0));
	setLayout(layout);
	initWebView(htmlPath);
	KMobileScanManager::getMobileScanManager()->init();
	connect(KMobileScanManager::getMobileScanManager(), &KMobileScanManager::webDialogClose, this, &KxMobileScanWidget::onDialogClose);
	connect(KMobileScanManager::getMobileScanManager(), &KMobileScanManager::sigSizeChange, this, &KxMobileScanWidget::onSizeChange);
	resetGeomotry();
	return true;
}

void KxMobileScanWidget::unInit()
{
	if (KMobileScanManager::getMobileScanManager())
	{
		disconnect(KMobileScanManager::getMobileScanManager(), &KMobileScanManager::webDialogClose, this, &KxMobileScanWidget::onDialogClose);
		disconnect(KMobileScanManager::getMobileScanManager(), &KMobileScanManager::sigSizeChange, this, &KxMobileScanWidget::onSizeChange);
	}
}

void KxMobileScanWidget::initShadowBorder()
{
	const qreal radius = KLiteStyle::dpiScaled(k_miniradius);
	KLiteShadowBorder* pBorder = new KLiteShadowBorder(this, this, false);
	pBorder->setFundamentalStyle(KLiteShadowBorder::ShadowMenu);
	pBorder->setRadius(radius);
	pBorder->setNeedShadowGrabToHostCorner(true);
	pBorder->setCornerShrinkedHost(false);
	pBorder->setHostShrinkMargins(KLiteStyle::dpiScaledMargins(QMargins(0, 0, 1, 1)));
	QColor shadowColor = KDrawHelper::getColorFromTheme("CommonThemePalette", "kd-color-line-regular");
	shadowColor.setAlpha(21);
	pBorder->setFundamentalShadowColor(shadowColor);
}

void KxMobileScanWidget::initWebView(const QString& htmlPath)
{
	m_cefView = new KCefViewWidget(this);
#ifdef Q_OS_DARWIN
	m_cefView->setProperty("isGlassEffect", true);
#endif
	KWebviewParams webViewParams;
	webViewParams.url = htmlPath;
	webViewParams.useCookie = true;
	webViewParams.viewID = QString::number(qint64(this));
	webViewParams.enableDrag = true;
	webViewParams.enablePopup = true;
	webViewParams.enableEscQuit = false;
	webViewParams.hasFocus = true;
	m_cefView->init(webViewParams);
	layout()->addWidget(m_cefView);
}

void KxMobileScanWidget::onDialogClose()
{
	reject();
}

void KxMobileScanWidget::onSizeChange(int nWidth, int nHeight)
{
	setFixedSize(KLiteStyle::dpiScaledSize(QSize(nWidth, nHeight)));
	resetGeomotry();
}

QStringList KxMobileScanWidget::getScanfiles()
{
	KMobileScanManager::getMobileScanManager()->clear();
	return KMobileScanManager::getMobileScanManager()->getScanfiles();
}

void KxMobileScanWidget::resetGeomotry()
{
#ifdef Q_OS_DARWIN
	QDesktopWidget desktop;
	QRect primaryRect = desktop.availableGeometry(desktop.primaryScreen());
	int x = primaryRect.x() + (primaryRect.width() - width()) / 2;
	int y = primaryRect.y() + (primaryRect.height() - height()) / 2;
	move(x, y);
#endif
}
