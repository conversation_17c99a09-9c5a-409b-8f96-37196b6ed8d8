﻿#ifndef __KPROMETHEUS_NEWDOCSWAITWIDGET_H__
#define __KPROMETHEUS_NEWDOCSWAITWIDGET_H__
#include <public_header/kliteui/kdrawhelper.h>
#include <public_header/kliteui/uicontrol/klitewidget.h>
class KLiteLabel;
class KLiteButton;
class KLiteProgressRingGroup;

class KPromeNewdocsLoadingWidget: public KLiteWidget
{
	Q_OBJECT
public:
	explicit KPromeNewdocsLoadingWidget(bool bSupport, QWidget* parent);

public slots:
	void onStartLoading();
	void onStopLoading();

private:
	void initUI();
	void initTimer();
	void initTimeout();

private:
	QVBoxLayout* m_vb = nullptr;
	KLiteProgressRingGroup* m_progressBar = nullptr;
	QTimer* m_timer = nullptr;
	int m_msTimeout;
	bool m_bSupportDefaultPage = false;
};

class KPromeNewdocsErrorWidget : public KLiteWidget
{
	Q_OBJECT
public:
	explicit KPromeNewdocsErrorWidget(QWidget* parent);
signals:
	void sigLoadRetry();
private:
	void initUI();
private:
	QVBoxLayout* m_vb = nullptr;
	KLiteLabel* m_textLabel = nullptr;
	KLiteButton* m_liteBtn = nullptr;
	KLiteLabel* m_errorIconLbl = nullptr;
};

class KPromeNewdocsWaitWidget: public KLiteWidget
{
	Q_OBJECT
public:
	explicit KPromeNewdocsWaitWidget(bool bComponentType, QWidget* parent);
signals:
	void sigLoadRetry();
	void sigLoadTimeout();

public slots:
	void onPluginLoadFailed();
	void onWebLoadfinished();

protected :
	void paintEvent(QPaintEvent* event) override;

private:
	void initUI();
private:
	bool m_bComponentType = false;
	QVBoxLayout* m_vbLayout = nullptr;
	QStackedWidget* m_stackWidget = nullptr;
	KPromeNewdocsLoadingWidget* m_loadingWidget = nullptr;
	KPromeNewdocsErrorWidget* m_loadErrorWidget = nullptr;
};

#endif
